{"abi": [{"type": "constructor", "inputs": [{"name": "vaultFactory_", "type": "address", "internalType": "address"}, {"name": "farmFactory_", "type": "address", "internalType": "address"}, {"name": "name_", "type": "string", "internalType": "string"}, {"name": "version_", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "CALLER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "DEFAULT_ADMIN_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "MELLOW_VAULT_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "SYMBIOTIC_FARM_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "SYMBIOTIC_VAULT_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "farmFactory", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract ISymbioticRegistry"}], "stateMutability": "view"}, {"type": "function", "name": "getRoleAdmin", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "getRoleMember", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getRoleMemberCount", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getRoleMembers", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "grantRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "hasRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "hasSupportedRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "renounceRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "callerConfirmation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "revokeRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportedRoleAt", "inputs": [{"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "supportedRoles", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "vaultFactory", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract ISymbioticRegistry"}], "stateMutability": "view"}, {"type": "function", "name": "verifyCall", "inputs": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "callData", "type": "bytes", "internalType": "bytes"}, {"name": "", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "RoleAdded", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "RoleAdminChanged", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "previousAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "newAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "RoleGranted", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RoleRemoved", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "RoleRevoked", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AccessControlBadConfirmation", "inputs": []}, {"type": "error", "name": "AccessControlUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "neededRole", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "ZeroValue", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "309:3521:135:-:0;;;968:266;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1092:5;1099:8;308:5:134;315:8;432:49:130;;;;;;;;;;;;;;-1:-1:-1;;;432:49:130;;;465:5;472:8;432:19;;;:49;;:::i;:::-;408:73;;491:22;:20;:22::i;:::-;-1:-1:-1;335:22:134::1;::::0;-1:-1:-1;335:20:134::1;:22::i;:::-;-1:-1:-1::0;;;;;;;;1123:48:135;;::::1;;::::0;-1:-1:-1;1181:46:135::1;;::::0;309:3521;;656:343:113;761:7;986:4;970:22;;969:23;941:1;908:12;922:4;928:7;856:80;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;846:91;;;;;;838:100;;:104;;;;:::i;:::-;810:146;;;;;;2601:25:192;;2589:2;2574:18;;2455:177;810:146:113;;;;;;;;;;;;;787:179;;;;;;:205;780:212;;656:343;;;;;:::o;7709:422:3:-;7824:30;7857:26;:24;:26::i;:::-;7898:15;;;;-1:-1:-1;7898:15:3;;;;;7894:76;;;7936:23;;-1:-1:-1;;;7936:23:3;;;;;;;;;;;7894:76;7983:14;;-1:-1:-1;;;;;7983:14:3;;;:34;7979:146;;8033:33;;-1:-1:-1;;;;;;8033:33:3;-1:-1:-1;;;;;8033:33:3;;;;;8085:29;;2781:50:192;;;8085:29:3;;2769:2:192;2754:18;8085:29:3;;;;;;;7979:146;7758:373;7709:422::o;9071:205::-;9129:30;;3147:66;9186:27;9171:42;9071:205;-1:-1:-1;;9071:205:3:o;14:177:192:-;93:13;;-1:-1:-1;;;;;135:31:192;;125:42;;115:70;;181:1;178;171:12;115:70;14:177;;;:::o;196:127::-;257:10;252:3;248:20;245:1;238:31;288:4;285:1;278:15;312:4;309:1;302:15;328:1144;435:6;443;451;459;512:3;500:9;491:7;487:23;483:33;480:53;;;529:1;526;519:12;480:53;552:40;582:9;552:40;:::i;:::-;542:50;;611:49;656:2;645:9;641:18;611:49;:::i;:::-;704:2;689:18;;683:25;601:59;;-1:-1:-1;;;;;;757:14:192;;;754:34;;;784:1;781;774:12;754:34;822:6;811:9;807:22;797:32;;867:7;860:4;856:2;852:13;848:27;838:55;;889:1;886;879:12;838:55;918:2;912:9;940:2;936;933:10;930:36;;;946:18;;:::i;:::-;1021:2;1015:9;989:2;1075:13;;-1:-1:-1;;1071:22:192;;;1095:2;1067:31;1063:40;1051:53;;;1119:18;;;1139:22;;;1116:46;1113:72;;;1165:18;;:::i;:::-;1205:10;1201:2;1194:22;1240:2;1232:6;1225:18;1280:7;1275:2;1270;1266;1262:11;1258:20;1255:33;1252:53;;;1301:1;1298;1291:12;1252:53;1350:2;1345;1341;1337:11;1332:2;1324:6;1320:15;1314:39;1395:1;1373:15;;;1390:2;1369:24;1362:35;;;;-1:-1:-1;1462:2:192;1447:18;;;;1441:25;328:1144;;;;-1:-1:-1;;;;;328:1144:192:o;1477:212::-;1519:3;1557:5;1551:12;1601:6;1594:4;1587:5;1583:16;1578:3;1572:36;1663:1;1627:16;;1652:13;;;-1:-1:-1;1627:16:192;;1477:212;-1:-1:-1;1477:212:192:o;1694:526::-;2032:33;2027:3;2020:46;2002:3;2088:66;2114:39;2149:2;2144:3;2140:12;2132:6;2114:39;:::i;:::-;2106:6;2088:66;:::i;:::-;2163:21;;;-1:-1:-1;;2211:2:192;2200:14;;1694:526;-1:-1:-1;;1694:526:192:o;2225:225::-;2292:9;;;2313:11;;;2310:134;;;2366:10;2361:3;2357:20;2354:1;2347:31;2401:4;2398:1;2391:15;2429:4;2426:1;2419:15;2637:200;309:3521:135;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x608060405234801561000f575f80fd5b5060043610610132575f3560e01c806370e46bcb116100b4578063a2cb31e511610079578063a2cb31e5146102b6578063a3246ad3146102c9578063c7cc5f24146102e9578063ca15c87314610310578063d547741f14610323578063d8a06f7314610336575f80fd5b806370e46bcb1461024f578063774237fc146102625780639010d07c1461028957806391d148541461029c578063a217fddf146102af575f80fd5b8063403ec940116100fa578063403ec940146101bb578063419a2053146101e2578063439fab91146101ea5780634a46b4cf146101fd5780636d25e17514610210575f80fd5b806301ffc9a714610136578063248a9ca31461015e5780632f2ff15d1461017f57806336568abe146101945780633cfa1929146101a7575b5f80fd5b610149610144366004610fd7565b61035d565b60405190151581526020015b60405180910390f35b61017161016c366004610ffe565b610387565b604051908152602001610155565b61019261018d366004611029565b6103a7565b005b6101926101a2366004611029565b6103c9565b6101715f8051602061157c83398151915281565b6101717fefe4de88b49c8b8f17f4c2ff9d6f775c4747e6bf57255a802349a8ebf484223981565b610171610401565b6101926101f836600461109c565b610430565b61014961020b366004610ffe565b61062e565b6102377f000000000000000000000000000000000000000000000000000000000000000081565b6040516001600160a01b039091168152602001610155565b61014961025d3660046110db565b610659565b6101717f3b48a429628aa7e067d22e77e8f4703a9d84dc4efdabe07716c4d0322e7f271481565b610237610297366004611173565b610992565b6101496102aa366004611029565b6109bf565b6101715f81565b6101716102c4366004610ffe565b6109f5565b6102dc6102d7366004610ffe565b610a20565b6040516101559190611193565b6101717f909c8b12bf402721ba65c08a75ac086b15941cd32bcb6ecba9dc69a7757d1ca981565b61017161031e366004610ffe565b610a50565b610192610331366004611029565b610a74565b6102377f000000000000000000000000000000000000000000000000000000000000000081565b5f6001600160e01b03198216635a05180f60e01b1480610381575061038182610a90565b92915050565b5f9081525f8051602061159c833981519152602052604090206001015490565b6103b082610387565b6103b981610ac4565b6103c38383610ad1565b50505050565b6001600160a01b03811633146103f25760405163334bd91960e11b815260040160405180910390fd5b6103fc8282610b4b565b505050565b5f61042b7f0000000000000000000000000000000000000000000000000000000000000000610bc7565b905090565b5f610439610bd0565b805490915060ff600160401b820416159067ffffffffffffffff165f811580156104605750825b90505f8267ffffffffffffffff16600114801561047c5750303b155b90508115801561048a575080155b156104a85760405163f92ee8a960e01b815260040160405180910390fd5b845467ffffffffffffffff1916600117855583156104d257845460ff60401b1916600160401b1785555b5f80806104e1898b018b6112b3565b919450925090506001600160a01b03831661050f57604051637c946ed760e01b815260040160405180910390fd5b6105195f84610ad1565b505f5b82518110156105db575f6001600160a01b031683828151811061054157610541611382565b60200260200101516001600160a01b0316148061057857505f801b82828151811061056e5761056e611382565b6020026020010151145b1561059657604051637c946ed760e01b815260040160405180910390fd5b6105d28282815181106105ab576105ab611382565b60200260200101518483815181106105c5576105c5611382565b6020026020010151610ad1565b5060010161051c565b50505050831561062557845460ff60401b19168555604051600181527fc7f505b2f371ae2175ee4913f4499e1f2633a7b5936321eed1cdaeb6115181d29060200160405180910390a15b50505050505050565b5f6103817f000000000000000000000000000000000000000000000000000000000000000083610bf8565b5f851515806106685750600484105b8061069a57506106987f3b48a429628aa7e067d22e77e8f4703a9d84dc4efdabe07716c4d0322e7f2714896109bf565b155b156106a657505f610987565b5f6106b46004828789611396565b6106bd916113bd565b90506106e97f909c8b12bf402721ba65c08a75ac086b15941cd32bcb6ecba9dc69a7757d1ca9896109bf565b1561086557632e06043760e21b6001600160e01b03198216016107d7575f80610715876004818b611396565b81019061072291906113ed565b9150915061073d5f8051602061157c833981519152836109bf565b1580610747575080155b15610757575f9350505050610987565b8787604051610767929190611417565b6040519081900381206001600160a01b03841660248301526044820183905290849060640160408051601f198184030181529190526020810180516001600160e01b03166001600160e01b0319909316929092178252519020146107d0575f9350505050610987565b5050610981565b630c010c5d60e01b6001600160e01b03198216016107fe575f80610715876004818b611396565b632a9609b560e11b6001600160e01b031982160161085c575f80610825876004818b611396565b81019061083291906113ed565b9150915061084d5f8051602061157c833981519152836109bf565b610757575f9350505050610987565b5f915050610987565b61088f7fefe4de88b49c8b8f17f4c2ff9d6f775c4747e6bf57255a802349a8ebf4842239896109bf565b1561085c5763a2f4adfb60e01b6001600160e01b031982160161085c575f80806108bc886004818c611396565b8101906108c99190611426565b9250925092506108e65f8051602061157c833981519152846109bf565b15806108f957506001600160a01b038216155b1561090a575f945050505050610987565b888860405161091a929190611417565b604051809103902084848484604051602401610938939291906114d9565b60408051601f198184030181529190526020810180516001600160e01b03166001600160e01b03199093169290921782525190201461097d575f945050505050610987565b5050505b60019150505b979650505050505050565b5f8281525f8051602061155c8339815191526020819052604082206109b79084610c0f565b949350505050565b5f9182525f8051602061159c833981519152602090815260408084206001600160a01b0393909316845291905290205460ff1690565b5f6103817f000000000000000000000000000000000000000000000000000000000000000083610c0f565b5f8181525f8051602061155c8339815191526020819052604090912060609190610a4990610c1a565b9392505050565b5f8181525f8051602061155c833981519152602081905260408220610a4990610bc7565b610a7d82610387565b610a8681610ac4565b6103c38383610b4b565b5f6001600160e01b03198216637965db0b60e01b148061038157506301ffc9a760e01b6001600160e01b0319831614610381565b610ace8133610c26565b50565b5f610adc8383610c67565b15610b4357610b0b7f000000000000000000000000000000000000000000000000000000000000000084610ca9565b15610b3b5760405183907fea0f1c470fa813c725756c036120b6688028969f5afbc607918fcd1ff9229435905f90a25b506001610381565b505f92915050565b5f610b568383610cb4565b15610b4357610b6483610a50565b5f03610b3b57610b947f000000000000000000000000000000000000000000000000000000000000000084610ced565b5060405183907f4c9a714f78b79aa08074addab7cbdb196cccdf6d67efbf0b99914db8a6b08e73905f90a2506001610381565b5f610381825490565b5f807ff0c57e16840df040f15088dc2f81fe391c3923bec73e23a9662efc9c229c6a00610381565b5f8181526001830160205260408120541515610a49565b5f610a498383610cf8565b60605f610a4983610d1e565b610c3082826109bf565b610c635760405163e2517d3f60e01b81526001600160a01b03821660048201526024810183905260440160405180910390fd5b5050565b5f5f8051602061155c83398151915281610c818585610d77565b905080156109b7575f858152602083905260409020610ca09085610e18565b50949350505050565b5f610a498383610e28565b5f5f8051602061155c83398151915281610cce8585610e74565b905080156109b7575f858152602083905260409020610ca09085610eed565b5f610a498383610efd565b5f825f018281548110610d0d57610d0d611382565b905f5260205f200154905092915050565b6060815f01805480602002602001604051908101604052809291908181526020018280548015610d6b57602002820191905f5260205f20905b815481526020019060010190808311610d57575b50505050509050919050565b5f5f8051602061159c833981519152610d9084846109bf565b610e0f575f848152602082815260408083206001600160a01b03871684529091529020805460ff19166001179055610dc53390565b6001600160a01b0316836001600160a01b0316857f2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d60405160405180910390a46001915050610381565b5f915050610381565b5f610a49836001600160a01b0384165b5f818152600183016020526040812054610e6d57508154600181810184555f848152602080822090930184905584548482528286019093526040902091909155610381565b505f610381565b5f5f8051602061159c833981519152610e8d84846109bf565b15610e0f575f848152602082815260408083206001600160a01b0387168085529252808320805460ff1916905551339287917ff6391f5c32d9c69d2a47ea670b442974b53935d1edc7fd64eb21e047a839171b9190a46001915050610381565b5f610a49836001600160a01b0384165b5f8181526001830160205260408120548015610e0f575f610f1f600183611528565b85549091505f90610f3290600190611528565b9050808214610f91575f865f018281548110610f5057610f50611382565b905f5260205f200154905080875f018481548110610f7057610f70611382565b5f918252602080832090910192909255918252600188019052604090208390555b8554869080610fa257610fa2611547565b600190038181905f5260205f20015f90559055856001015f8681526020019081526020015f205f905560019350505050610381565b5f60208284031215610fe7575f80fd5b81356001600160e01b031981168114610a49575f80fd5b5f6020828403121561100e575f80fd5b5035919050565b6001600160a01b0381168114610ace575f80fd5b5f806040838503121561103a575f80fd5b82359150602083013561104c81611015565b809150509250929050565b5f8083601f840112611067575f80fd5b50813567ffffffffffffffff81111561107e575f80fd5b602083019150836020828501011115611095575f80fd5b9250929050565b5f80602083850312156110ad575f80fd5b823567ffffffffffffffff8111156110c3575f80fd5b6110cf85828601611057565b90969095509350505050565b5f805f805f805f60a0888a0312156110f1575f80fd5b87356110fc81611015565b9650602088013561110c81611015565b955060408801359450606088013567ffffffffffffffff8082111561112f575f80fd5b61113b8b838c01611057565b909650945060808a0135915080821115611153575f80fd5b506111608a828b01611057565b989b979a50959850939692959293505050565b5f8060408385031215611184575f80fd5b50508035926020909101359150565b602080825282518282018190525f9190848201906040850190845b818110156111d35783516001600160a01b0316835292840192918401916001016111ae565b50909695505050505050565b634e487b7160e01b5f52604160045260245ffd5b604051601f8201601f1916810167ffffffffffffffff8111828210171561121c5761121c6111df565b604052919050565b5f67ffffffffffffffff82111561123d5761123d6111df565b5060051b60200190565b5f82601f830112611256575f80fd5b8135602061126b61126683611224565b6111f3565b8083825260208201915060208460051b87010193508684111561128c575f80fd5b602086015b848110156112a85780358352918301918301611291565b509695505050505050565b5f805f606084860312156112c5575f80fd5b83356112d081611015565b925060208481013567ffffffffffffffff808211156112ed575f80fd5b818701915087601f830112611300575f80fd5b813561130e61126682611224565b81815260059190911b8301840190848101908a83111561132c575f80fd5b938501935b8285101561135357843561134481611015565b82529385019390850190611331565b96505050604087013592508083111561136a575f80fd5b505061137886828701611247565b9150509250925092565b634e487b7160e01b5f52603260045260245ffd5b5f80858511156113a4575f80fd5b838611156113b0575f80fd5b5050820193919092039150565b6001600160e01b031981358181169160048510156113e55780818660040360031b1b83161692505b505092915050565b5f80604083850312156113fe575f80fd5b823561140981611015565b946020939093013593505050565b818382375f9101908152919050565b5f805f60608486031215611438575f80fd5b833561144381611015565b925060208481013561145481611015565b9250604085013567ffffffffffffffff80821115611470575f80fd5b818701915087601f830112611483575f80fd5b813581811115611495576114956111df565b6114a7601f8201601f191685016111f3565b915080825288848285010111156114bc575f80fd5b80848401858401375f848284010152508093505050509250925092565b5f60018060a01b0380861683528085166020840152506060604083015282518060608401528060208501608085015e5f608082850101526080601f19601f830116840101915050949350505050565b8181038181111561038157634e487b7160e01b5f52601160045260245ffd5b634e487b7160e01b5f52603160045260245ffdfec1f6fe24621ce81ec5827caf0253cadb74709b061630e6b55e82371705932000abf80feb2ee273d208fadd5ac778f2fc719588bcc130607c0fcddd743ce6f6c102dd7bc7dec4dceedda775e58dd541e08a116c6c53815c0bd028192f7b626800a26469706673582212201132feaecfd9846978c1751f20885da9e1c499efc3f8a5e7d167c2973381140b64736f6c63430008190033", "sourceMap": "309:3521:135:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1695:212:2;;;;;;:::i;:::-;;:::i;:::-;;;470:14:192;;463:22;445:41;;433:2;418:18;1695:212:2;;;;;;;;4759:191:0;;;;;;:::i;:::-;;:::i;:::-;;;828:25:192;;;816:2;801:18;4759:191:0;682:177:192;5246:136:0;;;;;;:::i;:::-;;:::i;:::-;;6348:245;;;;;;:::i;:::-;;:::i;473:114:135:-;;-1:-1:-1;;;;;;;;;;;473:114:135;;593:126;;647:72;593:126;;580:125:130;;;:::i;396:563:134:-;;;;;;:::i;:::-;;:::i;919:142:130:-;;;;;;:::i;:::-;;:::i;914:47:135:-;;;;;;;;-1:-1:-1;;;;;2460:32:192;;;2442:51;;2430:2;2415:18;914:47:135;2268:231:192;1263:2565:135;;;;;;:::i;:::-;;:::i;365:102::-;;403:64;365:102;;2492:233:2;;;;;;:::i;:::-;;:::i;3732:207:0:-;;;;;;:::i;:::-;;:::i;2317:49::-;;2362:4;2317:49;;742:140:130;;;;;;:::i;:::-;;:::i;3658:227:2:-;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;725:128:135:-;;780:73;725:128;;2893:222:2;;;;;;:::i;:::-;;:::i;5662:138:0:-;;;;;;:::i;:::-;;:::i;860:48:135:-;;;;;1695:212:2;1780:4;-1:-1:-1;;;;;;1803:57:2;;-1:-1:-1;;;1803:57:2;;:97;;;1864:36;1888:11;1864:23;:36::i;:::-;1796:104;1695:212;-1:-1:-1;;1695:212:2:o;4759:191:0:-;4824:7;4919:14;;;-1:-1:-1;;;;;;;;;;;4919:14:0;;;;;:24;;;;4759:191::o;5246:136::-;5320:18;5333:4;5320:12;:18::i;:::-;3191:16;3202:4;3191:10;:16::i;:::-;5350:25:::1;5361:4;5367:7;5350:10;:25::i;:::-;;5246:136:::0;;;:::o;6348:245::-;-1:-1:-1;;;;;6441:34:0;;966:10:5;6441:34:0;6437:102;;6498:30;;-1:-1:-1;;;6498:30:0;;;;;;;;;;;6437:102;6549:37;6561:4;6567:18;6549:11;:37::i;:::-;;6348:245;;:::o;580:125:130:-;629:7;655:43;1902:21;655:41;:43::i;:::-;648:50;;580:125;:::o;396:563:134:-;4158:30:3;4191:26;:24;:26::i;:::-;4302:15;;4158:59;;-1:-1:-1;4302:15:3;-1:-1:-1;;;4302:15:3;;;4301:16;;4348:14;;4279:19;4724:16;;:34;;;;;4744:14;4724:34;4704:54;;4768:17;4788:11;:16;;4803:1;4788:16;:50;;;;-1:-1:-1;4816:4:3;4808:25;:30;4788:50;4768:70;;4854:12;4853:13;:30;;;;;4871:12;4870:13;4853:30;4849:91;;;4906:23;;-1:-1:-1;;;4906:23:3;;;;;;;;;;;4849:91;4949:18;;-1:-1:-1;;4949:18:3;4966:1;4949:18;;;4977:67;;;;5011:22;;-1:-1:-1;;;;5011:22:3;-1:-1:-1;;;5011:22:3;;;4977:67;469:13:134::1;::::0;;548:49:::1;::::0;;::::1;559:4:::0;548:49:::1;:::i;:::-;468:129:::0;;-1:-1:-1;468:129:134;-1:-1:-1;468:129:134;-1:-1:-1;;;;;;611:19:134;::::1;607:68;;653:11;;-1:-1:-1::0;;;653:11:134::1;;;;;;;;;;;607:68;684:37;2362:4:0;715:5:134::0;684:10:::1;:37::i;:::-;;736:9;731:222;755:7;:14;751:1;:18;731:222;;;816:1;-1:-1:-1::0;;;;;794:24:134::1;:7;802:1;794:10;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1::0;;;;;794:24:134::1;;:50;;;;842:1;834:10:::0;::::1;822:5;828:1;822:8;;;;;;;;:::i;:::-;;;;;;;:22;794:50;790:107;;;871:11;;-1:-1:-1::0;;;871:11:134::1;;;;;;;;;;;790:107;910:32;921:5;927:1;921:8;;;;;;;;:::i;:::-;;;;;;;931:7;939:1;931:10;;;;;;;;:::i;:::-;;;;;;;910;:32::i;:::-;-1:-1:-1::0;771:3:134::1;;731:222;;;;458:501;;;5068:14:3::0;5064:101;;;5098:23;;-1:-1:-1;;;;5098:23:3;;;5140:14;;-1:-1:-1;7806:50:192;;5140:14:3;;7794:2:192;7779:18;5140:14:3;;;;;;;5064:101;4092:1079;;;;;396:563:134;;:::o;919:142:130:-;982:4;1005:49;1902:21;1049:4;1005:43;:49::i;1263:2565:135:-;1457:4;1477:10;;;;:33;;-1:-1:-1;1509:1:135;1491:19;;1477:33;:63;;;;1515:25;403:64;1536:3;1515:7;:25::i;:::-;1514:26;1477:63;1473:106;;;-1:-1:-1;1563:5:135;1556:12;;1473:106;1589:15;1614:12;1624:1;1589:15;1614:8;;:12;:::i;:::-;1607:20;;;:::i;:::-;1589:38;;1641:36;780:73;1671:5;1641:7;:36::i;:::-;1637:2164;;;-1:-1:-1;;;;;;;;;1697:44:135;;;1693:1407;;1762:18;;1811:12;:8;1820:1;1811:8;;:12;:::i;:::-;1800:44;;;;;;;:::i;:::-;1761:83;;;;1867:38;-1:-1:-1;;;;;;;;;;;1894:10:135;1867:7;:38::i;:::-;1866:39;:54;;;-1:-1:-1;1909:11:135;;1866:54;1862:113;;;1951:5;1944:12;;;;;;;1862:113;2073:8;;2063:19;;;;;;;:::i;:::-;;;;;;;;;-1:-1:-1;;;;;9327:32:192;;2006:52:135;;;9309:51:192;9376:18;;;9369:34;;;2063:19:135;2029:8;;9282:18:192;;2006:52:135;;;-1:-1:-1;;2006:52:135;;;;;;;;;;;;;;-1:-1:-1;;;;;2006:52:135;-1:-1:-1;;;;;;2006:52:135;;;;;;;;;1996:63;;;:86;1992:145;;2113:5;2106:12;;;;;;;1992:145;1743:408;;1637:2164;;1693:1407;-1:-1:-1;;;;;;;;;2161:45:135;;;2157:943;;2227:15;;2273:12;:8;2282:1;2273:8;;:12;:::i;2157:943::-;-1:-1:-1;;;;;;;;;2617:42:135;;;2613:487;;2680:17;;2727:12;:8;2736:1;2727:8;;:12;:::i;:::-;2716:44;;;;;;;:::i;:::-;2679:81;;;;2783:37;-1:-1:-1;;;;;;;;;;;2810:9:135;2783:7;:37::i;:::-;2778:97;;2851:5;2844:12;;;;;;;2613:487;3080:5;3073:12;;;;;1637:2164;3120:35;647:72;3149:5;3120:7;:35::i;:::-;3116:685;;;-1:-1:-1;;;;;;;;;3175:57:135;;;3171:577;;3253:17;;;3339:12;:8;3348:1;3339:8;;:12;:::i;:::-;3328:51;;;;;;;:::i;:::-;3252:127;;;;;;3402:37;-1:-1:-1;;;;;;;;;;;3429:9:135;3402:7;:37::i;:::-;3401:38;:61;;;-1:-1:-1;;;;;;3443:19:135;;;3401:61;3397:120;;;3493:5;3486:12;;;;;;;;3397:120;3619:8;;3609:19;;;;;;;:::i;:::-;;;;;;;;3571:8;3581:9;3592:5;3599:4;3548:56;;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;3548:56:135;;;;;;;;;;;;;;-1:-1:-1;;;;;3548:56:135;-1:-1:-1;;;;;;3548:56:135;;;;;;;;;3538:67;;;:90;3534:149;;3659:5;3652:12;;;;;;;;3534:149;3234:463;;;3116:685;3817:4;3810:11;;;1263:2565;;;;;;;;;;:::o;2492:233:2:-;2573:7;2688:20;;;-1:-1:-1;;;;;;;;;;;2688:20:2;;;;;;;:30;;2712:5;2688:23;:30::i;:::-;2681:37;2492:233;-1:-1:-1;;;;2492:233:2:o;3732:207:0:-;3809:4;3901:14;;;-1:-1:-1;;;;;;;;;;;3901:14:0;;;;;;;;-1:-1:-1;;;;;3901:31:0;;;;;;;;;;;;;;;3732:207::o;742:140:130:-;805:7;831:44;1902:21;869:5;831:37;:44::i;3658:227:2:-;3753:40;3849:20;;;-1:-1:-1;;;;;;;;;;;3849:20:2;;;;;;;;3725:16;;1403:38;3849:29;;:27;:29::i;:::-;3842:36;3658:227;-1:-1:-1;;;3658:227:2:o;2893:222::-;2964:7;3079:20;;;-1:-1:-1;;;;;;;;;;;3079:20:2;;;;;;;:29;;:27;:29::i;5662:138:0:-;5737:18;5750:4;5737:12;:18::i;:::-;3191:16;3202:4;3191:10;:16::i;:::-;5767:26:::1;5779:4;5785:7;5767:11;:26::i;3443:202::-:0;3528:4;-1:-1:-1;;;;;;3551:47:0;;-1:-1:-1;;;3551:47:0;;:87;;-1:-1:-1;;;;;;;;;;1134:40:8;;;3602:36:0;1035:146:8;4148:103:0;4214:30;4225:4;966:10:5;4214::0;:30::i;:::-;4148:103;:::o;1094:319:130:-;1180:4;1200:31;1217:4;1223:7;1200:16;:31::i;:::-;1196:189;;;1251:44;1902:21;1290:4;1251:38;:44::i;:::-;1247:103;;;1320:15;;1330:4;;1320:15;;;;;1247:103;-1:-1:-1;1370:4:130;1363:11;;1196:189;-1:-1:-1;1401:5:130;1094:319;;;;:::o;1419:373::-;1506:4;1526:32;1544:4;1550:7;1526:17;:32::i;:::-;1522:242;;;1578:24;1597:4;1578:18;:24::i;:::-;1606:1;1578:29;1574:155;;1627:47;1902:21;1669:4;1627:41;:47::i;:::-;-1:-1:-1;1697:17:130;;1709:4;;1697:17;;;;;-1:-1:-1;1749:4:130;1742:11;;7693:115:72;7756:7;7782:19;7790:3;5202:18;;5120:107;9071:205:3;9129:30;;3147:66;9186:27;8819:122;7474:138:72;7554:4;5006:21;;;:14;;;:21;;;;;;:26;;7577:28;4910:129;10987:156;11061:7;11111:22;11115:3;11127:5;11111:3;:22::i;11683:273::-;11746:16;11774:22;11799:19;11807:3;11799:7;:19::i;4381:197:0:-;4469:22;4477:4;4483:7;4469;:22::i;:::-;4464:108;;4514:47;;-1:-1:-1;;;4514:47:0;;-1:-1:-1;;;;;9327:32:192;;4514:47:0;;;9309:51:192;9376:18;;;9369:34;;;9282:18;;4514:47:0;;;;;;;4464:108;4381:197;;:::o;3987:348:2:-;4073:4;-1:-1:-1;;;;;;;;;;;4073:4:2;4193:31;4210:4;4216:7;4193:16;:31::i;:::-;4178:46;;4238:7;4234:71;;;4261:14;:20;;;;;;;;;;:33;;4286:7;4261:24;:33::i;:::-;;4321:7;3987:348;-1:-1:-1;;;;3987:348:2:o;6576:123:72:-;6646:4;6669:23;6674:3;6686:5;6669:4;:23::i;4438:353:2:-;4525:4;-1:-1:-1;;;;;;;;;;;4525:4:2;4645:32;4663:4;4669:7;4645:17;:32::i;:::-;4630:47;;4691:7;4687:74;;;4714:14;:20;;;;;;;;;;:36;;4742:7;4714:27;:36::i;6867:129:72:-;6940:4;6963:26;6971:3;6983:5;6963:7;:26::i;5569:118::-;5636:7;5662:3;:11;;5674:5;5662:18;;;;;;;;:::i;:::-;;;;;;;;;5655:25;;5569:118;;;;:::o;6227:109::-;6283:16;6318:3;:11;;6311:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6227:109;;;:::o;7270:387:0:-;7347:4;-1:-1:-1;;;;;;;;;;;7437:22:0;7445:4;7451:7;7437;:22::i;:::-;7432:219;;7475:8;:14;;;;;;;;;;;-1:-1:-1;;;;;7475:31:0;;;;;;;;;:38;;-1:-1:-1;;7475:38:0;7509:4;7475:38;;;7559:12;966:10:5;;887:96;7559:12:0;-1:-1:-1;;;;;7532:40:0;7550:7;-1:-1:-1;;;;;7532:40:0;7544:4;7532:40;;;;;;;;;;7593:4;7586:11;;;;;7432:219;7635:5;7628:12;;;;;9332:150:72;9402:4;9425:50;9430:3;-1:-1:-1;;;;;9450:23:72;;2336:406;2399:4;5006:21;;;:14;;;:21;;;;;;2415:321;;-1:-1:-1;2457:23:72;;;;;;;;:11;:23;;;;;;;;;;;;;2639:18;;2615:21;;;:14;;;:21;;;;;;:42;;;;2671:11;;2415:321;-1:-1:-1;2720:5:72;2713:12;;7894:388:0;7972:4;-1:-1:-1;;;;;;;;;;;8061:22:0;8069:4;8075:7;8061;:22::i;:::-;8057:219;;;8133:5;8099:14;;;;;;;;;;;-1:-1:-1;;;;;8099:31:0;;;;;;;;;;:39;;-1:-1:-1;;8099:39:0;;;8157:40;966:10:5;;8099:14:0;;8157:40;;8133:5;8157:40;8218:4;8211:11;;;;;9650:156:72;9723:4;9746:53;9754:3;-1:-1:-1;;;;;9774:23:72;;2910:1368;2976:4;3105:21;;;:14;;;:21;;;;;;3141:13;;3137:1135;;3508:18;3529:12;3540:1;3529:8;:12;:::i;:::-;3575:18;;3508:33;;-1:-1:-1;3555:17:72;;3575:22;;3596:1;;3575:22;:::i;:::-;3555:42;;3630:9;3616:10;:23;3612:378;;3659:17;3679:3;:11;;3691:9;3679:22;;;;;;;;:::i;:::-;;;;;;;;;3659:42;;3826:9;3800:3;:11;;3812:10;3800:23;;;;;;;;:::i;:::-;;;;;;;;;;;;:35;;;;3939:25;;;:14;;;:25;;;;;:36;;;3612:378;4068:17;;:3;;:17;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;4171:3;:14;;:21;4186:5;4171:21;;;;;;;;;;;4164:28;;;4214:4;4207:11;;;;;;;14:286:192;72:6;125:2;113:9;104:7;100:23;96:32;93:52;;;141:1;138;131:12;93:52;167:23;;-1:-1:-1;;;;;;219:32:192;;209:43;;199:71;;266:1;263;256:12;497:180;556:6;609:2;597:9;588:7;584:23;580:32;577:52;;;625:1;622;615:12;577:52;-1:-1:-1;648:23:192;;497:180;-1:-1:-1;497:180:192:o;864:131::-;-1:-1:-1;;;;;939:31:192;;929:42;;919:70;;985:1;982;975:12;1000:315;1068:6;1076;1129:2;1117:9;1108:7;1104:23;1100:32;1097:52;;;1145:1;1142;1135:12;1097:52;1181:9;1168:23;1158:33;;1241:2;1230:9;1226:18;1213:32;1254:31;1279:5;1254:31;:::i;:::-;1304:5;1294:15;;;1000:315;;;;;:::o;1502:347::-;1553:8;1563:6;1617:3;1610:4;1602:6;1598:17;1594:27;1584:55;;1635:1;1632;1625:12;1584:55;-1:-1:-1;1658:20:192;;1701:18;1690:30;;1687:50;;;1733:1;1730;1723:12;1687:50;1770:4;1762:6;1758:17;1746:29;;1822:3;1815:4;1806:6;1798;1794:19;1790:30;1787:39;1784:59;;;1839:1;1836;1829:12;1784:59;1502:347;;;;;:::o;1854:409::-;1924:6;1932;1985:2;1973:9;1964:7;1960:23;1956:32;1953:52;;;2001:1;1998;1991:12;1953:52;2041:9;2028:23;2074:18;2066:6;2063:30;2060:50;;;2106:1;2103;2096:12;2060:50;2145:58;2195:7;2186:6;2175:9;2171:22;2145:58;:::i;:::-;2222:8;;2119:84;;-1:-1:-1;1854:409:192;-1:-1:-1;;;;1854:409:192:o;2504:1063::-;2621:6;2629;2637;2645;2653;2661;2669;2722:3;2710:9;2701:7;2697:23;2693:33;2690:53;;;2739:1;2736;2729:12;2690:53;2778:9;2765:23;2797:31;2822:5;2797:31;:::i;:::-;2847:5;-1:-1:-1;2904:2:192;2889:18;;2876:32;2917:33;2876:32;2917:33;:::i;:::-;2969:7;-1:-1:-1;3023:2:192;3008:18;;2995:32;;-1:-1:-1;3078:2:192;3063:18;;3050:32;3101:18;3131:14;;;3128:34;;;3158:1;3155;3148:12;3128:34;3197:58;3247:7;3238:6;3227:9;3223:22;3197:58;:::i;:::-;3274:8;;-1:-1:-1;3171:84:192;-1:-1:-1;3362:3:192;3347:19;;3334:33;;-1:-1:-1;3379:16:192;;;3376:36;;;3408:1;3405;3398:12;3376:36;;3447:60;3499:7;3488:8;3477:9;3473:24;3447:60;:::i;:::-;2504:1063;;;;-1:-1:-1;2504:1063:192;;-1:-1:-1;2504:1063:192;;;;3421:86;;-1:-1:-1;;;2504:1063:192:o;3572:248::-;3640:6;3648;3701:2;3689:9;3680:7;3676:23;3672:32;3669:52;;;3717:1;3714;3707:12;3669:52;-1:-1:-1;;3740:23:192;;;3810:2;3795:18;;;3782:32;;-1:-1:-1;3572:248:192:o;4218:658::-;4389:2;4441:21;;;4511:13;;4414:18;;;4533:22;;;4360:4;;4389:2;4612:15;;;;4586:2;4571:18;;;4360:4;4655:195;4669:6;4666:1;4663:13;4655:195;;;4734:13;;-1:-1:-1;;;;;4730:39:192;4718:52;;4825:15;;;;4790:12;;;;4766:1;4684:9;4655:195;;;-1:-1:-1;4867:3:192;;4218:658;-1:-1:-1;;;;;;4218:658:192:o;4881:127::-;4942:10;4937:3;4933:20;4930:1;4923:31;4973:4;4970:1;4963:15;4997:4;4994:1;4987:15;5013:275;5084:2;5078:9;5149:2;5130:13;;-1:-1:-1;;5126:27:192;5114:40;;5184:18;5169:34;;5205:22;;;5166:62;5163:88;;;5231:18;;:::i;:::-;5267:2;5260:22;5013:275;;-1:-1:-1;5013:275:192:o;5293:183::-;5353:4;5386:18;5378:6;5375:30;5372:56;;;5408:18;;:::i;:::-;-1:-1:-1;5453:1:192;5449:14;5465:4;5445:25;;5293:183::o;5481:668::-;5535:5;5588:3;5581:4;5573:6;5569:17;5565:27;5555:55;;5606:1;5603;5596:12;5555:55;5642:6;5629:20;5668:4;5692:60;5708:43;5748:2;5708:43;:::i;:::-;5692:60;:::i;:::-;5774:3;5798:2;5793:3;5786:15;5826:4;5821:3;5817:14;5810:21;;5883:4;5877:2;5874:1;5870:10;5862:6;5858:23;5854:34;5840:48;;5911:3;5903:6;5900:15;5897:35;;;5928:1;5925;5918:12;5897:35;5964:4;5956:6;5952:17;5978:142;5994:6;5989:3;5986:15;5978:142;;;6060:17;;6048:30;;6098:12;;;;6011;;5978:142;;;-1:-1:-1;6138:5:192;5481:668;-1:-1:-1;;;;;;5481:668:192:o;6154:1362::-;6289:6;6297;6305;6358:2;6346:9;6337:7;6333:23;6329:32;6326:52;;;6374:1;6371;6364:12;6326:52;6413:9;6400:23;6432:31;6457:5;6432:31;:::i;:::-;6482:5;-1:-1:-1;6506:2:192;6544:18;;;6531:32;6582:18;6612:14;;;6609:34;;;6639:1;6636;6629:12;6609:34;6677:6;6666:9;6662:22;6652:32;;6722:7;6715:4;6711:2;6707:13;6703:27;6693:55;;6744:1;6741;6734:12;6693:55;6780:2;6767:16;6803:60;6819:43;6859:2;6819:43;:::i;6803:60::-;6897:15;;;6979:1;6975:10;;;;6967:19;;6963:28;;;6928:12;;;;7003:19;;;7000:39;;;7035:1;7032;7025:12;7000:39;7059:11;;;;7079:223;7095:6;7090:3;7087:15;7079:223;;;7177:3;7164:17;7194:33;7219:7;7194:33;:::i;:::-;7240:20;;7112:12;;;;7280;;;;7079:223;;;7321:5;-1:-1:-1;;;7379:2:192;7364:18;;7351:32;;-1:-1:-1;7395:16:192;;;7392:36;;;7424:1;7421;7414:12;7392:36;;;7447:63;7502:7;7491:8;7480:9;7476:24;7447:63;:::i;:::-;7437:73;;;6154:1362;;;;;:::o;7521:127::-;7582:10;7577:3;7573:20;7570:1;7563:31;7613:4;7610:1;7603:15;7637:4;7634:1;7627:15;7867:331;7972:9;7983;8025:8;8013:10;8010:24;8007:44;;;8047:1;8044;8037:12;8007:44;8076:6;8066:8;8063:20;8060:40;;;8096:1;8093;8086:12;8060:40;-1:-1:-1;;8122:23:192;;;8167:25;;;;;-1:-1:-1;7867:331:192:o;8203:323::-;-1:-1:-1;;;;;;8323:19:192;;8399:11;;;;8430:1;8422:10;;8419:101;;;8507:2;8501;8494:3;8491:1;8487:11;8484:1;8480:19;8476:28;8472:2;8468:37;8464:46;8455:55;;8419:101;;;8203:323;;;;:::o;8531:::-;8607:6;8615;8668:2;8656:9;8647:7;8643:23;8639:32;8636:52;;;8684:1;8681;8674:12;8636:52;8723:9;8710:23;8742:31;8767:5;8742:31;:::i;:::-;8792:5;8844:2;8829:18;;;;8816:32;;-1:-1:-1;;;8531:323:192:o;8859:271::-;9042:6;9034;9029:3;9016:33;8998:3;9068:16;;9093:13;;;9068:16;8859:271;-1:-1:-1;8859:271:192:o;9414:1055::-;9516:6;9524;9532;9585:2;9573:9;9564:7;9560:23;9556:32;9553:52;;;9601:1;9598;9591:12;9553:52;9640:9;9627:23;9659:31;9684:5;9659:31;:::i;:::-;9709:5;-1:-1:-1;9733:2:192;9772:18;;;9759:32;9800:33;9759:32;9800:33;:::i;:::-;9852:7;-1:-1:-1;9910:2:192;9895:18;;9882:32;9933:18;9963:14;;;9960:34;;;9990:1;9987;9980:12;9960:34;10028:6;10017:9;10013:22;10003:32;;10073:7;10066:4;10062:2;10058:13;10054:27;10044:55;;10095:1;10092;10085:12;10044:55;10131:2;10118:16;10153:2;10149;10146:10;10143:36;;;10159:18;;:::i;:::-;10201:53;10244:2;10225:13;;-1:-1:-1;;10221:27:192;10217:36;;10201:53;:::i;:::-;10188:66;;10277:2;10270:5;10263:17;10317:7;10312:2;10307;10303;10299:11;10295:20;10292:33;10289:53;;;10338:1;10335;10328:12;10289:53;10393:2;10388;10384;10380:11;10375:2;10368:5;10364:14;10351:45;10437:1;10432:2;10427;10420:5;10416:14;10412:23;10405:34;;10458:5;10448:15;;;;;9414:1055;;;;;:::o;10474:617::-;10640:4;10686:1;10682;10677:3;10673:11;10669:19;10727:2;10719:6;10715:15;10704:9;10697:34;10779:2;10771:6;10767:15;10762:2;10751:9;10747:18;10740:43;;10819:2;10814;10803:9;10799:18;10792:30;10851:6;10845:13;10894:6;10889:2;10878:9;10874:18;10867:34;10954:6;10949:2;10941:6;10937:15;10931:3;10920:9;10916:19;10910:51;11011:1;11005:3;10996:6;10985:9;10981:22;10977:32;10970:43;11081:3;11074:2;11070:7;11065:2;11057:6;11053:15;11049:29;11038:9;11034:45;11030:55;11022:63;;;10474:617;;;;;;:::o;11375:225::-;11442:9;;;11463:11;;;11460:134;;;11516:10;11511:3;11507:20;11504:1;11497:31;11551:4;11548:1;11541:15;11579:4;11576:1;11569:15;11605:127;11666:10;11661:3;11657:20;11654:1;11647:31;11697:4;11694:1;11687:15;11721:4;11718:1;11711:15", "linkReferences": {}, "immutableReferences": {"69670": [{"start": 1031, "length": 32}, {"start": 1588, "length": 32}, {"start": 2555, "length": 32}, {"start": 2790, "length": 32}, {"start": 2927, "length": 32}], "71356": [{"start": 827, "length": 32}], "71359": [{"start": 533, "length": 32}]}}, "methodIdentifiers": {"CALLER_ROLE()": "774237fc", "DEFAULT_ADMIN_ROLE()": "a217fddf", "MELLOW_VAULT_ROLE()": "3cfa1929", "SYMBIOTIC_FARM_ROLE()": "403ec940", "SYMBIOTIC_VAULT_ROLE()": "c7cc5f24", "farmFactory()": "6d25e175", "getRoleAdmin(bytes32)": "248a9ca3", "getRoleMember(bytes32,uint256)": "9010d07c", "getRoleMemberCount(bytes32)": "ca15c873", "getRoleMembers(bytes32)": "a3246ad3", "grantRole(bytes32,address)": "2f2ff15d", "hasRole(bytes32,address)": "91d14854", "hasSupportedRole(bytes32)": "4a46b4cf", "initialize(bytes)": "439fab91", "renounceRole(bytes32,address)": "36568abe", "revokeRole(bytes32,address)": "d547741f", "supportedRoleAt(uint256)": "a2cb31e5", "supportedRoles()": "419a2053", "supportsInterface(bytes4)": "01ffc9a7", "vaultFactory()": "d8a06f73", "verifyCall(address,address,uint256,bytes,bytes)": "70e46bcb"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vaultFactory_\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"farmFactory_\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"name_\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"version_\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"AccessControlBadConfirmation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"neededRole\",\"type\":\"bytes32\"}],\"name\":\"AccessControlUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZeroValue\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"RoleAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"previousAdminRole\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"newAdminRole\",\"type\":\"bytes32\"}],\"name\":\"RoleAdminChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleGranted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"RoleRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleRevoked\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"CALLER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEFAULT_ADMIN_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MELLOW_VAULT_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SYMBIOTIC_FARM_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SYMBIOTIC_VAULT_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"farmFactory\",\"outputs\":[{\"internalType\":\"contract ISymbioticRegistry\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"getRoleAdmin\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"getRoleMember\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"getRoleMemberCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"getRoleMembers\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"grantRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"hasRole\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"hasSupportedRole\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"callerConfirmation\",\"type\":\"address\"}],\"name\":\"renounceRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"revokeRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"supportedRoleAt\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"supportedRoles\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"vaultFactory\",\"outputs\":[{\"internalType\":\"contract ISymbioticRegistry\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"callData\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"verifyCall\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"AccessControlBadConfirmation()\":[{\"details\":\"The caller of a function is not the expected one. NOTE: Don't confuse with {AccessControlUnauthorizedAccount}.\"}],\"AccessControlUnauthorizedAccount(address,bytes32)\":[{\"details\":\"The `account` is missing a role.\"}],\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}]},\"events\":{\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"},\"RoleAdminChanged(bytes32,bytes32,bytes32)\":{\"details\":\"Emitted when `newAdminRole` is set as ``role``'s admin role, replacing `previousAdminRole` `DEFAULT_ADMIN_ROLE` is the starting admin for all roles, despite {RoleAdminChanged} not being emitted to signal this.\"},\"RoleGranted(bytes32,address,address)\":{\"details\":\"Emitted when `account` is granted `role`. `sender` is the account that originated the contract call. This account bears the admin role (for the granted role). Expected in cases where the role was granted using the internal {AccessControl-_grantRole}.\"},\"RoleRevoked(bytes32,address,address)\":{\"details\":\"Emitted when `account` is revoked `role`. `sender` is the account that originated the contract call:   - if using `revokeRole`, it is the admin role bearer   - if using `renounceRole`, it is the role bearer (i.e. `account`)\"}},\"kind\":\"dev\",\"methods\":{\"getRoleAdmin(bytes32)\":{\"details\":\"Returns the admin role that controls `role`. See {grantRole} and {revokeRole}. To change a role's admin, use {_setRoleAdmin}.\"},\"getRoleMember(bytes32,uint256)\":{\"details\":\"Returns one of the accounts that have `role`. `index` must be a value between 0 and {getRoleMemberCount}, non-inclusive. Role bearers are not sorted in any particular way, and their ordering may change at any point. WARNING: When using {getRoleMember} and {getRoleMemberCount}, make sure you perform all queries on the same block. See the following https://forum.openzeppelin.com/t/iterating-over-elements-on-enumerableset-in-openzeppelin-contracts/2296[forum post] for more information.\"},\"getRoleMemberCount(bytes32)\":{\"details\":\"Returns the number of accounts that have `role`. Can be used together with {getRoleMember} to enumerate all bearers of a role.\"},\"getRoleMembers(bytes32)\":{\"details\":\"Return all accounts that have `role` WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that this function has an unbounded cost, and using it as part of a state-changing function may render the function uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\"},\"grantRole(bytes32,address)\":{\"details\":\"Grants `role` to `account`. If `account` had not been already granted `role`, emits a {RoleGranted} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleGranted} event.\"},\"hasRole(bytes32,address)\":{\"details\":\"Returns `true` if `account` has been granted `role`.\"},\"hasSupportedRole(bytes32)\":{\"params\":{\"role\":\"The bytes32 identifier of the role to check\"},\"returns\":{\"_0\":\"isActive True if the role has any members assigned\"}},\"renounceRole(bytes32,address)\":{\"details\":\"Revokes `role` from the calling account. Roles are often managed via {grantRole} and {revokeRole}: this function's purpose is to provide a mechanism for accounts to lose their privileges if they are compromised (such as when a trusted device is misplaced). If the calling account had been revoked `role`, emits a {RoleRevoked} event. Requirements: - the caller must be `callerConfirmation`. May emit a {RoleRevoked} event.\"},\"revokeRole(bytes32,address)\":{\"details\":\"Revokes `role` from `account`. If `account` had been granted `role`, emits a {RoleRevoked} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleRevoked} event.\"},\"supportedRoleAt(uint256)\":{\"params\":{\"index\":\"Index within the supported role set\"},\"returns\":{\"_0\":\"role The bytes32 identifier of the role\"}},\"supportsInterface(bytes4)\":{\"details\":\"See {IERC165-supportsInterface}.\"}},\"version\":1},\"userdoc\":{\"events\":{\"RoleAdded(bytes32)\":{\"notice\":\"Emitted when a new role is granted for the first time\"},\"RoleRemoved(bytes32)\":{\"notice\":\"Emitted when a role loses its last member\"}},\"kind\":\"user\",\"methods\":{\"hasSupportedRole(bytes32)\":{\"notice\":\"Checks whether a given role is currently active (i.e., has at least one member)\"},\"supportedRoleAt(uint256)\":{\"notice\":\"Returns the role at the specified index in the set of active roles\"},\"supportedRoles()\":{\"notice\":\"Returns the total number of unique roles that are currently assigned\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/permissions/protocols/SymbioticVerifier.sol\":\"SymbioticVerifier\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/contracts/access/AccessControlUpgradeable.sol\":{\"keccak256\":\"0x0d4d7d19a052a2ef9d17b28450133631188b895e1755747fa8ad0280aadfb534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://838551e10cc07e570b70aee1078e490658d3a0ab8f4adfd4f3f2a565200753da\",\"dweb:/ipfs/QmceT7R3Pqzywyxr3MSEA7VXU4axZAPS6BLy92iV97rGdG\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/access/extensions/AccessControlEnumerableUpgradeable.sol\":{\"keccak256\":\"0xaec38804089a16494f7d45ebbbeab78fe05e5dc4bc2eae1d2af458fe999fb43c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://04796408e2c4a1051afb23319e9505edb595a3d9c663ff5d6fcfb8e185ab1d93\",\"dweb:/ipfs/Qma8ZTi4kGyqg45SyNibbJSqMF7broiQnJPHhEGLcZyF9U\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/introspection/ERC165Upgradeable.sol\":{\"keccak256\":\"0xc8ed8d2056934b7675b695dec032f2920c2f5c6cf33a17ca85650940675323ab\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3c8ccc75d1cd792d192aa09e54dd49ea35fe85baa9fcd17486f29227d9f29b89\",\"dweb:/ipfs/QmbboSbFUEiM9tdEgBwuTRb7bykFoJXZ7dsSr1PSREJXMr\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0\",\"dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/extensions/IAccessControlEnumerable.sol\":{\"keccak256\":\"0xca774fbe0568762efdc1a7cba31f09549c7fa96dbe97410f4843fa2f0bc000a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0187ffdbf3d61b6d86cba4fcd9826e53d876987d620533ee84c681bdaf0f3ba3\",\"dweb:/ipfs/QmVJDqdJv6uzHY7ifncfv2QJep8XTzS3bGb4s5Exhuv86m\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol\":{\"keccak256\":\"0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd\",\"dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol\":{\"keccak256\":\"0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd\",\"dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol\":{\"keccak256\":\"0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2\",\"dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621\",\"dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol\":{\"keccak256\":\"0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231\",\"dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y\"]},\"src/interfaces/external/symbiotic/ISymbioticRegistry.sol\":{\"keccak256\":\"0xb3a7f80d25c077fa757f7cfaef935d1e1a426a38387eb764ef7598ce57d15384\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://d4e176c23860a8e65f28f023b6360f46a8859d6523adac714f1e807b57a30a5b\",\"dweb:/ipfs/QmXLga7mwWJz16zqHhE1CZtXSAVUAvbSkP4pscSzwPWxEN\"]},\"src/interfaces/external/symbiotic/ISymbioticStakerRewards.sol\":{\"keccak256\":\"0x9a9d13cae981450efa28d097557899c71c9d98dfd581f17d246f5ac63be529da\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://dc7ef0d3995c6786112c3003c8da40111644544f8ff47092473704649bd90d38\",\"dweb:/ipfs/QmTkEZViXmJmTcRYzetSe7vveRm6xKuLaqWZBTvYZPLpSw\"]},\"src/interfaces/external/symbiotic/ISymbioticVault.sol\":{\"keccak256\":\"0x87dafe9de0c5f57fe3eac074a198ea7711ee743ea2490d4d23ded6be5a0523c4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://87c13eb577eaf7bf58764a929eae5b96dfca99017017c570cbf8257c2a0697c4\",\"dweb:/ipfs/QmYKDNKYGj2Kvv9b6AdAQBJSGmWhfBU5nTMhnfv9eLfqU8\"]},\"src/interfaces/permissions/ICustomVerifier.sol\":{\"keccak256\":\"0xdb343366ed86f63f8a94ff478c5c8e1d127888e4993c27fcb3bb3b51ae0cb8c7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://12407e89b20982a00961ccc03258e353962607747f214940d18b0989c11547c9\",\"dweb:/ipfs/Qmay19iWpwiV8LuvWccPjsazVU5Kkhme9QykFfLKfBmvSr\"]},\"src/interfaces/permissions/IMellowACL.sol\":{\"keccak256\":\"0xcc86b0ce4e4a53c1c4fa16ad79f9de0e2a1f1a77a1f256c0856120a9d6ea36df\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://6942df2ffadacae5563e01ec60697f16116362ba84a856a346b36bc7b9dd1ec2\",\"dweb:/ipfs/QmNYhqZGpS98UP89CS86CjoG4ZWbMHzTHRtd7bZzQqn3UY\"]},\"src/libraries/SlotLibrary.sol\":{\"keccak256\":\"0x35dcf16feec83c772add30250b0987ce29ccfbf308d04159701ebf9243444eeb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://97658d0c93ecc8174169e643d5a24c515b55905a2fcaaa6d6ec0188a3dd71a10\",\"dweb:/ipfs/QmS1z7ac8RbMvz15gVQ9YA1cCT5bs6KMHCiDhgcG8Pq8oK\"]},\"src/permissions/MellowACL.sol\":{\"keccak256\":\"0xeea276fd09714ef09b380fc9dfb8ae6639c9ff338b3e32cf3b824282a4a3a280\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c87b6a2c3e1c70772ceff8578785aa8e0daa76bccfb031ff2ecb72ccc7d2d0f2\",\"dweb:/ipfs/QmefXov36R68VpEaeafPJCWG89yMEWqe6UnKcWyX4vRsVH\"]},\"src/permissions/protocols/OwnedCustomVerifier.sol\":{\"keccak256\":\"0x85b3aa3ddb2f6807348bd11a48bcc5254238db149a6541c51ea2bb62831e0a67\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b9219f0b3e458bc1a9994ac0b69bc20a7619d9033d0b22bf923489957335a5a7\",\"dweb:/ipfs/QmUdQmv1QbJ5227N7Ytm8amwwzXzpwf7yCXMUZbXFxw2FA\"]},\"src/permissions/protocols/SymbioticVerifier.sol\":{\"keccak256\":\"0x259efd66a78a5f76bb3956099ad6e2ae265db0d00fd28ab3fea35ae2073d5cac\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c01d8edb13e4ca1d70994e7450ddeb513cb953d0f34802103061774c7bd00139\",\"dweb:/ipfs/QmQywsyUSrrmhac7PhZVfAzfcRzG55mGvxkD9f8Yc8aDZh\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "vaultFactory_", "type": "address"}, {"internalType": "address", "name": "farmFactory_", "type": "address"}, {"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "uint256", "name": "version_", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "AccessControlBadConfirmation"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "type": "error", "name": "AccessControlUnauthorizedAccount"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [], "type": "error", "name": "ZeroValue"}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}], "type": "event", "name": "RoleAdded", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32", "indexed": true}, {"internalType": "bytes32", "name": "newAdminRole", "type": "bytes32", "indexed": true}], "type": "event", "name": "RoleAdminChanged", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": true}], "type": "event", "name": "RoleGranted", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}], "type": "event", "name": "RoleRemoved", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": true}], "type": "event", "name": "RoleRevoked", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "CALLER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "MELLOW_VAULT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SYMBIOTIC_FARM_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SYMBIOTIC_VAULT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "farmFactory", "outputs": [{"internalType": "contract ISymbioticRegistry", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getRoleMember", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "getRoleMemberCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "getRoleMembers", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "grantRole"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "hasSupportedRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "renounceRole"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "revokeRole"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "supportedRoleAt", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "supportedRoles", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "stateMutability": "view", "type": "function", "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "vaultFactory", "outputs": [{"internalType": "contract ISymbioticRegistry", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function", "name": "verifyCall", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"getRoleAdmin(bytes32)": {"details": "Returns the admin role that controls `role`. See {grantRole} and {revokeRole}. To change a role's admin, use {_setRoleAdmin}."}, "getRoleMember(bytes32,uint256)": {"details": "Returns one of the accounts that have `role`. `index` must be a value between 0 and {getRoleMemberCount}, non-inclusive. Role bearers are not sorted in any particular way, and their ordering may change at any point. WARNING: When using {getRoleMember} and {getRoleMemberCount}, make sure you perform all queries on the same block. See the following https://forum.openzeppelin.com/t/iterating-over-elements-on-enumerableset-in-openzeppelin-contracts/2296[forum post] for more information."}, "getRoleMemberCount(bytes32)": {"details": "Returns the number of accounts that have `role`. Can be used together with {getRoleMember} to enumerate all bearers of a role."}, "getRoleMembers(bytes32)": {"details": "Return all accounts that have `role` WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that this function has an unbounded cost, and using it as part of a state-changing function may render the function uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block."}, "grantRole(bytes32,address)": {"details": "Grants `role` to `account`. If `account` had not been already granted `role`, emits a {RoleGranted} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleGranted} event."}, "hasRole(bytes32,address)": {"details": "Returns `true` if `account` has been granted `role`."}, "hasSupportedRole(bytes32)": {"params": {"role": "The bytes32 identifier of the role to check"}, "returns": {"_0": "isActive True if the role has any members assigned"}}, "renounceRole(bytes32,address)": {"details": "Revokes `role` from the calling account. Roles are often managed via {grantRole} and {revokeRole}: this function's purpose is to provide a mechanism for accounts to lose their privileges if they are compromised (such as when a trusted device is misplaced). If the calling account had been revoked `role`, emits a {RoleRevoked} event. Requirements: - the caller must be `callerConfirmation`. May emit a {RoleRevoked} event."}, "revokeRole(bytes32,address)": {"details": "Revokes `role` from `account`. If `account` had been granted `role`, emits a {RoleRevoked} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleRevoked} event."}, "supportedRoleAt(uint256)": {"params": {"index": "Index within the supported role set"}, "returns": {"_0": "role The bytes32 identifier of the role"}}, "supportsInterface(bytes4)": {"details": "See {IERC165-supportsInterface}."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"hasSupportedRole(bytes32)": {"notice": "Checks whether a given role is currently active (i.e., has at least one member)"}, "supportedRoleAt(uint256)": {"notice": "Returns the role at the specified index in the set of active roles"}, "supportedRoles()": {"notice": "Returns the total number of unique roles that are currently assigned"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/permissions/protocols/SymbioticVerifier.sol": "SymbioticVerifier"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/contracts/access/AccessControlUpgradeable.sol": {"keccak256": "0x0d4d7d19a052a2ef9d17b28450133631188b895e1755747fa8ad0280aadfb534", "urls": ["bzz-raw://838551e10cc07e570b70aee1078e490658d3a0ab8f4adfd4f3f2a565200753da", "dweb:/ipfs/QmceT7R3Pqzywyxr3MSEA7VXU4axZAPS6BLy92iV97rGdG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/access/extensions/AccessControlEnumerableUpgradeable.sol": {"keccak256": "0xaec38804089a16494f7d45ebbbeab78fe05e5dc4bc2eae1d2af458fe999fb43c", "urls": ["bzz-raw://04796408e2c4a1051afb23319e9505edb595a3d9c663ff5d6fcfb8e185ab1d93", "dweb:/ipfs/Qma8ZTi4kGyqg45SyNibbJSqMF7broiQnJPHhEGLcZyF9U"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05", "urls": ["bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08", "dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/introspection/ERC165Upgradeable.sol": {"keccak256": "0xc8ed8d2056934b7675b695dec032f2920c2f5c6cf33a17ca85650940675323ab", "urls": ["bzz-raw://3c8ccc75d1cd792d192aa09e54dd49ea35fe85baa9fcd17486f29227d9f29b89", "dweb:/ipfs/QmbboSbFUEiM9tdEgBwuTRb7bykFoJXZ7dsSr1PSREJXMr"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530", "urls": ["bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0", "dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/extensions/IAccessControlEnumerable.sol": {"keccak256": "0xca774fbe0568762efdc1a7cba31f09549c7fa96dbe97410f4843fa2f0bc000a3", "urls": ["bzz-raw://0187ffdbf3d61b6d86cba4fcd9826e53d876987d620533ee84c681bdaf0f3ba3", "dweb:/ipfs/QmVJDqdJv6uzHY7ifncfv2QJep8XTzS3bGb4s5Exhuv86m"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol": {"keccak256": "0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6", "urls": ["bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd", "dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol": {"keccak256": "0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58", "urls": ["bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd", "dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol": {"keccak256": "0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26", "urls": ["bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2", "dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8", "urls": ["bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621", "dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol": {"keccak256": "0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06", "urls": ["bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231", "dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y"], "license": "MIT"}, "src/interfaces/external/symbiotic/ISymbioticRegistry.sol": {"keccak256": "0xb3a7f80d25c077fa757f7cfaef935d1e1a426a38387eb764ef7598ce57d15384", "urls": ["bzz-raw://d4e176c23860a8e65f28f023b6360f46a8859d6523adac714f1e807b57a30a5b", "dweb:/ipfs/QmXLga7mwWJz16zqHhE1CZtXSAVUAvbSkP4pscSzwPWxEN"], "license": "BUSL-1.1"}, "src/interfaces/external/symbiotic/ISymbioticStakerRewards.sol": {"keccak256": "0x9a9d13cae981450efa28d097557899c71c9d98dfd581f17d246f5ac63be529da", "urls": ["bzz-raw://dc7ef0d3995c6786112c3003c8da40111644544f8ff47092473704649bd90d38", "dweb:/ipfs/QmTkEZViXmJmTcRYzetSe7vveRm6xKuLaqWZBTvYZPLpSw"], "license": "MIT"}, "src/interfaces/external/symbiotic/ISymbioticVault.sol": {"keccak256": "0x87dafe9de0c5f57fe3eac074a198ea7711ee743ea2490d4d23ded6be5a0523c4", "urls": ["bzz-raw://87c13eb577eaf7bf58764a929eae5b96dfca99017017c570cbf8257c2a0697c4", "dweb:/ipfs/QmYKDNKYGj2Kvv9b6AdAQBJSGmWhfBU5nTMhnfv9eLfqU8"], "license": "BUSL-1.1"}, "src/interfaces/permissions/ICustomVerifier.sol": {"keccak256": "0xdb343366ed86f63f8a94ff478c5c8e1d127888e4993c27fcb3bb3b51ae0cb8c7", "urls": ["bzz-raw://12407e89b20982a00961ccc03258e353962607747f214940d18b0989c11547c9", "dweb:/ipfs/Qmay19iWpwiV8LuvWccPjsazVU5Kkhme9QykFfLKfBmvSr"], "license": "BUSL-1.1"}, "src/interfaces/permissions/IMellowACL.sol": {"keccak256": "0xcc86b0ce4e4a53c1c4fa16ad79f9de0e2a1f1a77a1f256c0856120a9d6ea36df", "urls": ["bzz-raw://6942df2ffadacae5563e01ec60697f16116362ba84a856a346b36bc7b9dd1ec2", "dweb:/ipfs/QmNYhqZGpS98UP89CS86CjoG4ZWbMHzTHRtd7bZzQqn3UY"], "license": "BUSL-1.1"}, "src/libraries/SlotLibrary.sol": {"keccak256": "0x35dcf16feec83c772add30250b0987ce29ccfbf308d04159701ebf9243444eeb", "urls": ["bzz-raw://97658d0c93ecc8174169e643d5a24c515b55905a2fcaaa6d6ec0188a3dd71a10", "dweb:/ipfs/QmS1z7ac8RbMvz15gVQ9YA1cCT5bs6KMHCiDhgcG8Pq8oK"], "license": "BUSL-1.1"}, "src/permissions/MellowACL.sol": {"keccak256": "0xeea276fd09714ef09b380fc9dfb8ae6639c9ff338b3e32cf3b824282a4a3a280", "urls": ["bzz-raw://c87b6a2c3e1c70772ceff8578785aa8e0daa76bccfb031ff2ecb72ccc7d2d0f2", "dweb:/ipfs/QmefXov36R68VpEaeafPJCWG89yMEWqe6UnKcWyX4vRsVH"], "license": "BUSL-1.1"}, "src/permissions/protocols/OwnedCustomVerifier.sol": {"keccak256": "0x85b3aa3ddb2f6807348bd11a48bcc5254238db149a6541c51ea2bb62831e0a67", "urls": ["bzz-raw://b9219f0b3e458bc1a9994ac0b69bc20a7619d9033d0b22bf923489957335a5a7", "dweb:/ipfs/QmUdQmv1QbJ5227N7Ytm8amwwzXzpwf7yCXMUZbXFxw2FA"], "license": "BUSL-1.1"}, "src/permissions/protocols/SymbioticVerifier.sol": {"keccak256": "0x259efd66a78a5f76bb3956099ad6e2ae265db0d00fd28ab3fea35ae2073d5cac", "urls": ["bzz-raw://c01d8edb13e4ca1d70994e7450ddeb513cb953d0f34802103061774c7bd00139", "dweb:/ipfs/QmQywsyUSrrmhac7PhZVfAzfcRzG55mGvxkD9f8Yc8aDZh"], "license": "BUSL-1.1"}}, "version": 1}, "id": 135}