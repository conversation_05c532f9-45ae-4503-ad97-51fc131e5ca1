{"abi": [{"type": "error", "name": "IndexOutOfBounds", "inputs": []}, {"type": "error", "name": "InvalidLength", "inputs": []}], "bytecode": {"object": "0x60556032600b8282823980515f1a607314602657634e487b7160e01b5f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f80fdfea2646970667358221220f7dde1b204b23b6fb548a2c0a5589fe7ba37168700f65d338767ae15aefbc03d64736f6c63430008190033", "sourceMap": "903:4304:111:-:0;;;;;;;;;;;;;;;-1:-1:-1;;;903:4304:111;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x730000000000000000000000000000000000000000301460806040525f80fdfea2646970667358221220f7dde1b204b23b6fb548a2c0a5589fe7ba37168700f65d338767ae15aefbc03d64736f6c63430008190033", "sourceMap": "903:4304:111:-:0;;;;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"IndexOutOfBounds\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidLength\",\"type\":\"error\"}],\"devdoc\":{\"details\":\"Enables efficient updates and prefix sum queries over a dynamic array. # Overview Fenwick Tree is a compact data structure optimized for cumulative frequency computations: - `update(i, delta)` increments the element at index `i` by signed `delta`. - `prefixSum(i)` returns the sum of elements in the range `[0, i]`. This library provides: - `O(log n)` time complexity for updates and prefix queries. - `O(1)` fixed cost for extending the tree by doubling its capacity. - Support only for arrays whose lengths are powers of two (2^k). # References - https://cp-algorithms.com/data_structures/fenwick.html - https://en.wikipedia.org/wiki/Fenwick_tree\",\"kind\":\"dev\",\"methods\":{},\"title\":\"FenwickTreeLibrary\",\"version\":1},\"userdoc\":{\"errors\":{\"IndexOutOfBounds()\":[{\"notice\":\"Thrown when an index is outside the bounds of the tree.\"}],\"InvalidLength()\":[{\"notice\":\"Thrown when initializing with an invalid length (must be power of 2 and nonzero), or during overflow.\"}]},\"kind\":\"user\",\"methods\":{},\"notice\":\"Implements a 0-indexed Fenwick Tree (Binary Indexed Tree) for prefix sum operations.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/libraries/FenwickTreeLibrary.sol\":\"FenwickTreeLibrary\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"src/libraries/FenwickTreeLibrary.sol\":{\"keccak256\":\"0xfd159ed27b96f288c4220febac4bfe286e4545b8ca7f523ae6e63d123341f8a3\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://58f0cc202896722786140571b111bf5e589730358a24b2940a450c4c75d864fe\",\"dweb:/ipfs/QmeRSjvcYURUSpvFe4r4VRwwpHcb1NmqiEUDEJ5rBxAxuf\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "IndexOutOfBounds"}, {"inputs": [], "type": "error", "name": "InvalidLength"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/libraries/FenwickTreeLibrary.sol": "FenwickTreeLibrary"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"src/libraries/FenwickTreeLibrary.sol": {"keccak256": "0xfd159ed27b96f288c4220febac4bfe286e4545b8ca7f523ae6e63d123341f8a3", "urls": ["bzz-raw://58f0cc202896722786140571b111bf5e589730358a24b2940a450c4c75d864fe", "dweb:/ipfs/QmeRSjvcYURUSpvFe4r4VRwwpHcb1NmqiEUDEJ5rBxAxuf"], "license": "BUSL-1.1"}}, "version": 1}, "id": 111}