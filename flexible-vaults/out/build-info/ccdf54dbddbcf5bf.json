{"id": "ccdf54dbddbcf5bf", "source_id_to_path": {"0": "lib/openzeppelin-contracts-upgradeable/contracts/access/AccessControlUpgradeable.sol", "1": "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "2": "lib/openzeppelin-contracts-upgradeable/contracts/access/extensions/AccessControlEnumerableUpgradeable.sol", "3": "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "4": "lib/openzeppelin-contracts-upgradeable/contracts/token/ERC20/ERC20Upgradeable.sol", "5": "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "6": "lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol", "7": "lib/openzeppelin-contracts-upgradeable/contracts/utils/cryptography/EIP712Upgradeable.sol", "8": "lib/openzeppelin-contracts-upgradeable/contracts/utils/introspection/ERC165Upgradeable.sol", "9": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/Base.sol", "10": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdAssertions.sol", "11": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdChains.sol", "12": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdCheats.sol", "13": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdError.sol", "14": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdInvariant.sol", "15": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdJson.sol", "16": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdMath.sol", "17": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdStorage.sol", "18": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdStyle.sol", "19": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdToml.sol", "20": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdUtils.sol", "21": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/Test.sol", "22": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/Vm.sol", "23": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/console.sol", "24": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/console2.sol", "25": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/interfaces/IERC165.sol", "26": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/interfaces/IERC20.sol", "27": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/interfaces/IERC721.sol", "28": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/interfaces/IMulticall3.sol", "29": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/mocks/MockERC20.sol", "30": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/mocks/MockERC721.sol", "31": "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/safeconsole.sol", "32": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol", "33": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/Ownable.sol", "34": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/extensions/IAccessControlEnumerable.sol", "35": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol", "36": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol", "37": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol", "38": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol", "39": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol", "40": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol", "41": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "42": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "43": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol", "44": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "45": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "46": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol", "47": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol", "48": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "49": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "50": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "51": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "52": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol", "53": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Address.sol", "54": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol", "55": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol", "56": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Context.sol", "57": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Errors.sol", "58": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol", "59": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol", "60": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "61": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Strings.sol", "62": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "63": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/Hashes.sol", "64": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/MerkleProof.sol", "65": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol", "66": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "67": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "68": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "69": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol", "70": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol", "71": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableMap.sol", "72": "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol", "73": "src/factories/Factory.sol", "74": "src/hooks/BasicRedeemHook.sol", "75": "src/hooks/LidoDepositHook.sol", "76": "src/hooks/RedirectingDepositHook.sol", "77": "src/interfaces/external/eigen-layer/IAllocationManager.sol", "78": "src/interfaces/external/eigen-layer/IDelegationManager.sol", "79": "src/interfaces/external/eigen-layer/IRewardsCoordinator.sol", "80": "src/interfaces/external/eigen-layer/ISignatureUtils.sol", "81": "src/interfaces/external/eigen-layer/IStrategy.sol", "82": "src/interfaces/external/eigen-layer/IStrategyManager.sol", "83": "src/interfaces/external/symbiotic/ISymbioticRegistry.sol", "84": "src/interfaces/external/symbiotic/ISymbioticStakerRewards.sol", "85": "src/interfaces/external/symbiotic/ISymbioticVault.sol", "86": "src/interfaces/external/tokens/IWETH.sol", "87": "src/interfaces/external/tokens/IWSTETH.sol", "88": "src/interfaces/factories/IFactory.sol", "89": "src/interfaces/factories/IFactoryEntity.sol", "90": "src/interfaces/hooks/IHook.sol", "91": "src/interfaces/hooks/IRedeemHook.sol", "92": "src/interfaces/managers/IFeeManager.sol", "93": "src/interfaces/managers/IRiskManager.sol", "94": "src/interfaces/managers/IShareManager.sol", "95": "src/interfaces/modules/IACLModule.sol", "96": "src/interfaces/modules/IBaseModule.sol", "97": "src/interfaces/modules/ICallModule.sol", "98": "src/interfaces/modules/IShareModule.sol", "99": "src/interfaces/modules/ISubvaultModule.sol", "100": "src/interfaces/modules/IVaultModule.sol", "101": "src/interfaces/modules/IVerifierModule.sol", "102": "src/interfaces/oracles/IOracle.sol", "103": "src/interfaces/permissions/IConsensus.sol", "104": "src/interfaces/permissions/ICustomVerifier.sol", "105": "src/interfaces/permissions/IMellowACL.sol", "106": "src/interfaces/permissions/IVerifier.sol", "107": "src/interfaces/queues/IDepositQueue.sol", "108": "src/interfaces/queues/IQueue.sol", "109": "src/interfaces/queues/IRedeemQueue.sol", "110": "src/interfaces/queues/ISignatureQueue.sol", "111": "src/libraries/FenwickTreeLibrary.sol", "112": "src/libraries/ShareManagerFlagLibrary.sol", "113": "src/libraries/SlotLibrary.sol", "114": "src/libraries/TransferLibrary.sol", "115": "src/managers/BasicShareManager.sol", "116": "src/managers/FeeManager.sol", "117": "src/managers/RiskManager.sol", "118": "src/managers/ShareManager.sol", "119": "src/managers/TokenizedShareManager.sol", "120": "src/modules/ACLModule.sol", "121": "src/modules/BaseModule.sol", "122": "src/modules/CallModule.sol", "123": "src/modules/ShareModule.sol", "124": "src/modules/SubvaultModule.sol", "125": "src/modules/VaultModule.sol", "126": "src/modules/VerifierModule.sol", "127": "src/oracles/Oracle.sol", "128": "src/permissions/BitmaskVerifier.sol", "129": "src/permissions/Consensus.sol", "130": "src/permissions/MellowACL.sol", "131": "src/permissions/Verifier.sol", "132": "src/permissions/protocols/ERC20Verifier.sol", "133": "src/permissions/protocols/EigenLayerVerifier.sol", "134": "src/permissions/protocols/OwnedCustomVerifier.sol", "135": "src/permissions/protocols/SymbioticVerifier.sol", "136": "src/queues/DepositQueue.sol", "137": "src/queues/Queue.sol", "138": "src/queues/RedeemQueue.sol", "139": "src/queues/SignatureDepositQueue.sol", "140": "src/queues/SignatureQueue.sol", "141": "src/queues/SignatureRedeemQueue.sol", "142": "src/strategies/SymbioticStrategy.sol", "143": "src/vaults/Subvault.sol", "144": "src/vaults/Vault.sol", "145": "src/vaults/VaultConfigurator.sol", "146": "test/Fixture.t.sol", "147": "test/Imports.sol", "148": "test/integration/BaseIntegrationTest.sol", "149": "test/integration/ProtocolFees.t.sol", "150": "test/integration/SymbioticFlow.t.sol", "151": "test/integration/SymbioticFlowWithSlashing.t.sol", "152": "test/integration/SymbioticFlowWithSlashingWithRedeemFee.t.sol", "153": "test/mocks/MockACLModule.sol", "154": "test/mocks/MockERC20.sol", "155": "test/mocks/MockRiskManager.sol", "156": "test/mocks/MockVault.sol", "157": "test/unit/factories/Factory.t.sol", "158": "test/unit/hooks/BasicRedeemHook.t.sol", "159": "test/unit/hooks/LidoDepositHook.t.sol", "160": "test/unit/hooks/RedirectingDepositHook.t.sol", "161": "test/unit/libraries/FenwickTreeLibrary.t.sol", "162": "test/unit/libraries/ShareManagerFlagLibrary.t.sol", "163": "test/unit/libraries/TransferLibrary.t.sol", "164": "test/unit/managers/BasicShareManager.t.sol", "165": "test/unit/managers/FeeManager.t.sol", "166": "test/unit/managers/RiskManager.t.sol", "167": "test/unit/managers/ShareManager.t.sol", "168": "test/unit/managers/TokenizedShareManager.t.sol", "169": "test/unit/modules/ACLModule.t.sol", "170": "test/unit/modules/BaseModule.t.sol", "171": "test/unit/modules/CallModule.t.sol", "172": "test/unit/modules/ShareModule.t.sol", "173": "test/unit/modules/SubvaultModule.t.sol", "174": "test/unit/modules/VaultModule.t.sol", "175": "test/unit/modules/VerifierModule.t.sol", "176": "test/unit/oracles/Oracle.t.sol", "177": "test/unit/permissions/BitmaskVerifier.t.sol", "178": "test/unit/permissions/Consensus.t.sol", "179": "test/unit/permissions/MellowACL.t.sol", "180": "test/unit/permissions/Verifier.t.sol", "181": "test/unit/permissions/protocols/ERC20Verifier.t.sol", "182": "test/unit/permissions/protocols/EigenLayerVerifier.t.sol", "183": "test/unit/permissions/protocols/SymbioticVerifier.t.sol", "184": "test/unit/queues/DepositQueue.t.sol", "185": "test/unit/queues/Queue.t.sol", "186": "test/unit/queues/SignatureDepositQueue.t.sol", "187": "test/unit/queues/SignatureQueue.t.sol", "188": "test/unit/queues/SignatureRedeemQueue.t.sol", "189": "test/unit/vaults/Subvault.t.sol", "190": "test/unit/vaults/Vault.t.sol", "191": "test/unit/vaults/VaultConfigurator.t.sol"}, "language": "Solidity"}