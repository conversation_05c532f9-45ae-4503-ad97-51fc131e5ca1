{"abi": [{"type": "function", "name": "addStrategiesToOperatorSet", "inputs": [{"name": "avs", "type": "address", "internalType": "address"}, {"name": "operatorSetId", "type": "uint32", "internalType": "uint32"}, {"name": "strategies", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "clearDeallocationQueue", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "strategies", "type": "address[]", "internalType": "address[]"}, {"name": "numToClear", "type": "uint16[]", "internalType": "uint16[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "createOperatorSets", "inputs": [{"name": "avs", "type": "address", "internalType": "address"}, {"name": "params", "type": "tuple[]", "internalType": "struct IAllocationManager.CreateSetParams[]", "components": [{"name": "operatorSetId", "type": "uint32", "internalType": "uint32"}, {"name": "strategies", "type": "address[]", "internalType": "address[]"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "deregisterFromOperatorSets", "inputs": [{"name": "params", "type": "tuple", "internalType": "struct IAllocationManager.DeregisterParams", "components": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "avs", "type": "address", "internalType": "address"}, {"name": "operatorSetIds", "type": "uint32[]", "internalType": "uint32[]"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getAVSRegistrar", "inputs": [{"name": "avs", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getAllocatableMagnitude", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "strategy", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint64", "internalType": "uint64"}], "stateMutability": "view"}, {"type": "function", "name": "getAllocatedSets", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple[]", "internalType": "struct IAllocationManager.OperatorSet[]", "components": [{"name": "avs", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint32", "internalType": "uint32"}]}], "stateMutability": "view"}, {"type": "function", "name": "getAllocatedStake", "inputs": [{"name": "operatorSet", "type": "tuple", "internalType": "struct IAllocationManager.OperatorSet", "components": [{"name": "avs", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint32", "internalType": "uint32"}]}, {"name": "operators", "type": "address[]", "internalType": "address[]"}, {"name": "strategies", "type": "address[]", "internalType": "address[]"}], "outputs": [{"name": "slashableStake", "type": "uint256[][]", "internalType": "uint256[][]"}], "stateMutability": "view"}, {"type": "function", "name": "getAllocatedStrategies", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "operatorSet", "type": "tuple", "internalType": "struct IAllocationManager.OperatorSet", "components": [{"name": "avs", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint32", "internalType": "uint32"}]}], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getAllocation", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "operatorSet", "type": "tuple", "internalType": "struct IAllocationManager.OperatorSet", "components": [{"name": "avs", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint32", "internalType": "uint32"}]}, {"name": "strategy", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IAllocationManager.Allocation", "components": [{"name": "currentMagnitude", "type": "uint64", "internalType": "uint64"}, {"name": "pendingDiff", "type": "int128", "internalType": "int128"}, {"name": "effectBlock", "type": "uint32", "internalType": "uint32"}]}], "stateMutability": "view"}, {"type": "function", "name": "getAllocationDelay", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}], "outputs": [{"name": "isSet", "type": "bool", "internalType": "bool"}, {"name": "delay", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "getAllocations", "inputs": [{"name": "operators", "type": "address[]", "internalType": "address[]"}, {"name": "operatorSet", "type": "tuple", "internalType": "struct IAllocationManager.OperatorSet", "components": [{"name": "avs", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint32", "internalType": "uint32"}]}, {"name": "strategy", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple[]", "internalType": "struct IAllocationManager.Allocation[]", "components": [{"name": "currentMagnitude", "type": "uint64", "internalType": "uint64"}, {"name": "pendingDiff", "type": "int128", "internalType": "int128"}, {"name": "effectBlock", "type": "uint32", "internalType": "uint32"}]}], "stateMutability": "view"}, {"type": "function", "name": "getEncumberedMagnitude", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "strategy", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint64", "internalType": "uint64"}], "stateMutability": "view"}, {"type": "function", "name": "getMaxMagnitude", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "strategy", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint64", "internalType": "uint64"}], "stateMutability": "view"}, {"type": "function", "name": "getMaxMagnitudes", "inputs": [{"name": "operators", "type": "address[]", "internalType": "address[]"}, {"name": "strategy", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint64[]", "internalType": "uint64[]"}], "stateMutability": "view"}, {"type": "function", "name": "getMaxMagnitudes", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "strategies", "type": "address[]", "internalType": "address[]"}], "outputs": [{"name": "", "type": "uint64[]", "internalType": "uint64[]"}], "stateMutability": "view"}, {"type": "function", "name": "getMaxMagnitudesAtBlock", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "strategies", "type": "address[]", "internalType": "address[]"}, {"name": "blockNumber", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "", "type": "uint64[]", "internalType": "uint64[]"}], "stateMutability": "view"}, {"type": "function", "name": "getMemberCount", "inputs": [{"name": "operatorSet", "type": "tuple", "internalType": "struct IAllocationManager.OperatorSet", "components": [{"name": "avs", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint32", "internalType": "uint32"}]}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getMembers", "inputs": [{"name": "operatorSet", "type": "tuple", "internalType": "struct IAllocationManager.OperatorSet", "components": [{"name": "avs", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint32", "internalType": "uint32"}]}], "outputs": [{"name": "operators", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getMinimumSlashableStake", "inputs": [{"name": "operatorSet", "type": "tuple", "internalType": "struct IAllocationManager.OperatorSet", "components": [{"name": "avs", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint32", "internalType": "uint32"}]}, {"name": "operators", "type": "address[]", "internalType": "address[]"}, {"name": "strategies", "type": "address[]", "internalType": "address[]"}, {"name": "futureBlock", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "slashableStake", "type": "uint256[][]", "internalType": "uint256[][]"}], "stateMutability": "view"}, {"type": "function", "name": "getOperatorSetCount", "inputs": [{"name": "avs", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getRegisteredSets", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}], "outputs": [{"name": "operatorSets", "type": "tuple[]", "internalType": "struct IAllocationManager.OperatorSet[]", "components": [{"name": "avs", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint32", "internalType": "uint32"}]}], "stateMutability": "view"}, {"type": "function", "name": "getStrategiesInOperatorSet", "inputs": [{"name": "operatorSet", "type": "tuple", "internalType": "struct IAllocationManager.OperatorSet", "components": [{"name": "avs", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint32", "internalType": "uint32"}]}], "outputs": [{"name": "strategies", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getStrategyAllocations", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "strategy", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple[]", "internalType": "struct IAllocationManager.OperatorSet[]", "components": [{"name": "avs", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint32", "internalType": "uint32"}]}, {"name": "", "type": "tuple[]", "internalType": "struct IAllocationManager.Allocation[]", "components": [{"name": "currentMagnitude", "type": "uint64", "internalType": "uint64"}, {"name": "pendingDiff", "type": "int128", "internalType": "int128"}, {"name": "effectBlock", "type": "uint32", "internalType": "uint32"}]}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "initialOwner", "type": "address", "internalType": "address"}, {"name": "initialPausedStatus", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isMemberOfOperatorSet", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "operatorSet", "type": "tuple", "internalType": "struct IAllocationManager.OperatorSet", "components": [{"name": "avs", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint32", "internalType": "uint32"}]}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isOperatorSet", "inputs": [{"name": "operatorSet", "type": "tuple", "internalType": "struct IAllocationManager.OperatorSet", "components": [{"name": "avs", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint32", "internalType": "uint32"}]}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isOperatorSlashable", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "operatorSet", "type": "tuple", "internalType": "struct IAllocationManager.OperatorSet", "components": [{"name": "avs", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint32", "internalType": "uint32"}]}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "modifyAllocations", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "params", "type": "tuple[]", "internalType": "struct IAllocationManager.AllocateParams[]", "components": [{"name": "operatorSet", "type": "tuple", "internalType": "struct IAllocationManager.OperatorSet", "components": [{"name": "avs", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint32", "internalType": "uint32"}]}, {"name": "strategies", "type": "address[]", "internalType": "address[]"}, {"name": "newMagnitudes", "type": "uint64[]", "internalType": "uint64[]"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "registerForOperatorSets", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "params", "type": "tuple", "internalType": "struct IAllocationManager.RegisterParams", "components": [{"name": "avs", "type": "address", "internalType": "address"}, {"name": "operatorSetIds", "type": "uint32[]", "internalType": "uint32[]"}, {"name": "data", "type": "bytes", "internalType": "bytes"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "removeStrategiesFromOperatorSet", "inputs": [{"name": "avs", "type": "address", "internalType": "address"}, {"name": "operatorSetId", "type": "uint32", "internalType": "uint32"}, {"name": "strategies", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setAVSRegistrar", "inputs": [{"name": "avs", "type": "address", "internalType": "address"}, {"name": "registrar", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setAllocationDelay", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "delay", "type": "uint32", "internalType": "uint32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "slashOperator", "inputs": [{"name": "avs", "type": "address", "internalType": "address"}, {"name": "params", "type": "tuple", "internalType": "struct IAllocationManager.SlashingParams", "components": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "operatorSetId", "type": "uint32", "internalType": "uint32"}, {"name": "strategies", "type": "address[]", "internalType": "address[]"}, {"name": "wadsToSlash", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "description", "type": "string", "internalType": "string"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateAVSMetadataURI", "inputs": [{"name": "avs", "type": "address", "internalType": "address"}, {"name": "metadataURI", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"addStrategiesToOperatorSet(address,uint32,address[])": "50feea20", "clearDeallocationQueue(address,address[],uint16[])": "4b5046ef", "createOperatorSets(address,(uint32,address[])[])": "261f84e0", "deregisterFromOperatorSets((address,address,uint32[]))": "6e3492b5", "getAVSRegistrar(address)": "304c10cd", "getAllocatableMagnitude(address,address)": "6cfb4481", "getAllocatedSets(address)": "15fe5028", "getAllocatedStake((address,uint32),address[],address[])": "2b453a9a", "getAllocatedStrategies(address,(address,uint32))": "c221d8ae", "getAllocation(address,(address,uint32),address)": "10e1b9b8", "getAllocationDelay(address)": "b9fbaed1", "getAllocations(address[],(address,uint32),address)": "8ce64854", "getEncumberedMagnitude(address,address)": "f605ce08", "getMaxMagnitude(address,address)": "a9333ec8", "getMaxMagnitudes(address,address[])": "547afb87", "getMaxMagnitudes(address[],address)": "4a10ffe5", "getMaxMagnitudesAtBlock(address,address[],uint32)": "94d7d00c", "getMemberCount((address,uint32))": "b2447af7", "getMembers((address,uint32))": "6e875dba", "getMinimumSlashableStake((address,uint32),address[],address[],uint32)": "2bab2c4a", "getOperatorSetCount(address)": "ba1a84e5", "getRegisteredSets(address)": "79ae50cd", "getStrategiesInOperatorSet((address,uint32))": "4177a87c", "getStrategyAllocations(address,address)": "40120dab", "initialize(address,uint256)": "cd6dc687", "isMemberOfOperatorSet(address,(address,uint32))": "670d3ba2", "isOperatorSet((address,uint32))": "260dc758", "isOperatorSlashable(address,(address,uint32))": "1352c3e6", "modifyAllocations(address,((address,uint32),address[],uint64[])[])": "952899ee", "registerForOperatorSets(address,(address,uint32[],bytes))": "adc2e3d9", "removeStrategiesFromOperatorSet(address,uint32,address[])": "b66bd989", "setAVSRegistrar(address,address)": "d3d96ff4", "setAllocationDelay(address,uint32)": "56c483e6", "slashOperator(address,(address,uint32,address[],uint256[],string))": "36352057", "updateAVSMetadataURI(address,string)": "a9821821"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"operatorSetId\",\"type\":\"uint32\"},{\"internalType\":\"address[]\",\"name\":\"strategies\",\"type\":\"address[]\"}],\"name\":\"addStrategiesToOperatorSet\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"address[]\",\"name\":\"strategies\",\"type\":\"address[]\"},{\"internalType\":\"uint16[]\",\"name\":\"numToClear\",\"type\":\"uint16[]\"}],\"name\":\"clearDeallocationQueue\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"uint32\",\"name\":\"operatorSetId\",\"type\":\"uint32\"},{\"internalType\":\"address[]\",\"name\":\"strategies\",\"type\":\"address[]\"}],\"internalType\":\"struct IAllocationManager.CreateSetParams[]\",\"name\":\"params\",\"type\":\"tuple[]\"}],\"name\":\"createOperatorSets\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"internalType\":\"uint32[]\",\"name\":\"operatorSetIds\",\"type\":\"uint32[]\"}],\"internalType\":\"struct IAllocationManager.DeregisterParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"deregisterFromOperatorSets\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"}],\"name\":\"getAVSRegistrar\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"strategy\",\"type\":\"address\"}],\"name\":\"getAllocatableMagnitude\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"getAllocatedSets\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"id\",\"type\":\"uint32\"}],\"internalType\":\"struct IAllocationManager.OperatorSet[]\",\"name\":\"\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"id\",\"type\":\"uint32\"}],\"internalType\":\"struct IAllocationManager.OperatorSet\",\"name\":\"operatorSet\",\"type\":\"tuple\"},{\"internalType\":\"address[]\",\"name\":\"operators\",\"type\":\"address[]\"},{\"internalType\":\"address[]\",\"name\":\"strategies\",\"type\":\"address[]\"}],\"name\":\"getAllocatedStake\",\"outputs\":[{\"internalType\":\"uint256[][]\",\"name\":\"slashableStake\",\"type\":\"uint256[][]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"id\",\"type\":\"uint32\"}],\"internalType\":\"struct IAllocationManager.OperatorSet\",\"name\":\"operatorSet\",\"type\":\"tuple\"}],\"name\":\"getAllocatedStrategies\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"id\",\"type\":\"uint32\"}],\"internalType\":\"struct IAllocationManager.OperatorSet\",\"name\":\"operatorSet\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"strategy\",\"type\":\"address\"}],\"name\":\"getAllocation\",\"outputs\":[{\"components\":[{\"internalType\":\"uint64\",\"name\":\"currentMagnitude\",\"type\":\"uint64\"},{\"internalType\":\"int128\",\"name\":\"pendingDiff\",\"type\":\"int128\"},{\"internalType\":\"uint32\",\"name\":\"effectBlock\",\"type\":\"uint32\"}],\"internalType\":\"struct IAllocationManager.Allocation\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"getAllocationDelay\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"isSet\",\"type\":\"bool\"},{\"internalType\":\"uint32\",\"name\":\"delay\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"operators\",\"type\":\"address[]\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"id\",\"type\":\"uint32\"}],\"internalType\":\"struct IAllocationManager.OperatorSet\",\"name\":\"operatorSet\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"strategy\",\"type\":\"address\"}],\"name\":\"getAllocations\",\"outputs\":[{\"components\":[{\"internalType\":\"uint64\",\"name\":\"currentMagnitude\",\"type\":\"uint64\"},{\"internalType\":\"int128\",\"name\":\"pendingDiff\",\"type\":\"int128\"},{\"internalType\":\"uint32\",\"name\":\"effectBlock\",\"type\":\"uint32\"}],\"internalType\":\"struct IAllocationManager.Allocation[]\",\"name\":\"\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"strategy\",\"type\":\"address\"}],\"name\":\"getEncumberedMagnitude\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"strategy\",\"type\":\"address\"}],\"name\":\"getMaxMagnitude\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"operators\",\"type\":\"address[]\"},{\"internalType\":\"address\",\"name\":\"strategy\",\"type\":\"address\"}],\"name\":\"getMaxMagnitudes\",\"outputs\":[{\"internalType\":\"uint64[]\",\"name\":\"\",\"type\":\"uint64[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"address[]\",\"name\":\"strategies\",\"type\":\"address[]\"}],\"name\":\"getMaxMagnitudes\",\"outputs\":[{\"internalType\":\"uint64[]\",\"name\":\"\",\"type\":\"uint64[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"address[]\",\"name\":\"strategies\",\"type\":\"address[]\"},{\"internalType\":\"uint32\",\"name\":\"blockNumber\",\"type\":\"uint32\"}],\"name\":\"getMaxMagnitudesAtBlock\",\"outputs\":[{\"internalType\":\"uint64[]\",\"name\":\"\",\"type\":\"uint64[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"id\",\"type\":\"uint32\"}],\"internalType\":\"struct IAllocationManager.OperatorSet\",\"name\":\"operatorSet\",\"type\":\"tuple\"}],\"name\":\"getMemberCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"id\",\"type\":\"uint32\"}],\"internalType\":\"struct IAllocationManager.OperatorSet\",\"name\":\"operatorSet\",\"type\":\"tuple\"}],\"name\":\"getMembers\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"operators\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"id\",\"type\":\"uint32\"}],\"internalType\":\"struct IAllocationManager.OperatorSet\",\"name\":\"operatorSet\",\"type\":\"tuple\"},{\"internalType\":\"address[]\",\"name\":\"operators\",\"type\":\"address[]\"},{\"internalType\":\"address[]\",\"name\":\"strategies\",\"type\":\"address[]\"},{\"internalType\":\"uint32\",\"name\":\"futureBlock\",\"type\":\"uint32\"}],\"name\":\"getMinimumSlashableStake\",\"outputs\":[{\"internalType\":\"uint256[][]\",\"name\":\"slashableStake\",\"type\":\"uint256[][]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"}],\"name\":\"getOperatorSetCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"getRegisteredSets\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"id\",\"type\":\"uint32\"}],\"internalType\":\"struct IAllocationManager.OperatorSet[]\",\"name\":\"operatorSets\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"id\",\"type\":\"uint32\"}],\"internalType\":\"struct IAllocationManager.OperatorSet\",\"name\":\"operatorSet\",\"type\":\"tuple\"}],\"name\":\"getStrategiesInOperatorSet\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"strategies\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"strategy\",\"type\":\"address\"}],\"name\":\"getStrategyAllocations\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"id\",\"type\":\"uint32\"}],\"internalType\":\"struct IAllocationManager.OperatorSet[]\",\"name\":\"\",\"type\":\"tuple[]\"},{\"components\":[{\"internalType\":\"uint64\",\"name\":\"currentMagnitude\",\"type\":\"uint64\"},{\"internalType\":\"int128\",\"name\":\"pendingDiff\",\"type\":\"int128\"},{\"internalType\":\"uint32\",\"name\":\"effectBlock\",\"type\":\"uint32\"}],\"internalType\":\"struct IAllocationManager.Allocation[]\",\"name\":\"\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"initialOwner\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"initialPausedStatus\",\"type\":\"uint256\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"id\",\"type\":\"uint32\"}],\"internalType\":\"struct IAllocationManager.OperatorSet\",\"name\":\"operatorSet\",\"type\":\"tuple\"}],\"name\":\"isMemberOfOperatorSet\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"id\",\"type\":\"uint32\"}],\"internalType\":\"struct IAllocationManager.OperatorSet\",\"name\":\"operatorSet\",\"type\":\"tuple\"}],\"name\":\"isOperatorSet\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"id\",\"type\":\"uint32\"}],\"internalType\":\"struct IAllocationManager.OperatorSet\",\"name\":\"operatorSet\",\"type\":\"tuple\"}],\"name\":\"isOperatorSlashable\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"components\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"id\",\"type\":\"uint32\"}],\"internalType\":\"struct IAllocationManager.OperatorSet\",\"name\":\"operatorSet\",\"type\":\"tuple\"},{\"internalType\":\"address[]\",\"name\":\"strategies\",\"type\":\"address[]\"},{\"internalType\":\"uint64[]\",\"name\":\"newMagnitudes\",\"type\":\"uint64[]\"}],\"internalType\":\"struct IAllocationManager.AllocateParams[]\",\"name\":\"params\",\"type\":\"tuple[]\"}],\"name\":\"modifyAllocations\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"internalType\":\"uint32[]\",\"name\":\"operatorSetIds\",\"type\":\"uint32[]\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"internalType\":\"struct IAllocationManager.RegisterParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"registerForOperatorSets\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"operatorSetId\",\"type\":\"uint32\"},{\"internalType\":\"address[]\",\"name\":\"strategies\",\"type\":\"address[]\"}],\"name\":\"removeStrategiesFromOperatorSet\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"registrar\",\"type\":\"address\"}],\"name\":\"setAVSRegistrar\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"delay\",\"type\":\"uint32\"}],\"name\":\"setAllocationDelay\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"operatorSetId\",\"type\":\"uint32\"},{\"internalType\":\"address[]\",\"name\":\"strategies\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"wadsToSlash\",\"type\":\"uint256[]\"},{\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"}],\"internalType\":\"struct IAllocationManager.SlashingParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"name\":\"slashOperator\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"metadataURI\",\"type\":\"string\"}],\"name\":\"updateAVSMetadataURI\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/external/eigen-layer/IAllocationManager.sol\":\"IAllocationManager\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"src/interfaces/external/eigen-layer/IAllocationManager.sol\":{\"keccak256\":\"0xae3e733a2af24b269f9e861eb091a70123dfb662073296af1e0404d3ef38077c\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b35c72a2bdebe1d6a1515a1215e90bb354414de570e3723bd1f764204fe51e86\",\"dweb:/ipfs/QmWibt9unFu2MYSMXmkb8rAtCEpxUxio5Rn2fM43D4cKth\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "uint32", "name": "operatorSetId", "type": "uint32"}, {"internalType": "address[]", "name": "strategies", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "addStrategiesToOperatorSet"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address[]", "name": "strategies", "type": "address[]"}, {"internalType": "uint16[]", "name": "numToClear", "type": "uint16[]"}], "stateMutability": "nonpayable", "type": "function", "name": "clearDeallocationQueue"}, {"inputs": [{"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "struct IAllocationManager.CreateSetParams[]", "name": "params", "type": "tuple[]", "components": [{"internalType": "uint32", "name": "operatorSetId", "type": "uint32"}, {"internalType": "address[]", "name": "strategies", "type": "address[]"}]}], "stateMutability": "nonpayable", "type": "function", "name": "createOperatorSets"}, {"inputs": [{"internalType": "struct IAllocationManager.DeregisterParams", "name": "params", "type": "tuple", "components": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "uint32[]", "name": "operatorSetIds", "type": "uint32[]"}]}], "stateMutability": "nonpayable", "type": "function", "name": "deregisterFromOperatorSets"}, {"inputs": [{"internalType": "address", "name": "avs", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getAVSRegistrar", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "strategy", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getAllocatableMagnitude", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getAllocatedSets", "outputs": [{"internalType": "struct IAllocationManager.OperatorSet[]", "name": "", "type": "tuple[]", "components": [{"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "uint32", "name": "id", "type": "uint32"}]}]}, {"inputs": [{"internalType": "struct IAllocationManager.OperatorSet", "name": "operatorSet", "type": "tuple", "components": [{"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "uint32", "name": "id", "type": "uint32"}]}, {"internalType": "address[]", "name": "operators", "type": "address[]"}, {"internalType": "address[]", "name": "strategies", "type": "address[]"}], "stateMutability": "view", "type": "function", "name": "getAllocatedStake", "outputs": [{"internalType": "uint256[][]", "name": "slashableStake", "type": "uint256[][]"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "struct IAllocationManager.OperatorSet", "name": "operatorSet", "type": "tuple", "components": [{"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "uint32", "name": "id", "type": "uint32"}]}], "stateMutability": "view", "type": "function", "name": "getAllocatedStrategies", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "struct IAllocationManager.OperatorSet", "name": "operatorSet", "type": "tuple", "components": [{"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "uint32", "name": "id", "type": "uint32"}]}, {"internalType": "address", "name": "strategy", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getAllocation", "outputs": [{"internalType": "struct IAllocationManager.Allocation", "name": "", "type": "tuple", "components": [{"internalType": "uint64", "name": "currentMagnitude", "type": "uint64"}, {"internalType": "int128", "name": "pendingDiff", "type": "int128"}, {"internalType": "uint32", "name": "effectBlock", "type": "uint32"}]}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getAllocationDelay", "outputs": [{"internalType": "bool", "name": "isSet", "type": "bool"}, {"internalType": "uint32", "name": "delay", "type": "uint32"}]}, {"inputs": [{"internalType": "address[]", "name": "operators", "type": "address[]"}, {"internalType": "struct IAllocationManager.OperatorSet", "name": "operatorSet", "type": "tuple", "components": [{"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "uint32", "name": "id", "type": "uint32"}]}, {"internalType": "address", "name": "strategy", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getAllocations", "outputs": [{"internalType": "struct IAllocationManager.Allocation[]", "name": "", "type": "tuple[]", "components": [{"internalType": "uint64", "name": "currentMagnitude", "type": "uint64"}, {"internalType": "int128", "name": "pendingDiff", "type": "int128"}, {"internalType": "uint32", "name": "effectBlock", "type": "uint32"}]}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "strategy", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getEncumberedMagnitude", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "strategy", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getMaxMagnitude", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}]}, {"inputs": [{"internalType": "address[]", "name": "operators", "type": "address[]"}, {"internalType": "address", "name": "strategy", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getMaxMagnitudes", "outputs": [{"internalType": "uint64[]", "name": "", "type": "uint64[]"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address[]", "name": "strategies", "type": "address[]"}], "stateMutability": "view", "type": "function", "name": "getMaxMagnitudes", "outputs": [{"internalType": "uint64[]", "name": "", "type": "uint64[]"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address[]", "name": "strategies", "type": "address[]"}, {"internalType": "uint32", "name": "blockNumber", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "getMaxMagnitudesAtBlock", "outputs": [{"internalType": "uint64[]", "name": "", "type": "uint64[]"}]}, {"inputs": [{"internalType": "struct IAllocationManager.OperatorSet", "name": "operatorSet", "type": "tuple", "components": [{"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "uint32", "name": "id", "type": "uint32"}]}], "stateMutability": "view", "type": "function", "name": "getMemberCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "struct IAllocationManager.OperatorSet", "name": "operatorSet", "type": "tuple", "components": [{"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "uint32", "name": "id", "type": "uint32"}]}], "stateMutability": "view", "type": "function", "name": "getMembers", "outputs": [{"internalType": "address[]", "name": "operators", "type": "address[]"}]}, {"inputs": [{"internalType": "struct IAllocationManager.OperatorSet", "name": "operatorSet", "type": "tuple", "components": [{"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "uint32", "name": "id", "type": "uint32"}]}, {"internalType": "address[]", "name": "operators", "type": "address[]"}, {"internalType": "address[]", "name": "strategies", "type": "address[]"}, {"internalType": "uint32", "name": "futureBlock", "type": "uint32"}], "stateMutability": "view", "type": "function", "name": "getMinimumSlashableStake", "outputs": [{"internalType": "uint256[][]", "name": "slashableStake", "type": "uint256[][]"}]}, {"inputs": [{"internalType": "address", "name": "avs", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getOperatorSetCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getRegisteredSets", "outputs": [{"internalType": "struct IAllocationManager.OperatorSet[]", "name": "operatorSets", "type": "tuple[]", "components": [{"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "uint32", "name": "id", "type": "uint32"}]}]}, {"inputs": [{"internalType": "struct IAllocationManager.OperatorSet", "name": "operatorSet", "type": "tuple", "components": [{"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "uint32", "name": "id", "type": "uint32"}]}], "stateMutability": "view", "type": "function", "name": "getStrategiesInOperatorSet", "outputs": [{"internalType": "address[]", "name": "strategies", "type": "address[]"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "strategy", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getStrategyAllocations", "outputs": [{"internalType": "struct IAllocationManager.OperatorSet[]", "name": "", "type": "tuple[]", "components": [{"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "uint32", "name": "id", "type": "uint32"}]}, {"internalType": "struct IAllocationManager.Allocation[]", "name": "", "type": "tuple[]", "components": [{"internalType": "uint64", "name": "currentMagnitude", "type": "uint64"}, {"internalType": "int128", "name": "pendingDiff", "type": "int128"}, {"internalType": "uint32", "name": "effectBlock", "type": "uint32"}]}]}, {"inputs": [{"internalType": "address", "name": "initialOwner", "type": "address"}, {"internalType": "uint256", "name": "initialPausedStatus", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "struct IAllocationManager.OperatorSet", "name": "operatorSet", "type": "tuple", "components": [{"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "uint32", "name": "id", "type": "uint32"}]}], "stateMutability": "view", "type": "function", "name": "isMemberOfOperatorSet", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "struct IAllocationManager.OperatorSet", "name": "operatorSet", "type": "tuple", "components": [{"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "uint32", "name": "id", "type": "uint32"}]}], "stateMutability": "view", "type": "function", "name": "isOperatorSet", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "struct IAllocationManager.OperatorSet", "name": "operatorSet", "type": "tuple", "components": [{"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "uint32", "name": "id", "type": "uint32"}]}], "stateMutability": "view", "type": "function", "name": "isOperatorSlashable", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "struct IAllocationManager.AllocateParams[]", "name": "params", "type": "tuple[]", "components": [{"internalType": "struct IAllocationManager.OperatorSet", "name": "operatorSet", "type": "tuple", "components": [{"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "uint32", "name": "id", "type": "uint32"}]}, {"internalType": "address[]", "name": "strategies", "type": "address[]"}, {"internalType": "uint64[]", "name": "newMagnitudes", "type": "uint64[]"}]}], "stateMutability": "nonpayable", "type": "function", "name": "modifyAllocations"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "struct IAllocationManager.RegisterParams", "name": "params", "type": "tuple", "components": [{"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "uint32[]", "name": "operatorSetIds", "type": "uint32[]"}, {"internalType": "bytes", "name": "data", "type": "bytes"}]}], "stateMutability": "nonpayable", "type": "function", "name": "registerForOperatorSets"}, {"inputs": [{"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "uint32", "name": "operatorSetId", "type": "uint32"}, {"internalType": "address[]", "name": "strategies", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "removeStrategiesFromOperatorSet"}, {"inputs": [{"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "address", "name": "registrar", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setAVSRegistrar"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "uint32", "name": "delay", "type": "uint32"}], "stateMutability": "nonpayable", "type": "function", "name": "setAllocationDelay"}, {"inputs": [{"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "struct IAllocationManager.SlashingParams", "name": "params", "type": "tuple", "components": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "uint32", "name": "operatorSetId", "type": "uint32"}, {"internalType": "address[]", "name": "strategies", "type": "address[]"}, {"internalType": "uint256[]", "name": "wadsToSlash", "type": "uint256[]"}, {"internalType": "string", "name": "description", "type": "string"}]}], "stateMutability": "nonpayable", "type": "function", "name": "slashOperator"}, {"inputs": [{"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "string", "name": "metadataURI", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "updateAVSMetadataURI"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/external/eigen-layer/IAllocationManager.sol": "IAllocationManager"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"src/interfaces/external/eigen-layer/IAllocationManager.sol": {"keccak256": "0xae3e733a2af24b269f9e861eb091a70123dfb662073296af1e0404d3ef38077c", "urls": ["bzz-raw://b35c72a2bdebe1d6a1515a1215e90bb354414de570e3723bd1f764204fe51e86", "dweb:/ipfs/QmWibt9unFu2MYSMXmkb8rAtCEpxUxio5Rn2fM43D4cKth"], "license": "BUSL-1.1"}}, "version": 1}, "id": 77}