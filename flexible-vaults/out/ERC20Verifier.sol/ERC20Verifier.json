{"abi": [{"type": "constructor", "inputs": [{"name": "name_", "type": "string", "internalType": "string"}, {"name": "version_", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "ASSET_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "CALLER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "DEFAULT_ADMIN_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "RECIPIENT_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "getRoleAdmin", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "getRoleMember", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getRoleMemberCount", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getRoleMembers", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "grantRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "hasRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "hasSupportedRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "renounceRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "callerConfirmation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "revokeRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportedRoleAt", "inputs": [{"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "supportedRoles", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "verifyCall", "inputs": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "callData", "type": "bytes", "internalType": "bytes"}, {"name": "", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "RoleAdded", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "RoleAdminChanged", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "previousAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "newAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "RoleGranted", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RoleRemoved", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "RoleRevoked", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AccessControlBadConfirmation", "inputs": []}, {"type": "error", "name": "AccessControlUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "neededRole", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "ZeroValue", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "157:1519:132:-:0;;;526:90;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;597:5;604:8;308:5:134;315:8;432:49:130;;;;;;;;;;;;;;-1:-1:-1;;;432:49:130;;;465:5;472:8;432:19;;;:49;;:::i;:::-;408:73;;491:22;:20;:22::i;:::-;-1:-1:-1;335:22:134::1;::::0;-1:-1:-1;335:20:134::1;:22::i;:::-;247:117:::0;;526:90:132;;157:1519;;656:343:113;761:7;986:4;970:22;;969:23;941:1;908:12;922:4;928:7;856:80;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;846:91;;;;;;838:100;;:104;;;;:::i;:::-;810:146;;;;;;2258:25:192;;2246:2;2231:18;;2112:177;810:146:113;;;;;;;;;;;;;787:179;;;;;;:205;780:212;;656:343;;;;;:::o;7709:422:3:-;7824:30;7857:26;:24;:26::i;:::-;7898:15;;;;-1:-1:-1;7898:15:3;;;;;7894:76;;;7936:23;;-1:-1:-1;;;7936:23:3;;;;;;;;;;;7894:76;7983:14;;-1:-1:-1;;;;;7983:14:3;;;:34;7979:146;;8033:33;;-1:-1:-1;;;;;;8033:33:3;-1:-1:-1;;;;;8033:33:3;;;;;8085:29;;2438:50:192;;;8085:29:3;;2426:2:192;2411:18;8085:29:3;;;;;;;7979:146;7758:373;7709:422::o;9071:205::-;9129:30;;3147:66;9186:27;9171:42;9071:205;-1:-1:-1;;9071:205:3:o;14:127:192:-;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:983;235:6;243;296:2;284:9;275:7;271:23;267:32;264:52;;;312:1;309;302:12;264:52;339:16;;-1:-1:-1;;;;;404:14:192;;;401:34;;;431:1;428;421:12;401:34;469:6;458:9;454:22;444:32;;514:7;507:4;503:2;499:13;495:27;485:55;;536:1;533;526:12;485:55;565:2;559:9;587:2;583;580:10;577:36;;;593:18;;:::i;:::-;668:2;662:9;636:2;722:13;;-1:-1:-1;;718:22:192;;;742:2;714:31;710:40;698:53;;;766:18;;;786:22;;;763:46;760:72;;;812:18;;:::i;:::-;852:10;848:2;841:22;887:2;879:6;872:18;929:7;922:4;917:2;913;909:11;905:22;902:35;899:55;;;950:1;947;940:12;899:55;1003:2;996:4;992:2;988:13;981:4;973:6;969:17;963:43;1050:1;1043:4;1038:2;1030:6;1026:15;1022:26;1015:37;1071:6;1061:16;;;;;;;1117:4;1106:9;1102:20;1096:27;1086:37;;146:983;;;;;:::o;1134:212::-;1176:3;1214:5;1208:12;1258:6;1251:4;1244:5;1240:16;1235:3;1229:36;1320:1;1284:16;;1309:13;;;-1:-1:-1;1284:16:192;;1134:212;-1:-1:-1;1134:212:192:o;1351:526::-;1689:33;1684:3;1677:46;1659:3;1745:66;1771:39;1806:2;1801:3;1797:12;1789:6;1771:39;:::i;:::-;1763:6;1745:66;:::i;:::-;1820:21;;;-1:-1:-1;;1868:2:192;1857:14;;1351:526;-1:-1:-1;;1351:526:192:o;1882:225::-;1949:9;;;1970:11;;;1967:134;;;2023:10;2018:3;2014:20;2011:1;2004:31;2058:4;2055:1;2048:15;2086:4;2083:1;2076:15;2294:200;157:1519:132;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "157:1519:132:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1695:212:2;;;;;;:::i;:::-;;:::i;:::-;;;470:14:192;;463:22;445:41;;433:2;418:18;1695:212:2;;;;;;;;209:96:132;;246:59;209:96;;;;;643:25:192;;;631:2;616:18;209:96:132;497:177:192;4759:191:0;;;;;;:::i;:::-;;:::i;5246:136::-;;;;;;:::i;:::-;;:::i;:::-;;6348:245;;;;;;:::i;:::-;;:::i;580:125:130:-;;;:::i;396:563:134:-;;;;;;:::i;:::-;;:::i;919:142:130:-;;;;;;:::i;:::-;;:::i;645:1029:132:-;;;;;;:::i;:::-;;:::i;311:98::-;;349:60;311:98;;415:104;;456:63;415:104;;2492:233:2;;;;;;:::i;:::-;;:::i;:::-;;;-1:-1:-1;;;;;3753:32:192;;;3735:51;;3723:2;3708:18;2492:233:2;3589:203:192;3732:207:0;;;;;;:::i;:::-;;:::i;2317:49::-;;2362:4;2317:49;;742:140:130;;;;;;:::i;:::-;;:::i;3658:227:2:-;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;2893:222::-;;;;;;:::i;:::-;;:::i;5662:138:0:-;;;;;;:::i;:::-;;:::i;1695:212:2:-;1780:4;-1:-1:-1;;;;;;1803:57:2;;-1:-1:-1;;;1803:57:2;;:97;;;1864:36;1888:11;1864:23;:36::i;:::-;1796:104;1695:212;-1:-1:-1;;1695:212:2:o;4759:191:0:-;4824:7;4919:14;;;-1:-1:-1;;;;;;;;;;;4919:14:0;;;;;:24;;;;4759:191::o;5246:136::-;5320:18;5333:4;5320:12;:18::i;:::-;3191:16;3202:4;3191:10;:16::i;:::-;5350:25:::1;5361:4;5367:7;5350:10;:25::i;:::-;;5246:136:::0;;;:::o;6348:245::-;-1:-1:-1;;;;;6441:34:0;;966:10:5;6441:34:0;6437:102;;6498:30;;-1:-1:-1;;;6498:30:0;;;;;;;;;;;6437:102;6549:37;6561:4;6567:18;6549:11;:37::i;:::-;;6348:245;;:::o;580:125:130:-;629:7;655:43;1902:21;655:41;:43::i;:::-;648:50;;580:125;:::o;396:563:134:-;4158:30:3;4191:26;:24;:26::i;:::-;4302:15;;4158:59;;-1:-1:-1;4302:15:3;-1:-1:-1;;;4302:15:3;;;4301:16;;4348:14;;4279:19;4724:16;;:34;;;;;4744:14;4724:34;4704:54;;4768:17;4788:11;:16;;4803:1;4788:16;:50;;;;-1:-1:-1;4816:4:3;4808:25;:30;4788:50;4768:70;;4854:12;4853:13;:30;;;;;4871:12;4870:13;4853:30;4849:91;;;4906:23;;-1:-1:-1;;;4906:23:3;;;;;;;;;;;4849:91;4949:18;;-1:-1:-1;;4949:18:3;4966:1;4949:18;;;4977:67;;;;5011:22;;-1:-1:-1;;;;5011:22:3;-1:-1:-1;;;5011:22:3;;;4977:67;469:13:134::1;::::0;;548:49:::1;::::0;;::::1;559:4:::0;548:49:::1;:::i;:::-;468:129:::0;;-1:-1:-1;468:129:134;-1:-1:-1;468:129:134;-1:-1:-1;;;;;;611:19:134;::::1;607:68;;653:11;;-1:-1:-1::0;;;653:11:134::1;;;;;;;;;;;607:68;684:37;2362:4:0;715:5:134::0;684:10:::1;:37::i;:::-;;736:9;731:222;755:7;:14;751:1;:18;731:222;;;816:1;-1:-1:-1::0;;;;;794:24:134::1;:7;802:1;794:10;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1::0;;;;;794:24:134::1;;:50;;;;842:1;834:10:::0;::::1;822:5;828:1;822:8;;;;;;;;:::i;:::-;;;;;;;:22;794:50;790:107;;;871:11;;-1:-1:-1::0;;;871:11:134::1;;;;;;;;;;;790:107;910:32;921:5;927:1;921:8;;;;;;;;:::i;:::-;;;;;;;931:7;939:1;931:10;;;;;;;;:::i;:::-;;;;;;;910;:32::i;:::-;-1:-1:-1::0;771:3:134::1;;731:222;;;;458:501;;;5068:14:3::0;5064:101;;;5098:23;;-1:-1:-1;;;;5098:23:3;;;5140:14;;-1:-1:-1;7570:50:192;;5140:14:3;;7558:2:192;7543:18;5140:14:3;;;;;;;5064:101;4092:1079;;;;;396:563:134;;:::o;919:142:130:-;982:4;1005:49;1902:21;1049:4;1005:43;:49::i;645:1029:132:-;850:4;870:10;;;;:35;;-1:-1:-1;903:2:132;884:21;;;870:35;:66;;;;910:26;246:59;930:5;910:7;:26::i;:::-;909:27;870:66;:96;;;;941:25;349:60;962:3;941:7;:25::i;:::-;940:26;870:96;866:139;;;-1:-1:-1;989:5:132;982:12;;866:139;1014:15;1039:12;1049:1;1014:15;1039:8;;:12;:::i;:::-;1032:20;;;:::i;:::-;1014:38;-1:-1:-1;;;;;;;1066:35:132;;-1:-1:-1;;;1066:35:132;;:75;;-1:-1:-1;;;;;;;1105:36:132;;-1:-1:-1;;;1105:36:132;1066:75;1062:585;;;1158:10;;1199:12;:8;1208:1;1199:8;;:12;:::i;:::-;1188:44;;;;;;;:::i;:::-;1157:75;;-1:-1:-1;1157:75:132;-1:-1:-1;;;;;;1267:16:132;;;;:73;;-1:-1:-1;;;;;;;1288:36:132;;-1:-1:-1;;;1288:36:132;:51;;;;-1:-1:-1;1328:11:132;;1288:51;1267:125;;;;1365:27;456:63;1389:2;1365:7;:27::i;:::-;1364:28;1267:125;1246:206;;;1432:5;1425:12;;;;;;;1246:206;1538:8;;1528:19;;;;;;;:::i;:::-;;;;;;;;;-1:-1:-1;;;;;9091:32:192;;1479:44:132;;;9073:51:192;9140:18;;;9133:34;;;1528:19:132;1502:8;;9046:18:192;;1479:44:132;;;-1:-1:-1;;1479:44:132;;;;;;;;;;;;;;-1:-1:-1;;;;;1479:44:132;-1:-1:-1;;;;;;1479:44:132;;;;;;;;;1469:55;;;:78;1465:129;;1574:5;1567:12;;;;;;;1465:129;1143:461;;1062:585;;;1631:5;1624:12;;;;;1062:585;1663:4;1656:11;;;645:1029;;;;;;;;;;:::o;2492:233:2:-;2573:7;2688:20;;;-1:-1:-1;;;;;;;;;;;2688:20:2;;;;;;;:30;;2712:5;2688:23;:30::i;:::-;2681:37;2492:233;-1:-1:-1;;;;2492:233:2:o;3732:207:0:-;3809:4;3901:14;;;-1:-1:-1;;;;;;;;;;;3901:14:0;;;;;;;;-1:-1:-1;;;;;3901:31:0;;;;;;;;;;;;;;;3732:207::o;742:140:130:-;805:7;831:44;1902:21;869:5;831:37;:44::i;3658:227:2:-;3753:40;3849:20;;;-1:-1:-1;;;;;;;;;;;3849:20:2;;;;;;;;3725:16;;1403:38;3849:29;;:27;:29::i;:::-;3842:36;3658:227;-1:-1:-1;;;3658:227:2:o;2893:222::-;2964:7;3079:20;;;-1:-1:-1;;;;;;;;;;;3079:20:2;;;;;;;:29;;:27;:29::i;5662:138:0:-;5737:18;5750:4;5737:12;:18::i;:::-;3191:16;3202:4;3191:10;:16::i;:::-;5767:26:::1;5779:4;5785:7;5767:11;:26::i;3443:202::-:0;3528:4;-1:-1:-1;;;;;;3551:47:0;;-1:-1:-1;;;3551:47:0;;:87;;-1:-1:-1;;;;;;;;;;1134:40:8;;;3602:36:0;1035:146:8;4148:103:0;4214:30;4225:4;966:10:5;4214::0;:30::i;:::-;4148:103;:::o;1094:319:130:-;1180:4;1200:31;1217:4;1223:7;1200:16;:31::i;:::-;1196:189;;;1251:44;1902:21;1290:4;1251:38;:44::i;:::-;1247:103;;;1320:15;;1330:4;;1320:15;;;;;1247:103;-1:-1:-1;1370:4:130;1363:11;;1196:189;-1:-1:-1;1401:5:130;1094:319;;;;:::o;1419:373::-;1506:4;1526:32;1544:4;1550:7;1526:17;:32::i;:::-;1522:242;;;1578:24;1597:4;1578:18;:24::i;:::-;1606:1;1578:29;1574:155;;1627:47;1902:21;1669:4;1627:41;:47::i;:::-;-1:-1:-1;1697:17:130;;1709:4;;1697:17;;;;;-1:-1:-1;1749:4:130;1742:11;;7693:115:72;7756:7;7782:19;7790:3;5202:18;;5120:107;9071:205:3;9129:30;;3147:66;9186:27;8819:122;7474:138:72;7554:4;5006:21;;;:14;;;:21;;;;;;:26;;7577:28;4910:129;10987:156;11061:7;11111:22;11115:3;11127:5;11111:3;:22::i;11683:273::-;11746:16;11774:22;11799:19;11807:3;11799:7;:19::i;4381:197:0:-;4469:22;4477:4;4483:7;4469;:22::i;:::-;4464:108;;4514:47;;-1:-1:-1;;;4514:47:0;;-1:-1:-1;;;;;9091:32:192;;4514:47:0;;;9073:51:192;9140:18;;;9133:34;;;9046:18;;4514:47:0;;;;;;;4464:108;4381:197;;:::o;3987:348:2:-;4073:4;-1:-1:-1;;;;;;;;;;;4073:4:2;4193:31;4210:4;4216:7;4193:16;:31::i;:::-;4178:46;;4238:7;4234:71;;;4261:14;:20;;;;;;;;;;:33;;4286:7;4261:24;:33::i;:::-;;4321:7;3987:348;-1:-1:-1;;;;3987:348:2:o;6576:123:72:-;6646:4;6669:23;6674:3;6686:5;6669:4;:23::i;4438:353:2:-;4525:4;-1:-1:-1;;;;;;;;;;;4525:4:2;4645:32;4663:4;4669:7;4645:17;:32::i;:::-;4630:47;;4691:7;4687:74;;;4714:14;:20;;;;;;;;;;:36;;4742:7;4714:27;:36::i;6867:129:72:-;6940:4;6963:26;6971:3;6983:5;6963:7;:26::i;5569:118::-;5636:7;5662:3;:11;;5674:5;5662:18;;;;;;;;:::i;:::-;;;;;;;;;5655:25;;5569:118;;;;:::o;6227:109::-;6283:16;6318:3;:11;;6311:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6227:109;;;:::o;7270:387:0:-;7347:4;-1:-1:-1;;;;;;;;;;;7437:22:0;7445:4;7451:7;7437;:22::i;:::-;7432:219;;7475:8;:14;;;;;;;;;;;-1:-1:-1;;;;;7475:31:0;;;;;;;;;:38;;-1:-1:-1;;7475:38:0;7509:4;7475:38;;;7559:12;966:10:5;;887:96;7559:12:0;-1:-1:-1;;;;;7532:40:0;7550:7;-1:-1:-1;;;;;7532:40:0;7544:4;7532:40;;;;;;;;;;7593:4;7586:11;;;;;7432:219;7635:5;7628:12;;;;;9332:150:72;9402:4;9425:50;9430:3;-1:-1:-1;;;;;9450:23:72;;2336:406;2399:4;5006:21;;;:14;;;:21;;;;;;2415:321;;-1:-1:-1;2457:23:72;;;;;;;;:11;:23;;;;;;;;;;;;;2639:18;;2615:21;;;:14;;;:21;;;;;;:42;;;;2671:11;;2415:321;-1:-1:-1;2720:5:72;2713:12;;7894:388:0;7972:4;-1:-1:-1;;;;;;;;;;;8061:22:0;8069:4;8075:7;8061;:22::i;:::-;8057:219;;;8133:5;8099:14;;;;;;;;;;;-1:-1:-1;;;;;8099:31:0;;;;;;;;;;:39;;-1:-1:-1;;8099:39:0;;;8157:40;966:10:5;;8099:14:0;;8157:40;;8133:5;8157:40;8218:4;8211:11;;;;;9650:156:72;9723:4;9746:53;9754:3;-1:-1:-1;;;;;9774:23:72;;2910:1368;2976:4;3105:21;;;:14;;;:21;;;;;;3141:13;;3137:1135;;3508:18;3529:12;3540:1;3529:8;:12;:::i;:::-;3575:18;;3508:33;;-1:-1:-1;3555:17:72;;3575:22;;3596:1;;3575:22;:::i;:::-;3555:42;;3630:9;3616:10;:23;3612:378;;3659:17;3679:3;:11;;3691:9;3679:22;;;;;;;;:::i;:::-;;;;;;;;;3659:42;;3826:9;3800:3;:11;;3812:10;3800:23;;;;;;;;:::i;:::-;;;;;;;;;;;;:35;;;;3939:25;;;:14;;;:25;;;;;:36;;;3612:378;4068:17;;:3;;:17;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;4171:3;:14;;:21;4186:5;4171:21;;;;;;;;;;;4164:28;;;4214:4;4207:11;;;;;;;14:286:192;72:6;125:2;113:9;104:7;100:23;96:32;93:52;;;141:1;138;131:12;93:52;167:23;;-1:-1:-1;;;;;;219:32:192;;209:43;;199:71;;266:1;263;256:12;679:180;738:6;791:2;779:9;770:7;766:23;762:32;759:52;;;807:1;804;797:12;759:52;-1:-1:-1;830:23:192;;679:180;-1:-1:-1;679:180:192:o;864:131::-;-1:-1:-1;;;;;939:31:192;;929:42;;919:70;;985:1;982;975:12;1000:315;1068:6;1076;1129:2;1117:9;1108:7;1104:23;1100:32;1097:52;;;1145:1;1142;1135:12;1097:52;1181:9;1168:23;1158:33;;1241:2;1230:9;1226:18;1213:32;1254:31;1279:5;1254:31;:::i;:::-;1304:5;1294:15;;;1000:315;;;;;:::o;1502:347::-;1553:8;1563:6;1617:3;1610:4;1602:6;1598:17;1594:27;1584:55;;1635:1;1632;1625:12;1584:55;-1:-1:-1;1658:20:192;;1701:18;1690:30;;1687:50;;;1733:1;1730;1723:12;1687:50;1770:4;1762:6;1758:17;1746:29;;1822:3;1815:4;1806:6;1798;1794:19;1790:30;1787:39;1784:59;;;1839:1;1836;1829:12;1784:59;1502:347;;;;;:::o;1854:409::-;1924:6;1932;1985:2;1973:9;1964:7;1960:23;1956:32;1953:52;;;2001:1;1998;1991:12;1953:52;2041:9;2028:23;2074:18;2066:6;2063:30;2060:50;;;2106:1;2103;2096:12;2060:50;2145:58;2195:7;2186:6;2175:9;2171:22;2145:58;:::i;:::-;2222:8;;2119:84;;-1:-1:-1;1854:409:192;-1:-1:-1;;;;1854:409:192:o;2268:1063::-;2385:6;2393;2401;2409;2417;2425;2433;2486:3;2474:9;2465:7;2461:23;2457:33;2454:53;;;2503:1;2500;2493:12;2454:53;2542:9;2529:23;2561:31;2586:5;2561:31;:::i;:::-;2611:5;-1:-1:-1;2668:2:192;2653:18;;2640:32;2681:33;2640:32;2681:33;:::i;:::-;2733:7;-1:-1:-1;2787:2:192;2772:18;;2759:32;;-1:-1:-1;2842:2:192;2827:18;;2814:32;2865:18;2895:14;;;2892:34;;;2922:1;2919;2912:12;2892:34;2961:58;3011:7;3002:6;2991:9;2987:22;2961:58;:::i;:::-;3038:8;;-1:-1:-1;2935:84:192;-1:-1:-1;3126:3:192;3111:19;;3098:33;;-1:-1:-1;3143:16:192;;;3140:36;;;3172:1;3169;3162:12;3140:36;;3211:60;3263:7;3252:8;3241:9;3237:24;3211:60;:::i;:::-;2268:1063;;;;-1:-1:-1;2268:1063:192;;-1:-1:-1;2268:1063:192;;;;3185:86;;-1:-1:-1;;;2268:1063:192:o;3336:248::-;3404:6;3412;3465:2;3453:9;3444:7;3440:23;3436:32;3433:52;;;3481:1;3478;3471:12;3433:52;-1:-1:-1;;3504:23:192;;;3574:2;3559:18;;;3546:32;;-1:-1:-1;3336:248:192:o;3982:658::-;4153:2;4205:21;;;4275:13;;4178:18;;;4297:22;;;4124:4;;4153:2;4376:15;;;;4350:2;4335:18;;;4124:4;4419:195;4433:6;4430:1;4427:13;4419:195;;;4498:13;;-1:-1:-1;;;;;4494:39:192;4482:52;;4589:15;;;;4554:12;;;;4530:1;4448:9;4419:195;;;-1:-1:-1;4631:3:192;;3982:658;-1:-1:-1;;;;;;3982:658:192:o;4645:127::-;4706:10;4701:3;4697:20;4694:1;4687:31;4737:4;4734:1;4727:15;4761:4;4758:1;4751:15;4777:275;4848:2;4842:9;4913:2;4894:13;;-1:-1:-1;;4890:27:192;4878:40;;4948:18;4933:34;;4969:22;;;4930:62;4927:88;;;4995:18;;:::i;:::-;5031:2;5024:22;4777:275;;-1:-1:-1;4777:275:192:o;5057:183::-;5117:4;5150:18;5142:6;5139:30;5136:56;;;5172:18;;:::i;:::-;-1:-1:-1;5217:1:192;5213:14;5229:4;5209:25;;5057:183::o;5245:668::-;5299:5;5352:3;5345:4;5337:6;5333:17;5329:27;5319:55;;5370:1;5367;5360:12;5319:55;5406:6;5393:20;5432:4;5456:60;5472:43;5512:2;5472:43;:::i;:::-;5456:60;:::i;:::-;5538:3;5562:2;5557:3;5550:15;5590:4;5585:3;5581:14;5574:21;;5647:4;5641:2;5638:1;5634:10;5626:6;5622:23;5618:34;5604:48;;5675:3;5667:6;5664:15;5661:35;;;5692:1;5689;5682:12;5661:35;5728:4;5720:6;5716:17;5742:142;5758:6;5753:3;5750:15;5742:142;;;5824:17;;5812:30;;5862:12;;;;5775;;5742:142;;;-1:-1:-1;5902:5:192;5245:668;-1:-1:-1;;;;;;5245:668:192:o;5918:1362::-;6053:6;6061;6069;6122:2;6110:9;6101:7;6097:23;6093:32;6090:52;;;6138:1;6135;6128:12;6090:52;6177:9;6164:23;6196:31;6221:5;6196:31;:::i;:::-;6246:5;-1:-1:-1;6270:2:192;6308:18;;;6295:32;6346:18;6376:14;;;6373:34;;;6403:1;6400;6393:12;6373:34;6441:6;6430:9;6426:22;6416:32;;6486:7;6479:4;6475:2;6471:13;6467:27;6457:55;;6508:1;6505;6498:12;6457:55;6544:2;6531:16;6567:60;6583:43;6623:2;6583:43;:::i;6567:60::-;6661:15;;;6743:1;6739:10;;;;6731:19;;6727:28;;;6692:12;;;;6767:19;;;6764:39;;;6799:1;6796;6789:12;6764:39;6823:11;;;;6843:223;6859:6;6854:3;6851:15;6843:223;;;6941:3;6928:17;6958:33;6983:7;6958:33;:::i;:::-;7004:20;;6876:12;;;;7044;;;;6843:223;;;7085:5;-1:-1:-1;;;7143:2:192;7128:18;;7115:32;;-1:-1:-1;7159:16:192;;;7156:36;;;7188:1;7185;7178:12;7156:36;;;7211:63;7266:7;7255:8;7244:9;7240:24;7211:63;:::i;:::-;7201:73;;;5918:1362;;;;;:::o;7285:127::-;7346:10;7341:3;7337:20;7334:1;7327:31;7377:4;7374:1;7367:15;7401:4;7398:1;7391:15;7631:331;7736:9;7747;7789:8;7777:10;7774:24;7771:44;;;7811:1;7808;7801:12;7771:44;7840:6;7830:8;7827:20;7824:40;;;7860:1;7857;7850:12;7824:40;-1:-1:-1;;7886:23:192;;;7931:25;;;;;-1:-1:-1;7631:331:192:o;7967:323::-;-1:-1:-1;;;;;;8087:19:192;;8163:11;;;;8194:1;8186:10;;8183:101;;;8271:2;8265;8258:3;8255:1;8251:11;8248:1;8244:19;8240:28;8236:2;8232:37;8228:46;8219:55;;8183:101;;;7967:323;;;;:::o;8295:::-;8371:6;8379;8432:2;8420:9;8411:7;8407:23;8403:32;8400:52;;;8448:1;8445;8438:12;8400:52;8487:9;8474:23;8506:31;8531:5;8506:31;:::i;:::-;8556:5;8608:2;8593:18;;;;8580:32;;-1:-1:-1;;;8295:323:192:o;8623:271::-;8806:6;8798;8793:3;8780:33;8762:3;8832:16;;8857:13;;;8832:16;8623:271;-1:-1:-1;8623:271:192:o;9457:225::-;9524:9;;;9545:11;;;9542:134;;;9598:10;9593:3;9589:20;9586:1;9579:31;9633:4;9630:1;9623:15;9661:4;9658:1;9651:15;9687:127;9748:10;9743:3;9739:20;9736:1;9729:31;9779:4;9776:1;9769:15;9803:4;9800:1;9793:15", "linkReferences": {}, "immutableReferences": {"69670": [{"start": 900, "length": 32}, {"start": 1457, "length": 32}, {"start": 2104, "length": 32}, {"start": 2339, "length": 32}, {"start": 2476, "length": 32}]}}, "methodIdentifiers": {"ASSET_ROLE()": "03100aa4", "CALLER_ROLE()": "774237fc", "DEFAULT_ADMIN_ROLE()": "a217fddf", "RECIPIENT_ROLE()": "8595f32a", "getRoleAdmin(bytes32)": "248a9ca3", "getRoleMember(bytes32,uint256)": "9010d07c", "getRoleMemberCount(bytes32)": "ca15c873", "getRoleMembers(bytes32)": "a3246ad3", "grantRole(bytes32,address)": "2f2ff15d", "hasRole(bytes32,address)": "91d14854", "hasSupportedRole(bytes32)": "4a46b4cf", "initialize(bytes)": "439fab91", "renounceRole(bytes32,address)": "36568abe", "revokeRole(bytes32,address)": "d547741f", "supportedRoleAt(uint256)": "a2cb31e5", "supportedRoles()": "419a2053", "supportsInterface(bytes4)": "01ffc9a7", "verifyCall(address,address,uint256,bytes,bytes)": "70e46bcb"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name_\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"version_\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"AccessControlBadConfirmation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"neededRole\",\"type\":\"bytes32\"}],\"name\":\"AccessControlUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZeroValue\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"RoleAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"previousAdminRole\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"newAdminRole\",\"type\":\"bytes32\"}],\"name\":\"RoleAdminChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleGranted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"RoleRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleRevoked\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"ASSET_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"CALLER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEFAULT_ADMIN_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"RECIPIENT_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"getRoleAdmin\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"getRoleMember\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"getRoleMemberCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"getRoleMembers\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"grantRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"hasRole\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"hasSupportedRole\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"callerConfirmation\",\"type\":\"address\"}],\"name\":\"renounceRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"revokeRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"supportedRoleAt\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"supportedRoles\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"callData\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"verifyCall\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"AccessControlBadConfirmation()\":[{\"details\":\"The caller of a function is not the expected one. NOTE: Don't confuse with {AccessControlUnauthorizedAccount}.\"}],\"AccessControlUnauthorizedAccount(address,bytes32)\":[{\"details\":\"The `account` is missing a role.\"}],\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}]},\"events\":{\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"},\"RoleAdminChanged(bytes32,bytes32,bytes32)\":{\"details\":\"Emitted when `newAdminRole` is set as ``role``'s admin role, replacing `previousAdminRole` `DEFAULT_ADMIN_ROLE` is the starting admin for all roles, despite {RoleAdminChanged} not being emitted to signal this.\"},\"RoleGranted(bytes32,address,address)\":{\"details\":\"Emitted when `account` is granted `role`. `sender` is the account that originated the contract call. This account bears the admin role (for the granted role). Expected in cases where the role was granted using the internal {AccessControl-_grantRole}.\"},\"RoleRevoked(bytes32,address,address)\":{\"details\":\"Emitted when `account` is revoked `role`. `sender` is the account that originated the contract call:   - if using `revokeRole`, it is the admin role bearer   - if using `renounceRole`, it is the role bearer (i.e. `account`)\"}},\"kind\":\"dev\",\"methods\":{\"getRoleAdmin(bytes32)\":{\"details\":\"Returns the admin role that controls `role`. See {grantRole} and {revokeRole}. To change a role's admin, use {_setRoleAdmin}.\"},\"getRoleMember(bytes32,uint256)\":{\"details\":\"Returns one of the accounts that have `role`. `index` must be a value between 0 and {getRoleMemberCount}, non-inclusive. Role bearers are not sorted in any particular way, and their ordering may change at any point. WARNING: When using {getRoleMember} and {getRoleMemberCount}, make sure you perform all queries on the same block. See the following https://forum.openzeppelin.com/t/iterating-over-elements-on-enumerableset-in-openzeppelin-contracts/2296[forum post] for more information.\"},\"getRoleMemberCount(bytes32)\":{\"details\":\"Returns the number of accounts that have `role`. Can be used together with {getRoleMember} to enumerate all bearers of a role.\"},\"getRoleMembers(bytes32)\":{\"details\":\"Return all accounts that have `role` WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that this function has an unbounded cost, and using it as part of a state-changing function may render the function uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\"},\"grantRole(bytes32,address)\":{\"details\":\"Grants `role` to `account`. If `account` had not been already granted `role`, emits a {RoleGranted} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleGranted} event.\"},\"hasRole(bytes32,address)\":{\"details\":\"Returns `true` if `account` has been granted `role`.\"},\"hasSupportedRole(bytes32)\":{\"params\":{\"role\":\"The bytes32 identifier of the role to check\"},\"returns\":{\"_0\":\"isActive True if the role has any members assigned\"}},\"renounceRole(bytes32,address)\":{\"details\":\"Revokes `role` from the calling account. Roles are often managed via {grantRole} and {revokeRole}: this function's purpose is to provide a mechanism for accounts to lose their privileges if they are compromised (such as when a trusted device is misplaced). If the calling account had been revoked `role`, emits a {RoleRevoked} event. Requirements: - the caller must be `callerConfirmation`. May emit a {RoleRevoked} event.\"},\"revokeRole(bytes32,address)\":{\"details\":\"Revokes `role` from `account`. If `account` had been granted `role`, emits a {RoleRevoked} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleRevoked} event.\"},\"supportedRoleAt(uint256)\":{\"params\":{\"index\":\"Index within the supported role set\"},\"returns\":{\"_0\":\"role The bytes32 identifier of the role\"}},\"supportsInterface(bytes4)\":{\"details\":\"See {IERC165-supportsInterface}.\"}},\"version\":1},\"userdoc\":{\"events\":{\"RoleAdded(bytes32)\":{\"notice\":\"Emitted when a new role is granted for the first time\"},\"RoleRemoved(bytes32)\":{\"notice\":\"Emitted when a role loses its last member\"}},\"kind\":\"user\",\"methods\":{\"hasSupportedRole(bytes32)\":{\"notice\":\"Checks whether a given role is currently active (i.e., has at least one member)\"},\"supportedRoleAt(uint256)\":{\"notice\":\"Returns the role at the specified index in the set of active roles\"},\"supportedRoles()\":{\"notice\":\"Returns the total number of unique roles that are currently assigned\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/permissions/protocols/ERC20Verifier.sol\":\"ERC20Verifier\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/contracts/access/AccessControlUpgradeable.sol\":{\"keccak256\":\"0x0d4d7d19a052a2ef9d17b28450133631188b895e1755747fa8ad0280aadfb534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://838551e10cc07e570b70aee1078e490658d3a0ab8f4adfd4f3f2a565200753da\",\"dweb:/ipfs/QmceT7R3Pqzywyxr3MSEA7VXU4axZAPS6BLy92iV97rGdG\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/access/extensions/AccessControlEnumerableUpgradeable.sol\":{\"keccak256\":\"0xaec38804089a16494f7d45ebbbeab78fe05e5dc4bc2eae1d2af458fe999fb43c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://04796408e2c4a1051afb23319e9505edb595a3d9c663ff5d6fcfb8e185ab1d93\",\"dweb:/ipfs/Qma8ZTi4kGyqg45SyNibbJSqMF7broiQnJPHhEGLcZyF9U\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/introspection/ERC165Upgradeable.sol\":{\"keccak256\":\"0xc8ed8d2056934b7675b695dec032f2920c2f5c6cf33a17ca85650940675323ab\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3c8ccc75d1cd792d192aa09e54dd49ea35fe85baa9fcd17486f29227d9f29b89\",\"dweb:/ipfs/QmbboSbFUEiM9tdEgBwuTRb7bykFoJXZ7dsSr1PSREJXMr\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0\",\"dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/extensions/IAccessControlEnumerable.sol\":{\"keccak256\":\"0xca774fbe0568762efdc1a7cba31f09549c7fa96dbe97410f4843fa2f0bc000a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0187ffdbf3d61b6d86cba4fcd9826e53d876987d620533ee84c681bdaf0f3ba3\",\"dweb:/ipfs/QmVJDqdJv6uzHY7ifncfv2QJep8XTzS3bGb4s5Exhuv86m\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://df6f0c459663c9858b6cba2cda1d14a7d05a985bed6d2de72bd8e78c25ee79db\",\"dweb:/ipfs/QmeTTxZ7qVk9rjEv2R4CpCwdf8UMCcRqDNMvzNxHc3Fnn9\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol\":{\"keccak256\":\"0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd\",\"dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol\":{\"keccak256\":\"0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd\",\"dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol\":{\"keccak256\":\"0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2\",\"dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621\",\"dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol\":{\"keccak256\":\"0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231\",\"dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y\"]},\"src/interfaces/permissions/ICustomVerifier.sol\":{\"keccak256\":\"0xdb343366ed86f63f8a94ff478c5c8e1d127888e4993c27fcb3bb3b51ae0cb8c7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://12407e89b20982a00961ccc03258e353962607747f214940d18b0989c11547c9\",\"dweb:/ipfs/Qmay19iWpwiV8LuvWccPjsazVU5Kkhme9QykFfLKfBmvSr\"]},\"src/interfaces/permissions/IMellowACL.sol\":{\"keccak256\":\"0xcc86b0ce4e4a53c1c4fa16ad79f9de0e2a1f1a77a1f256c0856120a9d6ea36df\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://6942df2ffadacae5563e01ec60697f16116362ba84a856a346b36bc7b9dd1ec2\",\"dweb:/ipfs/QmNYhqZGpS98UP89CS86CjoG4ZWbMHzTHRtd7bZzQqn3UY\"]},\"src/libraries/SlotLibrary.sol\":{\"keccak256\":\"0x35dcf16feec83c772add30250b0987ce29ccfbf308d04159701ebf9243444eeb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://97658d0c93ecc8174169e643d5a24c515b55905a2fcaaa6d6ec0188a3dd71a10\",\"dweb:/ipfs/QmS1z7ac8RbMvz15gVQ9YA1cCT5bs6KMHCiDhgcG8Pq8oK\"]},\"src/permissions/MellowACL.sol\":{\"keccak256\":\"0xeea276fd09714ef09b380fc9dfb8ae6639c9ff338b3e32cf3b824282a4a3a280\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c87b6a2c3e1c70772ceff8578785aa8e0daa76bccfb031ff2ecb72ccc7d2d0f2\",\"dweb:/ipfs/QmefXov36R68VpEaeafPJCWG89yMEWqe6UnKcWyX4vRsVH\"]},\"src/permissions/protocols/ERC20Verifier.sol\":{\"keccak256\":\"0x4170ea1e9535cc426f02bdd636104c8ddba6269ffa58aaf5d6d8819435eed33b\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://89356f67e69abe49548b18e0d43f8002329c8b166b8af92e37a33f0c4d1cc3ba\",\"dweb:/ipfs/Qma2EuRgGiaeThXAiLNU4cSXwKPL5EPgrSjESr6zuedefZ\"]},\"src/permissions/protocols/OwnedCustomVerifier.sol\":{\"keccak256\":\"0x85b3aa3ddb2f6807348bd11a48bcc5254238db149a6541c51ea2bb62831e0a67\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b9219f0b3e458bc1a9994ac0b69bc20a7619d9033d0b22bf923489957335a5a7\",\"dweb:/ipfs/QmUdQmv1QbJ5227N7Ytm8amwwzXzpwf7yCXMUZbXFxw2FA\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "uint256", "name": "version_", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "AccessControlBadConfirmation"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "type": "error", "name": "AccessControlUnauthorizedAccount"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [], "type": "error", "name": "ZeroValue"}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}], "type": "event", "name": "RoleAdded", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32", "indexed": true}, {"internalType": "bytes32", "name": "newAdminRole", "type": "bytes32", "indexed": true}], "type": "event", "name": "RoleAdminChanged", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": true}], "type": "event", "name": "RoleGranted", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}], "type": "event", "name": "RoleRemoved", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": true}], "type": "event", "name": "RoleRevoked", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "ASSET_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "CALLER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "RECIPIENT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getRoleMember", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "getRoleMemberCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "getRoleMembers", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "grantRole"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "hasSupportedRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "renounceRole"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "revokeRole"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "supportedRoleAt", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "supportedRoles", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "stateMutability": "view", "type": "function", "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function", "name": "verifyCall", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"getRoleAdmin(bytes32)": {"details": "Returns the admin role that controls `role`. See {grantRole} and {revokeRole}. To change a role's admin, use {_setRoleAdmin}."}, "getRoleMember(bytes32,uint256)": {"details": "Returns one of the accounts that have `role`. `index` must be a value between 0 and {getRoleMemberCount}, non-inclusive. Role bearers are not sorted in any particular way, and their ordering may change at any point. WARNING: When using {getRoleMember} and {getRoleMemberCount}, make sure you perform all queries on the same block. See the following https://forum.openzeppelin.com/t/iterating-over-elements-on-enumerableset-in-openzeppelin-contracts/2296[forum post] for more information."}, "getRoleMemberCount(bytes32)": {"details": "Returns the number of accounts that have `role`. Can be used together with {getRoleMember} to enumerate all bearers of a role."}, "getRoleMembers(bytes32)": {"details": "Return all accounts that have `role` WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that this function has an unbounded cost, and using it as part of a state-changing function may render the function uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block."}, "grantRole(bytes32,address)": {"details": "Grants `role` to `account`. If `account` had not been already granted `role`, emits a {RoleGranted} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleGranted} event."}, "hasRole(bytes32,address)": {"details": "Returns `true` if `account` has been granted `role`."}, "hasSupportedRole(bytes32)": {"params": {"role": "The bytes32 identifier of the role to check"}, "returns": {"_0": "isActive True if the role has any members assigned"}}, "renounceRole(bytes32,address)": {"details": "Revokes `role` from the calling account. Roles are often managed via {grantRole} and {revokeRole}: this function's purpose is to provide a mechanism for accounts to lose their privileges if they are compromised (such as when a trusted device is misplaced). If the calling account had been revoked `role`, emits a {RoleRevoked} event. Requirements: - the caller must be `callerConfirmation`. May emit a {RoleRevoked} event."}, "revokeRole(bytes32,address)": {"details": "Revokes `role` from `account`. If `account` had been granted `role`, emits a {RoleRevoked} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleRevoked} event."}, "supportedRoleAt(uint256)": {"params": {"index": "Index within the supported role set"}, "returns": {"_0": "role The bytes32 identifier of the role"}}, "supportsInterface(bytes4)": {"details": "See {IERC165-supportsInterface}."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"hasSupportedRole(bytes32)": {"notice": "Checks whether a given role is currently active (i.e., has at least one member)"}, "supportedRoleAt(uint256)": {"notice": "Returns the role at the specified index in the set of active roles"}, "supportedRoles()": {"notice": "Returns the total number of unique roles that are currently assigned"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/permissions/protocols/ERC20Verifier.sol": "ERC20Verifier"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/contracts/access/AccessControlUpgradeable.sol": {"keccak256": "0x0d4d7d19a052a2ef9d17b28450133631188b895e1755747fa8ad0280aadfb534", "urls": ["bzz-raw://838551e10cc07e570b70aee1078e490658d3a0ab8f4adfd4f3f2a565200753da", "dweb:/ipfs/QmceT7R3Pqzywyxr3MSEA7VXU4axZAPS6BLy92iV97rGdG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/access/extensions/AccessControlEnumerableUpgradeable.sol": {"keccak256": "0xaec38804089a16494f7d45ebbbeab78fe05e5dc4bc2eae1d2af458fe999fb43c", "urls": ["bzz-raw://04796408e2c4a1051afb23319e9505edb595a3d9c663ff5d6fcfb8e185ab1d93", "dweb:/ipfs/Qma8ZTi4kGyqg45SyNibbJSqMF7broiQnJPHhEGLcZyF9U"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05", "urls": ["bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08", "dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/introspection/ERC165Upgradeable.sol": {"keccak256": "0xc8ed8d2056934b7675b695dec032f2920c2f5c6cf33a17ca85650940675323ab", "urls": ["bzz-raw://3c8ccc75d1cd792d192aa09e54dd49ea35fe85baa9fcd17486f29227d9f29b89", "dweb:/ipfs/QmbboSbFUEiM9tdEgBwuTRb7bykFoJXZ7dsSr1PSREJXMr"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530", "urls": ["bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0", "dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/extensions/IAccessControlEnumerable.sol": {"keccak256": "0xca774fbe0568762efdc1a7cba31f09549c7fa96dbe97410f4843fa2f0bc000a3", "urls": ["bzz-raw://0187ffdbf3d61b6d86cba4fcd9826e53d876987d620533ee84c681bdaf0f3ba3", "dweb:/ipfs/QmVJDqdJv6uzHY7ifncfv2QJep8XTzS3bGb4s5Exhuv86m"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7", "urls": ["bzz-raw://df6f0c459663c9858b6cba2cda1d14a7d05a985bed6d2de72bd8e78c25ee79db", "dweb:/ipfs/QmeTTxZ7qVk9rjEv2R4CpCwdf8UMCcRqDNMvzNxHc3Fnn9"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol": {"keccak256": "0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6", "urls": ["bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd", "dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol": {"keccak256": "0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58", "urls": ["bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd", "dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol": {"keccak256": "0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26", "urls": ["bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2", "dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8", "urls": ["bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621", "dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol": {"keccak256": "0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06", "urls": ["bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231", "dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y"], "license": "MIT"}, "src/interfaces/permissions/ICustomVerifier.sol": {"keccak256": "0xdb343366ed86f63f8a94ff478c5c8e1d127888e4993c27fcb3bb3b51ae0cb8c7", "urls": ["bzz-raw://12407e89b20982a00961ccc03258e353962607747f214940d18b0989c11547c9", "dweb:/ipfs/Qmay19iWpwiV8LuvWccPjsazVU5Kkhme9QykFfLKfBmvSr"], "license": "BUSL-1.1"}, "src/interfaces/permissions/IMellowACL.sol": {"keccak256": "0xcc86b0ce4e4a53c1c4fa16ad79f9de0e2a1f1a77a1f256c0856120a9d6ea36df", "urls": ["bzz-raw://6942df2ffadacae5563e01ec60697f16116362ba84a856a346b36bc7b9dd1ec2", "dweb:/ipfs/QmNYhqZGpS98UP89CS86CjoG4ZWbMHzTHRtd7bZzQqn3UY"], "license": "BUSL-1.1"}, "src/libraries/SlotLibrary.sol": {"keccak256": "0x35dcf16feec83c772add30250b0987ce29ccfbf308d04159701ebf9243444eeb", "urls": ["bzz-raw://97658d0c93ecc8174169e643d5a24c515b55905a2fcaaa6d6ec0188a3dd71a10", "dweb:/ipfs/QmS1z7ac8RbMvz15gVQ9YA1cCT5bs6KMHCiDhgcG8Pq8oK"], "license": "BUSL-1.1"}, "src/permissions/MellowACL.sol": {"keccak256": "0xeea276fd09714ef09b380fc9dfb8ae6639c9ff338b3e32cf3b824282a4a3a280", "urls": ["bzz-raw://c87b6a2c3e1c70772ceff8578785aa8e0daa76bccfb031ff2ecb72ccc7d2d0f2", "dweb:/ipfs/QmefXov36R68VpEaeafPJCWG89yMEWqe6UnKcWyX4vRsVH"], "license": "BUSL-1.1"}, "src/permissions/protocols/ERC20Verifier.sol": {"keccak256": "0x4170ea1e9535cc426f02bdd636104c8ddba6269ffa58aaf5d6d8819435eed33b", "urls": ["bzz-raw://89356f67e69abe49548b18e0d43f8002329c8b166b8af92e37a33f0c4d1cc3ba", "dweb:/ipfs/Qma2EuRgGiaeThXAiLNU4cSXwKPL5EPgrSjESr6zuedefZ"], "license": "BUSL-1.1"}, "src/permissions/protocols/OwnedCustomVerifier.sol": {"keccak256": "0x85b3aa3ddb2f6807348bd11a48bcc5254238db149a6541c51ea2bb62831e0a67", "urls": ["bzz-raw://b9219f0b3e458bc1a9994ac0b69bc20a7619d9033d0b22bf923489957335a5a7", "dweb:/ipfs/QmUdQmv1QbJ5227N7Ytm8amwwzXzpwf7yCXMUZbXFxw2FA"], "license": "BUSL-1.1"}}, "version": 1}, "id": 132}