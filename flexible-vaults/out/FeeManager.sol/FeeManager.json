{"abi": [{"type": "constructor", "inputs": [{"name": "name_", "type": "string", "internalType": "string"}, {"name": "version_", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "baseAsset", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "calculateDepositFee", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "calculateFee", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "asset", "type": "address", "internalType": "address"}, {"name": "priceD18", "type": "uint256", "internalType": "uint256"}, {"name": "totalShares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "calculateRedeemFee", "inputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "depositFeeD6", "inputs": [], "outputs": [{"name": "", "type": "uint24", "internalType": "uint24"}], "stateMutability": "view"}, {"type": "function", "name": "feeRecipient", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "minPriceD18", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "performanceFeeD6", "inputs": [], "outputs": [{"name": "", "type": "uint24", "internalType": "uint24"}], "stateMutability": "view"}, {"type": "function", "name": "protocolFeeD6", "inputs": [], "outputs": [{"name": "", "type": "uint24", "internalType": "uint24"}], "stateMutability": "view"}, {"type": "function", "name": "redeemFeeD6", "inputs": [], "outputs": [{"name": "", "type": "uint24", "internalType": "uint24"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setBaseAsset", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "baseAsset_", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setFeeRecipient", "inputs": [{"name": "feeRecipient_", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setFees", "inputs": [{"name": "depositFeeD6_", "type": "uint24", "internalType": "uint24"}, {"name": "redeemFeeD6_", "type": "uint24", "internalType": "uint24"}, {"name": "performanceFeeD6_", "type": "uint24", "internalType": "uint24"}, {"name": "protocolFeeD6_", "type": "uint24", "internalType": "uint24"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "timestamps", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateState", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}, {"name": "priceD18", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "initParams", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetBaseAsset", "inputs": [{"name": "vault", "type": "address", "indexed": true, "internalType": "address"}, {"name": "baseAsset", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetFeeRecipient", "inputs": [{"name": "feeRecipient", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetFees", "inputs": [{"name": "depositFeeD6", "type": "uint24", "indexed": false, "internalType": "uint24"}, {"name": "redeemFeeD6", "type": "uint24", "indexed": false, "internalType": "uint24"}, {"name": "performanceFeeD6", "type": "uint24", "indexed": false, "internalType": "uint24"}, {"name": "protocolFeeD6", "type": "uint24", "indexed": false, "internalType": "uint24"}], "anonymous": false}, {"type": "event", "name": "UpdateState", "inputs": [{"name": "vault", "type": "address", "indexed": true, "internalType": "address"}, {"name": "asset", "type": "address", "indexed": true, "internalType": "address"}, {"name": "priceD18", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "BaseAssetAlreadySet", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "baseAsset", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "InvalidFees", "inputs": [{"name": "depositFeeD6", "type": "uint24", "internalType": "uint24"}, {"name": "redeemFeeD6", "type": "uint24", "internalType": "uint24"}, {"name": "performanceFeeD6", "type": "uint24", "internalType": "uint24"}, {"name": "protocolFeeD6", "type": "uint24", "internalType": "uint24"}]}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ZeroAddress", "inputs": []}], "bytecode": {"object": "0x60a060405234801561000f575f80fd5b5060405161144838038061144883398101604081905261002e916101b8565b60408051808201909152600a8152692332b2a6b0b730b3b2b960b11b602082015261005a90838361006c565b6080526100656100dd565b50506102ea565b5f60ff5f1b19600185858560405160200161008993929190610283565b604051602081830303815290604052805190602001205f1c6100ab91906102cb565b6040516020016100bd91815260200190565b604051602081830303815290604052805190602001201690509392505050565b5f6100e661017a565b805490915068010000000000000000900460ff16156101185760405163f92ee8a960e01b815260040160405180910390fd5b80546001600160401b03908116146101775780546001600160401b0319166001600160401b0390811782556040519081527fc7f505b2f371ae2175ee4913f4499e1f2633a7b5936321eed1cdaeb6115181d29060200160405180910390a15b50565b5f807ff0c57e16840df040f15088dc2f81fe391c3923bec73e23a9662efc9c229c6a005b92915050565b634e487b7160e01b5f52604160045260245ffd5b5f80604083850312156101c9575f80fd5b82516001600160401b03808211156101df575f80fd5b818501915085601f8301126101f2575f80fd5b815181811115610204576102046101a4565b604051601f8201601f19908116603f0116810190838211818310171561022c5761022c6101a4565b81604052828152886020848701011115610244575f80fd5b8260208601602083015e5f602084830101528096505050505050602083015190509250929050565b5f81518060208401855e5f93019283525090919050565b7f6d656c6c6f772e666c657869626c652d7661756c74732e73746f726167652e0081525f6102bd6102b7601f84018761026c565b8561026c565b928352505060200192915050565b8181038181111561019e57634e487b7160e01b5f52601160045260245ffd5b6080516110eb61035d5f395f8181610141015281816101d501528181610233015281816102c30152818161030301528181610351015281816103b4015281816104380152818161052401528181610720015281816108130152818161085301528181610a750152610b4401526110eb5ff3fe608060405234801561000f575f80fd5b506004361061011c575f3560e01c80638da5cb5b116100a9578063e0e193a21161006e578063e0e193a21461033c578063e3707d181461034f578063e74b981b14610382578063e9c98e6814610395578063f2fde38b146103df575f80fd5b80638da5cb5b14610274578063980679e2146102a45780639ae26062146102ee578063a51fac2214610301578063bf9587fb14610334575f80fd5b806346904840116100ef57806346904840146101d357806346f6cd2714610203578063715018a6146102165780637f0c60101461021e5780638005c6de14610231575f80fd5b806319b3ebea146101205780631ded75b41461018a578063247d284b1461019f578063439fab91146101c0575b5f80fd5b61016d61012e366004610dc2565b6001600160a01b039081165f90815260037f00000000000000000000000000000000000000000000000000000000000000000160205260409020541690565b6040516001600160a01b0390911681526020015b60405180910390f35b61019d610198366004610ddd565b6103f2565b005b6101b26101ad366004610e14565b61051a565b604051908152602001610181565b61019d6101ce366004610e2b565b610573565b7f0000000000000000000000000000000000000000000000000000000000000000546001600160a01b031661016d565b61019d610211366004610eae565b6106df565b61019d6106f9565b6101b261022c366004610e14565b61070c565b7f000000000000000000000000000000000000000000000000000000000000000054600160e81b900462ffffff165b60405162ffffff9091168152602001610181565b7f9016d09d72d40fdae2fd8ceac6b6234c7706214fd39c1cd1e609a0528c199300546001600160a01b031661016d565b6101b26102b2366004610dc2565b6001600160a01b03165f90815260027f000000000000000000000000000000000000000000000000000000000000000001602052604090205490565b61019d6102fc366004610eff565b610719565b7f000000000000000000000000000000000000000000000000000000000000000054600160d01b900462ffffff16610260565b610260610811565b6101b261034a366004610f29565b610842565b7f000000000000000000000000000000000000000000000000000000000000000054600160a01b900462ffffff16610260565b61019d610390366004610dc2565b61096a565b6101b26103a3366004610dc2565b6001600160a01b03165f90815260017f000000000000000000000000000000000000000000000000000000000000000001602052604090205490565b61019d6103ed366004610dc2565b61097e565b6103fa6109b8565b6001600160a01b038216158061041757506001600160a01b038116155b156104355760405163d92e233d60e01b815260040160405180910390fd5b5f7f00000000000000000000000000000000000000000000000000000000000000006001600160a01b038481165f90815260038301602052604090205491925016156104c1576001600160a01b038381165f81815260038401602052604090819020549051635e7b423360e11b8152600481019290925290911660248201526044015b60405180910390fd5b6001600160a01b038381165f81815260038401602052604080822080546001600160a01b0319169487169485179055517f3b2050d40f5dc88f766d8678058178aab19aa9c73a67800b54628ff5b3bd15f69190a3505050565b5f620f42406105547f00000000000000000000000000000000000000000000000000000000000000005462ffffff600160a01b9091041690565b6105639062ffffff1684610f80565b61056d9190610fab565b92915050565b5f61057c610a13565b805490915060ff600160401b820416159067ffffffffffffffff165f811580156105a35750825b90505f8267ffffffffffffffff1660011480156105bf5750303b155b9050811580156105cd575080155b156105eb5760405163f92ee8a960e01b815260040160405180910390fd5b845467ffffffffffffffff19166001178555831561061557845460ff60401b1916600160401b1785555b5f80808080806106278c8e018e610fca565b95509550955095509550955061063c86610a3b565b61064585610a4c565b61065184848484610adb565b7f5e399709a9ff1709f6f6be7268c8e5c3eeaa9da9cd9797e78f07ef287c3717fe8d8d60405161068292919061103e565b60405180910390a150505050505083156106d657845460ff60401b19168555604051600181527fc7f505b2f371ae2175ee4913f4499e1f2633a7b5936321eed1cdaeb6115181d29060200160405180910390a15b50505050505050565b6106e76109b8565b6106f384848484610adb565b50505050565b6107016109b8565b61070a5f610c1a565b565b5f620f4240610554610811565b335f8181527f0000000000000000000000000000000000000000000000000000000000000000600381016020526040909120549091906001600160a01b038581169116146107675750505050565b6001600160a01b0381165f90815260028301602052604090205480158061078d57508381115b156107af576001600160a01b0382165f90815260028401602052604090208490555b6001600160a01b038083165f8181526001860160205260409081902042905551918716917f460d73b7c22bbbab34011f9b96d35654d7648b64131f49c795324b961eeb3925906108029088815260200190565b60405180910390a35050505050565b7f000000000000000000000000000000000000000000000000000000000000000054600160b81b900462ffffff1690565b6001600160a01b038481165f9081527f0000000000000000000000000000000000000000000000000000000000000000600381016020526040822054919290918116908616036108f4576001600160a01b0386165f90815260028201602052604090205480851080156108b457508015155b156108f2576108ef6108c6868361106c565b83546108df908790600160d01b900462ffffff16610f80565b69d3c21bcecceda1000000610c8a565b92505b505b6001600160a01b0386165f908152600182016020526040902054801580159061091c57508042115b15610960576109538461092f834261106c565b84546109479190600160e81b900462ffffff16610f80565b651cae8c13e000610c8a565b61095d908461107f565b92505b5050949350505050565b6109726109b8565b61097b81610a4c565b50565b6109866109b8565b6001600160a01b0381166109af57604051631e4fbdf760e01b81525f60048201526024016104b8565b61097b81610c1a565b336109ea7f9016d09d72d40fdae2fd8ceac6b6234c7706214fd39c1cd1e609a0528c199300546001600160a01b031690565b6001600160a01b03161461070a5760405163118cdaa760e01b81523360048201526024016104b8565b5f807ff0c57e16840df040f15088dc2f81fe391c3923bec73e23a9662efc9c229c6a0061056d565b610a43610d3b565b61097b81610d60565b6001600160a01b038116610a735760405163d92e233d60e01b815260040160405180910390fd5b7f000000000000000000000000000000000000000000000000000000000000000080546001600160a01b0319166001600160a01b03831690811782556040517f2e979f80fe4d43055c584cf4a8467c55875ea36728fc37176c05acd784eb7a73905f90a25050565b620f42408183610aeb8688611092565b610af59190611092565b610aff9190611092565b62ffffff161115610b4257604051630e9b677f60e11b815262ffffff808616600483015280851660248301528084166044830152821660648201526084016104b8565b7f0000000000000000000000000000000000000000000000000000000000000000805462ffffff838116600160e81b81026001600160e81b03878416600160d01b8102919091166001600160d01b038a8616600160b81b810262ffffff60b81b19978e16600160a01b81029890981665ffffffffffff60a01b199099169890981797909717161791909117855560408051938452602084019490945282840152606082015290517f81dd8e63f2d9b91e56d22c805329f99fda578f10e2fb1e84b7096f2b8ee4ea4c9181900360800190a15050505050565b7f9016d09d72d40fdae2fd8ceac6b6234c7706214fd39c1cd1e609a0528c19930080546001600160a01b031981166001600160a01b03848116918217845560405192169182907f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0905f90a3505050565b5f805f610c978686610d68565b91509150815f03610cbb57838181610cb157610cb1610f97565b0492505050610d34565b818411610cd257610cd26003851502601118610d84565b5f848688095f868103871696879004966002600389028118808a02820302808a02820302808a02820302808a02820302808a02820302808a02909103029181900381900460010185841190960395909502919093039390930492909217029150505b9392505050565b610d43610d95565b61070a57604051631afcd79f60e31b815260040160405180910390fd5b610986610d3b565b5f805f1983850993909202808410938190039390930393915050565b634e487b715f52806020526024601cfd5b5f610d9e610a13565b54600160401b900460ff16919050565b6001600160a01b038116811461097b575f80fd5b5f60208284031215610dd2575f80fd5b8135610d3481610dae565b5f8060408385031215610dee575f80fd5b8235610df981610dae565b91506020830135610e0981610dae565b809150509250929050565b5f60208284031215610e24575f80fd5b5035919050565b5f8060208385031215610e3c575f80fd5b823567ffffffffffffffff80821115610e53575f80fd5b818501915085601f830112610e66575f80fd5b813581811115610e74575f80fd5b866020828501011115610e85575f80fd5b60209290920196919550909350505050565b803562ffffff81168114610ea9575f80fd5b919050565b5f805f8060808587031215610ec1575f80fd5b610eca85610e97565b9350610ed860208601610e97565b9250610ee660408601610e97565b9150610ef460608601610e97565b905092959194509250565b5f8060408385031215610f10575f80fd5b8235610f1b81610dae565b946020939093013593505050565b5f805f8060808587031215610f3c575f80fd5b8435610f4781610dae565b93506020850135610f5781610dae565b93969395505050506040820135916060013590565b634e487b7160e01b5f52601160045260245ffd5b808202811582820484141761056d5761056d610f6c565b634e487b7160e01b5f52601260045260245ffd5b5f82610fc557634e487b7160e01b5f52601260045260245ffd5b500490565b5f805f805f8060c08789031215610fdf575f80fd5b8635610fea81610dae565b95506020870135610ffa81610dae565b945061100860408801610e97565b935061101660608801610e97565b925061102460808801610e97565b915061103260a08801610e97565b90509295509295509295565b60208152816020820152818360408301375f818301604090810191909152601f909201601f19160101919050565b8181038181111561056d5761056d610f6c565b8082018082111561056d5761056d610f6c565b62ffffff8181168382160190808211156110ae576110ae610f6c565b509291505056fea2646970667358221220cbb29852bb67d686467aa46b5d23b8c027f658aeb381216108a0e6e450775d9164736f6c63430008190033", "sourceMap": "152:5943:116:-:0;;;268:175;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;354:50;;;;;;;;;;;;-1:-1:-1;;;354:50:116;;;;;;388:5;395:8;354:19;:50::i;:::-;329:75;;414:22;:20;:22::i;:::-;268:175;;152:5943;;656:343:113;761:7;986:4;970:22;;969:23;941:1;908:12;922:4;928:7;856:80;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;846:91;;;;;;838:100;;:104;;;;:::i;:::-;810:146;;;;;;2258:25:192;;2246:2;2231:18;;2112:177;810:146:113;;;;;;;;;;;;;787:179;;;;;;:205;780:212;;656:343;;;;;:::o;7709:422:3:-;7824:30;7857:26;:24;:26::i;:::-;7898:15;;;;-1:-1:-1;7898:15:3;;;;;7894:76;;;7936:23;;-1:-1:-1;;;7936:23:3;;;;;;;;;;;7894:76;7983:14;;-1:-1:-1;;;;;7983:14:3;;;:34;7979:146;;8033:33;;-1:-1:-1;;;;;;8033:33:3;-1:-1:-1;;;;;8033:33:3;;;;;8085:29;;2438:50:192;;;8085:29:3;;2426:2:192;2411:18;8085:29:3;;;;;;;7979:146;7758:373;7709:422::o;9071:205::-;9129:30;;3147:66;9186:27;9171:42;9071:205;-1:-1:-1;;9071:205:3:o;14:127:192:-;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:983;235:6;243;296:2;284:9;275:7;271:23;267:32;264:52;;;312:1;309;302:12;264:52;339:16;;-1:-1:-1;;;;;404:14:192;;;401:34;;;431:1;428;421:12;401:34;469:6;458:9;454:22;444:32;;514:7;507:4;503:2;499:13;495:27;485:55;;536:1;533;526:12;485:55;565:2;559:9;587:2;583;580:10;577:36;;;593:18;;:::i;:::-;668:2;662:9;636:2;722:13;;-1:-1:-1;;718:22:192;;;742:2;714:31;710:40;698:53;;;766:18;;;786:22;;;763:46;760:72;;;812:18;;:::i;:::-;852:10;848:2;841:22;887:2;879:6;872:18;929:7;922:4;917:2;913;909:11;905:22;902:35;899:55;;;950:1;947;940:12;899:55;1003:2;996:4;992:2;988:13;981:4;973:6;969:17;963:43;1050:1;1043:4;1038:2;1030:6;1026:15;1022:26;1015:37;1071:6;1061:16;;;;;;;1117:4;1106:9;1102:20;1096:27;1086:37;;146:983;;;;;:::o;1134:212::-;1176:3;1214:5;1208:12;1258:6;1251:4;1244:5;1240:16;1235:3;1229:36;1320:1;1284:16;;1309:13;;;-1:-1:-1;1284:16:192;;1134:212;-1:-1:-1;1134:212:192:o;1351:526::-;1689:33;1684:3;1677:46;1659:3;1745:66;1771:39;1806:2;1801:3;1797:12;1789:6;1771:39;:::i;:::-;1763:6;1745:66;:::i;:::-;1820:21;;;-1:-1:-1;;1868:2:192;1857:14;;1351:526;-1:-1:-1;;1351:526:192:o;1882:225::-;1949:9;;;1970:11;;;1967:134;;;2023:10;2018:3;2014:20;2011:1;2004:31;2058:4;2055:1;2048:15;2086:4;2083:1;2076:15;2294:200;152:5943:116;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "152:5943:116:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1585:125;;;;;;:::i;:::-;-1:-1:-1;;;;;1666:37:116;;;1640:7;1666:37;;;:30;6008:22;1666:30;:37;;;;;;;;1585:125;;;;-1:-1:-1;;;;;566:32:192;;;548:51;;536:2;521:18;1585:125:116;;;;;;;;3317:462;;;;;;:::i;:::-;;:::i;:::-;;1748:130;;;;;;:::i;:::-;;:::i;:::-;;;1334:25:192;;;1322:2;1307:18;1748:130:116;1188:177:192;4364:550:116;;;;;;:::i;:::-;;:::i;504:111::-;6008:22;575:33;-1:-1:-1;;;;;575:33:116;504:111;;3042:237;;;;;;:::i;:::-;;:::i;3155:101:1:-;;;:::i;1916:128:116:-;;;;;;:::i;:::-;;:::i;1103:112::-;6008:22;1174:34;-1:-1:-1;;;1174:34:116;;;;1103:112;;;2712:8:192;2700:21;;;2682:40;;2670:2;2655:18;1103:112:116;2538:190:192;2441:144:1;1313:22;2570:8;-1:-1:-1;;;;;2570:8:1;2441:144;;1418:129:116;;;;;;:::i;:::-;-1:-1:-1;;;;;1501:39:116;1475:7;1501:39;;;:32;6008:22;1501:32;:39;;;;;;;1418:129;3817:506;;;;;;:::i;:::-;;:::i;947:118::-;6008:22;1021:37;-1:-1:-1;;;1021:37:116;;;;947:118;;801:108;;;:::i;2082:743::-;;;;;;:::i;:::-;;:::i;653:110::-;6008:22;723:33;-1:-1:-1;;;723:33:116;;;;653:110;;2889:115;;;;;;:::i;:::-;;:::i;1253:127::-;;;;;;:::i;:::-;-1:-1:-1;;;;;1335:38:116;1309:7;1335:38;;;:31;6008:22;1335:31;:38;;;;;;;1253:127;3405:215:1;;;;;;:::i;:::-;;:::i;3317:462:116:-;2334:13:1;:11;:13::i;:::-;-1:-1:-1;;;;;3407:19:116;::::1;::::0;;:47:::1;;-1:-1:-1::0;;;;;;3430:24:116;::::1;::::0;3407:47:::1;3403:98;;;3477:13;;-1:-1:-1::0;;;3477:13:116::1;;;;;;;;;;;3403:98;3510:27;6008:22:::0;-1:-1:-1;;;;;3574:18:116;;::::1;3604:1;3574:18:::0;;;:11:::1;::::0;::::1;:18;::::0;;;;;3510:50;;-1:-1:-1;3574:18:116::1;:32:::0;3570:116:::1;;-1:-1:-1::0;;;;;3656:18:116;;::::1;;::::0;;;:11:::1;::::0;::::1;:18;::::0;;;;;;;3629:46;;-1:-1:-1;;;3629:46:116;;::::1;::::0;::::1;3795:34:192::0;;;;3656:18:116;;::::1;3845::192::0;;;3838:43;3730:18;;3629:46:116::1;;;;;;;;3570:116;-1:-1:-1::0;;;;;3695:18:116;;::::1;;::::0;;;:11:::1;::::0;::::1;:18;::::0;;;;;:31;;-1:-1:-1;;;;;;3695:31:116::1;::::0;;::::1;::::0;;::::1;::::0;;3741;::::1;::::0;3695:18;3741:31:::1;3393:386;3317:462:::0;;:::o;1748:130::-;1814:7;1868:3;1850:14;6008:22;723:33;;-1:-1:-1;;;723:33:116;;;;;653:110;1850:14;1841:23;;;;:6;:23;:::i;:::-;1840:31;;;;:::i;:::-;1833:38;1748:130;-1:-1:-1;;1748:130:116:o;4364:550::-;4158:30:3;4191:26;:24;:26::i;:::-;4302:15;;4158:59;;-1:-1:-1;4302:15:3;-1:-1:-1;;;4302:15:3;;;4301:16;;4348:14;;4279:19;4724:16;;:34;;;;;4744:14;4724:34;4704:54;;4768:17;4788:11;:16;;4803:1;4788:16;:50;;;;-1:-1:-1;4816:4:3;4808:25;:30;4788:50;4768:70;;4854:12;4853:13;:30;;;;;4871:12;4870:13;4853:30;4849:91;;;4906:23;;-1:-1:-1;;;4906:23:3;;;;;;;;;;;4849:91;4949:18;;-1:-1:-1;;4949:18:3;4966:1;4949:18;;;4977:67;;;;5011:22;;-1:-1:-1;;;;5011:22:3;-1:-1:-1;;;5011:22:3;;;4977:67;4450:14:116::1;::::0;;;;;4652:68:::1;::::0;;::::1;4663:4:::0;4652:68:::1;:::i;:::-;4436:284;;;;;;;;;;;;4730:22;4745:6;4730:14;:22::i;:::-;4762:31;4779:13;4762:16;:31::i;:::-;4803:72;4812:13;4827:12;4841:17;4860:14;4803:8;:72::i;:::-;4890:17;4902:4;;4890:17;;;;;;;:::i;:::-;;;;;;;;4426:488;;;;;;5068:14:3::0;5064:101;;;5098:23;;-1:-1:-1;;;;5098:23:3;;;5140:14;;-1:-1:-1;5797:50:192;;5140:14:3;;5785:2:192;5770:18;5140:14:3;;;;;;;5064:101;4092:1079;;;;;4364:550:116;;:::o;3042:237::-;2334:13:1;:11;:13::i;:::-;3200:72:116::1;3209:13;3224:12;3238:17;3257:14;3200:8;:72::i;:::-;3042:237:::0;;;;:::o;3155:101:1:-;2334:13;:11;:13::i;:::-;3219:30:::1;3246:1;3219:18;:30::i;:::-;3155:101::o:0;1916:128:116:-;1981:7;2034:3;2017:13;:11;:13::i;3817:506::-;966:10:5;3890:27:116;3992:18;;;6008:22;3992:11;;;:18;;;;;;;6008:22;;966:10:5;-1:-1:-1;;;;;3992:27:116;;;:18;;:27;3988:64;;4035:7;;3817:506;;:::o;3988:64::-;-1:-1:-1;;;;;4084:20:116;;4061;4084;;;:13;;;:20;;;;;;4118:17;;;:44;;;4154:8;4139:12;:23;4118:44;4114:106;;;-1:-1:-1;;;;;4178:20:116;;;;;;:13;;;:20;;;;;:31;;;4114:106;-1:-1:-1;;;;;4229:19:116;;;;;;;:12;;;:19;;;;;;;4251:15;4229:37;;4281:35;;;;;;;;;4307:8;1334:25:192;;1322:2;1307:18;;1188:177;4281:35:116;;;;;;;;3880:443;;;3817:506;;:::o;801:108::-;6008:22;870:32;-1:-1:-1;;;870:32:116;;;;;801:108::o;2082:743::-;-1:-1:-1;;;;;2321:18:116;;;2218:14;2321:18;;;6008:22;2321:11;;;:18;;;;;;2218:14;;6008:22;;2321:18;;2312:27;;;;2308:282;;-1:-1:-1;;;;;2378:20:116;;2355;2378;;;:13;;;:20;;;;;;2416:23;;;:44;;;;-1:-1:-1;2443:17:116;;;2416:44;2412:168;;;2489:76;2501:23;2516:8;2501:12;:23;:::i;:::-;2526:18;;:32;;2547:11;;-1:-1:-1;;;2526:18:116;;;;:32;:::i;:::-;2560:4;2489:11;:76::i;:::-;2480:85;;2412:168;2341:249;2308:282;-1:-1:-1;;;;;2619:19:116;;2599:17;2619:19;;;:12;;;:19;;;;;;2652:14;;;;;:45;;;2688:9;2670:15;:27;2652:45;2648:171;;;2723:85;2735:11;2767:27;2785:9;2767:15;:27;:::i;:::-;2748:15;;:47;;;-1:-1:-1;;;2748:15:116;;;;:47;:::i;:::-;2797:10;2723:11;:85::i;:::-;2713:95;;;;:::i;:::-;;;2648:171;2238:587;;2082:743;;;;;;:::o;2889:115::-;2334:13:1;:11;:13::i;:::-;2966:31:116::1;2983:13;2966:16;:31::i;:::-;2889:115:::0;:::o;3405:215:1:-;2334:13;:11;:13::i;:::-;-1:-1:-1;;;;;3489:22:1;::::1;3485:91;;3534:31;::::0;-1:-1:-1;;;3534:31:1;;3562:1:::1;3534:31;::::0;::::1;548:51:192::0;521:18;;3534:31:1::1;402:203:192::0;3485:91:1::1;3585:28;3604:8;3585:18;:28::i;2658:162::-:0;966:10:5;2717:7:1;1313:22;2570:8;-1:-1:-1;;;;;2570:8:1;;2441:144;2717:7;-1:-1:-1;;;;;2717:23:1;;2713:101;;2763:40;;-1:-1:-1;;;2763:40:1;;966:10:5;2763:40:1;;;548:51:192;521:18;;2763:40:1;402:203:192;9071:205:3;9129:30;;3147:66;9186:27;8819:122;1847:127:1;6929:20:3;:18;:20::i;:::-;1929:38:1::1;1954:12;1929:24;:38::i;4947:297:116:-:0;-1:-1:-1;;;;;5019:27:116;;5015:78;;5069:13;;-1:-1:-1;;;5069:13:116;;;;;;;;;;;5015:78;6008:22;5162:30;;-1:-1:-1;;;;;;5162:30:116;-1:-1:-1;;;;;5162:30:116;;;;;;;5207;;;;-1:-1:-1;;5207:30:116;5005:239;4947:297;:::o;5250:645::-;5463:3;5446:14;5426:17;5395:28;5411:12;5395:13;:28;:::i;:::-;:48;;;;:::i;:::-;:65;;;;:::i;:::-;:71;;;5391:184;;;5489:75;;-1:-1:-1;;;5489:75:116;;6529:8:192;6564:15;;;5489:75:116;;;6546:34:192;6616:15;;;6596:18;;;6589:43;6668:15;;;6648:18;;;6641:43;6720:15;;6700:18;;;6693:43;6491:19;;5489:75:116;6296:446:192;5391:184:116;6008:22;5644:30;;;5770:32;;;-1:-1:-1;;;5770:32:116;;-1:-1:-1;;;;;5722:38:116;;;-1:-1:-1;;;5722:38:116;;5770:32;;;;-1:-1:-1;;;;;5684:28:116;;;-1:-1:-1;;;5684:28:116;;-1:-1:-1;;;;5644:30:116;;;-1:-1:-1;;;5644:30:116;;5684:28;;;;-1:-1:-1;;;;5684:28:116;;;;;;;;;;;5770:32;;;;;;;;5817:71;;;6546:34:192;;;6611:2;6596:18;;6589:43;;;;6648:18;;;6641:43;6715:2;6700:18;;6693:43;5817:71:116;;;;;;;6506:3:192;5817:71:116;;;5381:514;5250:645;;;;:::o;3774:248:1:-;1313:22;3923:8;;-1:-1:-1;;;;;;3941:19:1;;-1:-1:-1;;;;;3941:19:1;;;;;;;;3975:40;;3923:8;;;;;3975:40;;3847:24;;3975:40;3837:185;;3774:248;:::o;7242:3683:67:-;7324:14;7375:12;7389:11;7404:12;7411:1;7414;7404:6;:12::i;:::-;7374:42;;;;7498:4;7506:1;7498:9;7494:365;;7833:11;7827:3;:17;;;;;:::i;:::-;;7820:24;;;;;;7494:365;7984:4;7969:11;:19;7965:142;;8008:84;5312:5;8028:16;;5311:36;940:4:58;5306:42:67;8008:11;:84::i;:::-;8359:17;8510:11;8507:1;8504;8497:25;8902:12;8932:15;;;8917:31;;9067:22;;;;;9800:1;9781;:15;;9780:21;;10033;;;10029:25;;10018:36;10103:21;;;10099:25;;10088:36;10175:21;;;10171:25;;10160:36;10246:21;;;10242:25;;10231:36;10319:21;;;10315:25;;10304:36;10393:21;;;10389:25;;;10378:36;9309:12;;;;9305:23;;;9330:1;9301:31;8622:18;;;8612:29;;;9416:11;;;;8665:19;;;;9160:14;;;;9409:18;;;;10868:13;;-1:-1:-1;;7242:3683:67;;;;;;:::o;7082:141:3:-;7149:17;:15;:17::i;:::-;7144:73;;7189:17;;-1:-1:-1;;;7189:17:3;;;;;;;;;;;1980:235:1;6929:20:3;:18;:20::i;1027:550:67:-;1088:12;;-1:-1:-1;;1471:1:67;1468;1461:20;1501:9;;;;1549:11;;;1535:12;;;;1531:30;;;;;1027:550;-1:-1:-1;;1027:550:67:o;1776:194:58:-;1881:10;1875:4;1868:24;1918:4;1912;1905:18;1949:4;1943;1936:18;8485:120:3;8535:4;8558:26;:24;:26::i;:::-;:40;-1:-1:-1;;;8558:40:3;;;;;;-1:-1:-1;8485:120:3:o;14:131:192:-;-1:-1:-1;;;;;89:31:192;;79:42;;69:70;;135:1;132;125:12;150:247;209:6;262:2;250:9;241:7;237:23;233:32;230:52;;;278:1;275;268:12;230:52;317:9;304:23;336:31;361:5;336:31;:::i;610:388::-;678:6;686;739:2;727:9;718:7;714:23;710:32;707:52;;;755:1;752;745:12;707:52;794:9;781:23;813:31;838:5;813:31;:::i;:::-;863:5;-1:-1:-1;920:2:192;905:18;;892:32;933:33;892:32;933:33;:::i;:::-;985:7;975:17;;;610:388;;;;;:::o;1003:180::-;1062:6;1115:2;1103:9;1094:7;1090:23;1086:32;1083:52;;;1131:1;1128;1121:12;1083:52;-1:-1:-1;1154:23:192;;1003:180;-1:-1:-1;1003:180:192:o;1370:591::-;1440:6;1448;1501:2;1489:9;1480:7;1476:23;1472:32;1469:52;;;1517:1;1514;1507:12;1469:52;1557:9;1544:23;1586:18;1627:2;1619:6;1616:14;1613:34;;;1643:1;1640;1633:12;1613:34;1681:6;1670:9;1666:22;1656:32;;1726:7;1719:4;1715:2;1711:13;1707:27;1697:55;;1748:1;1745;1738:12;1697:55;1788:2;1775:16;1814:2;1806:6;1803:14;1800:34;;;1830:1;1827;1820:12;1800:34;1875:7;1870:2;1861:6;1857:2;1853:15;1849:24;1846:37;1843:57;;;1896:1;1893;1886:12;1843:57;1927:2;1919:11;;;;;1949:6;;-1:-1:-1;1370:591:192;;-1:-1:-1;;;;1370:591:192:o;1966:161::-;2033:20;;2093:8;2082:20;;2072:31;;2062:59;;2117:1;2114;2107:12;2062:59;1966:161;;;:::o;2132:401::-;2214:6;2222;2230;2238;2291:3;2279:9;2270:7;2266:23;2262:33;2259:53;;;2308:1;2305;2298:12;2259:53;2331:28;2349:9;2331:28;:::i;:::-;2321:38;;2378:37;2411:2;2400:9;2396:18;2378:37;:::i;:::-;2368:47;;2434:37;2467:2;2456:9;2452:18;2434:37;:::i;:::-;2424:47;;2490:37;2523:2;2512:9;2508:18;2490:37;:::i;:::-;2480:47;;2132:401;;;;;;;:::o;2733:315::-;2801:6;2809;2862:2;2850:9;2841:7;2837:23;2833:32;2830:52;;;2878:1;2875;2868:12;2830:52;2917:9;2904:23;2936:31;2961:5;2936:31;:::i;:::-;2986:5;3038:2;3023:18;;;;3010:32;;-1:-1:-1;;;2733:315:192:o;3053:525::-;3139:6;3147;3155;3163;3216:3;3204:9;3195:7;3191:23;3187:33;3184:53;;;3233:1;3230;3223:12;3184:53;3272:9;3259:23;3291:31;3316:5;3291:31;:::i;:::-;3341:5;-1:-1:-1;3398:2:192;3383:18;;3370:32;3411:33;3370:32;3411:33;:::i;:::-;3053:525;;3463:7;;-1:-1:-1;;;;3517:2:192;3502:18;;3489:32;;3568:2;3553:18;3540:32;;3053:525::o;3892:127::-;3953:10;3948:3;3944:20;3941:1;3934:31;3984:4;3981:1;3974:15;4008:4;4005:1;3998:15;4024:168;4097:9;;;4128;;4145:15;;;4139:22;;4125:37;4115:71;;4166:18;;:::i;4197:127::-;4258:10;4253:3;4249:20;4246:1;4239:31;4289:4;4286:1;4279:15;4313:4;4310:1;4303:15;4329:217;4369:1;4395;4385:132;;4439:10;4434:3;4430:20;4427:1;4420:31;4474:4;4471:1;4464:15;4502:4;4499:1;4492:15;4385:132;-1:-1:-1;4531:9:192;;4329:217::o;4551:695::-;4667:6;4675;4683;4691;4699;4707;4760:3;4748:9;4739:7;4735:23;4731:33;4728:53;;;4777:1;4774;4767:12;4728:53;4816:9;4803:23;4835:31;4860:5;4835:31;:::i;:::-;4885:5;-1:-1:-1;4942:2:192;4927:18;;4914:32;4955:33;4914:32;4955:33;:::i;:::-;5007:7;-1:-1:-1;5033:37:192;5066:2;5051:18;;5033:37;:::i;:::-;5023:47;;5089:37;5122:2;5111:9;5107:18;5089:37;:::i;:::-;5079:47;;5145:38;5178:3;5167:9;5163:19;5145:38;:::i;:::-;5135:48;;5202:38;5235:3;5224:9;5220:19;5202:38;:::i;:::-;5192:48;;4551:695;;;;;;;;:::o;5251:388::-;5408:2;5397:9;5390:21;5447:6;5442:2;5431:9;5427:18;5420:34;5504:6;5496;5491:2;5480:9;5476:18;5463:48;5560:1;5531:22;;;5555:2;5527:31;;;5520:42;;;;5623:2;5602:15;;;-1:-1:-1;;5598:29:192;5583:45;5579:54;;5251:388;-1:-1:-1;5251:388:192:o;5858:128::-;5925:9;;;5946:11;;;5943:37;;;5960:18;;:::i;5991:125::-;6056:9;;;6077:10;;;6074:36;;;6090:18;;:::i;6121:170::-;6188:8;6216:10;;;6228;;;6212:27;;6251:11;;;6248:37;;;6265:18;;:::i;:::-;6248:37;6121:170;;;;:::o", "linkReferences": {}, "immutableReferences": {"63204": [{"start": 321, "length": 32}, {"start": 469, "length": 32}, {"start": 563, "length": 32}, {"start": 707, "length": 32}, {"start": 771, "length": 32}, {"start": 849, "length": 32}, {"start": 948, "length": 32}, {"start": 1080, "length": 32}, {"start": 1316, "length": 32}, {"start": 1824, "length": 32}, {"start": 2067, "length": 32}, {"start": 2131, "length": 32}, {"start": 2677, "length": 32}, {"start": 2884, "length": 32}]}}, "methodIdentifiers": {"baseAsset(address)": "19b3<PERSON><PERSON>", "calculateDepositFee(uint256)": "247d284b", "calculateFee(address,address,uint256,uint256)": "e0e193a2", "calculateRedeemFee(uint256)": "7f0c6010", "depositFeeD6()": "e3707d18", "feeRecipient()": "46904840", "initialize(bytes)": "439fab91", "minPriceD18(address)": "980679e2", "owner()": "8da5cb5b", "performanceFeeD6()": "a51fac22", "protocolFeeD6()": "8005c6de", "redeemFeeD6()": "bf9587fb", "renounceOwnership()": "715018a6", "setBaseAsset(address,address)": "1ded75b4", "setFeeRecipient(address)": "e74b981b", "setFees(uint24,uint24,uint24,uint24)": "46f6cd27", "timestamps(address)": "e9c98e68", "transferOwnership(address)": "f2fde38b", "updateState(address,uint256)": "9ae26062"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name_\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"version_\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"baseAsset\",\"type\":\"address\"}],\"name\":\"BaseAssetAlreadySet\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint24\",\"name\":\"depositFeeD6\",\"type\":\"uint24\"},{\"internalType\":\"uint24\",\"name\":\"redeemFeeD6\",\"type\":\"uint24\"},{\"internalType\":\"uint24\",\"name\":\"performanceFeeD6\",\"type\":\"uint24\"},{\"internalType\":\"uint24\",\"name\":\"protocolFeeD6\",\"type\":\"uint24\"}],\"name\":\"InvalidFees\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZeroAddress\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"initParams\",\"type\":\"bytes\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"baseAsset\",\"type\":\"address\"}],\"name\":\"SetBaseAsset\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"feeRecipient\",\"type\":\"address\"}],\"name\":\"SetFeeRecipient\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint24\",\"name\":\"depositFeeD6\",\"type\":\"uint24\"},{\"indexed\":false,\"internalType\":\"uint24\",\"name\":\"redeemFeeD6\",\"type\":\"uint24\"},{\"indexed\":false,\"internalType\":\"uint24\",\"name\":\"performanceFeeD6\",\"type\":\"uint24\"},{\"indexed\":false,\"internalType\":\"uint24\",\"name\":\"protocolFeeD6\",\"type\":\"uint24\"}],\"name\":\"SetFees\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"priceD18\",\"type\":\"uint256\"}],\"name\":\"UpdateState\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"baseAsset\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"calculateDepositFee\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"priceD18\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"totalShares\",\"type\":\"uint256\"}],\"name\":\"calculateFee\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"calculateRedeemFee\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"depositFeeD6\",\"outputs\":[{\"internalType\":\"uint24\",\"name\":\"\",\"type\":\"uint24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"feeRecipient\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"minPriceD18\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"performanceFeeD6\",\"outputs\":[{\"internalType\":\"uint24\",\"name\":\"\",\"type\":\"uint24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"protocolFeeD6\",\"outputs\":[{\"internalType\":\"uint24\",\"name\":\"\",\"type\":\"uint24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"redeemFeeD6\",\"outputs\":[{\"internalType\":\"uint24\",\"name\":\"\",\"type\":\"uint24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"baseAsset_\",\"type\":\"address\"}],\"name\":\"setBaseAsset\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"feeRecipient_\",\"type\":\"address\"}],\"name\":\"setFeeRecipient\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint24\",\"name\":\"depositFeeD6_\",\"type\":\"uint24\"},{\"internalType\":\"uint24\",\"name\":\"redeemFeeD6_\",\"type\":\"uint24\"},{\"internalType\":\"uint24\",\"name\":\"performanceFeeD6_\",\"type\":\"uint24\"},{\"internalType\":\"uint24\",\"name\":\"protocolFeeD6_\",\"type\":\"uint24\"}],\"name\":\"setFees\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"timestamps\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"priceD18\",\"type\":\"uint256\"}],\"name\":\"updateState\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}]},\"events\":{\"Initialized(bytes)\":{\"params\":{\"initParams\":\"The initialization parameters.\"}},\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"}},\"kind\":\"dev\",\"methods\":{\"calculateDepositFee(uint256)\":{\"params\":{\"amount\":\"Number of shares being deposited\"},\"returns\":{\"_0\":\"Fee in shares to be deducted\"}},\"calculateFee(address,address,uint256,uint256)\":{\"params\":{\"asset\":\"Asset used for pricing\",\"priceD18\":\"Current vault share price for the specific `asset` (price = shares / assets)\",\"totalShares\":\"Total shares of the vault\",\"vault\":\"Address of the vault\"},\"returns\":{\"shares\":\"Fee to be added in shares\"}},\"calculateRedeemFee(uint256)\":{\"params\":{\"amount\":\"Number of shares being redeemed\"},\"returns\":{\"_0\":\"Fee in shares to be deducted\"}},\"initialize(bytes)\":{\"params\":{\"initParams\":\"The initialization parameters.\"}},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"setBaseAsset(address,address)\":{\"details\":\"Can only be set once per vault\"},\"setFeeRecipient(address)\":{\"params\":{\"feeRecipient_\":\"Address to receive fees\"}},\"setFees(uint24,uint24,uint24,uint24)\":{\"details\":\"Total of all fees must be <= 1e6 (i.e. 100%)\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"},\"updateState(address,uint256)\":{\"details\":\"Used by the vault to notify FeeManager of new price highs or protocol fee accrual checkpoints\"}},\"version\":1},\"userdoc\":{\"errors\":{\"BaseAssetAlreadySet(address,address)\":[{\"notice\":\"Thrown when trying to overwrite a vault's base asset that was already set\"}],\"InvalidFees(uint24,uint24,uint24,uint24)\":[{\"notice\":\"Thrown when the sum of all fees exceeds 100% (1e6 in D6 precision)\"}],\"ZeroAddress()\":[{\"notice\":\"Thrown when a required address is zero\"}]},\"events\":{\"Initialized(bytes)\":{\"notice\":\"Emitted once the entity has been initialized.\"},\"SetBaseAsset(address,address)\":{\"notice\":\"Emitted when a vault's base asset is set\"},\"SetFeeRecipient(address)\":{\"notice\":\"Emitted when the fee recipient is changed\"},\"SetFees(uint24,uint24,uint24,uint24)\":{\"notice\":\"Emitted when the fee configuration is updated\"},\"UpdateState(address,address,uint256)\":{\"notice\":\"Emitted when the vault's min price or timestamp is updated\"}},\"kind\":\"user\",\"methods\":{\"baseAsset(address)\":{\"notice\":\"Returns the base asset configured for a vault\"},\"calculateDepositFee(uint256)\":{\"notice\":\"Calculates the deposit fee in shares based on the amount\"},\"calculateFee(address,address,uint256,uint256)\":{\"notice\":\"Calculates the combined performance and protocol fee in shares\"},\"calculateRedeemFee(uint256)\":{\"notice\":\"Calculates the redeem fee in shares based on the amount\"},\"depositFeeD6()\":{\"notice\":\"Returns the configured deposit fee (in D6 precision)\"},\"feeRecipient()\":{\"notice\":\"Returns the current fee recipient address\"},\"initialize(bytes)\":{\"notice\":\"Initializes the factory-created entity with arbitrary initialization data.\"},\"minPriceD18(address)\":{\"notice\":\"Returns the last recorded min price for a vault's base asset (used for performance fee)\"},\"performanceFeeD6()\":{\"notice\":\"Returns the configured performance fee (in D6 precision)\"},\"protocolFeeD6()\":{\"notice\":\"Returns the configured protocol fee (in D6 precision per year)\"},\"redeemFeeD6()\":{\"notice\":\"Returns the configured redeem fee (in D6 precision)\"},\"setBaseAsset(address,address)\":{\"notice\":\"Sets the base asset for a vault, required for performance fee calculation\"},\"setFeeRecipient(address)\":{\"notice\":\"Sets the recipient address for all collected fees\"},\"setFees(uint24,uint24,uint24,uint24)\":{\"notice\":\"Sets the global fee configuration (deposit, redeem, performance, protocol)\"},\"timestamps(address)\":{\"notice\":\"Returns the last recorded timestamp for a given vault (used for protocol fee accrual)\"},\"updateState(address,uint256)\":{\"notice\":\"Updates the vault's state (min price and timestamp) based on asset price only if `asset` == `baseAssets[vault]`\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/managers/FeeManager.sol\":\"FeeManager\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol\":{\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6\",\"dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"src/interfaces/factories/IFactoryEntity.sol\":{\"keccak256\":\"0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812\",\"dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx\"]},\"src/interfaces/managers/IFeeManager.sol\":{\"keccak256\":\"0x37d3c06ed6b23e367216c2dfc14f99f3fd6d686c984eca124406c2002e4f1516\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://5b3b1b974856f76c6fe2347895ab05e973c84c963fdf8f5483c37e2e4d434cad\",\"dweb:/ipfs/QmVeLCrCWgeQ3aS4V38y9FszFvZdSkbPHKKCMWKKM3V4qw\"]},\"src/libraries/SlotLibrary.sol\":{\"keccak256\":\"0x35dcf16feec83c772add30250b0987ce29ccfbf308d04159701ebf9243444eeb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://97658d0c93ecc8174169e643d5a24c515b55905a2fcaaa6d6ec0188a3dd71a10\",\"dweb:/ipfs/QmS1z7ac8RbMvz15gVQ9YA1cCT5bs6KMHCiDhgcG8Pq8oK\"]},\"src/managers/FeeManager.sol\":{\"keccak256\":\"0x7dbae425aba2dbe4d81a5f2852e1c61d41396b1e77854931df8cbf2006ef113d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://02c9d8ac81178f0e3c47100a0e975dc40cb3d53755d6d48137d2025079978300\",\"dweb:/ipfs/QmPxmQCphPPa5LN6ZYoAqJHi8A61eFfHvQk36Gao1BvaHR\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "uint256", "name": "version_", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "baseAsset", "type": "address"}], "type": "error", "name": "BaseAssetAlreadySet"}, {"inputs": [{"internalType": "uint24", "name": "depositFeeD6", "type": "uint24"}, {"internalType": "uint24", "name": "redeemFeeD6", "type": "uint24"}, {"internalType": "uint24", "name": "performanceFeeD6", "type": "uint24"}, {"internalType": "uint24", "name": "protocolFeeD6", "type": "uint24"}], "type": "error", "name": "InvalidFees"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [], "type": "error", "name": "ZeroAddress"}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "initParams", "type": "bytes", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address", "indexed": true}, {"internalType": "address", "name": "baseAsset", "type": "address", "indexed": true}], "type": "event", "name": "SetBaseAsset", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "feeRecipient", "type": "address", "indexed": true}], "type": "event", "name": "SetFeeRecipient", "anonymous": false}, {"inputs": [{"internalType": "uint24", "name": "depositFeeD6", "type": "uint24", "indexed": false}, {"internalType": "uint24", "name": "redeemFeeD6", "type": "uint24", "indexed": false}, {"internalType": "uint24", "name": "performanceFeeD6", "type": "uint24", "indexed": false}, {"internalType": "uint24", "name": "protocolFeeD6", "type": "uint24", "indexed": false}], "type": "event", "name": "SetFees", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address", "indexed": true}, {"internalType": "address", "name": "asset", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "priceD18", "type": "uint256", "indexed": false}], "type": "event", "name": "UpdateState", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}], "stateMutability": "view", "type": "function", "name": "baseAsset", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "calculateDepositFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "priceD18", "type": "uint256"}, {"internalType": "uint256", "name": "totalShares", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "calculateFee", "outputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "calculateRedeemFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "depositFeeD6", "outputs": [{"internalType": "uint24", "name": "", "type": "uint24"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "feeRecipient", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}], "stateMutability": "view", "type": "function", "name": "minPriceD18", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "performanceFeeD6", "outputs": [{"internalType": "uint24", "name": "", "type": "uint24"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "protocolFeeD6", "outputs": [{"internalType": "uint24", "name": "", "type": "uint24"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "redeemFeeD6", "outputs": [{"internalType": "uint24", "name": "", "type": "uint24"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "baseAsset_", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setBaseAsset"}, {"inputs": [{"internalType": "address", "name": "feeRecipient_", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setFeeRecipient"}, {"inputs": [{"internalType": "uint24", "name": "depositFeeD6_", "type": "uint24"}, {"internalType": "uint24", "name": "redeemFeeD6_", "type": "uint24"}, {"internalType": "uint24", "name": "performanceFeeD6_", "type": "uint24"}, {"internalType": "uint24", "name": "protocolFeeD6_", "type": "uint24"}], "stateMutability": "nonpayable", "type": "function", "name": "setFees"}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}], "stateMutability": "view", "type": "function", "name": "timestamps", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "priceD18", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "updateState"}], "devdoc": {"kind": "dev", "methods": {"calculateDepositFee(uint256)": {"params": {"amount": "Number of shares being deposited"}, "returns": {"_0": "Fee in shares to be deducted"}}, "calculateFee(address,address,uint256,uint256)": {"params": {"asset": "Asset used for pricing", "priceD18": "Current vault share price for the specific `asset` (price = shares / assets)", "totalShares": "Total shares of the vault", "vault": "Address of the vault"}, "returns": {"shares": "Fee to be added in shares"}}, "calculateRedeemFee(uint256)": {"params": {"amount": "Number of shares being redeemed"}, "returns": {"_0": "Fee in shares to be deducted"}}, "initialize(bytes)": {"params": {"initParams": "The initialization parameters."}}, "owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "setBaseAsset(address,address)": {"details": "Can only be set once per vault"}, "setFeeRecipient(address)": {"params": {"feeRecipient_": "Address to receive fees"}}, "setFees(uint24,uint24,uint24,uint24)": {"details": "Total of all fees must be <= 1e6 (i.e. 100%)"}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}, "updateState(address,uint256)": {"details": "Used by the vault to notify FeeManager of new price highs or protocol fee accrual checkpoints"}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"baseAsset(address)": {"notice": "Returns the base asset configured for a vault"}, "calculateDepositFee(uint256)": {"notice": "Calculates the deposit fee in shares based on the amount"}, "calculateFee(address,address,uint256,uint256)": {"notice": "Calculates the combined performance and protocol fee in shares"}, "calculateRedeemFee(uint256)": {"notice": "Calculates the redeem fee in shares based on the amount"}, "depositFeeD6()": {"notice": "Returns the configured deposit fee (in D6 precision)"}, "feeRecipient()": {"notice": "Returns the current fee recipient address"}, "initialize(bytes)": {"notice": "Initializes the factory-created entity with arbitrary initialization data."}, "minPriceD18(address)": {"notice": "Returns the last recorded min price for a vault's base asset (used for performance fee)"}, "performanceFeeD6()": {"notice": "Returns the configured performance fee (in D6 precision)"}, "protocolFeeD6()": {"notice": "Returns the configured protocol fee (in D6 precision per year)"}, "redeemFeeD6()": {"notice": "Returns the configured redeem fee (in D6 precision)"}, "setBaseAsset(address,address)": {"notice": "Sets the base asset for a vault, required for performance fee calculation"}, "setFeeRecipient(address)": {"notice": "Sets the recipient address for all collected fees"}, "setFees(uint24,uint24,uint24,uint24)": {"notice": "Sets the global fee configuration (deposit, redeem, performance, protocol)"}, "timestamps(address)": {"notice": "Returns the last recorded timestamp for a given vault (used for protocol fee accrual)"}, "updateState(address,uint256)": {"notice": "Updates the vault's state (min price and timestamp) based on asset price only if `asset` == `baseAssets[vault]`"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/managers/FeeManager.sol": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"keccak256": "0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a", "urls": ["bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6", "dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05", "urls": ["bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08", "dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "src/interfaces/factories/IFactoryEntity.sol": {"keccak256": "0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5", "urls": ["bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812", "dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx"], "license": "BUSL-1.1"}, "src/interfaces/managers/IFeeManager.sol": {"keccak256": "0x37d3c06ed6b23e367216c2dfc14f99f3fd6d686c984eca124406c2002e4f1516", "urls": ["bzz-raw://5b3b1b974856f76c6fe2347895ab05e973c84c963fdf8f5483c37e2e4d434cad", "dweb:/ipfs/QmVeLCrCWgeQ3aS4V38y9FszFvZdSkbPHKKCMWKKM3V4qw"], "license": "BUSL-1.1"}, "src/libraries/SlotLibrary.sol": {"keccak256": "0x35dcf16feec83c772add30250b0987ce29ccfbf308d04159701ebf9243444eeb", "urls": ["bzz-raw://97658d0c93ecc8174169e643d5a24c515b55905a2fcaaa6d6ec0188a3dd71a10", "dweb:/ipfs/QmS1z7ac8RbMvz15gVQ9YA1cCT5bs6KMHCiDhgcG8Pq8oK"], "license": "BUSL-1.1"}, "src/managers/FeeManager.sol": {"keccak256": "0x7dbae425aba2dbe4d81a5f2852e1c61d41396b1e77854931df8cbf2006ef113d", "urls": ["bzz-raw://02c9d8ac81178f0e3c47100a0e975dc40cb3d53755d6d48137d2025079978300", "dweb:/ipfs/QmPxmQCphPPa5LN6ZYoAqJHi8A61eFfHvQk36Gao1BvaHR"], "license": "BUSL-1.1"}}, "version": 1}, "id": 116}