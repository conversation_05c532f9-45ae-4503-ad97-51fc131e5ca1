{"abi": [{"type": "constructor", "inputs": [{"name": "name_", "type": "string", "internalType": "string"}, {"name": "version_", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "ALLOW_CALL_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "CALLER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "DISALLOW_CALL_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "SET_MERKLE_ROOT_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "allowCalls", "inputs": [{"name": "compactCalls", "type": "tuple[]", "internalType": "struct IVerifier.CompactCall[]", "components": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "selector", "type": "bytes4", "internalType": "bytes4"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "allowedCallAt", "inputs": [{"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IVerifier.CompactCall", "components": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "selector", "type": "bytes4", "internalType": "bytes4"}]}], "stateMutability": "view"}, {"type": "function", "name": "allowedCalls", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "disallowCalls", "inputs": [{"name": "compactCalls", "type": "tuple[]", "internalType": "struct IVerifier.CompactCall[]", "components": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "selector", "type": "bytes4", "internalType": "bytes4"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getVerificationResult", "inputs": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}, {"name": "payload", "type": "tuple", "internalType": "struct IVerifier.VerificationPayload", "components": [{"name": "verificationType", "type": "uint8", "internalType": "enum IVerifier.VerificationType"}, {"name": "verificationData", "type": "bytes", "internalType": "bytes"}, {"name": "proof", "type": "bytes32[]", "internalType": "bytes32[]"}]}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "hashCall", "inputs": [{"name": "call", "type": "tuple", "internalType": "struct IVerifier.CompactCall", "components": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "selector", "type": "bytes4", "internalType": "bytes4"}]}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "hashCall", "inputs": [{"name": "call", "type": "tuple", "internalType": "struct IVerifier.ExtendedCall", "components": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "initialize", "inputs": [{"name": "initParams", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isAllowedCall", "inputs": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "callData", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "merkleRoot", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "setMerkleRoot", "inputs": [{"name": "merkleRoot_", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "vault", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IAccessControl"}], "stateMutability": "view"}, {"type": "function", "name": "verifyCall", "inputs": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}, {"name": "payload", "type": "tuple", "internalType": "struct IVerifier.VerificationPayload", "components": [{"name": "verificationType", "type": "uint8", "internalType": "enum IVerifier.VerificationType"}, {"name": "verificationData", "type": "bytes", "internalType": "bytes"}, {"name": "proof", "type": "bytes32[]", "internalType": "bytes32[]"}]}], "outputs": [], "stateMutability": "view"}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "initParams", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "error", "name": "CompactCallAlreadyAllowed", "inputs": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "selector", "type": "bytes4", "internalType": "bytes4"}]}, {"type": "error", "name": "CompactCallNotFound", "inputs": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "selector", "type": "bytes4", "internalType": "bytes4"}]}, {"type": "error", "name": "Forbidden", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "InvalidLength", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "VerificationFailed", "inputs": []}, {"type": "error", "name": "ZeroValue", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "153:6633:131:-:0;;;985:171;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1069:48;;;;;;;;;;;;-1:-1:-1;;;1069:48:131;;;;;;1101:5;1108:8;1069:19;:48::i;:::-;1046:71;;1127:22;:20;:22::i;:::-;985:171;;153:6633;;656:343:113;761:7;986:4;970:22;;969:23;941:1;908:12;922:4;928:7;856:80;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;846:91;;;;;;838:100;;:104;;;;:::i;:::-;810:146;;;;;;2258:25:192;;2246:2;2231:18;;2112:177;810:146:113;;;;;;;;;;;;;787:179;;;;;;:205;780:212;;656:343;;;;;:::o;7709:422:3:-;7824:30;7857:26;:24;:26::i;:::-;7898:15;;;;-1:-1:-1;7898:15:3;;;;;7894:76;;;7936:23;;-1:-1:-1;;;7936:23:3;;;;;;;;;;;7894:76;7983:14;;-1:-1:-1;;;;;7983:14:3;;;:34;7979:146;;8033:33;;-1:-1:-1;;;;;;8033:33:3;-1:-1:-1;;;;;8033:33:3;;;;;8085:29;;2438:50:192;;;8085:29:3;;2426:2:192;2411:18;8085:29:3;;;;;;;7979:146;7758:373;7709:422::o;9071:205::-;9129:30;;3147:66;9186:27;9171:42;9071:205;-1:-1:-1;;9071:205:3:o;14:127:192:-;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:983;235:6;243;296:2;284:9;275:7;271:23;267:32;264:52;;;312:1;309;302:12;264:52;339:16;;-1:-1:-1;;;;;404:14:192;;;401:34;;;431:1;428;421:12;401:34;469:6;458:9;454:22;444:32;;514:7;507:4;503:2;499:13;495:27;485:55;;536:1;533;526:12;485:55;565:2;559:9;587:2;583;580:10;577:36;;;593:18;;:::i;:::-;668:2;662:9;636:2;722:13;;-1:-1:-1;;718:22:192;;;742:2;714:31;710:40;698:53;;;766:18;;;786:22;;;763:46;760:72;;;812:18;;:::i;:::-;852:10;848:2;841:22;887:2;879:6;872:18;929:7;922:4;917:2;913;909:11;905:22;902:35;899:55;;;950:1;947;940:12;899:55;1003:2;996:4;992:2;988:13;981:4;973:6;969:17;963:43;1050:1;1043:4;1038:2;1030:6;1026:15;1022:26;1015:37;1071:6;1061:16;;;;;;;1117:4;1106:9;1102:20;1096:27;1086:37;;146:983;;;;;:::o;1134:212::-;1176:3;1214:5;1208:12;1258:6;1251:4;1244:5;1240:16;1235:3;1229:36;1320:1;1284:16;;1309:13;;;-1:-1:-1;1284:16:192;;1134:212;-1:-1:-1;1134:212:192:o;1351:526::-;1689:33;1684:3;1677:46;1659:3;1745:66;1771:39;1806:2;1801:3;1797:12;1789:6;1771:39;:::i;:::-;1763:6;1745:66;:::i;:::-;1820:21;;;-1:-1:-1;;1868:2:192;1857:14;;1351:526;-1:-1:-1;;1351:526:192:o;1882:225::-;1949:9;;;1970:11;;;1967:134;;;2023:10;2018:3;2014:20;2011:1;2004:31;2058:4;2055:1;2048:15;2086:4;2083:1;2076:15;2294:200;153:6633:131;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "153:6633:131:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2632:312;;;;;;:::i;:::-;;:::i;:::-;;1369:105;6701:20;1438:29;;;1369:105;;;1706:25:192;;;1694:2;1679:18;1369:105:131;;;;;;;;5168:679;;;;;;:::i;:::-;;:::i;1669:235::-;;;;;;:::i;:::-;;:::i;:::-;;;;2848:13:192;;-1:-1:-1;;;;;2844:22:192;;;2826:41;;2927:4;2915:17;;;2909:24;2905:33;;;2883:20;;;2876:63;2987:17;;;2981:24;-1:-1:-1;;;;;;2977:51:192;2955:20;;;2948:81;2776:2;2761:18;1669:235:131;2582:453:192;5883:682:131;;;;;;:::i;:::-;;:::i;4551:401::-;;;;;;:::i;:::-;;:::i;678:97::-;;723:52;678:97;;1510:123;;;:::i;432:83::-;;470:45;432:83;;4988:144;;;;;;:::i;:::-;;:::i;295:101::-;;342:54;295:101;;2980:1504;;;;;;:::i;:::-;;:::i;:::-;;;3986:14:192;;3979:22;3961:41;;3949:2;3934:18;2980:1504:131;3821:187:192;1940:265:131;;;;;;:::i;:::-;;:::i;2241:155::-;;;;;;:::i;:::-;;:::i;2432:164::-;;;;;;:::i;:::-;;:::i;551:91::-;;593:49;551:91;;1215:118;;;:::i;:::-;;;-1:-1:-1;;;;;7850:32:192;;;7832:51;;7820:2;7805:18;1215:118:131;7662:227:192;2632:312:131;2829:55;2851:3;2856:5;2863;2870:4;;2876:7;2829:21;:55::i;:::-;2824:114;;2907:20;;-1:-1:-1;;;2907:20:131;;;;;;;;;;;2824:114;2632:312;;;;;;:::o;1369:105::-;1438:29;6701:20;1438:29;;;1369:105::o;5168:679::-;593:49;882:7;:5;:7::i;:::-;-1:-1:-1;;;;;882:15:131;;898:4;966:10:5;882:35:131;;-1:-1:-1;;;;;;882:35:131;;;;;;;;;;8068:25:192;;;;-1:-1:-1;;;;;8129:32:192;8109:18;;;8102:60;8041:18;;882:35:131;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;877:85;;940:11;;-1:-1:-1;;;940:11:131;;;;;;;;;;;877:85;6701:20;5382:14:::1;::::0;::::1;5460:19;::::0;::::1;5270:25;5489:352;5509:23:::0;;::::1;5489:352;;;5553:13;5569:25;5578:12;;5591:1;5578:15;;;;;;;:::i;:::-;;;;;;5569:25;;;;;;;;;;:::i;:::-;5553:41:::0;-1:-1:-1;5613:29:131::1;:18:::0;5553:41;5613:22:::1;:29::i;:::-;5608:171;;5695:12;;5708:1;5695:15;;;;;;;:::i;:::-;:19;::::0;::::1;:15;::::0;;::::1;;:19:::0;;::::1;::::0;-1:-1:-1;5695:19:131::1;:::i;:::-;5716:12;;5729:1;5716:15;;;;;;;:::i;:::-;;;;;;:21;;;;;;;;;;:::i;:::-;5739:12;;5752:1;5739:15;;;;;;;:::i;:::-;;;;;;:24;;;;;;;;;;:::i;:::-;5669:95;;-1:-1:-1::0;;;5669:95:131::1;;;;;;;;;;:::i;:::-;;;;;;;;5608:171;5815:12;;5828:1;5815:15;;;;;;;:::i;:::-;;;;;;5792:13;:20;5806:5;5792:20;;;;;;;;;;;:38;;;;;;:::i;:::-;-1:-1:-1::0;;;5534:3:131::1;;5489:352;;;;5260:587;;;5168:679:::0;;;:::o;1669:235::-;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;6701:20:131;;1830:29;:19;;;1853:5;1830:22;:29::i;:::-;1876:21;;;;:14;;;;:21;;;;;;;;;1869:28;;;;;;;;;-1:-1:-1;;;;;1869:28:131;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;1869:28:131;;;;;;;;;;;;;-1:-1:-1;1869:28:131;1669:235;-1:-1:-1;;1669:235:131:o;5883:682::-;723:52;882:7;:5;:7::i;:::-;-1:-1:-1;;;;;882:15:131;;898:4;966:10:5;882:35:131;;-1:-1:-1;;;;;;882:35:131;;;;;;;;;;8068:25:192;;;;-1:-1:-1;;;;;8129:32:192;8109:18;;;8102:60;8041:18;;882:35:131;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;877:85;;940:11;;-1:-1:-1;;;940:11:131;;;;;;;;;;;877:85;6701:20;6103:14:::1;::::0;::::1;6181:19;::::0;::::1;5991:25;6210:349;6230:23:::0;;::::1;6210:349;;;6274:13;6290:25;6299:12;;6312:1;6299:15;;;;;;;:::i;6290:25::-;6274:41:::0;-1:-1:-1;6334:32:131::1;:18:::0;6274:41;6334:25:::1;:32::i;:::-;6329:168;;6413:12;;6426:1;6413:15;;;;;;;:::i;:::-;:19;::::0;::::1;:15;::::0;;::::1;;:19:::0;;::::1;::::0;-1:-1:-1;6413:19:131::1;:::i;:::-;6434:12;;6447:1;6434:15;;;;;;;:::i;:::-;;;;;;:21;;;;;;;;;;:::i;:::-;6457:12;;6470:1;6457:15;;;;;;;:::i;:::-;;;;;;:24;;;;;;;;;;:::i;:::-;6393:89;;-1:-1:-1::0;;;6393:89:131::1;;;;;;;;;;:::i;6329:168::-;6533:12;;6546:1;6533:15;;;;;;;:::i;:::-;;;;;;6510:13;:20;6524:5;6510:20;;;;;;;;;;;:38;;;;;;:::i;:::-;-1:-1:-1::0;;;6255:3:131::1;;6210:349;;4551:401:::0;4158:30:3;4191:26;:24;:26::i;:::-;4302:15;;4158:59;;-1:-1:-1;4302:15:3;-1:-1:-1;;;4302:15:3;;;4301:16;;-1:-1:-1;;;;;4348:14:3;4279:19;4724:16;;:34;;;;;4744:14;4724:34;4704:54;;4768:17;4788:11;-1:-1:-1;;;;;4788:16:3;4803:1;4788:16;:50;;;;-1:-1:-1;4816:4:3;4808:25;:30;4788:50;4768:70;;4854:12;4853:13;:30;;;;;4871:12;4870:13;4853:30;4849:91;;;4906:23;;-1:-1:-1;;;4906:23:3;;;;;;;;;;;4849:91;4949:18;;-1:-1:-1;;4949:18:3;4966:1;4949:18;;;4977:67;;;;5011:22;;-1:-1:-1;;;;5011:22:3;-1:-1:-1;;;5011:22:3;;;4977:67;4630:14:131::1;::::0;4669:42:::1;::::0;;::::1;4680:10:::0;4669:42:::1;:::i;:::-;4629:82:::0;;-1:-1:-1;4629:82:131;-1:-1:-1;;;;;;4725:20:131;::::1;4721:69;;4768:11;;-1:-1:-1::0;;;4768:11:131::1;;;;;;;;;;;4721:69;6701:20:::0;4855:16;;-1:-1:-1;;;;;;4855:16:131::1;-1:-1:-1::0;;;;;4855:16:131;::::1;;::::0;;-1:-1:-1;4881:12:131;::::1;:26:::0;;;4922:23:::1;::::0;::::1;::::0;::::1;::::0;4934:10;;;;4922:23:::1;:::i;:::-;;;;;;;;4619:333;;;5068:14:3::0;5064:101;;;5098:23;;-1:-1:-1;;;;5098:23:3;;;5140:14;;-1:-1:-1;11451:50:192;;5140:14:3;;11439:2:192;11424:18;5140:14:3;;;;;;;4092:1079;;;;;4551:401:131;;:::o;1510:123::-;1555:7;1581:45;6701:20;1581:36;;:43;:45::i;:::-;1574:52;;1510:123;:::o;4988:144::-;342:54;882:7;:5;:7::i;:::-;-1:-1:-1;;;;;882:15:131;;898:4;966:10:5;882:35:131;;-1:-1:-1;;;;;;882:35:131;;;;;;;;;;8068:25:192;;;;-1:-1:-1;;;;;8129:32:192;8109:18;;;8102:60;8041:18;;882:35:131;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;877:85;;940:11;;-1:-1:-1;;;940:11:131;;;;;;;;;;;877:85;-1:-1:-1;5082:29:131::1;6701:20:::0;5082:29:::1;:43:::0;4988:144::o;2980:1504::-;3180:4;3201:7;:5;:7::i;:::-;:33;;-1:-1:-1;;;3201:33:131;;470:45;3201:33;;;8068:25:192;-1:-1:-1;;;;;8129:32:192;;;8109:18;;;8102:60;3201:15:131;;;;;;;8041:18:192;;3201:33:131;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3196:77;;-1:-1:-1;3257:5:131;3250:12;;3196:77;3315:32;3287:24;;;;:7;:24;:::i;:::-;:60;;;;;;;;:::i;:::-;;3283:129;;3370:31;3384:3;3389:5;3396:4;;3370:13;:31::i;:::-;3363:38;;;;3283:129;3422:31;;3456:24;;;;:7;:24;:::i;:::-;3422:58;;-1:-1:-1;3422:58:131;-1:-1:-1;3490:12:131;3561:24;;;;:7;:24;:::i;:::-;3597:16;;3587:27;;;;;;;:::i;:::-;;;;;;;;;3550:65;;;;;;:::i;:::-;;;;-1:-1:-1;;3550:65:131;;;;;;;;;3540:76;;3550:65;3540:76;;;;3527:90;;;13284:19:192;13319:12;3527:90:131;;;;;;;;;;;;3517:101;;;;;;3490:128;;3633:53;3652:7;:13;;;;;;;;:::i;:::-;3633:53;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;3667:12:131;;-1:-1:-1;3667:10:131;;-1:-1:-1;;3667:12:131:i;:::-;3681:4;3633:18;:53::i;:::-;3628:97;;3709:5;3702:12;;;;;;;3628:97;3767:32;3739:24;;;;:7;:24;:::i;:::-;:60;;;;;;;;:::i;:::-;;3735:743;;3873:25;3881:16;;3873:25;:::i;:::-;3822:47;3831:37;;;;;;;;3844:3;-1:-1:-1;;;;;3831:37:131;;;;;3849:5;-1:-1:-1;;;;;3831:37:131;;;;;3856:5;3831:37;;;;3863:4;;3831:37;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;3831:37:131;;-1:-1:-1;3822:8:131;:47::i;:::-;:76;3815:83;;;;;;;3735:743;3947:31;3919:24;;;;:7;:24;:::i;:::-;:59;;;;;;;;:::i;:::-;;3915:563;;4016:1;4001:16;;;;;:100;;-1:-1:-1;4076:25:131;4084:16;;4076:25;:::i;:::-;4030:41;;;;;;;;-1:-1:-1;;;;;4030:41:131;;;;;;;;;;;4021:51;;4030:41;;4061:8;4067:1;-1:-1:-1;4061:4:131;;:8;:::i;:::-;4054:16;;;:::i;:::-;-1:-1:-1;;;;;;4030:41:131;;;4021:8;:51::i;4001:100::-;3994:107;;;;;;;3915:563;4150:32;4122:24;;;;:7;:24;:::i;:::-;:60;;;;;;;;:::i;:::-;;4118:360;;4267:37;;-1:-1:-1;;;;;4338:36:131;;;4375:3;4380:5;4387;4394:4;;4400:23;:16;4417:4;4400:16;4280:23;4400;:::i;:::-;4338:86;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4331:93;;;;;;;;4118:360;4462:5;4455:12;;;;;2980:1504;;;;;;;;;:::o;1940:265::-;2037:4;2079:1;2060:20;;;;;:138;;-1:-1:-1;2151:45:131;;;;;;;;-1:-1:-1;;;;;2151:45:131;;;;;;;;;;;2096:102;;2142:55;;2151:45;;;2182:12;2192:1;-1:-1:-1;2182:8:131;;:12;:::i;2142:55::-;6701:20;2096:36;;;:45;:102::i;:::-;2053:145;1940:265;-1:-1:-1;;;;;1940:265:131:o;2241:155::-;2305:7;2352:4;:8;;;2362:4;:10;;;2374:4;:13;;;2341:47;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;2331:58;;;;;;2324:65;;2241:155;;;:::o;2432:164::-;2497:7;2544:4;:8;;;2554:4;:10;;;2566:4;:10;;;2578:4;:9;;;2533:55;;;;;;;;;;;:::i;1215:118::-;6701:20;1301:24;-1:-1:-1;;;;;1301:24:131;;1215:118::o;6576:123:72:-;6646:4;6669:23;6674:3;6686:5;6669:4;:23::i;:::-;6662:30;;6576:123;;;;;:::o;8150:129::-;8224:7;8250:22;8254:3;8266:5;8250:3;:22::i;6867:129::-;6940:4;6963:26;6971:3;6983:5;6963:7;:26::i;9071:205:3:-;9129:30;;3147:66;9186:27;8819:122;7693:115:72;7756:7;7782:19;7790:3;5202:18;;5120:107;1902:154:64;1993:4;2045;2016:25;2029:5;2036:4;2016:12;:25::i;:::-;:33;;1902:154;-1:-1:-1;;;;1902:154:64:o;7474:138:72:-;7554:4;5006:21;;;:14;;;:21;;;;;;:26;;7577:28;4910:129;2336:406;2399:4;5006:21;;;:14;;;:21;;;;;;2415:321;;-1:-1:-1;2457:23:72;;;;;;;;:11;:23;;;;;;;;;;;;;2639:18;;2615:21;;;:14;;;:21;;;;;;:42;;;;2671:11;;2415:321;-1:-1:-1;2720:5:72;2713:12;;5569:118;5636:7;5662:3;:11;;5674:5;5662:18;;;;;;;;:::i;:::-;;;;;;;;;5655:25;;5569:118;;;;:::o;2910:1368::-;2976:4;3105:21;;;:14;;;:21;;;;;;3141:13;;3137:1135;;3508:18;3529:12;3540:1;3529:8;:12;:::i;:::-;3575:18;;3508:33;;-1:-1:-1;3555:17:72;;3575:22;;3596:1;;3575:22;:::i;:::-;3555:42;;3630:9;3616:10;:23;3612:378;;3659:17;3679:3;:11;;3691:9;3679:22;;;;;;;;:::i;:::-;;;;;;;;;3659:42;;3826:9;3800:3;:11;;3812:10;3800:23;;;;;;;;:::i;:::-;;;;;;;;;;;;:35;;;;3939:25;;;:14;;;:25;;;;;:36;;;3612:378;4068:17;;:3;;:17;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;4171:3;:14;;:21;4186:5;4171:21;;;;;;;;;;;4164:28;;;4214:4;4207:11;;;;;;;3137:1135;4256:5;4249:12;;;;;2457:308:64;2540:7;2582:4;2540:7;2596:134;2620:5;:12;2616:1;:16;2596:134;;;2668:51;2696:12;2710:5;2716:1;2710:8;;;;;;;;:::i;:::-;;;;;;;2668:27;:51::i;:::-;2653:66;-1:-1:-1;2634:3:64;;2596:134;;;-1:-1:-1;2746:12:64;2457:308;-1:-1:-1;;;2457:308:64:o;504:167:63:-;579:7;609:1;605;:5;:59;;864:13;928:15;;;963:4;956:15;;;1009:4;993:21;;605:59;;;864:13;928:15;;;963:4;956:15;;;1009:4;993:21;;613:24;791:239;14:131:192;-1:-1:-1;;;;;89:31:192;;79:42;;69:70;;135:1;132;125:12;69:70;14:131;:::o;150:347::-;201:8;211:6;265:3;258:4;250:6;246:17;242:27;232:55;;283:1;280;273:12;232:55;-1:-1:-1;306:20:192;;-1:-1:-1;;;;;338:30:192;;335:50;;;381:1;378;371:12;335:50;418:4;410:6;406:17;394:29;;470:3;463:4;454:6;446;442:19;438:30;435:39;432:59;;;487:1;484;477:12;432:59;150:347;;;;;:::o;502:1053::-;648:6;656;664;672;680;688;741:3;729:9;720:7;716:23;712:33;709:53;;;758:1;755;748:12;709:53;797:9;784:23;816:31;841:5;816:31;:::i;:::-;866:5;-1:-1:-1;923:2:192;908:18;;895:32;936:33;895:32;936:33;:::i;:::-;988:7;-1:-1:-1;1042:2:192;1027:18;;1014:32;;-1:-1:-1;1097:2:192;1082:18;;1069:32;-1:-1:-1;;;;;1150:14:192;;;1147:34;;;1177:1;1174;1167:12;1147:34;1216:58;1266:7;1257:6;1246:9;1242:22;1216:58;:::i;:::-;1293:8;;-1:-1:-1;1190:84:192;-1:-1:-1;1381:3:192;1366:19;;1353:33;;-1:-1:-1;1398:16:192;;;1395:36;;;1427:1;1424;1417:12;1395:36;-1:-1:-1;1450:24:192;;1508:2;1490:16;;;1486:25;1483:45;;;1524:1;1521;1514:12;1483:45;1547:2;1537:12;;;502:1053;;;;;;;;:::o;1742:650::-;1860:6;1868;1921:2;1909:9;1900:7;1896:23;1892:32;1889:52;;;1937:1;1934;1927:12;1889:52;1977:9;1964:23;-1:-1:-1;;;;;2047:2:192;2039:6;2036:14;2033:34;;;2063:1;2060;2053:12;2033:34;2101:6;2090:9;2086:22;2076:32;;2146:7;2139:4;2135:2;2131:13;2127:27;2117:55;;2168:1;2165;2158:12;2117:55;2208:2;2195:16;2234:2;2226:6;2223:14;2220:34;;;2250:1;2247;2240:12;2220:34;2306:7;2301:2;2293:4;2285:6;2281:17;2277:2;2273:26;2269:35;2266:48;2263:68;;;2327:1;2324;2317:12;2263:68;2358:2;2350:11;;;;;2380:6;;-1:-1:-1;1742:650:192;;-1:-1:-1;;;;1742:650:192:o;2397:180::-;2456:6;2509:2;2497:9;2488:7;2484:23;2480:32;2477:52;;;2525:1;2522;2515:12;2477:52;-1:-1:-1;2548:23:192;;2397:180;-1:-1:-1;2397:180:192:o;3040:409::-;3110:6;3118;3171:2;3159:9;3150:7;3146:23;3142:32;3139:52;;;3187:1;3184;3177:12;3139:52;3227:9;3214:23;-1:-1:-1;;;;;3252:6:192;3249:30;3246:50;;;3292:1;3289;3282:12;3246:50;3331:58;3381:7;3372:6;3361:9;3357:22;3331:58;:::i;:::-;3408:8;;3305:84;;-1:-1:-1;3040:409:192;-1:-1:-1;;;;3040:409:192:o;4013:685::-;4101:6;4109;4117;4125;4178:2;4166:9;4157:7;4153:23;4149:32;4146:52;;;4194:1;4191;4184:12;4146:52;4233:9;4220:23;4252:31;4277:5;4252:31;:::i;:::-;4302:5;-1:-1:-1;4359:2:192;4344:18;;4331:32;4372:33;4331:32;4372:33;:::i;:::-;4424:7;-1:-1:-1;4482:2:192;4467:18;;4454:32;-1:-1:-1;;;;;4498:30:192;;4495:50;;;4541:1;4538;4531:12;4495:50;4580:58;4630:7;4621:6;4610:9;4606:22;4580:58;:::i;:::-;4013:685;;;;-1:-1:-1;4657:8:192;-1:-1:-1;;;;4013:685:192:o;4703:127::-;4764:10;4759:3;4755:20;4752:1;4745:31;4795:4;4792:1;4785:15;4819:4;4816:1;4809:15;4835:253;4907:2;4901:9;4949:4;4937:17;;-1:-1:-1;;;;;4969:34:192;;5005:22;;;4966:62;4963:88;;;5031:18;;:::i;:::-;5067:2;5060:22;4835:253;:::o;5093:275::-;5164:2;5158:9;5229:2;5210:13;;-1:-1:-1;;5206:27:192;5194:40;;-1:-1:-1;;;;;5249:34:192;;5285:22;;;5246:62;5243:88;;;5311:18;;:::i;:::-;5347:2;5340:22;5093:275;;-1:-1:-1;5093:275:192:o;5373:131::-;-1:-1:-1;;;;;;5447:32:192;;5437:43;;5427:71;;5494:1;5491;5484:12;5509:787;5598:6;5651:2;5639:9;5630:7;5626:23;5622:32;5619:52;;;5667:1;5664;5657:12;5619:52;5700:2;5694:9;5742:2;5734:6;5730:15;5811:6;5799:10;5796:22;-1:-1:-1;;;;;5763:10:192;5760:34;5757:62;5754:88;;;5822:18;;:::i;:::-;5858:2;5851:22;5895:23;;5927:31;5895:23;5927:31;:::i;:::-;5967:21;;6040:2;6025:18;;6012:32;6053:33;6012:32;6053:33;:::i;:::-;6114:2;6102:15;;6095:32;6179:2;6164:18;;6151:32;6192;6151;6192;:::i;:::-;6252:2;6240:15;;6233:32;6244:6;5509:787;-1:-1:-1;;;5509:787:192:o;6301:1356::-;6391:6;6422:2;6465;6453:9;6444:7;6440:23;6436:32;6433:52;;;6481:1;6478;6471:12;6433:52;6521:9;6508:23;-1:-1:-1;;;;;6591:2:192;6583:6;6580:14;6577:34;;;6607:1;6604;6597:12;6577:34;6630:22;;;;6686:4;6668:16;;;6664:27;6661:47;;;6704:1;6701;6694:12;6661:47;6730:22;;:::i;:::-;6789:2;6776:16;6801:33;6826:7;6801:33;:::i;:::-;6843:22;;6902:11;;;6889:25;6923:33;6889:25;6923:33;:::i;:::-;6972:14;;;6965:31;7049:2;7041:11;;;7028:25;7012:14;;;7005:49;7100:2;7092:11;;7079:25;7116:16;;;7113:36;;;7145:1;7142;7135:12;7113:36;7176:8;7172:2;7168:17;7158:27;;;7223:7;7216:4;7212:2;7208:13;7204:27;7194:55;;7245:1;7242;7235:12;7194:55;7281:2;7268:16;7303:2;7299;7296:10;7293:36;;;7309:18;;:::i;:::-;7351:53;7394:2;7375:13;;-1:-1:-1;;7371:27:192;7367:36;;7351:53;:::i;:::-;7338:66;;7427:2;7420:5;7413:17;7467:7;7462:2;7457;7453;7449:11;7445:20;7442:33;7439:53;;;7488:1;7485;7478:12;7439:53;7543:2;7538;7534;7530:11;7525:2;7518:5;7514:14;7501:45;7587:1;7566:14;;;7562:23;;;7555:34;;;;7616:2;7605:14;;7598:29;-1:-1:-1;7609:5:192;6301:1356;-1:-1:-1;;;6301:1356:192:o;8173:277::-;8240:6;8293:2;8281:9;8272:7;8268:23;8264:32;8261:52;;;8309:1;8306;8299:12;8261:52;8341:9;8335:16;8394:5;8387:13;8380:21;8373:5;8370:32;8360:60;;8416:1;8413;8406:12;8360:60;8439:5;8173:277;-1:-1:-1;;;8173:277:192:o;8455:127::-;8516:10;8511:3;8507:20;8504:1;8497:31;8547:4;8544:1;8537:15;8571:4;8568:1;8561:15;8587:247;8646:6;8699:2;8687:9;8678:7;8674:23;8670:32;8667:52;;;8715:1;8712;8705:12;8667:52;8754:9;8741:23;8773:31;8798:5;8773:31;:::i;8839:245::-;8897:6;8950:2;8938:9;8929:7;8925:23;8921:32;8918:52;;;8966:1;8963;8956:12;8918:52;9005:9;8992:23;9024:30;9048:5;9024:30;:::i;9089:400::-;-1:-1:-1;;;;;9345:15:192;;;9327:34;;9397:15;;;;9392:2;9377:18;;9370:43;-1:-1:-1;;;;;;9449:33:192;;;9444:2;9429:18;;9422:61;9277:2;9262:18;;9089:400::o;9494:195::-;9598:11;;-1:-1:-1;;;;;;9594:54:192;-1:-1:-1;;;;;9650:31:192;;;;9591:91;;;;9578:105;;9494:195::o;9694:751::-;9869:5;9856:19;9884:33;9909:7;9884:33;:::i;:::-;9926:62;9980:7;9974:4;9926:62;:::i;:::-;;10025:1;10019:4;10015:12;10075:2;10068:5;10064:14;10051:28;10088:33;10113:7;10088:33;:::i;:::-;10130:68;10190:7;10178:10;10130:68;:::i;:::-;;10246:2;10239:5;10235:14;10222:28;10259:32;10283:7;10259:32;:::i;:::-;10310:17;;-1:-1:-1;;;;10358:34:192;10402:2;10398:16;;;;-1:-1:-1;;;10394:43:192;10355:83;10336:103;;-1:-1:-1;;9694:751:192:o;10450:323::-;10526:6;10534;10587:2;10575:9;10566:7;10562:23;10558:32;10555:52;;;10603:1;10600;10593:12;10555:52;10642:9;10629:23;10661:31;10686:5;10661:31;:::i;:::-;10711:5;10763:2;10748:18;;;;10735:32;;-1:-1:-1;;;10450:323:192:o;10778:266::-;10866:6;10861:3;10854:19;10918:6;10911:5;10904:4;10899:3;10895:14;10882:43;-1:-1:-1;10970:1:192;10945:16;;;10963:4;10941:27;;;10934:38;;;;11026:2;11005:15;;;-1:-1:-1;;11001:29:192;10992:39;;;10988:50;;10778:266::o;11049:244::-;11206:2;11195:9;11188:21;11169:4;11226:61;11283:2;11272:9;11268:18;11260:6;11252;11226:61;:::i;:::-;11218:69;11049:244;-1:-1:-1;;;;11049:244:192:o;11512:127::-;11573:10;11568:3;11564:20;11561:1;11554:31;11604:4;11601:1;11594:15;11628:4;11625:1;11618:15;11644:278;11725:6;11778:2;11766:9;11757:7;11753:23;11749:32;11746:52;;;11794:1;11791;11784:12;11746:52;11833:9;11820:23;11872:1;11865:5;11862:12;11852:40;;11888:1;11885;11878:12;11927:521;12004:4;12010:6;12070:11;12057:25;12164:2;12160:7;12149:8;12133:14;12129:29;12125:43;12105:18;12101:68;12091:96;;12183:1;12180;12173:12;12091:96;12210:33;;12262:20;;;-1:-1:-1;;;;;;12294:30:192;;12291:50;;;12337:1;12334;12327:12;12291:50;12370:4;12358:17;;-1:-1:-1;12401:14:192;12397:27;;;12387:38;;12384:58;;;12438:1;12435;12428:12;12453:271;12636:6;12628;12623:3;12610:33;12592:3;12662:16;;12687:13;;;12662:16;12453:271;-1:-1:-1;12453:271:192:o;12729:421::-;12911:2;12896:18;;12944:1;12933:13;;12923:144;;12989:10;12984:3;12980:20;12977:1;12970:31;13024:4;13021:1;13014:15;13052:4;13049:1;13042:15;12923:144;13076:25;;;13132:2;13117:18;13110:34;12729:421;:::o;13342:545::-;13435:4;13441:6;13501:11;13488:25;13595:2;13591:7;13580:8;13564:14;13560:29;13556:43;13536:18;13532:68;13522:96;;13614:1;13611;13604:12;13522:96;13641:33;;13693:20;;;-1:-1:-1;;;;;;13725:30:192;;13722:50;;;13768:1;13765;13758:12;13722:50;13801:4;13789:17;;-1:-1:-1;13852:1:192;13848:14;;;13832;13828:35;13818:46;;13815:66;;;13877:1;13874;13867:12;13892:255;14012:19;;14051:2;14043:11;;14040:101;;;-1:-1:-1;;14112:2:192;14108:12;;;14105:1;14101:20;14097:33;14086:45;13892:255;;;;:::o;14152:331::-;14257:9;14268;14310:8;14298:10;14295:24;14292:44;;;14332:1;14329;14322:12;14292:44;14361:6;14351:8;14348:20;14345:40;;;14381:1;14378;14371:12;14345:40;-1:-1:-1;;14407:23:192;;;14452:25;;;;;-1:-1:-1;14152:331:192:o;14488:323::-;-1:-1:-1;;;;;;14608:19:192;;14684:11;;;;14715:1;14707:10;;14704:101;;;14792:2;14786;14779:3;14776:1;14772:11;14769:1;14765:19;14761:28;14757:2;14753:37;14749:46;14740:55;;14704:101;;;14488:323;;;;:::o;14816:709::-;-1:-1:-1;;;;;15157:15:192;;;15139:34;;15209:15;;15204:2;15189:18;;15182:43;15256:2;15241:18;;15234:34;;;15119:3;15299:2;15284:18;;15277:31;;;15082:4;;15331:62;;15373:19;;15365:6;15357;15331:62;:::i;:::-;15442:9;15434:6;15430:22;15424:3;15413:9;15409:19;15402:51;15470:49;15512:6;15504;15496;15470:49;:::i;:::-;15462:57;14816:709;-1:-1:-1;;;;;;;;;;14816:709:192:o;15530:690::-;15724:4;15770:1;15766;15761:3;15757:11;15753:19;15811:2;15803:6;15799:15;15788:9;15781:34;15863:2;15855:6;15851:15;15846:2;15835:9;15831:18;15824:43;;15903:6;15898:2;15887:9;15883:18;15876:34;15946:3;15941:2;15930:9;15926:18;15919:31;15979:6;15973:13;16023:6;16017:3;16006:9;16002:19;15995:35;16083:6;16078:2;16070:6;16066:15;16060:3;16049:9;16045:19;16039:51;16140:1;16134:3;16125:6;16114:9;16110:22;16106:32;16099:43;16210:3;16203:2;16199:7;16194:2;16186:6;16182:15;16178:29;16167:9;16163:45;16159:55;16151:63;;;15530:690;;;;;;;:::o;16225:225::-;16292:9;;;16313:11;;;16310:134;;;16366:10;16361:3;16357:20;16354:1;16347:31;16401:4;16398:1;16391:15;16429:4;16426:1;16419:15;16455:127;16516:10;16511:3;16507:20;16504:1;16497:31;16547:4;16544:1;16537:15;16571:4;16568:1;16561:15", "linkReferences": {}, "immutableReferences": {"69860": [{"start": 289, "length": 32}, {"start": 840, "length": 32}, {"start": 1072, "length": 32}, {"start": 1443, "length": 32}, {"start": 1778, "length": 32}, {"start": 2300, "length": 32}, {"start": 2508, "length": 32}, {"start": 2751, "length": 32}, {"start": 3806, "length": 32}, {"start": 3958, "length": 32}]}}, "methodIdentifiers": {"ALLOW_CALL_ROLE()": "e7815cd7", "CALLER_ROLE()": "774237fc", "DISALLOW_CALL_ROLE()": "5a6ca9f9", "SET_MERKLE_ROOT_ROLE()": "a95f4754", "allowCalls((address,address,bytes4)[])": "37b893bf", "allowedCallAt(uint256)": "3ab85463", "allowedCalls()": "6218e888", "disallowCalls((address,address,bytes4)[])": "3ae314e8", "getVerificationResult(address,address,uint256,bytes,(uint8,bytes,bytes32[]))": "b27fb509", "hashCall((address,address,bytes4))": "bffeda7d", "hashCall((address,address,uint256,bytes))": "d46c2491", "initialize(bytes)": "439fab91", "isAllowedCall(address,address,bytes)": "ba3c3485", "merkleRoot()": "2eb4a7ab", "setMerkleRoot(bytes32)": "7cb64759", "vault()": "fbfa77cf", "verifyCall(address,address,uint256,bytes,(uint8,bytes,bytes32[]))": "10749ebd"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name_\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"version_\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"bytes4\",\"name\":\"selector\",\"type\":\"bytes4\"}],\"name\":\"CompactCallAlreadyAllowed\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"bytes4\",\"name\":\"selector\",\"type\":\"bytes4\"}],\"name\":\"CompactCallNotFound\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Forbidden\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidLength\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"VerificationFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZeroValue\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"initParams\",\"type\":\"bytes\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"ALLOW_CALL_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"CALLER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DISALLOW_CALL_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SET_MERKLE_ROOT_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"bytes4\",\"name\":\"selector\",\"type\":\"bytes4\"}],\"internalType\":\"struct IVerifier.CompactCall[]\",\"name\":\"compactCalls\",\"type\":\"tuple[]\"}],\"name\":\"allowCalls\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"allowedCallAt\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"bytes4\",\"name\":\"selector\",\"type\":\"bytes4\"}],\"internalType\":\"struct IVerifier.CompactCall\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"allowedCalls\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"bytes4\",\"name\":\"selector\",\"type\":\"bytes4\"}],\"internalType\":\"struct IVerifier.CompactCall[]\",\"name\":\"compactCalls\",\"type\":\"tuple[]\"}],\"name\":\"disallowCalls\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"},{\"components\":[{\"internalType\":\"enum IVerifier.VerificationType\",\"name\":\"verificationType\",\"type\":\"uint8\"},{\"internalType\":\"bytes\",\"name\":\"verificationData\",\"type\":\"bytes\"},{\"internalType\":\"bytes32[]\",\"name\":\"proof\",\"type\":\"bytes32[]\"}],\"internalType\":\"struct IVerifier.VerificationPayload\",\"name\":\"payload\",\"type\":\"tuple\"}],\"name\":\"getVerificationResult\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"bytes4\",\"name\":\"selector\",\"type\":\"bytes4\"}],\"internalType\":\"struct IVerifier.CompactCall\",\"name\":\"call\",\"type\":\"tuple\"}],\"name\":\"hashCall\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"internalType\":\"struct IVerifier.ExtendedCall\",\"name\":\"call\",\"type\":\"tuple\"}],\"name\":\"hashCall\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"initParams\",\"type\":\"bytes\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"callData\",\"type\":\"bytes\"}],\"name\":\"isAllowedCall\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"merkleRoot\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"merkleRoot_\",\"type\":\"bytes32\"}],\"name\":\"setMerkleRoot\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"vault\",\"outputs\":[{\"internalType\":\"contract IAccessControl\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"},{\"components\":[{\"internalType\":\"enum IVerifier.VerificationType\",\"name\":\"verificationType\",\"type\":\"uint8\"},{\"internalType\":\"bytes\",\"name\":\"verificationData\",\"type\":\"bytes\"},{\"internalType\":\"bytes32[]\",\"name\":\"proof\",\"type\":\"bytes32[]\"}],\"internalType\":\"struct IVerifier.VerificationPayload\",\"name\":\"payload\",\"type\":\"tuple\"}],\"name\":\"verifyCall\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}]},\"events\":{\"Initialized(bytes)\":{\"params\":{\"initParams\":\"The initialization parameters.\"}},\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"}},\"kind\":\"dev\",\"methods\":{\"getVerificationResult(address,address,uint256,bytes,(uint8,bytes,bytes32[]))\":{\"returns\":{\"_0\":\"bool Returns whether a given call passes verification\"}},\"initialize(bytes)\":{\"params\":{\"initParams\":\"The initialization parameters.\"}}},\"version\":1},\"userdoc\":{\"errors\":{\"CompactCallAlreadyAllowed(address,address,bytes4)\":[{\"notice\":\"Thrown when attempting to allow an already allowed CompactCall\"}],\"CompactCallNotFound(address,address,bytes4)\":[{\"notice\":\"Thrown when attempting to disallow a non-existent CompactCall\"}],\"Forbidden()\":[{\"notice\":\"Thrown when a caller lacks necessary permissions\"}],\"InvalidLength()\":[{\"notice\":\"Thrown when input array lengths mismatch\"}],\"VerificationFailed()\":[{\"notice\":\"Thrown when a call fails verification\"}],\"ZeroValue()\":[{\"notice\":\"Thrown when a required value (e.g. address) is zero\"}]},\"events\":{\"Initialized(bytes)\":{\"notice\":\"Emitted once the entity has been initialized.\"}},\"kind\":\"user\",\"methods\":{\"ALLOW_CALL_ROLE()\":{\"notice\":\"Role identifier for allowing new CompactCalls\"},\"CALLER_ROLE()\":{\"notice\":\"Role identifier for permitted callers\"},\"DISALLOW_CALL_ROLE()\":{\"notice\":\"Role identifier for removing CompactCalls\"},\"SET_MERKLE_ROOT_ROLE()\":{\"notice\":\"Role identifier for setting Merkle root\"},\"allowCalls((address,address,bytes4)[])\":{\"notice\":\"Adds a list of CompactCalls to the allowlist\"},\"allowedCallAt(uint256)\":{\"notice\":\"Returns the compact call at a specific index\"},\"allowedCalls()\":{\"notice\":\"Returns number of currently allowed compact calls\"},\"disallowCalls((address,address,bytes4)[])\":{\"notice\":\"Removes a list of CompactCalls from the allowlist\"},\"hashCall((address,address,bytes4))\":{\"notice\":\"Computes the hash of a CompactCall\"},\"hashCall((address,address,uint256,bytes))\":{\"notice\":\"Computes the hash of an ExtendedCall\"},\"initialize(bytes)\":{\"notice\":\"Initializes the factory-created entity with arbitrary initialization data.\"},\"isAllowedCall(address,address,bytes)\":{\"notice\":\"Checks if a CompactCall is explicitly allowed\"},\"merkleRoot()\":{\"notice\":\"Returns the current Merkle root\"},\"setMerkleRoot(bytes32)\":{\"notice\":\"Sets the Merkle root used for verification\"},\"vault()\":{\"notice\":\"Returns the vault associated to this Verifier contract\"},\"verifyCall(address,address,uint256,bytes,(uint8,bytes,bytes32[]))\":{\"notice\":\"Validates a function call using the provided verification payload, reverts on failure\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/permissions/Verifier.sol\":\"Verifier\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0\",\"dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol\":{\"keccak256\":\"0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd\",\"dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol\":{\"keccak256\":\"0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd\",\"dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol\":{\"keccak256\":\"0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2\",\"dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/Hashes.sol\":{\"keccak256\":\"0x8cbd338f083224b4b6f0ff42cbda641a0a6c31ffcdca197452b97fe4d0918269\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f517dec5ba0c6491395acbf7f1d621f4e89e8f218bf5303c867b1c5ad70c6b11\",\"dweb:/ipfs/QmWmXHRLEw8W6ckth7NyYTU88YfvuS7xSsfae5ksL8qNUe\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/MerkleProof.sol\":{\"keccak256\":\"0x36a0c409c437a753cac9b92b75f93b0fbe92803bf2c8ff1517e54b247f166134\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f91ba472de411aa557cdbf6560c40750d87bd11c9060bc04d2ba7119af9d5a6\",\"dweb:/ipfs/QmQjtYo2i7dDvzCEzZ67bDoNSG4RrwMoxPWuqFmX5Xzpuw\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol\":{\"keccak256\":\"0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231\",\"dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y\"]},\"src/interfaces/factories/IFactoryEntity.sol\":{\"keccak256\":\"0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812\",\"dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx\"]},\"src/interfaces/permissions/ICustomVerifier.sol\":{\"keccak256\":\"0xdb343366ed86f63f8a94ff478c5c8e1d127888e4993c27fcb3bb3b51ae0cb8c7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://12407e89b20982a00961ccc03258e353962607747f214940d18b0989c11547c9\",\"dweb:/ipfs/Qmay19iWpwiV8LuvWccPjsazVU5Kkhme9QykFfLKfBmvSr\"]},\"src/interfaces/permissions/IVerifier.sol\":{\"keccak256\":\"0x4b42731d76efaffcb017577bd2a478d00a3058c69220d6ea8e26f30f74476be7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ff2a6f09a946dd8a470dfe495b2a64d24b849c6d071dc859589140fb1b983362\",\"dweb:/ipfs/Qmb6UoqkmaC8tJMqkMfjcWR7pn1WCwPwuKKvUbLBQbPcJS\"]},\"src/libraries/SlotLibrary.sol\":{\"keccak256\":\"0x35dcf16feec83c772add30250b0987ce29ccfbf308d04159701ebf9243444eeb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://97658d0c93ecc8174169e643d5a24c515b55905a2fcaaa6d6ec0188a3dd71a10\",\"dweb:/ipfs/QmS1z7ac8RbMvz15gVQ9YA1cCT5bs6KMHCiDhgcG8Pq8oK\"]},\"src/permissions/Verifier.sol\":{\"keccak256\":\"0xb02a18d274c3a7d8c77b7b0e011809874020f4fee778467b4d44c0d8b3f1c2a4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://961059227645bcd077202f5339ea04a07f056299bf8437319e651f5e4cdb4f0a\",\"dweb:/ipfs/QmcW55DPZ3PF3w7gfZ8zeb986qHZnVUy5FDPkpGHZt93j5\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "uint256", "name": "version_", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "bytes4", "name": "selector", "type": "bytes4"}], "type": "error", "name": "CompactCallAlreadyAllowed"}, {"inputs": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "bytes4", "name": "selector", "type": "bytes4"}], "type": "error", "name": "CompactCallNotFound"}, {"inputs": [], "type": "error", "name": "Forbidden"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [], "type": "error", "name": "InvalidLength"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [], "type": "error", "name": "VerificationFailed"}, {"inputs": [], "type": "error", "name": "ZeroValue"}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "initParams", "type": "bytes", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "ALLOW_CALL_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "CALLER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DISALLOW_CALL_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SET_MERKLE_ROOT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "struct IVerifier.CompactCall[]", "name": "compactCalls", "type": "tuple[]", "components": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "bytes4", "name": "selector", "type": "bytes4"}]}], "stateMutability": "nonpayable", "type": "function", "name": "allowCalls"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "allowedCallAt", "outputs": [{"internalType": "struct IVerifier.CompactCall", "name": "", "type": "tuple", "components": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "bytes4", "name": "selector", "type": "bytes4"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "allowedCalls", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "struct IVerifier.CompactCall[]", "name": "compactCalls", "type": "tuple[]", "components": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "bytes4", "name": "selector", "type": "bytes4"}]}], "stateMutability": "nonpayable", "type": "function", "name": "disallowCalls"}, {"inputs": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "struct IVerifier.VerificationPayload", "name": "payload", "type": "tuple", "components": [{"internalType": "enum IVerifier.VerificationType", "name": "verificationType", "type": "uint8"}, {"internalType": "bytes", "name": "verificationData", "type": "bytes"}, {"internalType": "bytes32[]", "name": "proof", "type": "bytes32[]"}]}], "stateMutability": "view", "type": "function", "name": "getVerificationResult", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "struct IVerifier.CompactCall", "name": "call", "type": "tuple", "components": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "bytes4", "name": "selector", "type": "bytes4"}]}], "stateMutability": "pure", "type": "function", "name": "hashCall", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "struct IVerifier.ExtendedCall", "name": "call", "type": "tuple", "components": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}]}], "stateMutability": "pure", "type": "function", "name": "hashCall", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "bytes", "name": "initParams", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}], "stateMutability": "view", "type": "function", "name": "isAllowedCall", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "merkleRoot", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "bytes32", "name": "merkleRoot_", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "setMerkleRoot"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "vault", "outputs": [{"internalType": "contract IAccessControl", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "struct IVerifier.VerificationPayload", "name": "payload", "type": "tuple", "components": [{"internalType": "enum IVerifier.VerificationType", "name": "verificationType", "type": "uint8"}, {"internalType": "bytes", "name": "verificationData", "type": "bytes"}, {"internalType": "bytes32[]", "name": "proof", "type": "bytes32[]"}]}], "stateMutability": "view", "type": "function", "name": "verifyCall"}], "devdoc": {"kind": "dev", "methods": {"getVerificationResult(address,address,uint256,bytes,(uint8,bytes,bytes32[]))": {"returns": {"_0": "bool Returns whether a given call passes verification"}}, "initialize(bytes)": {"params": {"initParams": "The initialization parameters."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"ALLOW_CALL_ROLE()": {"notice": "Role identifier for allowing new CompactCalls"}, "CALLER_ROLE()": {"notice": "Role identifier for permitted callers"}, "DISALLOW_CALL_ROLE()": {"notice": "Role identifier for removing CompactCalls"}, "SET_MERKLE_ROOT_ROLE()": {"notice": "Role identifier for setting <PERSON><PERSON><PERSON> root"}, "allowCalls((address,address,bytes4)[])": {"notice": "Adds a list of CompactCalls to the allowlist"}, "allowedCallAt(uint256)": {"notice": "Returns the compact call at a specific index"}, "allowedCalls()": {"notice": "Returns number of currently allowed compact calls"}, "disallowCalls((address,address,bytes4)[])": {"notice": "Removes a list of CompactCalls from the allowlist"}, "hashCall((address,address,bytes4))": {"notice": "Computes the hash of a CompactCall"}, "hashCall((address,address,uint256,bytes))": {"notice": "Computes the hash of an ExtendedCall"}, "initialize(bytes)": {"notice": "Initializes the factory-created entity with arbitrary initialization data."}, "isAllowedCall(address,address,bytes)": {"notice": "Checks if a CompactCall is explicitly allowed"}, "merkleRoot()": {"notice": "Returns the current <PERSON><PERSON><PERSON> root"}, "setMerkleRoot(bytes32)": {"notice": "Sets the <PERSON><PERSON><PERSON> root used for verification"}, "vault()": {"notice": "Returns the vault associated to this Verifier contract"}, "verifyCall(address,address,uint256,bytes,(uint8,bytes,bytes32[]))": {"notice": "Validates a function call using the provided verification payload, reverts on failure"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/permissions/Verifier.sol": "Verifier"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05", "urls": ["bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08", "dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530", "urls": ["bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0", "dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol": {"keccak256": "0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6", "urls": ["bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd", "dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol": {"keccak256": "0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58", "urls": ["bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd", "dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol": {"keccak256": "0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26", "urls": ["bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2", "dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/Hashes.sol": {"keccak256": "0x8cbd338f083224b4b6f0ff42cbda641a0a6c31ffcdca197452b97fe4d0918269", "urls": ["bzz-raw://f517dec5ba0c6491395acbf7f1d621f4e89e8f218bf5303c867b1c5ad70c6b11", "dweb:/ipfs/QmWmXHRLEw8W6ckth7NyYTU88YfvuS7xSsfae5ksL8qNUe"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/MerkleProof.sol": {"keccak256": "0x36a0c409c437a753cac9b92b75f93b0fbe92803bf2c8ff1517e54b247f166134", "urls": ["bzz-raw://0f91ba472de411aa557cdbf6560c40750d87bd11c9060bc04d2ba7119af9d5a6", "dweb:/ipfs/QmQjtYo2i7dDvzCEzZ67bDoNSG4RrwMoxPWuqFmX5Xzpuw"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol": {"keccak256": "0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06", "urls": ["bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231", "dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y"], "license": "MIT"}, "src/interfaces/factories/IFactoryEntity.sol": {"keccak256": "0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5", "urls": ["bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812", "dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx"], "license": "BUSL-1.1"}, "src/interfaces/permissions/ICustomVerifier.sol": {"keccak256": "0xdb343366ed86f63f8a94ff478c5c8e1d127888e4993c27fcb3bb3b51ae0cb8c7", "urls": ["bzz-raw://12407e89b20982a00961ccc03258e353962607747f214940d18b0989c11547c9", "dweb:/ipfs/Qmay19iWpwiV8LuvWccPjsazVU5Kkhme9QykFfLKfBmvSr"], "license": "BUSL-1.1"}, "src/interfaces/permissions/IVerifier.sol": {"keccak256": "0x4b42731d76efaffcb017577bd2a478d00a3058c69220d6ea8e26f30f74476be7", "urls": ["bzz-raw://ff2a6f09a946dd8a470dfe495b2a64d24b849c6d071dc859589140fb1b983362", "dweb:/ipfs/Qmb6UoqkmaC8tJMqkMfjcWR7pn1WCwPwuKKvUbLBQbPcJS"], "license": "BUSL-1.1"}, "src/libraries/SlotLibrary.sol": {"keccak256": "0x35dcf16feec83c772add30250b0987ce29ccfbf308d04159701ebf9243444eeb", "urls": ["bzz-raw://97658d0c93ecc8174169e643d5a24c515b55905a2fcaaa6d6ec0188a3dd71a10", "dweb:/ipfs/QmS1z7ac8RbMvz15gVQ9YA1cCT5bs6KMHCiDhgcG8Pq8oK"], "license": "BUSL-1.1"}, "src/permissions/Verifier.sol": {"keccak256": "0xb02a18d274c3a7d8c77b7b0e011809874020f4fee778467b4d44c0d8b3f1c2a4", "urls": ["bzz-raw://961059227645bcd077202f5339ea04a07f056299bf8437319e651f5e4cdb4f0a", "dweb:/ipfs/QmcW55DPZ3PF3w7gfZ8zeb986qHZnVUy5FDPkpGHZt93j5"], "license": "BUSL-1.1"}}, "version": 1}, "id": 131}