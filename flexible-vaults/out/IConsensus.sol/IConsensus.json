{"abi": [{"type": "function", "name": "addSigner", "inputs": [{"name": "signer", "type": "address", "internalType": "address"}, {"name": "threshold_", "type": "uint256", "internalType": "uint256"}, {"name": "signatureType", "type": "uint8", "internalType": "enum IConsensus.SignatureType"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "checkSignatures", "inputs": [{"name": "orderHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "signatures", "type": "tuple[]", "internalType": "struct IConsensus.Signature[]", "components": [{"name": "signer", "type": "address", "internalType": "address"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "initParams", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "removeSigner", "inputs": [{"name": "signer", "type": "address", "internalType": "address"}, {"name": "threshold_", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "requireValidSignatures", "inputs": [{"name": "orderHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "signatures", "type": "tuple[]", "internalType": "struct IConsensus.Signature[]", "components": [{"name": "signer", "type": "address", "internalType": "address"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}]}], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "threshold", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "signerAt", "inputs": [{"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "signers", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "threshold", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "event", "name": "Initialized", "inputs": [{"name": "initParams", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "SignerAdded", "inputs": [{"name": "signer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "signatureType", "type": "uint8", "indexed": false, "internalType": "enum IConsensus.SignatureType"}], "anonymous": false}, {"type": "event", "name": "SignerRemoved", "inputs": [{"name": "signer", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "ThresholdSet", "inputs": [{"name": "threshold", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "InvalidSignatures", "inputs": [{"name": "orderHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "signatures", "type": "tuple[]", "internalType": "struct IConsensus.Signature[]", "components": [{"name": "signer", "type": "address", "internalType": "address"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}]}]}, {"type": "error", "name": "InvalidThreshold", "inputs": [{"name": "threshold", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "SignerAlreadyExists", "inputs": [{"name": "signer", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "SignerNotFound", "inputs": [{"name": "signer", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ZeroAddress", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"addSigner(address,uint256,uint8)": "9aa1a31a", "checkSignatures(bytes32,(address,bytes)[])": "b31078d3", "initialize(bytes)": "439fab91", "isSigner(address)": "7df73e27", "removeSigner(address,uint256)": "3bad5426", "requireValidSignatures(bytes32,(address,bytes)[])": "faa4da34", "setThreshold(uint256)": "960bfe04", "signerAt(uint256)": "fa3e6da4", "signers()": "46f0975a", "threshold()": "42cde4e8"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"orderHash\",\"type\":\"bytes32\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"}],\"internalType\":\"struct IConsensus.Signature[]\",\"name\":\"signatures\",\"type\":\"tuple[]\"}],\"name\":\"InvalidSignatures\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"threshold\",\"type\":\"uint256\"}],\"name\":\"InvalidThreshold\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"}],\"name\":\"SignerAlreadyExists\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"}],\"name\":\"SignerNotFound\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZeroAddress\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"initParams\",\"type\":\"bytes\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"enum IConsensus.SignatureType\",\"name\":\"signatureType\",\"type\":\"uint8\"}],\"name\":\"SignerAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"}],\"name\":\"SignerRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"threshold\",\"type\":\"uint256\"}],\"name\":\"ThresholdSet\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"threshold_\",\"type\":\"uint256\"},{\"internalType\":\"enum IConsensus.SignatureType\",\"name\":\"signatureType\",\"type\":\"uint8\"}],\"name\":\"addSigner\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"orderHash\",\"type\":\"bytes32\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"}],\"internalType\":\"struct IConsensus.Signature[]\",\"name\":\"signatures\",\"type\":\"tuple[]\"}],\"name\":\"checkSignatures\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"initParams\",\"type\":\"bytes\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"isSigner\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"threshold_\",\"type\":\"uint256\"}],\"name\":\"removeSigner\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"orderHash\",\"type\":\"bytes32\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"}],\"internalType\":\"struct IConsensus.Signature[]\",\"name\":\"signatures\",\"type\":\"tuple[]\"}],\"name\":\"requireValidSignatures\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"threshold\",\"type\":\"uint256\"}],\"name\":\"setThreshold\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"signerAt\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"signers\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"threshold\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Enables off-chain consensus-based authorization for deposit and redemption queues or other vault operations. # Overview The `Consensus` contract acts as a cryptographic gatekeeper that validates signed messages from a trusted set of signers. It supports both EIP-712 structured signatures and EIP-1271 contract-based signature validation, enabling: - Multisignature validation for off-chain approvals. - Compatibility with externally owned accounts (EOAs) and smart contract wallets (e.g., Safe). This interface is typically used by modules like `SignatureQueue` that rely on off-chain consensus to approve user actions without requiring full on-chain execution or delays.\",\"events\":{\"Initialized(bytes)\":{\"params\":{\"initParams\":\"The initialization parameters.\"}}},\"kind\":\"dev\",\"methods\":{\"addSigner(address,uint256,uint8)\":{\"params\":{\"signatureType\":\"Signature type used by this signer\",\"signer\":\"Signer address to add\",\"threshold_\":\"New threshold to set after adding\"}},\"checkSignatures(bytes32,(address,bytes)[])\":{\"params\":{\"orderHash\":\"The message hash that was signed\",\"signatures\":\"List of (signer, signature) entries\"}},\"initialize(bytes)\":{\"params\":{\"initParams\":\"The initialization parameters.\"}},\"isSigner(address)\":{\"params\":{\"account\":\"Address to check\"}},\"removeSigner(address,uint256)\":{\"params\":{\"signer\":\"Signer address to remove\",\"threshold_\":\"New threshold to set after removal\"}},\"requireValidSignatures(bytes32,(address,bytes)[])\":{\"params\":{\"orderHash\":\"The message hash that was signed\",\"signatures\":\"List of (signer, signature) entries\"}},\"setThreshold(uint256)\":{\"params\":{\"threshold\":\"New threshold (must be > 0 and <= signer count)\"}},\"signerAt(uint256)\":{\"params\":{\"index\":\"Index into the signer list\"}}},\"title\":\"IConsensus\",\"version\":1},\"userdoc\":{\"errors\":{\"InvalidSignatures(bytes32,(address,bytes)[])\":[{\"notice\":\"Thrown when provided signatures are invalid or below the required threshold\"}],\"InvalidThreshold(uint256)\":[{\"notice\":\"Thrown when attempting to set an invalid threshold (zero or above signer count)\"}],\"SignerAlreadyExists(address)\":[{\"notice\":\"Thrown when trying to add a signer that already exists\"}],\"SignerNotFound(address)\":[{\"notice\":\"Thrown when trying to remove a signer that isn't in the signer set\"}],\"ZeroAddress()\":[{\"notice\":\"Thrown when a provided address is the zero address\"}]},\"events\":{\"Initialized(bytes)\":{\"notice\":\"Emitted once the entity has been initialized.\"},\"SignerAdded(address,uint8)\":{\"notice\":\"Emitted when a signer is added\"},\"SignerRemoved(address)\":{\"notice\":\"Emitted when a signer is removed\"},\"ThresholdSet(uint256)\":{\"notice\":\"Emitted when the threshold is changed\"}},\"kind\":\"user\",\"methods\":{\"addSigner(address,uint256,uint8)\":{\"notice\":\"Adds a new signer and updates the threshold\"},\"checkSignatures(bytes32,(address,bytes)[])\":{\"notice\":\"Returns true if the given signatures are valid and meet the current threshold\"},\"initialize(bytes)\":{\"notice\":\"Initializes the factory-created entity with arbitrary initialization data.\"},\"isSigner(address)\":{\"notice\":\"Checks if the given address is a registered signer\"},\"removeSigner(address,uint256)\":{\"notice\":\"Removes a signer and updates the threshold\"},\"requireValidSignatures(bytes32,(address,bytes)[])\":{\"notice\":\"Verifies the given signatures or reverts if invalid\"},\"setThreshold(uint256)\":{\"notice\":\"Updates the threshold required to approve an operation\"},\"signerAt(uint256)\":{\"notice\":\"Returns signer address and signature type at a given index\"},\"signers()\":{\"notice\":\"Returns the number of registered signers\"},\"threshold()\":{\"notice\":\"Returns the current threshold of required valid signatures\"}},\"notice\":\"Interface for a Consensus contract that validates multisignature approvals via EIP-712 and EIP-1271.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/permissions/IConsensus.sol\":\"IConsensus\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol\":{\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6\",\"dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol\":{\"keccak256\":\"0x3d4cd07258d675b6dfa3d9dfd623a77b38fb9935f4a5381ff4bc969948720976\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://30abb60a4d98ad9a33de4fa5e9072342e12df3a79e9c71b5f59ce049ae8cd535\",\"dweb:/ipfs/QmT6NXJDjsf8H14nPYQsFELDB8q5ZTmG9cjdX2bEQJW7ST\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol\":{\"keccak256\":\"0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd\",\"dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol\":{\"keccak256\":\"0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd\",\"dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol\":{\"keccak256\":\"0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2\",\"dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol\":{\"keccak256\":\"0x69f54c02b7d81d505910ec198c11ed4c6a728418a868b906b4a0cf29946fda84\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8e25e4bdb7ae1f21d23bfee996e22736fc0ab44cfabedac82a757b1edc5623b9\",\"dweb:/ipfs/QmQdWQvB6JCP9ZMbzi8EvQ1PTETqkcTWrbcVurS7DKpa5n\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableMap.sol\":{\"keccak256\":\"0x5d0c69ef32eae686acc22acfb1a54df9c91356e69adec593aa69eba83ae8f6f4\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f7a9fca2cb3d802c4f8a2d0fda84ccc7f880264885801c7df08373501dd22282\",\"dweb:/ipfs/QmdtM2Loz9NzNTuQ3RXNQCPBjoPUJw35Ukpt1MSM9XH3cD\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol\":{\"keccak256\":\"0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231\",\"dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y\"]},\"src/interfaces/factories/IFactoryEntity.sol\":{\"keccak256\":\"0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812\",\"dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx\"]},\"src/interfaces/permissions/IConsensus.sol\":{\"keccak256\":\"0x4a454dfa9c1a3852c45341d9eb076867f5b5c043cbb5b79f9f8c3d7c00fcc9d6\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://f4993f2afd615e45e9836e581b42518363be7c1a2dd7f842473b18cb7339a3c2\",\"dweb:/ipfs/QmXZSRP7k8dP49gD1ZQsgAh3MRRLUWcRdhtHi1JrkCz4Ka\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "bytes32", "name": "orderHash", "type": "bytes32"}, {"internalType": "struct IConsensus.Signature[]", "name": "signatures", "type": "tuple[]", "components": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}]}], "type": "error", "name": "InvalidSignatures"}, {"inputs": [{"internalType": "uint256", "name": "threshold", "type": "uint256"}], "type": "error", "name": "InvalidThreshold"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}], "type": "error", "name": "SignerAlreadyExists"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}], "type": "error", "name": "SignerNotFound"}, {"inputs": [], "type": "error", "name": "ZeroAddress"}, {"inputs": [{"internalType": "bytes", "name": "initParams", "type": "bytes", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address", "indexed": true}, {"internalType": "enum IConsensus.SignatureType", "name": "signatureType", "type": "uint8", "indexed": false}], "type": "event", "name": "SignerAdded", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address", "indexed": true}], "type": "event", "name": "SignerRemoved", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "threshold", "type": "uint256", "indexed": true}], "type": "event", "name": "ThresholdSet", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "uint256", "name": "threshold_", "type": "uint256"}, {"internalType": "enum IConsensus.SignatureType", "name": "signatureType", "type": "uint8"}], "stateMutability": "nonpayable", "type": "function", "name": "addSigner"}, {"inputs": [{"internalType": "bytes32", "name": "orderHash", "type": "bytes32"}, {"internalType": "struct IConsensus.Signature[]", "name": "signatures", "type": "tuple[]", "components": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}]}], "stateMutability": "view", "type": "function", "name": "checkSignatures", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "bytes", "name": "initParams", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "uint256", "name": "threshold_", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "removeSigner"}, {"inputs": [{"internalType": "bytes32", "name": "orderHash", "type": "bytes32"}, {"internalType": "struct IConsensus.Signature[]", "name": "signatures", "type": "tuple[]", "components": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}]}], "stateMutability": "view", "type": "function", "name": "requireValidSignatures"}, {"inputs": [{"internalType": "uint256", "name": "threshold", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "signerAt", "outputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "signers", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "threshold", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {"addSigner(address,uint256,uint8)": {"params": {"signatureType": "Signature type used by this signer", "signer": "Signer address to add", "threshold_": "New threshold to set after adding"}}, "checkSignatures(bytes32,(address,bytes)[])": {"params": {"orderHash": "The message hash that was signed", "signatures": "List of (signer, signature) entries"}}, "initialize(bytes)": {"params": {"initParams": "The initialization parameters."}}, "isSigner(address)": {"params": {"account": "Address to check"}}, "removeSigner(address,uint256)": {"params": {"signer": "Signer address to remove", "threshold_": "New threshold to set after removal"}}, "requireValidSignatures(bytes32,(address,bytes)[])": {"params": {"orderHash": "The message hash that was signed", "signatures": "List of (signer, signature) entries"}}, "setThreshold(uint256)": {"params": {"threshold": "New threshold (must be > 0 and <= signer count)"}}, "signerAt(uint256)": {"params": {"index": "Index into the signer list"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"addSigner(address,uint256,uint8)": {"notice": "Adds a new signer and updates the threshold"}, "checkSignatures(bytes32,(address,bytes)[])": {"notice": "Returns true if the given signatures are valid and meet the current threshold"}, "initialize(bytes)": {"notice": "Initializes the factory-created entity with arbitrary initialization data."}, "isSigner(address)": {"notice": "Checks if the given address is a registered signer"}, "removeSigner(address,uint256)": {"notice": "Removes a signer and updates the threshold"}, "requireValidSignatures(bytes32,(address,bytes)[])": {"notice": "Verifies the given signatures or reverts if invalid"}, "setThreshold(uint256)": {"notice": "Updates the threshold required to approve an operation"}, "signerAt(uint256)": {"notice": "Returns signer address and signature type at a given index"}, "signers()": {"notice": "Returns the number of registered signers"}, "threshold()": {"notice": "Returns the current threshold of required valid signatures"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/permissions/IConsensus.sol": "IConsensus"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"keccak256": "0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a", "urls": ["bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6", "dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05", "urls": ["bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08", "dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol": {"keccak256": "0x3d4cd07258d675b6dfa3d9dfd623a77b38fb9935f4a5381ff4bc969948720976", "urls": ["bzz-raw://30abb60a4d98ad9a33de4fa5e9072342e12df3a79e9c71b5f59ce049ae8cd535", "dweb:/ipfs/QmT6NXJDjsf8H14nPYQsFELDB8q5ZTmG9cjdX2bEQJW7ST"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol": {"keccak256": "0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6", "urls": ["bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd", "dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol": {"keccak256": "0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58", "urls": ["bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd", "dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol": {"keccak256": "0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26", "urls": ["bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2", "dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol": {"keccak256": "0x69f54c02b7d81d505910ec198c11ed4c6a728418a868b906b4a0cf29946fda84", "urls": ["bzz-raw://8e25e4bdb7ae1f21d23bfee996e22736fc0ab44cfabedac82a757b1edc5623b9", "dweb:/ipfs/QmQdWQvB6JCP9ZMbzi8EvQ1PTETqkcTWrbcVurS7DKpa5n"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableMap.sol": {"keccak256": "0x5d0c69ef32eae686acc22acfb1a54df9c91356e69adec593aa69eba83ae8f6f4", "urls": ["bzz-raw://f7a9fca2cb3d802c4f8a2d0fda84ccc7f880264885801c7df08373501dd22282", "dweb:/ipfs/QmdtM2Loz9NzNTuQ3RXNQCPBjoPUJw35Ukpt1MSM9XH3cD"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol": {"keccak256": "0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06", "urls": ["bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231", "dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y"], "license": "MIT"}, "src/interfaces/factories/IFactoryEntity.sol": {"keccak256": "0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5", "urls": ["bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812", "dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx"], "license": "BUSL-1.1"}, "src/interfaces/permissions/IConsensus.sol": {"keccak256": "0x4a454dfa9c1a3852c45341d9eb076867f5b5c043cbb5b79f9f8c3d7c00fcc9d6", "urls": ["bzz-raw://f4993f2afd615e45e9836e581b42518363be7c1a2dd7f842473b18cb7339a3c2", "dweb:/ipfs/QmXZSRP7k8dP49gD1ZQsgAh3MRRLUWcRdhtHi1JrkCz4Ka"], "license": "BUSL-1.1"}}, "version": 1}, "id": 103}