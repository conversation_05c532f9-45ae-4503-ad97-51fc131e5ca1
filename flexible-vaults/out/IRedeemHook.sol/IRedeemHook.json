{"abi": [{"type": "function", "name": "callHook", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}, {"name": "assets", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getLiquidAssets", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}], "outputs": [{"name": "assets", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"callHook(address,uint256)": "c3dcdb04", "getLiquidAssets(address)": "15ea0936"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"name\":\"callHook\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"}],\"name\":\"getLiquidAssets\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{\"callHook(address,uint256)\":{\"details\":\"This function is called via `delegatecall` by the ShareModule or Vault.\",\"params\":{\"asset\":\"The address of the ERC20 asset being processed.\",\"assets\":\"The amount of the asset involved in the operation.\"}},\"getLiquidAssets(address)\":{\"details\":\"Used by queues to determine how much can be processed in the current redemption cycle.\",\"params\":{\"asset\":\"The address of the ERC20 asset to check.\"},\"returns\":{\"assets\":\"The amount of the asset that is liquid and available.\"}}},\"title\":\"IRedeemHook\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"callHook(address,uint256)\":{\"notice\":\"Executes custom logic for the given asset and amount during queue processing.\"},\"getLiquidAssets(address)\":{\"notice\":\"Returns the amount of liquid (immediately withdrawable) assets available for a given token.\"}},\"notice\":\"Interface for redeem-side hooks that implement custom logic during asset redemptions.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/hooks/IRedeemHook.sol\":\"IRedeemHook\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"src/interfaces/hooks/IHook.sol\":{\"keccak256\":\"0xf414604a4cbf1feb04f976a806eb2825fa7dcce6815e209fc7aa50c6fe845bb3\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://a727a899804756179d294bfb0975487e8c6ca11f68aa741604d400ec6dfbf241\",\"dweb:/ipfs/QmRqsAigFe71aHAG5SgyKQKVpnHoiVyiP6VmnkxFK9KRz9\"]},\"src/interfaces/hooks/IRedeemHook.sol\":{\"keccak256\":\"0xd3df4e8013847ea38f603529d1062e6fccfc757ea62339bfbdd72719d2f7fbb1\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cddd2d10f020fa96f03b728dfcf8c7874942a19d871414010437ca3cdaef2974\",\"dweb:/ipfs/QmUZwodA14vQg3ntpzF6YxVQ2JCDmjXgQKrzE6HzJ19Mq8\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "assets", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "callHook"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getLiquidAssets", "outputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {"callHook(address,uint256)": {"details": "This function is called via `delegatecall` by the ShareModule or Vault.", "params": {"asset": "The address of the ERC20 asset being processed.", "assets": "The amount of the asset involved in the operation."}}, "getLiquidAssets(address)": {"details": "Used by queues to determine how much can be processed in the current redemption cycle.", "params": {"asset": "The address of the ERC20 asset to check."}, "returns": {"assets": "The amount of the asset that is liquid and available."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"callHook(address,uint256)": {"notice": "Executes custom logic for the given asset and amount during queue processing."}, "getLiquidAssets(address)": {"notice": "Returns the amount of liquid (immediately withdrawable) assets available for a given token."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/hooks/IRedeemHook.sol": "IRedeemHook"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"src/interfaces/hooks/IHook.sol": {"keccak256": "0xf414604a4cbf1feb04f976a806eb2825fa7dcce6815e209fc7aa50c6fe845bb3", "urls": ["bzz-raw://a727a899804756179d294bfb0975487e8c6ca11f68aa741604d400ec6dfbf241", "dweb:/ipfs/QmRqsAigFe71aHAG5SgyKQKVpnHoiVyiP6VmnkxFK9KRz9"], "license": "BUSL-1.1"}, "src/interfaces/hooks/IRedeemHook.sol": {"keccak256": "0xd3df4e8013847ea38f603529d1062e6fccfc757ea62339bfbdd72719d2f7fbb1", "urls": ["bzz-raw://cddd2d10f020fa96f03b728dfcf8c7874942a19d871414010437ca3cdaef2974", "dweb:/ipfs/QmUZwodA14vQg3ntpzF6YxVQ2JCDmjXgQKrzE6HzJ19Mq8"], "license": "BUSL-1.1"}}, "version": 1}, "id": 91}