{"abi": [{"type": "function", "name": "DOMAIN_SEPARATOR", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "name_", "type": "string", "internalType": "string"}, {"name": "symbol_", "type": "string", "internalType": "string"}, {"name": "decimals_", "type": "uint8", "internalType": "uint8"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "nonces", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "permit", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "v", "type": "uint8", "internalType": "uint8"}, {"name": "r", "type": "bytes32", "internalType": "bytes32"}, {"name": "s", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}], "bytecode": {"object": "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", "sourceMap": "369:7950:29:-:0;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "369:7950:29:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;677:92;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;3057:221;;;;;;:::i;:::-;;:::i;:::-;;;1039:14:192;;1032:22;1014:41;;1002:2;987:18;3057:221:29;874:187:192;2504:365:29;;;;;;:::i;:::-;;:::i;:::-;;1322:100;1403:12;;1322:100;;;2847:25:192;;;2835:2;2820:18;1322:100:29;2701:177:192;3578:472:29;;;;;;:::i;:::-;;:::i;877:92::-;953:9;;877:92;;953:9;;;;3358:36:192;;3346:2;3331:18;877:92:29;3216:184:192;5427:178:29;;;:::i;1428:116::-;;;;;;:::i;:::-;-1:-1:-1;;;;;1520:17:29;1494:7;1520:17;;;:10;:17;;;;;;;1428:116;1970:41;;;;;;:::i;:::-;;;;;;;;;;;;;;775:96;;;:::i;3284:288::-;;;;;;:::i;:::-;;:::i;4239:1182::-;;;;;;:::i;:::-;;:::i;1550:142::-;;;;;;:::i;:::-;-1:-1:-1;;;;;1659:17:29;;;1633:7;1659:17;;;:10;:17;;;;;;;;:26;;;;;;;;;;;;;1550:142;677:92;725:13;757:5;750:12;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;677:92;:::o;3057:221::-;3167:10;3140:4;3156:22;;;:10;:22;;;;;;;;-1:-1:-1;;;;;3156:31:29;;;;;;;;;;:40;;;3212:37;3140:4;;3156:31;;3212:37;;;;3190:6;2847:25:192;;2835:2;2820:18;;2701:177;3212:37:29;;;;;;;;-1:-1:-1;3267:4:29;3057:221;;;;;:::o;2504:365::-;2611:11;;;;2610:12;2602:44;;;;-1:-1:-1;;;2602:44:29;;5241:2:192;2602:44:29;;;5223:21:192;5280:2;5260:18;;;5253:30;-1:-1:-1;;;5299:18:192;;;5292:49;5358:18;;2602:44:29;;;;;;;;;2657:5;:13;2665:5;2657;:13;:::i;:::-;-1:-1:-1;2680:7:29;:17;2690:7;2680;:17;:::i;:::-;-1:-1:-1;2707:9:29;:21;;-1:-1:-1;;2707:21:29;;;;;;;2758:14;:12;:14::i;:::-;2739:16;:33;2809:24;:22;:24::i;:::-;2782;:51;-1:-1:-1;;2844:11:29;:18;;-1:-1:-1;;2844:18:29;2858:4;2844:18;;;-1:-1:-1;2504:365:29:o;3578:472::-;-1:-1:-1;;;;;3709:16:29;;3675:4;3709:16;;;:10;:16;;;;;;;;3726:10;3709:28;;;;;;;;-1:-1:-1;;3788:22:29;;3784:80;;3843:21;3848:7;3857:6;3843:4;:21::i;:::-;-1:-1:-1;;;;;3812:16:29;;;;;;:10;:16;;;;;;;;3829:10;3812:28;;;;;;;:52;3784:80;-1:-1:-1;;;;;3899:16:29;;;;;;:10;:16;;;;;;3894:30;;3917:6;3894:4;:30::i;:::-;-1:-1:-1;;;;;3875:16:29;;;;;;;:10;:16;;;;;;:49;;;;3956:14;;;;;;;3951:28;;3972:6;3951:4;:28::i;:::-;-1:-1:-1;;;;;3934:14:29;;;;;;;:10;:14;;;;;;;:45;;;;3995:26;;;;;;;;;;4014:6;2847:25:192;;2835:2;2820:18;;2701:177;3995:26:29;;;;;;;;-1:-1:-1;4039:4:29;;3578:472;-1:-1:-1;;;;3578:472:29:o;5427:178::-;5484:7;5528:16;;5510:14;:12;:14::i;:::-;:34;:88;;5574:24;:22;:24::i;:::-;5503:95;;5427:178;:::o;5510:88::-;-1:-1:-1;5547:24:29;;;5427:178::o;775:96::-;825:13;857:7;850:14;;;;;:::i;3284:288::-;3420:10;3363:4;3409:22;;;:10;:22;;;;;;3404:36;;3433:6;3404:4;:36::i;:::-;3390:10;3379:22;;;;:10;:22;;;;;;:61;;;;-1:-1:-1;;;;;3472:14:29;;;;;;3467:28;;3488:6;3467:4;:28::i;:::-;-1:-1:-1;;;;;3450:14:29;;;;;;:10;:14;;;;;;;:45;;;;3511:32;;3520:10;;3511:32;;;;3536:6;2847:25:192;;2835:2;2820:18;;2701:177;4239:1182:29;4416:15;4404:8;:27;;4396:63;;;;-1:-1:-1;;;4396:63:29;;7759:2:192;4396:63:29;;;7741:21:192;7798:2;7778:18;;;7771:30;7837:25;7817:18;;;7810:53;7880:18;;4396:63:29;7557:347:192;4396:63:29;4470:24;4497:717;4617:18;:16;:18::i;:::-;-1:-1:-1;;;;;5026:13:29;;;;;;:6;:13;;;;;:15;;4732:157;;4919:5;;4954:7;;4991:5;;5026:15;;:13;:15;;;:::i;:::-;;;;-1:-1:-1;4692:413:29;;;;;;8468:25:192;;;;-1:-1:-1;;;;;8567:15:192;;;8547:18;;;8540:43;8619:15;;;;8599:18;;;8592:43;8651:18;;;8644:34;8694:19;;;8687:35;8738:19;;;8731:35;;;8440:19;;4692:413:29;;;;;;;;;;;;4657:470;;;;;;4547:598;;;;;;;;-1:-1:-1;;;9035:27:192;;9087:1;9078:11;;9071:27;;;;9123:2;9114:12;;9107:28;9160:2;9151:12;;8777:392;4547:598:29;;;;-1:-1:-1;;4547:598:29;;;;;;;;;4520:639;;4547:598;4520:639;;;;4497:717;;;;;;;;;9401:25:192;9474:4;9462:17;;9442:18;;;9435:45;9496:18;;;9489:34;;;9539:18;;;9532:34;;;9373:19;;4497:717:29;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;4497:717:29;;-1:-1:-1;;4497:717:29;;;-1:-1:-1;;;;;;;5233:30:29;;;;;;:59;;;5287:5;-1:-1:-1;;;;;5267:25:29;:16;-1:-1:-1;;;;;5267:25:29;;5233:59;5225:86;;;;-1:-1:-1;;;5225:86:29;;9779:2:192;5225:86:29;;;9761:21:192;9818:2;9798:18;;;9791:30;-1:-1:-1;;;9837:18:192;;;9830:44;9891:18;;5225:86:29;9577:338:192;5225:86:29;-1:-1:-1;;;;;5322:28:29;;;;;;;:10;:28;;;;;;;;:37;;;;;;;;;;;;;:45;;;5383:31;2847:25:192;;;5322:37:29;;5383:31;;;;;2820:18:192;5383:31:29;;;;;;;4386:1035;4239:1182;;;;;;;:::o;8017:300::-;8063:15;8140:12;;8297:13;;;;:::i;:::-;8287:23;;8080:237;;8017:300;:::o;5611:404::-;5676:7;5753:95;5882:5;5866:23;;;;;;:::i;:::-;;;;;;;;5907:14;5939;:12;:14::i;:::-;5725:273;;;;;;11029:25:192;;;;11070:18;;11063:34;;;;11113:18;;;11106:34;11156:18;;;11149:34;5979:4:29;11199:19:192;;;11192:61;11001:19;;5725:273:29;;;;;;;;;;;;5702:306;;;;;;5695:313;;5611:404;:::o;7038:154::-;7097:7;7129:1;7124;:6;;7116:47;;;;-1:-1:-1;;;7116:47:29;;11466:2:192;7116:47:29;;;11448:21:192;11505:2;11485:18;;;11478:30;11544;11524:18;;;11517:58;11592:18;;7116:47:29;11264:352:192;7116:47:29;7180:5;7184:1;7180;:5;:::i;:::-;7173:12;7038:154;-1:-1:-1;;;7038:154:29:o;6859:173::-;6918:7;;6949:5;6953:1;6949;:5;:::i;:::-;6937:17;;6977:1;6972;:6;;6964:43;;;;-1:-1:-1;;;6964:43:29;;12086:2:192;6964:43:29;;;12068:21:192;12125:2;12105:18;;;12098:30;12164:26;12144:18;;;12137:54;12208:18;;6964:43:29;11884:348:192;7735:276:29;7918:9;;7735:276::o;14:418:192:-;163:2;152:9;145:21;126:4;195:6;189:13;238:6;233:2;222:9;218:18;211:34;297:6;292:2;284:6;280:15;275:2;264:9;260:18;254:50;353:1;348:2;339:6;328:9;324:22;320:31;313:42;423:2;416;412:7;407:2;399:6;395:15;391:29;380:9;376:45;372:54;364:62;;;14:418;;;;:::o;437:173::-;505:20;;-1:-1:-1;;;;;554:31:192;;544:42;;534:70;;600:1;597;590:12;534:70;437:173;;;:::o;615:254::-;683:6;691;744:2;732:9;723:7;719:23;715:32;712:52;;;760:1;757;750:12;712:52;783:29;802:9;783:29;:::i;:::-;773:39;859:2;844:18;;;;831:32;;-1:-1:-1;;;615:254:192:o;1066:127::-;1127:10;1122:3;1118:20;1115:1;1108:31;1158:4;1155:1;1148:15;1182:4;1179:1;1172:15;1198:719;1241:5;1294:3;1287:4;1279:6;1275:17;1271:27;1261:55;;1312:1;1309;1302:12;1261:55;1348:6;1335:20;1374:18;1411:2;1407;1404:10;1401:36;;;1417:18;;:::i;:::-;1492:2;1486:9;1460:2;1546:13;;-1:-1:-1;;1542:22:192;;;1566:2;1538:31;1534:40;1522:53;;;1590:18;;;1610:22;;;1587:46;1584:72;;;1636:18;;:::i;:::-;1676:10;1672:2;1665:22;1711:2;1703:6;1696:18;1757:3;1750:4;1745:2;1737:6;1733:15;1729:26;1726:35;1723:55;;;1774:1;1771;1764:12;1723:55;1838:2;1831:4;1823:6;1819:17;1812:4;1804:6;1800:17;1787:54;1885:1;1878:4;1873:2;1865:6;1861:15;1857:26;1850:37;1905:6;1896:15;;;;;;1198:719;;;;:::o;1922:156::-;1988:20;;2048:4;2037:16;;2027:27;;2017:55;;2068:1;2065;2058:12;2083:613;2178:6;2186;2194;2247:2;2235:9;2226:7;2222:23;2218:32;2215:52;;;2263:1;2260;2253:12;2215:52;2303:9;2290:23;2332:18;2373:2;2365:6;2362:14;2359:34;;;2389:1;2386;2379:12;2359:34;2412:50;2454:7;2445:6;2434:9;2430:22;2412:50;:::i;:::-;2402:60;;2515:2;2504:9;2500:18;2487:32;2471:48;;2544:2;2534:8;2531:16;2528:36;;;2560:1;2557;2550:12;2528:36;;2583:52;2627:7;2616:8;2605:9;2601:24;2583:52;:::i;:::-;2573:62;;;2654:36;2686:2;2675:9;2671:18;2654:36;:::i;:::-;2644:46;;2083:613;;;;;:::o;2883:328::-;2960:6;2968;2976;3029:2;3017:9;3008:7;3004:23;3000:32;2997:52;;;3045:1;3042;3035:12;2997:52;3068:29;3087:9;3068:29;:::i;:::-;3058:39;;3116:38;3150:2;3139:9;3135:18;3116:38;:::i;:::-;3106:48;;3201:2;3190:9;3186:18;3173:32;3163:42;;2883:328;;;;;:::o;3587:186::-;3646:6;3699:2;3687:9;3678:7;3674:23;3670:32;3667:52;;;3715:1;3712;3705:12;3667:52;3738:29;3757:9;3738:29;:::i;3778:606::-;3889:6;3897;3905;3913;3921;3929;3937;3990:3;3978:9;3969:7;3965:23;3961:33;3958:53;;;4007:1;4004;3997:12;3958:53;4030:29;4049:9;4030:29;:::i;:::-;4020:39;;4078:38;4112:2;4101:9;4097:18;4078:38;:::i;:::-;4068:48;;4163:2;4152:9;4148:18;4135:32;4125:42;;4214:2;4203:9;4199:18;4186:32;4176:42;;4237:37;4269:3;4258:9;4254:19;4237:37;:::i;:::-;4227:47;;4321:3;4310:9;4306:19;4293:33;4283:43;;4373:3;4362:9;4358:19;4345:33;4335:43;;3778:606;;;;;;;;;;:::o;4389:260::-;4457:6;4465;4518:2;4506:9;4497:7;4493:23;4489:32;4486:52;;;4534:1;4531;4524:12;4486:52;4557:29;4576:9;4557:29;:::i;:::-;4547:39;;4605:38;4639:2;4628:9;4624:18;4605:38;:::i;:::-;4595:48;;4389:260;;;;;:::o;4654:380::-;4733:1;4729:12;;;;4776;;;4797:61;;4851:4;4843:6;4839:17;4829:27;;4797:61;4904:2;4896:6;4893:14;4873:18;4870:38;4867:161;;4950:10;4945:3;4941:20;4938:1;4931:31;4985:4;4982:1;4975:15;5013:4;5010:1;5003:15;4867:161;;4654:380;;;:::o;5513:518::-;5615:2;5610:3;5607:11;5604:421;;;5651:5;5648:1;5641:16;5695:4;5692:1;5682:18;5765:2;5753:10;5749:19;5746:1;5742:27;5736:4;5732:38;5801:4;5789:10;5786:20;5783:47;;;-1:-1:-1;5824:4:192;5783:47;5879:2;5874:3;5870:12;5867:1;5863:20;5857:4;5853:31;5843:41;;5934:81;5952:2;5945:5;5942:13;5934:81;;;6011:1;5997:16;;5978:1;5967:13;5934:81;;;5938:3;;5604:421;5513:518;;;:::o;6207:1345::-;6333:3;6327:10;6360:18;6352:6;6349:30;6346:56;;;6382:18;;:::i;:::-;6411:97;6501:6;6461:38;6493:4;6487:11;6461:38;:::i;:::-;6455:4;6411:97;:::i;:::-;6563:4;;6620:2;6609:14;;6637:1;6632:663;;;;7339:1;7356:6;7353:89;;;-1:-1:-1;7408:19:192;;;7402:26;7353:89;-1:-1:-1;;6164:1:192;6160:11;;;6156:24;6152:29;6142:40;6188:1;6184:11;;;6139:57;7455:81;;6602:944;;6632:663;5460:1;5453:14;;;5497:4;5484:18;;-1:-1:-1;;6668:20:192;;;6786:236;6800:7;6797:1;6794:14;6786:236;;;6889:19;;;6883:26;6868:42;;6981:27;;;;6949:1;6937:14;;;;6816:19;;6786:236;;;6790:3;7050:6;7041:7;7038:19;7035:201;;;7111:19;;;7105:26;-1:-1:-1;;7194:1:192;7190:14;;;7206:3;7186:24;7182:37;7178:42;7163:58;7148:74;;7035:201;;;7282:1;7273:6;7270:1;7266:14;7262:22;7256:4;7249:36;6602:944;;;;;6207:1345;;:::o;7909:127::-;7970:10;7965:3;7961:20;7958:1;7951:31;8001:4;7998:1;7991:15;8025:4;8022:1;8015:15;8041:135;8080:3;8101:17;;;8098:43;;8121:18;;:::i;:::-;-1:-1:-1;8168:1:192;8157:13;;8041:135::o;9920:845::-;10050:3;10079:1;10112:6;10106:13;10142:36;10168:9;10142:36;:::i;:::-;10197:1;10214:17;;;10240:133;;;;10387:1;10382:358;;;;10207:533;;10240:133;-1:-1:-1;;10273:24:192;;10261:37;;10346:14;;10339:22;10327:35;;10318:45;;;-1:-1:-1;10240:133:192;;10382:358;10413:6;10410:1;10403:17;10443:4;10488;10485:1;10475:18;10515:1;10529:165;10543:6;10540:1;10537:13;10529:165;;;10621:14;;10608:11;;;10601:35;10664:16;;;;10558:10;;10529:165;;;10533:3;;;10723:6;10718:3;10714:16;10707:23;;10207:533;-1:-1:-1;10756:3:192;;9920:845;-1:-1:-1;;;;;;9920:845:192:o;11621:128::-;11688:9;;;11709:11;;;11706:37;;;11723:18;;:::i;11754:125::-;11819:9;;;11840:10;;;11837:36;;;11853:18;;:::i", "linkReferences": {}}, "methodIdentifiers": {"DOMAIN_SEPARATOR()": "3644e515", "allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "decimals()": "313ce567", "initialize(string,string,uint8)": "1624f6c6", "name()": "06fdde03", "nonces(address)": "7ecebe00", "permit(address,address,uint256,uint256,uint8,bytes32,bytes32)": "d505accf", "symbol()": "95d89b41", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"DOMAIN_SEPARATOR\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name_\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"symbol_\",\"type\":\"string\"},{\"internalType\":\"uint8\",\"name\":\"decimals_\",\"type\":\"uint8\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"nonces\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"},{\"internalType\":\"uint8\",\"name\":\"v\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"r\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"name\":\"permit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Forked from: https://github.com/transmissions11/solmate/blob/0384dbaaa4fcb5715738a9254a7c0a4cb62cf458/src/tokens/ERC20.sol\",\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set, where `value` is the new allowance.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`).\"}},\"kind\":\"dev\",\"methods\":{\"approve(address,uint256)\":{\"details\":\"Be aware of front-running risks: https://github.com/ethereum/EIPs/issues/20#issuecomment-*********\"},\"initialize(string,string,uint8)\":{\"details\":\"To hide constructor warnings across solc versions due to different constructor visibility requirements and syntaxes, we add an initialization function that can be called only once.\"}},\"stateVariables\":{\"initialized\":{\"details\":\"A bool to track whether the contract has been initialized.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"allowance(address,address)\":{\"notice\":\"Returns the remaining number of tokens that `spender` is allowed to spend on behalf of `owner`\"},\"approve(address,uint256)\":{\"notice\":\"Sets `amount` as the allowance of `spender` over the caller's tokens.\"},\"decimals()\":{\"notice\":\"Returns the decimals places of the token.\"},\"name()\":{\"notice\":\"Returns the name of the token.\"},\"symbol()\":{\"notice\":\"Returns the symbol of the token.\"},\"totalSupply()\":{\"notice\":\"Returns the amount of tokens in existence.\"},\"transfer(address,uint256)\":{\"notice\":\"Moves `amount` tokens from the caller's account to `to`.\"},\"transferFrom(address,address,uint256)\":{\"notice\":\"Moves `amount` tokens from `from` to `to` using the allowance mechanism. `amount` is then deducted from the caller's allowance.\"}},\"notice\":\"This is a mock contract of the ERC20 standard for testing purposes only, it SHOULD NOT be used in production.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/mocks/MockERC20.sol\":\"MockERC20\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/interfaces/IERC20.sol\":{\"keccak256\":\"0x4cab887298790f908c27de107e4e2907ca5413aee482ef776f8d2f353c5ef947\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bb715e0c4a2bdbe432bb624501506041f06e878e0b72675aebba30ad2c2b72e7\",\"dweb:/ipfs/QmWhhLSvkxS2NrukJJHqFY8gDVE5r9rD4PfHvR24pwdKv9\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/mocks/MockERC20.sol\":{\"keccak256\":\"0xadbdfc6639edec00ba94bb1133a0fd8de31ccafe45c2ef5df9b3ca61b60b559b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc766f01bccf669abac8cc7b2096cc1352a66471ca3772ae61b1801dedc4ed4f\",\"dweb:/ipfs/QmNigaPj1LYkP7Z5xr4ijkUxr2K66fDGeoifG3WoM9ruyw\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "spender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DOMAIN_SEPARATOR", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [{"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "string", "name": "symbol_", "type": "string"}, {"internalType": "uint8", "name": "decimals_", "type": "uint8"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "nonces", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "permit"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"approve(address,uint256)": {"details": "Be aware of front-running risks: https://github.com/ethereum/EIPs/issues/20#issuecomment-*********"}, "initialize(string,string,uint8)": {"details": "To hide constructor warnings across solc versions due to different constructor visibility requirements and syntaxes, we add an initialization function that can be called only once."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"allowance(address,address)": {"notice": "Returns the remaining number of tokens that `spender` is allowed to spend on behalf of `owner`"}, "approve(address,uint256)": {"notice": "Sets `amount` as the allowance of `spender` over the caller's tokens."}, "decimals()": {"notice": "Returns the decimals places of the token."}, "name()": {"notice": "Returns the name of the token."}, "symbol()": {"notice": "Returns the symbol of the token."}, "totalSupply()": {"notice": "Returns the amount of tokens in existence."}, "transfer(address,uint256)": {"notice": "Moves `amount` tokens from the caller's account to `to`."}, "transferFrom(address,address,uint256)": {"notice": "Moves `amount` tokens from `from` to `to` using the allowance mechanism. `amount` is then deducted from the caller's allowance."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/mocks/MockERC20.sol": "MockERC20"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/interfaces/IERC20.sol": {"keccak256": "0x4cab887298790f908c27de107e4e2907ca5413aee482ef776f8d2f353c5ef947", "urls": ["bzz-raw://bb715e0c4a2bdbe432bb624501506041f06e878e0b72675aebba30ad2c2b72e7", "dweb:/ipfs/QmWhhLSvkxS2NrukJJHqFY8gDVE5r9rD4PfHvR24pwdKv9"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/mocks/MockERC20.sol": {"keccak256": "0xadbdfc6639edec00ba94bb1133a0fd8de31ccafe45c2ef5df9b3ca61b60b559b", "urls": ["bzz-raw://bc766f01bccf669abac8cc7b2096cc1352a66471ca3772ae61b1801dedc4ed4f", "dweb:/ipfs/QmNigaPj1LYkP7Z5xr4ijkUxr2K66fDGeoifG3WoM9ruyw"], "license": "MIT"}}, "version": 1}, "id": 29}