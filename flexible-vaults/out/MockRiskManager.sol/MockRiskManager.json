{"abi": [{"type": "constructor", "inputs": [{"name": "limit", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "maxDeposit", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "test", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x6080604052348015600e575f80fd5b50604051610129380380610129833981016040819052602b916031565b5f556047565b5f602082840312156040575f80fd5b5051919050565b60d6806100535f395ff3fe6080604052348015600e575f80fd5b50600436106030575f3560e01c806376016870146034578063f8a8fd6d146058575b5f80fd5b6046603f3660046074565b50505f5490565b60405190815260200160405180910390f35b005b80356001600160a01b0381168114606f575f80fd5b919050565b5f80604083850312156084575f80fd5b608b83605a565b9150609760208401605a565b9050925092905056fea264697066735822122080b5eb623f94f2bb9c478671312b29a65609b3eb55a9ec2832236122dd5037f664736f6c63430008190033", "sourceMap": "62:260:155:-:0;;;123:58;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;160:6;:14;62:260;;14:184:192;84:6;137:2;125:9;116:7;112:23;108:32;105:52;;;153:1;150;143:12;105:52;-1:-1:-1;176:16:192;;14:184;-1:-1:-1;14:184:192:o;:::-;62:260:155;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x6080604052348015600e575f80fd5b50600436106030575f3560e01c806376016870146034578063f8a8fd6d146058575b5f80fd5b6046603f3660046074565b50505f5490565b60405190815260200160405180910390f35b005b80356001600160a01b0381168114606f575f80fd5b919050565b5f80604083850312156084575f80fd5b608b83605a565b9150609760208401605a565b9050925092905056fea264697066735822122080b5eb623f94f2bb9c478671312b29a65609b3eb55a9ec2832236122dd5037f664736f6c63430008190033", "sourceMap": "62:260:155:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;187:100;;;;;;:::i;:::-;-1:-1:-1;;248:7:155;274:6;;187:100;;;;603:25:192;;;591:2;576:18;187:100:155;;;;;;;293:27;;14:173:192;82:20;;-1:-1:-1;;;;;131:31:192;;121:42;;111:70;;177:1;174;167:12;111:70;14:173;;;:::o;192:260::-;260:6;268;321:2;309:9;300:7;296:23;292:32;289:52;;;337:1;334;327:12;289:52;360:29;379:9;360:29;:::i;:::-;350:39;;408:38;442:2;431:9;427:18;408:38;:::i;:::-;398:48;;192:260;;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"maxDeposit(address,address)": "76016870", "test()": "f8a8fd6d"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"limit\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"maxDeposit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/mocks/MockRiskManager.sol\":\"MockRiskManager\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"test/mocks/MockRiskManager.sol\":{\"keccak256\":\"0x099af4d6a6a37f416be3f13246c14e691bb63c04940efeb8d95859cdd8b3b9b1\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://15f7f3bc2dd811a74dcfec005766647cb8b4109760ca8a50e7da4aa471c62ae9\",\"dweb:/ipfs/QmRWrF6FjQFZtyy2EHLhsFmXTZyUgw4bU95bDXPoRCuMUh\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "uint256", "name": "limit", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "maxDeposit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/mocks/MockRiskManager.sol": "MockRiskManager"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"test/mocks/MockRiskManager.sol": {"keccak256": "0x099af4d6a6a37f416be3f13246c14e691bb63c04940efeb8d95859cdd8b3b9b1", "urls": ["bzz-raw://15f7f3bc2dd811a74dcfec005766647cb8b4109760ca8a50e7da4aa471c62ae9", "dweb:/ipfs/QmRWrF6FjQFZtyy2EHLhsFmXTZyUgw4bU95bDXPoRCuMUh"], "license": "BUSL-1.1"}}, "version": 1}, "id": 155}