{"abi": [{"type": "function", "name": "claim", "inputs": [{"name": "recipient", "type": "address", "internalType": "address"}, {"name": "epoch", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "deposit", "inputs": [{"name": "onBehalfOf", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "depositedAmount", "type": "uint256", "internalType": "uint256"}, {"name": "mintedShares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "claimer", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "burnedShares", "type": "uint256", "internalType": "uint256"}, {"name": "mintedShares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"claim(address,uint256)": "aad3ec96", "deposit(address,uint256)": "47e7ef24", "withdraw(address,uint256)": "f3fef3a3"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"epoch\",\"type\":\"uint256\"}],\"name\":\"claim\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalfOf\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"deposit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"depositedAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"mintedShares\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"claimer\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"withdraw\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"burnedShares\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"mintedShares\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/external/symbiotic/ISymbioticVault.sol\":\"ISymbioticVault\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"src/interfaces/external/symbiotic/ISymbioticVault.sol\":{\"keccak256\":\"0x87dafe9de0c5f57fe3eac074a198ea7711ee743ea2490d4d23ded6be5a0523c4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://87c13eb577eaf7bf58764a929eae5b96dfca99017017c570cbf8257c2a0697c4\",\"dweb:/ipfs/QmYKDNKYGj2Kvv9b6AdAQBJSGmWhfBU5nTMhnfv9eLfqU8\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "epoch", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "claim", "outputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "onBehalfOf", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "deposit", "outputs": [{"internalType": "uint256", "name": "depositedAmount", "type": "uint256"}, {"internalType": "uint256", "name": "mintedShares", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "claimer", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "withdraw", "outputs": [{"internalType": "uint256", "name": "burnedShares", "type": "uint256"}, {"internalType": "uint256", "name": "mintedShares", "type": "uint256"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/external/symbiotic/ISymbioticVault.sol": "ISymbioticVault"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"src/interfaces/external/symbiotic/ISymbioticVault.sol": {"keccak256": "0x87dafe9de0c5f57fe3eac074a198ea7711ee743ea2490d4d23ded6be5a0523c4", "urls": ["bzz-raw://87c13eb577eaf7bf58764a929eae5b96dfca99017017c570cbf8257c2a0697c4", "dweb:/ipfs/QmYKDNKYGj2Kvv9b6AdAQBJSGmWhfBU5nTMhnfv9eLfqU8"], "license": "BUSL-1.1"}}, "version": 1}, "id": 85}