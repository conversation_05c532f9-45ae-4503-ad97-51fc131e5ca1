{"abi": [{"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getApproved", "inputs": [{"name": "id", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "name_", "type": "string", "internalType": "string"}, {"name": "symbol_", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isApprovedForAll", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "operator", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "ownerOf", "inputs": [{"name": "id", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "owner", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "safeTransferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "safeTransferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "setApprovalForAll", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "approved", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "tokenURI", "inputs": [{"name": "id", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "id", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "payable"}, {"type": "event", "name": "Approval", "inputs": [{"name": "_owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "_approved", "type": "address", "indexed": true, "internalType": "address"}, {"name": "_tokenId", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "ApprovalForAll", "inputs": [{"name": "_owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "_operator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "_approved", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "_from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "_to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "_tokenId", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}], "bytecode": {"object": "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", "sourceMap": "403:7498:30:-:0;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "403:7498:30:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5376:332;;;;;;;;;;-1:-1:-1;5376:332:30;;;;;:::i;:::-;;:::i;:::-;;;565:14:192;;558:22;540:41;;528:2;513:18;5376:332:30;;;;;;;;693:92;;;;;;;;;;;;;:::i;:::-;;;;;;;:::i;1949:120::-;;;;;;;;;;-1:-1:-1;1949:120:30;;;;;:::i;:::-;2020:7;2046:16;;;:12;:16;;;;;;-1:-1:-1;;;;;2046:16:30;;1949:120;;;;-1:-1:-1;;;;;1460:32:192;;;1442:51;;1430:2;1415:18;1949:120:30;1296:203:192;3128:301:30;;;;;;:::i;:::-;;:::i;:::-;;3654:693;;;;;;:::i;:::-;;:::i;4353:386::-;;;;;;:::i;:::-;;:::i;2728:212::-;;;;;;;;;;-1:-1:-1;2728:212:30;;;;;:::i;:::-;;:::i;1280:158::-;;;;;;;;;;-1:-1:-1;1280:158:30;;;;;:::i;:::-;;:::i;1444:177::-;;;;;;;;;;-1:-1:-1;1444:177:30;;;;;:::i;:::-;;:::i;:::-;;;4155:25:192;;;4143:2;4128:18;1444:177:30;4009::192;791:96:30;;;;;;;;;;;;;:::i;3435:213::-;;;;;;;;;;-1:-1:-1;3435:213:30;;;;;:::i;:::-;;:::i;4745:443::-;;;;;;:::i;:::-;;:::i;893:85::-;;;;;;;;;;-1:-1:-1;893:85:30;;;;;:::i;:::-;-1:-1:-1;961:13:30;;893:85;2075:161;;;;;;;;;;-1:-1:-1;2075:161:30;;;;;:::i;:::-;-1:-1:-1;;;;;2195:24:30;;;2172:4;2195:24;;;:17;:24;;;;;;;;:34;;;;;;;;;;;;;;;2075:161;5376:332;5461:4;-1:-1:-1;;;;;;;;;5484:25:30;;;;:100;;-1:-1:-1;;;;;;;;;;5559:25:30;;;5484:100;:175;;;-1:-1:-1;;;;;;;;;;5634:25:30;;;5484:175;5477:182;5376:332;-1:-1:-1;;5376:332:30:o;693:92::-;741:13;773:5;766:12;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;693:92;:::o;3128:301::-;3216:13;3232:12;;;:8;:12;;;;;;-1:-1:-1;;;;;3232:12:30;3263:10;:19;;;:59;;-1:-1:-1;;;;;;3286:24:30;;;;;;:17;:24;;;;;;;;3311:10;3286:36;;;;;;;;;;3263:59;3255:86;;;;-1:-1:-1;;;3255:86:30;;6067:2:192;3255:86:30;;;6049:21:192;6106:2;6086:18;;;6079:30;-1:-1:-1;;;6125:18:192;;;6118:44;6179:18;;3255:86:30;;;;;;;;;3352:16;;;;:12;:16;;;;;;:26;;-1:-1:-1;;;;;;3352:26:30;-1:-1:-1;;;;;3352:26:30;;;;;;;;;3394:28;;3352:16;;3394:28;;;;;;;3206:223;3128:301;;:::o;3654:693::-;3772:12;;;;:8;:12;;;;;;-1:-1:-1;;;;;3764:20:30;;;3772:12;;3764:20;3756:43;;;;-1:-1:-1;;;3756:43:30;;6410:2:192;3756:43:30;;;6392:21:192;6449:2;6429:18;;;6422:30;-1:-1:-1;;;6468:18:192;;;6461:40;6518:18;;3756:43:30;6208:334:192;3756:43:30;-1:-1:-1;;;;;3818:16:30;;3810:46;;;;-1:-1:-1;;;3810:46:30;;6749:2:192;3810:46:30;;;6731:21:192;6788:2;6768:18;;;6761:30;-1:-1:-1;;;6807:18:192;;;6800:47;6864:18;;3810:46:30;6547:341:192;3810:46:30;3888:10;-1:-1:-1;;;;;3888:18:30;;;;:57;;-1:-1:-1;;;;;;3910:23:30;;;;;;:17;:23;;;;;;;;3934:10;3910:35;;;;;;;;;;3888:57;:91;;;-1:-1:-1;3963:16:30;;;;:12;:16;;;;;;-1:-1:-1;;;;;3963:16:30;3949:10;:30;3888:91;3867:152;;;;-1:-1:-1;;;3867:152:30;;6067:2:192;3867:152:30;;;6049:21:192;6106:2;6086:18;;;6079:30;-1:-1:-1;;;6125:18:192;;;6118:44;6179:18;;3867:152:30;5865:338:192;3867:152:30;-1:-1:-1;;;;;4195:16:30;;;;;;:10;:16;;;;;:18;;;;;;:::i;:::-;;;;-1:-1:-1;;;;;;;4224:14:30;;;;;;:10;:14;;;;;:16;;;;;;:::i;:::-;;;;-1:-1:-1;;4251:12:30;;;;:8;:12;;;;;;;;:17;;-1:-1:-1;;;;;4251:17:30;;;-1:-1:-1;;;;;;4251:17:30;;;;;;;;4286:12;:16;;;;;;4279:23;;;;;;;4318:22;;4260:2;;4251:17;;4318:22;;;;;;3654:693;;;:::o;4353:386::-;4459:26;4472:4;4478:2;4482;4459:12;:26::i;:::-;7833:18;;7878:14;;4517:173;;-1:-1:-1;4553:67:30;;-1:-1:-1;;;4553:67:30;;;4595:10;4553:67;;;7611:34:192;-1:-1:-1;;;;;7681:15:192;;;7661:18;;;7654:43;7713:18;;;7706:34;;;7776:3;7756:18;;;7749:31;-1:-1:-1;7796:19:192;;;7789:30;4644:46:30;;4553:41;;;;4644:46;;7836:19:192;;4553:67:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;;4553:137:30;;4517:173;4496:236;;;;-1:-1:-1;;;4496:236:30;;8322:2:192;4496:236:30;;;8304:21:192;8361:2;8341:18;;;8334:30;-1:-1:-1;;;8380:18:192;;;8373:46;8436:18;;4496:236:30;8120:340:192;4496:236:30;4353:386;;;:::o;2728:212::-;2818:11;;;;2817:12;2809:44;;;;-1:-1:-1;;;2809:44:30;;8667:2:192;2809:44:30;;;8649:21:192;8706:2;8686:18;;;8679:30;-1:-1:-1;;;8725:18:192;;;8718:49;8784:18;;2809:44:30;8465:343:192;2809:44:30;2864:5;:13;2872:5;2864;:13;:::i;:::-;-1:-1:-1;2887:7:30;:17;2897:7;2887;:17;:::i;:::-;-1:-1:-1;;2915:11:30;:18;;-1:-1:-1;;2915:18:30;2929:4;2915:18;;;-1:-1:-1;2728:212:30:o;1280:158::-;1347:13;1389:12;;;:8;:12;;;;;;-1:-1:-1;;;;;1389:12:30;;1372:59;;;;-1:-1:-1;;;1372:59:30;;11185:2:192;1372:59:30;;;11167:21:192;11224:2;11204:18;;;11197:30;-1:-1:-1;;;11243:18:192;;;11236:40;11293:18;;1372:59:30;10983:334:192;1372:59:30;1280:158;;;:::o;1444:177::-;1516:7;-1:-1:-1;;;;;1543:19:30;;1535:44;;;;-1:-1:-1;;;1535:44:30;;11524:2:192;1535:44:30;;;11506:21:192;11563:2;11543:18;;;11536:30;-1:-1:-1;;;11582:18:192;;;11575:42;11634:18;;1535:44:30;11322:336:192;1535:44:30;-1:-1:-1;;;;;;1597:17:30;;;;;:10;:17;;;;;;;1444:177::o;791:96::-;841:13;873:7;866:14;;;;;:::i;3435:213::-;3547:10;3529:29;;;;:17;:29;;;;;;;;-1:-1:-1;;;;;3529:39:30;;;;;;;;;;;;:50;;-1:-1:-1;;3529:50:30;;;;;;;;;;3595:46;;540:41:192;;;3529:39:30;;3547:10;3595:46;;513:18:192;3595:46:30;;;;;;;3435:213;;:::o;4745:443::-;4906:26;4919:4;4925:2;4929;4906:12;:26::i;:::-;7833:18;;7878:14;;4964:175;;-1:-1:-1;5000:69:30;;-1:-1:-1;;;5000:69:30;;;5093:46;-1:-1:-1;;;;;5000:41:30;;;5093:46;;5000:69;;5042:10;;5054:4;;5060:2;;5064:4;;5000:69;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;;5000:139:30;;4964:175;4943:238;;;;-1:-1:-1;;;4943:238:30;;8322:2:192;4943:238:30;;;8304:21:192;8361:2;8341:18;;;8334:30;-1:-1:-1;;;8380:18:192;;;8373:46;8436:18;;4943:238:30;8120:340:192;4943:238:30;4745:443;;;;:::o;14:131:192:-;-1:-1:-1;;;;;;88:32:192;;78:43;;68:71;;135:1;132;125:12;68:71;14:131;:::o;150:245::-;208:6;261:2;249:9;240:7;236:23;232:32;229:52;;;277:1;274;267:12;229:52;316:9;303:23;335:30;359:5;335:30;:::i;:::-;384:5;150:245;-1:-1:-1;;;150:245:192:o;592:289::-;634:3;672:5;666:12;699:6;694:3;687:19;755:6;748:4;741:5;737:16;730:4;725:3;721:14;715:47;807:1;800:4;791:6;786:3;782:16;778:27;771:38;870:4;863:2;859:7;854:2;846:6;842:15;838:29;833:3;829:39;825:50;818:57;;;592:289;;;;:::o;886:220::-;1035:2;1024:9;1017:21;998:4;1055:45;1096:2;1085:9;1081:18;1073:6;1055:45;:::i;1111:180::-;1170:6;1223:2;1211:9;1202:7;1198:23;1194:32;1191:52;;;1239:1;1236;1229:12;1191:52;-1:-1:-1;1262:23:192;;1111:180;-1:-1:-1;1111:180:192:o;1504:173::-;1572:20;;-1:-1:-1;;;;;1621:31:192;;1611:42;;1601:70;;1667:1;1664;1657:12;1682:254;1750:6;1758;1811:2;1799:9;1790:7;1786:23;1782:32;1779:52;;;1827:1;1824;1817:12;1779:52;1850:29;1869:9;1850:29;:::i;:::-;1840:39;1926:2;1911:18;;;;1898:32;;-1:-1:-1;;;1682:254:192:o;1941:328::-;2018:6;2026;2034;2087:2;2075:9;2066:7;2062:23;2058:32;2055:52;;;2103:1;2100;2093:12;2055:52;2126:29;2145:9;2126:29;:::i;:::-;2116:39;;2174:38;2208:2;2197:9;2193:18;2174:38;:::i;:::-;2164:48;;2259:2;2248:9;2244:18;2231:32;2221:42;;1941:328;;;;;:::o;2274:127::-;2335:10;2330:3;2326:20;2323:1;2316:31;2366:4;2363:1;2356:15;2390:4;2387:1;2380:15;2406:632;2471:5;2501:18;2542:2;2534:6;2531:14;2528:40;;;2548:18;;:::i;:::-;2623:2;2617:9;2591:2;2677:15;;-1:-1:-1;;2673:24:192;;;2699:2;2669:33;2665:42;2653:55;;;2723:18;;;2743:22;;;2720:46;2717:72;;;2769:18;;:::i;:::-;2809:10;2805:2;2798:22;2838:6;2829:15;;2868:6;2860;2853:22;2908:3;2899:6;2894:3;2890:16;2887:25;2884:45;;;2925:1;2922;2915:12;2884:45;2975:6;2970:3;2963:4;2955:6;2951:17;2938:44;3030:1;3023:4;3014:6;3006;3002:19;2998:30;2991:41;;;;2406:632;;;;;:::o;3043:222::-;3086:5;3139:3;3132:4;3124:6;3120:17;3116:27;3106:55;;3157:1;3154;3147:12;3106:55;3179:80;3255:3;3246:6;3233:20;3226:4;3218:6;3214:17;3179:80;:::i;3270:543::-;3358:6;3366;3419:2;3407:9;3398:7;3394:23;3390:32;3387:52;;;3435:1;3432;3425:12;3387:52;3475:9;3462:23;3504:18;3545:2;3537:6;3534:14;3531:34;;;3561:1;3558;3551:12;3531:34;3584:50;3626:7;3617:6;3606:9;3602:22;3584:50;:::i;:::-;3574:60;;3687:2;3676:9;3672:18;3659:32;3643:48;;3716:2;3706:8;3703:16;3700:36;;;3732:1;3729;3722:12;3700:36;;3755:52;3799:7;3788:8;3777:9;3773:24;3755:52;:::i;:::-;3745:62;;;3270:543;;;;;:::o;3818:186::-;3877:6;3930:2;3918:9;3909:7;3905:23;3901:32;3898:52;;;3946:1;3943;3936:12;3898:52;3969:29;3988:9;3969:29;:::i;4191:347::-;4256:6;4264;4317:2;4305:9;4296:7;4292:23;4288:32;4285:52;;;4333:1;4330;4323:12;4285:52;4356:29;4375:9;4356:29;:::i;:::-;4346:39;;4435:2;4424:9;4420:18;4407:32;4482:5;4475:13;4468:21;4461:5;4458:32;4448:60;;4504:1;4501;4494:12;4448:60;4527:5;4517:15;;;4191:347;;;;;:::o;4543:667::-;4638:6;4646;4654;4662;4715:3;4703:9;4694:7;4690:23;4686:33;4683:53;;;4732:1;4729;4722:12;4683:53;4755:29;4774:9;4755:29;:::i;:::-;4745:39;;4803:38;4837:2;4826:9;4822:18;4803:38;:::i;:::-;4793:48;;4888:2;4877:9;4873:18;4860:32;4850:42;;4943:2;4932:9;4928:18;4915:32;4970:18;4962:6;4959:30;4956:50;;;5002:1;4999;4992:12;4956:50;5025:22;;5078:4;5070:13;;5066:27;-1:-1:-1;5056:55:192;;5107:1;5104;5097:12;5056:55;5130:74;5196:7;5191:2;5178:16;5173:2;5169;5165:11;5130:74;:::i;:::-;5120:84;;;4543:667;;;;;;;:::o;5215:260::-;5283:6;5291;5344:2;5332:9;5323:7;5319:23;5315:32;5312:52;;;5360:1;5357;5350:12;5312:52;5383:29;5402:9;5383:29;:::i;:::-;5373:39;;5431:38;5465:2;5454:9;5450:18;5431:38;:::i;:::-;5421:48;;5215:260;;;;;:::o;5480:380::-;5559:1;5555:12;;;;5602;;;5623:61;;5677:4;5669:6;5665:17;5655:27;;5623:61;5730:2;5722:6;5719:14;5699:18;5696:38;5693:161;;5776:10;5771:3;5767:20;5764:1;5757:31;5811:4;5808:1;5801:15;5839:4;5836:1;5829:15;5693:161;;5480:380;;;:::o;6893:127::-;6954:10;6949:3;6945:20;6942:1;6935:31;6985:4;6982:1;6975:15;7009:4;7006:1;6999:15;7025:136;7064:3;7092:5;7082:39;;7101:18;;:::i;:::-;-1:-1:-1;;;7137:18:192;;7025:136::o;7166:135::-;7205:3;7226:17;;;7223:43;;7246:18;;:::i;:::-;-1:-1:-1;7293:1:192;7282:13;;7166:135::o;7866:249::-;7935:6;7988:2;7976:9;7967:7;7963:23;7959:32;7956:52;;;8004:1;8001;7994:12;7956:52;8036:9;8030:16;8055:30;8079:5;8055:30;:::i;8939:518::-;9041:2;9036:3;9033:11;9030:421;;;9077:5;9074:1;9067:16;9121:4;9118:1;9108:18;9191:2;9179:10;9175:19;9172:1;9168:27;9162:4;9158:38;9227:4;9215:10;9212:20;9209:47;;;-1:-1:-1;9250:4:192;9209:47;9305:2;9300:3;9296:12;9293:1;9289:20;9283:4;9279:31;9269:41;;9360:81;9378:2;9371:5;9368:13;9360:81;;;9437:1;9423:16;;9404:1;9393:13;9360:81;;;9364:3;;8939:518;;;:::o;9633:1345::-;9759:3;9753:10;9786:18;9778:6;9775:30;9772:56;;;9808:18;;:::i;:::-;9837:97;9927:6;9887:38;9919:4;9913:11;9887:38;:::i;:::-;9881:4;9837:97;:::i;:::-;9989:4;;10046:2;10035:14;;10063:1;10058:663;;;;10765:1;10782:6;10779:89;;;-1:-1:-1;10834:19:192;;;10828:26;10779:89;-1:-1:-1;;9590:1:192;9586:11;;;9582:24;9578:29;9568:40;9614:1;9610:11;;;9565:57;10881:81;;10028:944;;10058:663;8886:1;8879:14;;;8923:4;8910:18;;-1:-1:-1;;10094:20:192;;;10212:236;10226:7;10223:1;10220:14;10212:236;;;10315:19;;;10309:26;10294:42;;10407:27;;;;10375:1;10363:14;;;;10242:19;;10212:236;;;10216:3;10476:6;10467:7;10464:19;10461:201;;;10537:19;;;10531:26;-1:-1:-1;;10620:1:192;10616:14;;;10632:3;10612:24;10608:37;10604:42;10589:58;10574:74;;10461:201;;;10708:1;10699:6;10696:1;10692:14;10688:22;10682:4;10675:36;10028:944;;;;;9633:1345;;:::o;11663:489::-;-1:-1:-1;;;;;11932:15:192;;;11914:34;;11984:15;;11979:2;11964:18;;11957:43;12031:2;12016:18;;12009:34;;;12079:3;12074:2;12059:18;;12052:31;;;11857:4;;12100:46;;12126:19;;12118:6;12100:46;:::i;:::-;12092:54;11663:489;-1:-1:-1;;;;;;11663:489:192:o", "linkReferences": {}}, "methodIdentifiers": {"approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "getApproved(uint256)": "081812fc", "initialize(string,string)": "4cd88b76", "isApprovedForAll(address,address)": "e985e9c5", "name()": "06fdde03", "ownerOf(uint256)": "6352211e", "safeTransferFrom(address,address,uint256)": "42842e0e", "safeTransferFrom(address,address,uint256,bytes)": "b88d4fde", "setApprovalForAll(address,bool)": "a22cb465", "supportsInterface(bytes4)": "01ffc9a7", "symbol()": "95d89b41", "tokenURI(uint256)": "c87b56dd", "transferFrom(address,address,uint256)": "23b872dd"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_approved\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_tokenId\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_operator\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"_approved\",\"type\":\"bool\"}],\"name\":\"ApprovalForAll\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"_to\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"_tokenId\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"}],\"name\":\"getApproved\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name_\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"symbol_\",\"type\":\"string\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"isApprovedForAll\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"}],\"name\":\"ownerOf\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"}],\"name\":\"safeTransferFrom\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"safeTransferFrom\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"setApprovalForAll\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"}],\"name\":\"tokenURI\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Forked from: https://github.com/transmissions11/solmate/blob/0384dbaaa4fcb5715738a9254a7c0a4cb62cf458/src/tokens/ERC721.sol\",\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"This emits when the approved address for an NFT is changed or reaffirmed. The zero address indicates there is no approved address. When a Transfer event emits, this also indicates that the approved address for that NFT (if any) is reset to none.\"},\"ApprovalForAll(address,address,bool)\":{\"details\":\"This emits when an operator is enabled or disabled for an owner. The operator can manage all NFTs of the owner.\"},\"Transfer(address,address,uint256)\":{\"details\":\"This emits when ownership of any NFT changes by any mechanism. This event emits when NFTs are created (`from` == 0) and destroyed (`to` == 0). Exception: during contract creation, any number of NFTs may be created and assigned without emitting Transfer. At the time of any transfer, the approved address for that NFT (if any) is reset to none.\"}},\"kind\":\"dev\",\"methods\":{\"initialize(string,string)\":{\"details\":\"To hide constructor warnings across solc versions due to different constructor visibility requirements and syntaxes, we add an initialization function that can be called only once.\"}},\"stateVariables\":{\"initialized\":{\"details\":\"A bool to track whether the contract has been initialized.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"name()\":{\"notice\":\"A descriptive name for a collection of NFTs in this contract\"},\"symbol()\":{\"notice\":\"An abbreviated name for NFTs in this contract\"}},\"notice\":\"This is a mock contract of the ERC721 standard for testing purposes only, it SHOULD NOT be used in production.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/mocks/MockERC721.sol\":\"MockERC721\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/interfaces/IERC165.sol\":{\"keccak256\":\"0x414b2861b1acbf816ccb7346d3f16cf6c1e002e9e5e40d2f1f26fa5ddc2ea600\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://698352fb240868ea8f1d1fe389993035eeab930f10d06934f80ccfb2b6ccbfbc\",\"dweb:/ipfs/QmT6WLHAgXxFhh12kWym895oTzXid1326iZiwT3pyfggoT\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/interfaces/IERC721.sol\":{\"keccak256\":\"0xf069262a264fdb69f8f37a10d2df7374649e9ba73f8414c9c8a3b51184625f15\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://924a41ca82a68ffbd503a5faf2f76d13e9938f10501a71094716f12bb64b4b7f\",\"dweb:/ipfs/QmdWnqfKc5ZGhmxNPTde4zrFchnv9Yk9MpCMb2rdhXE5gm\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/mocks/MockERC721.sol\":{\"keccak256\":\"0xbfbb755bb379f58b97699f7cda0e9a2b9113daaa072e41b280a5ce6a3bff44c9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5ae0c31d29db14d6a0e9b62d33d550feb307f48a6f45068901a013321f337f87\",\"dweb:/ipfs/QmX25DVXW6XZDvRm6SNBGCWrZQ6oNhdq5CmgtVocvsUdwv\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "_owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "_approved", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "_tokenId", "type": "uint256", "indexed": true}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "_owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "_operator", "type": "address", "indexed": true}, {"internalType": "bool", "name": "_approved", "type": "bool", "indexed": false}], "type": "event", "name": "ApprovalForAll", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "_from", "type": "address", "indexed": true}, {"internalType": "address", "name": "_to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "_tokenId", "type": "uint256", "indexed": true}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "stateMutability": "payable", "type": "function", "name": "approve"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getApproved", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "string", "name": "symbol_", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "operator", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isApprovedForAll", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "ownerOf", "outputs": [{"internalType": "address", "name": "owner", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "stateMutability": "payable", "type": "function", "name": "safeTransferFrom"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "safeTransferFrom"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setApprovalForAll"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "stateMutability": "view", "type": "function", "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "tokenURI", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "stateMutability": "payable", "type": "function", "name": "transferFrom"}], "devdoc": {"kind": "dev", "methods": {"initialize(string,string)": {"details": "To hide constructor warnings across solc versions due to different constructor visibility requirements and syntaxes, we add an initialization function that can be called only once."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"name()": {"notice": "A descriptive name for a collection of NFTs in this contract"}, "symbol()": {"notice": "An abbreviated name for NFTs in this contract"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/mocks/MockERC721.sol": "MockERC721"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/interfaces/IERC165.sol": {"keccak256": "0x414b2861b1acbf816ccb7346d3f16cf6c1e002e9e5e40d2f1f26fa5ddc2ea600", "urls": ["bzz-raw://698352fb240868ea8f1d1fe389993035eeab930f10d06934f80ccfb2b6ccbfbc", "dweb:/ipfs/QmT6WLHAgXxFhh12kWym895oTzXid1326iZiwT3pyfggoT"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/interfaces/IERC721.sol": {"keccak256": "0xf069262a264fdb69f8f37a10d2df7374649e9ba73f8414c9c8a3b51184625f15", "urls": ["bzz-raw://924a41ca82a68ffbd503a5faf2f76d13e9938f10501a71094716f12bb64b4b7f", "dweb:/ipfs/QmdWnqfKc5ZGhmxNPTde4zrFchnv9Yk9MpCMb2rdhXE5gm"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/mocks/MockERC721.sol": {"keccak256": "0xbfbb755bb379f58b97699f7cda0e9a2b9113daaa072e41b280a5ce6a3bff44c9", "urls": ["bzz-raw://5ae0c31d29db14d6a0e9b62d33d550feb307f48a6f45068901a013321f337f87", "dweb:/ipfs/QmX25DVXW6XZDvRm6SNBGCWrZQ6oNhdq5CmgtVocvsUdwv"], "license": "MIT"}}, "version": 1}, "id": 30}