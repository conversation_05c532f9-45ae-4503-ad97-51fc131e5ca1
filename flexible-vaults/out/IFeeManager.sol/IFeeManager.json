{"abi": [{"type": "function", "name": "baseAsset", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "calculateDepositFee", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "calculateFee", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "asset", "type": "address", "internalType": "address"}, {"name": "priceD18", "type": "uint256", "internalType": "uint256"}, {"name": "totalShares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "calculateRedeemFee", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "depositFeeD6", "inputs": [], "outputs": [{"name": "", "type": "uint24", "internalType": "uint24"}], "stateMutability": "view"}, {"type": "function", "name": "feeRecipient", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "initParams", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "minPriceD18", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "performanceFeeD6", "inputs": [], "outputs": [{"name": "", "type": "uint24", "internalType": "uint24"}], "stateMutability": "view"}, {"type": "function", "name": "protocolFeeD6", "inputs": [], "outputs": [{"name": "", "type": "uint24", "internalType": "uint24"}], "stateMutability": "view"}, {"type": "function", "name": "redeemFeeD6", "inputs": [], "outputs": [{"name": "", "type": "uint24", "internalType": "uint24"}], "stateMutability": "view"}, {"type": "function", "name": "setBaseAsset", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "baseAsset_", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setFeeRecipient", "inputs": [{"name": "feeRecipient_", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setFees", "inputs": [{"name": "depositFeeD6_", "type": "uint24", "internalType": "uint24"}, {"name": "redeemFeeD6_", "type": "uint24", "internalType": "uint24"}, {"name": "performanceFeeD6_", "type": "uint24", "internalType": "uint24"}, {"name": "protocolFeeD6_", "type": "uint24", "internalType": "uint24"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "timestamps", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "updateState", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}, {"name": "priceD18", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "Initialized", "inputs": [{"name": "initParams", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "SetBaseAsset", "inputs": [{"name": "vault", "type": "address", "indexed": true, "internalType": "address"}, {"name": "baseAsset", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetFeeRecipient", "inputs": [{"name": "feeRecipient", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetFees", "inputs": [{"name": "depositFeeD6", "type": "uint24", "indexed": false, "internalType": "uint24"}, {"name": "redeemFeeD6", "type": "uint24", "indexed": false, "internalType": "uint24"}, {"name": "performanceFeeD6", "type": "uint24", "indexed": false, "internalType": "uint24"}, {"name": "protocolFeeD6", "type": "uint24", "indexed": false, "internalType": "uint24"}], "anonymous": false}, {"type": "event", "name": "UpdateState", "inputs": [{"name": "vault", "type": "address", "indexed": true, "internalType": "address"}, {"name": "asset", "type": "address", "indexed": true, "internalType": "address"}, {"name": "priceD18", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "BaseAssetAlreadySet", "inputs": [{"name": "vault", "type": "address", "internalType": "address"}, {"name": "baseAsset", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "InvalidFees", "inputs": [{"name": "depositFeeD6", "type": "uint24", "internalType": "uint24"}, {"name": "redeemFeeD6", "type": "uint24", "internalType": "uint24"}, {"name": "performanceFeeD6", "type": "uint24", "internalType": "uint24"}, {"name": "protocolFeeD6", "type": "uint24", "internalType": "uint24"}]}, {"type": "error", "name": "ZeroAddress", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"baseAsset(address)": "19b3<PERSON><PERSON>", "calculateDepositFee(uint256)": "247d284b", "calculateFee(address,address,uint256,uint256)": "e0e193a2", "calculateRedeemFee(uint256)": "7f0c6010", "depositFeeD6()": "e3707d18", "feeRecipient()": "46904840", "initialize(bytes)": "439fab91", "minPriceD18(address)": "980679e2", "performanceFeeD6()": "a51fac22", "protocolFeeD6()": "8005c6de", "redeemFeeD6()": "bf9587fb", "setBaseAsset(address,address)": "1ded75b4", "setFeeRecipient(address)": "e74b981b", "setFees(uint24,uint24,uint24,uint24)": "46f6cd27", "timestamps(address)": "e9c98e68", "updateState(address,uint256)": "9ae26062"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"baseAsset\",\"type\":\"address\"}],\"name\":\"BaseAssetAlreadySet\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint24\",\"name\":\"depositFeeD6\",\"type\":\"uint24\"},{\"internalType\":\"uint24\",\"name\":\"redeemFeeD6\",\"type\":\"uint24\"},{\"internalType\":\"uint24\",\"name\":\"performanceFeeD6\",\"type\":\"uint24\"},{\"internalType\":\"uint24\",\"name\":\"protocolFeeD6\",\"type\":\"uint24\"}],\"name\":\"InvalidFees\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZeroAddress\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"initParams\",\"type\":\"bytes\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"baseAsset\",\"type\":\"address\"}],\"name\":\"SetBaseAsset\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"feeRecipient\",\"type\":\"address\"}],\"name\":\"SetFeeRecipient\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint24\",\"name\":\"depositFeeD6\",\"type\":\"uint24\"},{\"indexed\":false,\"internalType\":\"uint24\",\"name\":\"redeemFeeD6\",\"type\":\"uint24\"},{\"indexed\":false,\"internalType\":\"uint24\",\"name\":\"performanceFeeD6\",\"type\":\"uint24\"},{\"indexed\":false,\"internalType\":\"uint24\",\"name\":\"protocolFeeD6\",\"type\":\"uint24\"}],\"name\":\"SetFees\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"priceD18\",\"type\":\"uint256\"}],\"name\":\"UpdateState\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"baseAsset\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"calculateDepositFee\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"priceD18\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"totalShares\",\"type\":\"uint256\"}],\"name\":\"calculateFee\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"calculateRedeemFee\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"depositFeeD6\",\"outputs\":[{\"internalType\":\"uint24\",\"name\":\"\",\"type\":\"uint24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"feeRecipient\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"initParams\",\"type\":\"bytes\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"minPriceD18\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"performanceFeeD6\",\"outputs\":[{\"internalType\":\"uint24\",\"name\":\"\",\"type\":\"uint24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"protocolFeeD6\",\"outputs\":[{\"internalType\":\"uint24\",\"name\":\"\",\"type\":\"uint24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"redeemFeeD6\",\"outputs\":[{\"internalType\":\"uint24\",\"name\":\"\",\"type\":\"uint24\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"baseAsset_\",\"type\":\"address\"}],\"name\":\"setBaseAsset\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"feeRecipient_\",\"type\":\"address\"}],\"name\":\"setFeeRecipient\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint24\",\"name\":\"depositFeeD6_\",\"type\":\"uint24\"},{\"internalType\":\"uint24\",\"name\":\"redeemFeeD6_\",\"type\":\"uint24\"},{\"internalType\":\"uint24\",\"name\":\"performanceFeeD6_\",\"type\":\"uint24\"},{\"internalType\":\"uint24\",\"name\":\"protocolFeeD6_\",\"type\":\"uint24\"}],\"name\":\"setFees\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"timestamps\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"priceD18\",\"type\":\"uint256\"}],\"name\":\"updateState\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Handles deposit, redeem, performance, and protocol fees for vaults, and tracks per-vault price/timestamp states\",\"events\":{\"Initialized(bytes)\":{\"params\":{\"initParams\":\"The initialization parameters.\"}}},\"kind\":\"dev\",\"methods\":{\"calculateDepositFee(uint256)\":{\"params\":{\"amount\":\"Number of shares being deposited\"},\"returns\":{\"_0\":\"Fee in shares to be deducted\"}},\"calculateFee(address,address,uint256,uint256)\":{\"params\":{\"asset\":\"Asset used for pricing\",\"priceD18\":\"Current vault share price for the specific `asset` (price = shares / assets)\",\"totalShares\":\"Total shares of the vault\",\"vault\":\"Address of the vault\"},\"returns\":{\"shares\":\"Fee to be added in shares\"}},\"calculateRedeemFee(uint256)\":{\"params\":{\"amount\":\"Number of shares being redeemed\"},\"returns\":{\"_0\":\"Fee in shares to be deducted\"}},\"initialize(bytes)\":{\"params\":{\"initParams\":\"The initialization parameters.\"}},\"setBaseAsset(address,address)\":{\"details\":\"Can only be set once per vault\"},\"setFeeRecipient(address)\":{\"params\":{\"feeRecipient_\":\"Address to receive fees\"}},\"setFees(uint24,uint24,uint24,uint24)\":{\"details\":\"Total of all fees must be <= 1e6 (i.e. 100%)\"},\"updateState(address,uint256)\":{\"details\":\"Used by the vault to notify FeeManager of new price highs or protocol fee accrual checkpoints\"}},\"version\":1},\"userdoc\":{\"errors\":{\"BaseAssetAlreadySet(address,address)\":[{\"notice\":\"Thrown when trying to overwrite a vault's base asset that was already set\"}],\"InvalidFees(uint24,uint24,uint24,uint24)\":[{\"notice\":\"Thrown when the sum of all fees exceeds 100% (1e6 in D6 precision)\"}],\"ZeroAddress()\":[{\"notice\":\"Thrown when a required address is zero\"}]},\"events\":{\"Initialized(bytes)\":{\"notice\":\"Emitted once the entity has been initialized.\"},\"SetBaseAsset(address,address)\":{\"notice\":\"Emitted when a vault's base asset is set\"},\"SetFeeRecipient(address)\":{\"notice\":\"Emitted when the fee recipient is changed\"},\"SetFees(uint24,uint24,uint24,uint24)\":{\"notice\":\"Emitted when the fee configuration is updated\"},\"UpdateState(address,address,uint256)\":{\"notice\":\"Emitted when the vault's min price or timestamp is updated\"}},\"kind\":\"user\",\"methods\":{\"baseAsset(address)\":{\"notice\":\"Returns the base asset configured for a vault\"},\"calculateDepositFee(uint256)\":{\"notice\":\"Calculates the deposit fee in shares based on the amount\"},\"calculateFee(address,address,uint256,uint256)\":{\"notice\":\"Calculates the combined performance and protocol fee in shares\"},\"calculateRedeemFee(uint256)\":{\"notice\":\"Calculates the redeem fee in shares based on the amount\"},\"depositFeeD6()\":{\"notice\":\"Returns the configured deposit fee (in D6 precision)\"},\"feeRecipient()\":{\"notice\":\"Returns the current fee recipient address\"},\"initialize(bytes)\":{\"notice\":\"Initializes the factory-created entity with arbitrary initialization data.\"},\"minPriceD18(address)\":{\"notice\":\"Returns the last recorded min price for a vault's base asset (used for performance fee)\"},\"performanceFeeD6()\":{\"notice\":\"Returns the configured performance fee (in D6 precision)\"},\"protocolFeeD6()\":{\"notice\":\"Returns the configured protocol fee (in D6 precision per year)\"},\"redeemFeeD6()\":{\"notice\":\"Returns the configured redeem fee (in D6 precision)\"},\"setBaseAsset(address,address)\":{\"notice\":\"Sets the base asset for a vault, required for performance fee calculation\"},\"setFeeRecipient(address)\":{\"notice\":\"Sets the recipient address for all collected fees\"},\"setFees(uint24,uint24,uint24,uint24)\":{\"notice\":\"Sets the global fee configuration (deposit, redeem, performance, protocol)\"},\"timestamps(address)\":{\"notice\":\"Returns the last recorded timestamp for a given vault (used for protocol fee accrual)\"},\"updateState(address,uint256)\":{\"notice\":\"Updates the vault's state (min price and timestamp) based on asset price only if `asset` == `baseAssets[vault]`\"}},\"notice\":\"Interface for the FeeManager contract\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/managers/IFeeManager.sol\":\"IFeeManager\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol\":{\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6\",\"dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"src/interfaces/factories/IFactoryEntity.sol\":{\"keccak256\":\"0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812\",\"dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx\"]},\"src/interfaces/managers/IFeeManager.sol\":{\"keccak256\":\"0x37d3c06ed6b23e367216c2dfc14f99f3fd6d686c984eca124406c2002e4f1516\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://5b3b1b974856f76c6fe2347895ab05e973c84c963fdf8f5483c37e2e4d434cad\",\"dweb:/ipfs/QmVeLCrCWgeQ3aS4V38y9FszFvZdSkbPHKKCMWKKM3V4qw\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "baseAsset", "type": "address"}], "type": "error", "name": "BaseAssetAlreadySet"}, {"inputs": [{"internalType": "uint24", "name": "depositFeeD6", "type": "uint24"}, {"internalType": "uint24", "name": "redeemFeeD6", "type": "uint24"}, {"internalType": "uint24", "name": "performanceFeeD6", "type": "uint24"}, {"internalType": "uint24", "name": "protocolFeeD6", "type": "uint24"}], "type": "error", "name": "InvalidFees"}, {"inputs": [], "type": "error", "name": "ZeroAddress"}, {"inputs": [{"internalType": "bytes", "name": "initParams", "type": "bytes", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address", "indexed": true}, {"internalType": "address", "name": "baseAsset", "type": "address", "indexed": true}], "type": "event", "name": "SetBaseAsset", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "feeRecipient", "type": "address", "indexed": true}], "type": "event", "name": "SetFeeRecipient", "anonymous": false}, {"inputs": [{"internalType": "uint24", "name": "depositFeeD6", "type": "uint24", "indexed": false}, {"internalType": "uint24", "name": "redeemFeeD6", "type": "uint24", "indexed": false}, {"internalType": "uint24", "name": "performanceFeeD6", "type": "uint24", "indexed": false}, {"internalType": "uint24", "name": "protocolFeeD6", "type": "uint24", "indexed": false}], "type": "event", "name": "SetFees", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address", "indexed": true}, {"internalType": "address", "name": "asset", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "priceD18", "type": "uint256", "indexed": false}], "type": "event", "name": "UpdateState", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}], "stateMutability": "view", "type": "function", "name": "baseAsset", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "calculateDepositFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "priceD18", "type": "uint256"}, {"internalType": "uint256", "name": "totalShares", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "calculateFee", "outputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "calculateRedeemFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "depositFeeD6", "outputs": [{"internalType": "uint24", "name": "", "type": "uint24"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "feeRecipient", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "bytes", "name": "initParams", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}], "stateMutability": "view", "type": "function", "name": "minPriceD18", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "performanceFeeD6", "outputs": [{"internalType": "uint24", "name": "", "type": "uint24"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "protocolFeeD6", "outputs": [{"internalType": "uint24", "name": "", "type": "uint24"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "redeemFeeD6", "outputs": [{"internalType": "uint24", "name": "", "type": "uint24"}]}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "baseAsset_", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setBaseAsset"}, {"inputs": [{"internalType": "address", "name": "feeRecipient_", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setFeeRecipient"}, {"inputs": [{"internalType": "uint24", "name": "depositFeeD6_", "type": "uint24"}, {"internalType": "uint24", "name": "redeemFeeD6_", "type": "uint24"}, {"internalType": "uint24", "name": "performanceFeeD6_", "type": "uint24"}, {"internalType": "uint24", "name": "protocolFeeD6_", "type": "uint24"}], "stateMutability": "nonpayable", "type": "function", "name": "setFees"}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address"}], "stateMutability": "view", "type": "function", "name": "timestamps", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "priceD18", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "updateState"}], "devdoc": {"kind": "dev", "methods": {"calculateDepositFee(uint256)": {"params": {"amount": "Number of shares being deposited"}, "returns": {"_0": "Fee in shares to be deducted"}}, "calculateFee(address,address,uint256,uint256)": {"params": {"asset": "Asset used for pricing", "priceD18": "Current vault share price for the specific `asset` (price = shares / assets)", "totalShares": "Total shares of the vault", "vault": "Address of the vault"}, "returns": {"shares": "Fee to be added in shares"}}, "calculateRedeemFee(uint256)": {"params": {"amount": "Number of shares being redeemed"}, "returns": {"_0": "Fee in shares to be deducted"}}, "initialize(bytes)": {"params": {"initParams": "The initialization parameters."}}, "setBaseAsset(address,address)": {"details": "Can only be set once per vault"}, "setFeeRecipient(address)": {"params": {"feeRecipient_": "Address to receive fees"}}, "setFees(uint24,uint24,uint24,uint24)": {"details": "Total of all fees must be <= 1e6 (i.e. 100%)"}, "updateState(address,uint256)": {"details": "Used by the vault to notify FeeManager of new price highs or protocol fee accrual checkpoints"}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"baseAsset(address)": {"notice": "Returns the base asset configured for a vault"}, "calculateDepositFee(uint256)": {"notice": "Calculates the deposit fee in shares based on the amount"}, "calculateFee(address,address,uint256,uint256)": {"notice": "Calculates the combined performance and protocol fee in shares"}, "calculateRedeemFee(uint256)": {"notice": "Calculates the redeem fee in shares based on the amount"}, "depositFeeD6()": {"notice": "Returns the configured deposit fee (in D6 precision)"}, "feeRecipient()": {"notice": "Returns the current fee recipient address"}, "initialize(bytes)": {"notice": "Initializes the factory-created entity with arbitrary initialization data."}, "minPriceD18(address)": {"notice": "Returns the last recorded min price for a vault's base asset (used for performance fee)"}, "performanceFeeD6()": {"notice": "Returns the configured performance fee (in D6 precision)"}, "protocolFeeD6()": {"notice": "Returns the configured protocol fee (in D6 precision per year)"}, "redeemFeeD6()": {"notice": "Returns the configured redeem fee (in D6 precision)"}, "setBaseAsset(address,address)": {"notice": "Sets the base asset for a vault, required for performance fee calculation"}, "setFeeRecipient(address)": {"notice": "Sets the recipient address for all collected fees"}, "setFees(uint24,uint24,uint24,uint24)": {"notice": "Sets the global fee configuration (deposit, redeem, performance, protocol)"}, "timestamps(address)": {"notice": "Returns the last recorded timestamp for a given vault (used for protocol fee accrual)"}, "updateState(address,uint256)": {"notice": "Updates the vault's state (min price and timestamp) based on asset price only if `asset` == `baseAssets[vault]`"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/managers/IFeeManager.sol": "<PERSON>ee<PERSON>anager"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"keccak256": "0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a", "urls": ["bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6", "dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05", "urls": ["bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08", "dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "src/interfaces/factories/IFactoryEntity.sol": {"keccak256": "0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5", "urls": ["bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812", "dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx"], "license": "BUSL-1.1"}, "src/interfaces/managers/IFeeManager.sol": {"keccak256": "0x37d3c06ed6b23e367216c2dfc14f99f3fd6d686c984eca124406c2002e4f1516", "urls": ["bzz-raw://5b3b1b974856f76c6fe2347895ab05e973c84c963fdf8f5483c37e2e4d434cad", "dweb:/ipfs/QmVeLCrCWgeQ3aS4V38y9FszFvZdSkbPHKKCMWKKM3V4qw"], "license": "BUSL-1.1"}}, "version": 1}, "id": 92}