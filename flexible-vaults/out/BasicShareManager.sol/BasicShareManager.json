{"abi": [{"type": "constructor", "inputs": [{"name": "name_", "type": "string", "internalType": "string"}, {"name": "version_", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "SET_ACCOUNT_INFO_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "SET_FLAGS_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "SET_WHITELIST_MERKLE_ROOT_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "accounts", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IShareManager.AccountInfo", "components": [{"name": "canDeposit", "type": "bool", "internalType": "bool"}, {"name": "canTransfer", "type": "bool", "internalType": "bool"}, {"name": "isBlacklisted", "type": "bool", "internalType": "bool"}, {"name": "lockedUntil", "type": "uint32", "internalType": "uint32"}]}], "stateMutability": "view"}, {"type": "function", "name": "activeShares", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "activeSharesOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "allocateShares", "inputs": [{"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "allocatedShares", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "burn", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimShares", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimableSharesOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "flags", "inputs": [], "outputs": [{"name": "f", "type": "tuple", "internalType": "struct IShareManager.Flags", "components": [{"name": "hasMintPause", "type": "bool", "internalType": "bool"}, {"name": "hasBurnPause", "type": "bool", "internalType": "bool"}, {"name": "hasTransferPause", "type": "bool", "internalType": "bool"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool", "internalType": "bool"}, {"name": "hasTransferW<PERSON><PERSON>st", "type": "bool", "internalType": "bool"}, {"name": "globalLockup", "type": "uint32", "internalType": "uint32"}, {"name": "targetedLockup", "type": "uint32", "internalType": "uint32"}]}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isDep<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "merkleProof", "type": "bytes32[]", "internalType": "bytes32[]"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "mint", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "mintAllocatedShares", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setAccountInfo", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "info", "type": "tuple", "internalType": "struct IShareManager.AccountInfo", "components": [{"name": "canDeposit", "type": "bool", "internalType": "bool"}, {"name": "canTransfer", "type": "bool", "internalType": "bool"}, {"name": "isBlacklisted", "type": "bool", "internalType": "bool"}, {"name": "lockedUntil", "type": "uint32", "internalType": "uint32"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setFlags", "inputs": [{"name": "f", "type": "tuple", "internalType": "struct IShareManager.Flags", "components": [{"name": "hasMintPause", "type": "bool", "internalType": "bool"}, {"name": "hasBurnPause", "type": "bool", "internalType": "bool"}, {"name": "hasTransferPause", "type": "bool", "internalType": "bool"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool", "internalType": "bool"}, {"name": "hasTransferW<PERSON><PERSON>st", "type": "bool", "internalType": "bool"}, {"name": "globalLockup", "type": "uint32", "internalType": "uint32"}, {"name": "targetedLockup", "type": "uint32", "internalType": "uint32"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON>", "inputs": [{"name": "vault_", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setWhitelistMerkleRoot", "inputs": [{"name": "newWhitelistMerkleRoot", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "sharesOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalShares", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "updateChecks", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "vault", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "whitelist<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "event", "name": "AllocateShares", "inputs": [{"name": "value", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "Burn", "inputs": [{"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "initParams", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "Mint", "inputs": [{"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "lockedUntil", "type": "uint32", "indexed": false, "internalType": "uint32"}], "anonymous": false}, {"type": "event", "name": "SetAccountInfo", "inputs": [{"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "info", "type": "tuple", "indexed": false, "internalType": "struct IShareManager.AccountInfo", "components": [{"name": "canDeposit", "type": "bool", "internalType": "bool"}, {"name": "canTransfer", "type": "bool", "internalType": "bool"}, {"name": "isBlacklisted", "type": "bool", "internalType": "bool"}, {"name": "lockedUntil", "type": "uint32", "internalType": "uint32"}]}], "anonymous": false}, {"type": "event", "name": "SetFlags", "inputs": [{"name": "flags", "type": "tuple", "indexed": false, "internalType": "struct IShareManager.Flags", "components": [{"name": "hasMintPause", "type": "bool", "internalType": "bool"}, {"name": "hasBurnPause", "type": "bool", "internalType": "bool"}, {"name": "hasTransferPause", "type": "bool", "internalType": "bool"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool", "internalType": "bool"}, {"name": "hasTransferW<PERSON><PERSON>st", "type": "bool", "internalType": "bool"}, {"name": "globalLockup", "type": "uint32", "internalType": "uint32"}, {"name": "targetedLockup", "type": "uint32", "internalType": "uint32"}]}], "anonymous": false}, {"type": "event", "name": "<PERSON><PERSON><PERSON>", "inputs": [{"name": "vault", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetWhitelistMerkleRoot", "inputs": [{"name": "newWhitelistMerkleRoot", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "Blacklisted", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "BurnPaused", "inputs": []}, {"type": "error", "name": "ERC20InsufficientBalance", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InvalidReceiver", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSender", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "Forbidden", "inputs": []}, {"type": "error", "name": "GlobalLockupNotExpired", "inputs": [{"name": "timestamp", "type": "uint256", "internalType": "uint256"}, {"name": "globalLockup", "type": "uint32", "internalType": "uint32"}]}, {"type": "error", "name": "InsufficientAllocatedShares", "inputs": [{"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "allocated", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "LimitExceeded", "inputs": [{"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "limit", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "MintPaused", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "TargetedLockupNotExpired", "inputs": [{"name": "timestamp", "type": "uint256", "internalType": "uint256"}, {"name": "targetedLockup", "type": "uint32", "internalType": "uint32"}]}, {"type": "error", "name": "TransferNotAllowed", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "TransferPaused", "inputs": []}, {"type": "error", "name": "ZeroValue", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "172:2225:115:-:0;;;390:83;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;454:5;461:8;917:52:118;;;;;;;;;;;;;;-1:-1:-1;;;917:52:118;;;953:5;960:8;917:19;;;:52;;:::i;:::-;890:79;;979:22;:20;:22::i;:::-;829:179;;390:83:115;;172:2225;;656:343:113;761:7;986:4;970:22;;969:23;941:1;908:12;922:4;928:7;856:80;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;846:91;;;;;;838:100;;:104;;;;:::i;:::-;810:146;;;;;;2258:25:192;;2246:2;2231:18;;2112:177;810:146:113;;;;;;;;;;;;;787:179;;;;;;:205;780:212;;656:343;;;;;:::o;7709:422:3:-;7824:30;7857:26;:24;:26::i;:::-;7898:15;;;;-1:-1:-1;7898:15:3;;;;;7894:76;;;7936:23;;-1:-1:-1;;;7936:23:3;;;;;;;;;;;7894:76;7983:14;;-1:-1:-1;;;;;7983:14:3;;;:34;7979:146;;8033:33;;-1:-1:-1;;;;;;8033:33:3;-1:-1:-1;;;;;8033:33:3;;;;;8085:29;;2438:50:192;;;8085:29:3;;2426:2:192;2411:18;8085:29:3;;;;;;;7979:146;7758:373;7709:422::o;9071:205::-;9129:30;;3147:66;9186:27;9171:42;9071:205;-1:-1:-1;;9071:205:3:o;14:127:192:-;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:983;235:6;243;296:2;284:9;275:7;271:23;267:32;264:52;;;312:1;309;302:12;264:52;339:16;;-1:-1:-1;;;;;404:14:192;;;401:34;;;431:1;428;421:12;401:34;469:6;458:9;454:22;444:32;;514:7;507:4;503:2;499:13;495:27;485:55;;536:1;533;526:12;485:55;565:2;559:9;587:2;583;580:10;577:36;;;593:18;;:::i;:::-;668:2;662:9;636:2;722:13;;-1:-1:-1;;718:22:192;;;742:2;714:31;710:40;698:53;;;766:18;;;786:22;;;763:46;760:72;;;812:18;;:::i;:::-;852:10;848:2;841:22;887:2;879:6;872:18;929:7;922:4;917:2;913;909:11;905:22;902:35;899:55;;;950:1;947;940:12;899:55;1003:2;996:4;992:2;988:13;981:4;973:6;969:17;963:43;1050:1;1043:4;1038:2;1030:6;1026:15;1022:26;1015:37;1071:6;1061:16;;;;;;;1117:4;1106:9;1102:20;1096:27;1086:37;;146:983;;;;;:::o;1134:212::-;1176:3;1214:5;1208:12;1258:6;1251:4;1244:5;1240:16;1235:3;1229:36;1320:1;1284:16;;1309:13;;;-1:-1:-1;1284:16:192;;1134:212;-1:-1:-1;1134:212:192:o;1351:526::-;1689:33;1684:3;1677:46;1659:3;1745:66;1771:39;1806:2;1801:3;1797:12;1789:6;1771:39;:::i;:::-;1763:6;1745:66;:::i;:::-;1820:21;;;-1:-1:-1;;1868:2:192;1857:14;;1351:526;-1:-1:-1;;1351:526:192:o;1882:225::-;1949:9;;;1970:11;;;1967:134;;;2023:10;2018:3;2014:20;2011:1;2004:31;2058:4;2055:1;2048:15;2086:4;2083:1;2076:15;2294:200;172:2225:115;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "172:2225:115:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;7225:234:118;;;;;;:::i;:::-;;:::i;:::-;;2442:167;;;;;;:::i;:::-;;:::i;:::-;;;714:25:192;;;702:2;687:18;2442:167:118;;;;;;;;6450:215;;;;;;:::i;:::-;;:::i;1664:557::-;;;;;;:::i;:::-;;:::i;:::-;;;3042:14:192;;3035:22;3017:41;;3005:2;2990:18;1664:557:118;2877:187:192;2870:132:118;;;:::i;7916:591::-;;;;;;:::i;:::-;;:::i;902:159:115:-;;;;;;:::i;:::-;;:::i;361:90:118:-;;402:49;361:90;;4041:140;;;;;;:::i;:::-;;:::i;:::-;;;;;;4444:13:192;;4437:21;4430:29;4412:48;;4530:4;4518:17;;;4512:24;4505:32;4498:40;4476:20;;;4469:70;4609:4;4597:17;;;4591:24;4584:32;4577:40;4555:20;;;4548:70;4678:4;4666:17;;;4660:24;4686:10;4656:41;4634:20;;;4627:71;;;;4399:3;4384:19;;4205:499;4221:1639:118;;;;;;:::i;:::-;;:::i;3340:494::-;;;:::i;:::-;;;;;;;:::i;5926:340::-;;;;;;:::i;:::-;;:::i;635:130::-;;700:65;635:130;;491:104;;539:56;491:104;;3042:119;;;:::i;694:141:115:-;;;;;;:::i;:::-;;:::i;8547:212:118:-;;;;;;:::i;:::-;;:::i;3874:127::-;;;:::i;6926:259::-;;;;;;:::i;:::-;;:::i;7499:377::-;;;;;;:::i;:::-;;:::i;536:118:115:-;;;:::i;6306:104:118:-;;;;;;:::i;:::-;;:::i;2261:141::-;;;;;;:::i;:::-;;:::i;6705:181::-;;;;;;:::i;:::-;;:::i;3201:99::-;;;:::i;:::-;;;-1:-1:-1;;;;;6286:32:192;;;6268:51;;6256:2;6241:18;3201:99:118;6122:203:192;7225:234:118;1086:22;:20;:22::i;:::-;:28;-1:-1:-1;;;;;1086:28:118;1073:51;966:10:5;1073:65:118;;-1:-1:-1;;;;;;1073:65:118;;;;;;;-1:-1:-1;;;;;6286:32:192;;;1073:65:118;;;6268:51:192;6241:18;;1073:65:118;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1068:115;;1161:11;;-1:-1:-1;;;1161:11:118;;;;;;;;;;;1068:115;7297:5:::1;7306:1;7297:10:::0;7293:59:::1;;7330:11;;-1:-1:-1::0;;;7330:11:118::1;;;;;;;;;;;7293:59;7403:5;7361:22;:20;:22::i;:::-;:38;;;:47;;;;;;;:::i;:::-;::::0;;;-1:-1:-1;;7423:29:118::1;::::0;714:25:192;;;7423:29:118::1;::::0;702:2:192;687:18;7423:29:118::1;;;;;;;7225:234:::0;:::o;2442:167::-;2507:7;2546:22;:20;:22::i;:::-;:28;2533:69;;-1:-1:-1;;;2533:69:118;;-1:-1:-1;;;;;6286:32:192;;;2533:69:118;;;6268:51:192;2546:28:118;;;;2533:60;;6241:18:192;;2533:69:118;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2526:76;2442:167;-1:-1:-1;;2442:167:118:o;6450:215::-;539:56;1505:22;:20;:22::i;:::-;:28;-1:-1:-1;;;;;1505:28:118;1494:48;1543:4;966:10:5;1494:68:118;;-1:-1:-1;;;;;;1494:68:118;;;;;;;;;;7385:25:192;;;;-1:-1:-1;;;;;7446:32:192;7426:18;;;7419:60;7358:18;;1494:68:118;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1489:118;;1585:11;;-1:-1:-1;;;1585:11:118;;;;;;;;;;;1489:118;6610:4:::1;6567:22;:20;:22::i;:::-;-1:-1:-1::0;;;;;6567:40:118;::::1;;::::0;;;:31:::1;::::0;;;::::1;:40;::::0;;;;;;;;:47;;;;;;::::1;::::0;;;::::1;::::0;::::1;::::0;;::::1;::::0;-1:-1:-1;;6567:47:118;;;;::::1;;-1:-1:-1::0;;6567:47:118;;;;;::::1;::::0;::::1;;::::0;;;::::1;;-1:-1:-1::0;;6567:47:118;;;::::1;;::::0;;;::::1;-1:-1:-1::0;;6567:47:118;;;;;;::::1;::::0;;::::1;;;::::0;;;6629:29;;4444:13:192;;4437:21;4430:29;4412:48;;4518:17;;;4512:24;4505:32;4498:40;4476:20;;;4469:70;;;;4597:17;;;4591:24;4584:32;4577:40;4555:20;;;4548:70;;;;4666:17;;;4660:24;4656:41;;;4634:20;;;4627:71;;;;6629:29:118::1;::::0;4399:3:192;4384:19;6629:29:118::1;;;;;;;6450:215:::0;;;:::o;1664:557::-;1766:4;1782:29;1814:22;:20;:22::i;:::-;1782:54;;1850:22;:1;:7;;;1638:1:112;1631:8;1630:15;;;1548:104;1850:22:118;:57;;;;-1:-1:-1;;;;;;1877:19:118;;;;;;:10;;;:19;;;;;:30;;;1876:31;1850:57;1846:100;;;1930:5;1923:12;;;;;1846:100;1986:21;;;;2024:34;;;:190;;;2074:140;2110:11;;2074:140;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;2178:19:118;;;-1:-1:-1;;;;;6286:32:192;;2178:19:118;;;6268:51:192;2123:20:118;;-1:-1:-1;6241:18:192;;-1:-1:-1;2178:19:118;;;-1:-1:-1;;2178:19:118;;;;;;;;;2168:30;;2178:19;2168:30;;;;2155:44;;;7619:19:192;7654:12;2155:44:118;;;;;;;;;;;;2145:55;;;;;;2074:18;:140::i;:::-;2017:197;;;;1664:557;;;;;;:::o;2870:132::-;2914:7;2981:14;:12;:14::i;:::-;2940:22;:20;:22::i;:::-;:38;;;:55;;;;:::i;:::-;2933:62;;2870:132;:::o;7916:591::-;966:10:5;1244:14:118;1300:7;:5;:7::i;:::-;1283:24;;1331:6;-1:-1:-1;;;;;1321:16:118;:6;-1:-1:-1;;;;;1321:16:118;;;:58;;;;-1:-1:-1;1342:37:118;;-1:-1:-1;;;1342:37:118;;-1:-1:-1;;;;;6286:32:192;;;1342:37:118;;;6268:51:192;1342:29:118;;;;;6241:18:192;;1342:37:118;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1341:38;1321:58;1317:107;;;1402:11;;-1:-1:-1;;;1402:11:118;;;;;;;;;;;1317:107;8000:5:::1;8009:1;8000:10:::0;7996:59:::1;;8033:11;;-1:-1:-1::0;;;8033:11:118::1;;;;;;;;;;;7996:59;8064:27;8076:7;8085:5;8064:11;:27::i;:::-;8101:29;8133:22;:20;:22::i;:::-;8101:54;;8165:19;8187:27;:1;:7;;;2444:2:112::0;2436:10;;2340:114;8187:27:118::1;8165:49:::0;-1:-1:-1;8228:17:118::1;::::0;::::1;::::0;8224:277:::1;;8261:18;8282:38;8308:12:::0;8289:15:::1;8282:38;:::i;:::-;-1:-1:-1::0;;;;;8334:19:118;::::1;;::::0;;;:10:::1;::::0;::::1;:19;::::0;;;;;;;;:45;;-1:-1:-1;;8334:45:118::1;::::0;::::1;::::0;::::1;::::0;;::::1;::::0;;;::::1;::::0;;;8398:33;;8026:25:192;;;8067:18;;;8060:51;8334:45:118;;-1:-1:-1;8334:19:118;;8398:33:::1;::::0;7999:18:192;8398:33:118::1;;;;;;;8247:195;8224:277;;;8467:23;::::0;;8026:25:192;;;8488:1:118::1;8082:2:192::0;8067:18;;8060:51;-1:-1:-1;;;;;8467:23:118;::::1;::::0;::::1;::::0;7999:18:192;8467:23:118::1;;;;;;;8224:277;7986:521;;1234:207:::0;;7916:591;;:::o;902:159:115:-;4158:30:3;4191:26;:24;:26::i;:::-;4302:15;;4158:59;;-1:-1:-1;4302:15:3;-1:-1:-1;;;4302:15:3;;;4301:16;;4348:14;;4279:19;4724:16;;:34;;;;;4744:14;4724:34;4704:54;;4768:17;4788:11;:16;;4803:1;4788:16;:50;;;;-1:-1:-1;4816:4:3;4808:25;:30;4788:50;4768:70;;4854:12;4853:13;:30;;;;;4871:12;4870:13;4853:30;4849:91;;;4906:23;;-1:-1:-1;;;4906:23:3;;;;;;;;;;;4849:91;4949:18;;-1:-1:-1;;4949:18:3;4966:1;4949:18;;;4977:67;;;;5011:22;;-1:-1:-1;;;;5011:22:3;-1:-1:-1;;;5011:22:3;;;4977:67;974:48:115::1;994:27;::::0;;::::1;1005:4:::0;994:27:::1;:::i;:::-;974:19;:48::i;:::-;1037:17;1049:4;;1037:17;;;;;;;:::i;:::-;;;;;;;;5068:14:3::0;5064:101;;;5098:23;;-1:-1:-1;;;;5098:23:3;;;5140:14;;-1:-1:-1;8945:50:192;;5140:14:3;;8933:2:192;8918:18;5140:14:3;;;;;;;5064:101;4092:1079;;;;;902:159:115;;:::o;4041:140:118:-;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4134:22:118;:20;:22::i;:::-;-1:-1:-1;;;;;4134:40:118;;;;;;;;:31;;;;:40;;;;;;;;;4127:47;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;4127:47:118;4041:140::o;4221:1639::-;4291:29;4323:22;:20;:22::i;:::-;4372:7;;;;-1:-1:-1;;;;;;;;4355:14:118;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;4291:54:118;;-1:-1:-1;4372:7:118;-1:-1:-1;;;;;4426:18:118;;;4422:1432;;-1:-1:-1;;;;;;4467:16:118;;;;;;:10;;;:16;;;;;;;;;4460:23;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2173:1:112;2165:9;;;4501:42:118;;:15;:42;4497:153;;;4593:15;4610:24;:6;2173:1:112;2165:9;;2071:111;4610:24:118;4570:65;;-1:-1:-1;;;4570:65:118;;;;;8026:25:192;;;;8099:10;8087:23;8067:18;;;8060:51;7999:18;;4570:65:118;;;;;;;;4497:153;4685:4;:16;;;4667:34;;:15;:34;4663:139;;;4770:16;;;;4728:59;;-1:-1:-1;;;4728:59:118;;4753:15;4728:59;;;8026:25:192;8099:10;8087:23;;;8067:18;;;8060:51;7999:18;;4728:59:118;7854:263:192;4663:139:118;4819:4;:18;;;4815:81;;;4864:17;;-1:-1:-1;;;;;;4864:17:118;;-1:-1:-1;;;;;6286:32:192;;4864:17:118;;;6268:51:192;6241:18;;4864:17:118;6122:203:192;4815:81:118;-1:-1:-1;;;;;4913:16:118;;;4909:503;;1377:1:112;1370:8;;1369:15;4949:95:118;;5009:16;;-1:-1:-1;;;5009:16:118;;;;;;;;;;;4949:95;1902:2:112;1895:9;;1894:16;5061:212:118;;5122:4;:16;;;:47;;;-1:-1:-1;;;;;;5143:14:118;;;;;;:10;;;:14;;;;;:26;;;;;;5142:27;5122:47;5118:137;;;5204:28;;-1:-1:-1;;;5204:28:118;;-1:-1:-1;;;;;9236:15:192;;;5204:28:118;;;9218:34:192;9288:15;;9268:18;;;9261:43;9153:18;;5204:28:118;9006:304:192;5118:137:118;4422:1432;;4909:503;1132:1:112;1125:8;;1124:15;5311:87:118;;5367:12;;-1:-1:-1;;;5367:12:118;;;;;;;;;;;4422:1432;886:1:112;879:8;;878:15;5442:79:118;;5494:12;;-1:-1:-1;;;5494:12:118;;;;;;;;;;;5442:79;-1:-1:-1;;;;;5538:16:118;;;5534:310;;-1:-1:-1;;;;;;5581:14:118;;;;;;:10;;;:14;;;;;;;;;5574:21;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1638:1:112;1631:8;;1630:15;;5617:41:118;;;;-1:-1:-1;5643:15:118;;5642:16;5617:41;5613:113;;;5689:18;;-1:-1:-1;;;5689:18:118;;-1:-1:-1;;;;;6286:32:192;;5689:18:118;;;6268:51:192;6241:18;;5689::118;6122:203:192;5613:113:118;5747:4;:18;;;5743:87;;;5796:15;;-1:-1:-1;;;;;;5796:15:118;;-1:-1:-1;;;;;6286:32:192;;5796:15:118;;;6268:51:192;6241:18;;5796:15:118;6122:203:192;5743:87:118;4281:1579;;;4221:1639;;:::o;3340:494::-;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3422:22:118;:20;:22::i;:::-;:28;;;3404:46;;3477:22;:7;886:1:112;879:8;878:15;;;796:104;3477:22:118;3460:39;;;;1132:1:112;1125:8;;1124:15;;3509:14:118;;;:39;1377:1:112;1370:8;;1369:15;;3558:18:118;;;:47;1638:1:112;1631:8;;1630:15;;3615:14:118;;;:39;1902:2:112;1895:9;;1894:16;;3664:22:118;;;:55;2173:1:112;2165:9;;;3729:42:118;;:14;;;:42;3800:27;:7;2444:2:112;2436:10;;2340:114;3800:27:118;3781:46;;:16;;;:46;-1:-1:-1;3781:1:118;3340:494::o;5926:340::-;-1:-1:-1;;;;;5983:20:118;;5979:69;;6026:11;;-1:-1:-1;;;6026:11:118;;;;;;;;;;;5979:69;6057:29;6089:22;:20;:22::i;:::-;6125:7;;6057:54;;-1:-1:-1;;;;;;6125:7:118;:21;6121:82;;6169:23;;-1:-1:-1;;;6169:23:118;;;;;;;;;;;6121:82;6212:16;;-1:-1:-1;;;;;;6212:16:118;-1:-1:-1;;;;;6212:16:118;;;;;;;6243;;;;-1:-1:-1;;6243:16:118;5969:297;5926:340;:::o;3042:119::-;3090:7;3116:22;:20;:22::i;:::-;:38;;;3109:45;;3042:119;:::o;694:141:115:-;-1:-1:-1;;;;;791:37:115;765:7;791:37;;;-1:-1:-1;;;;;;;;;;;791:37:115;;;;;;;694:141::o;8547:212:118:-;1086:22;:20;:22::i;:::-;:28;-1:-1:-1;;;;;1086:28:118;1073:51;966:10:5;1073:65:118;;-1:-1:-1;;;;;;1073:65:118;;;;;;;-1:-1:-1;;;;;6286:32:192;;;1073:65:118;;;6268:51:192;6241:18;;1073:65:118;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1068:115;;1161:11;;-1:-1:-1;;;1161:11:118;;;;;;;;;;;1068:115;8626:5:::1;8635:1;8626:10:::0;8622:59:::1;;8659:11;;-1:-1:-1::0;;;8659:11:118::1;;;;;;;;;;;8622:59;8690:27;8702:7;8711:5;8690:11;:27::i;:::-;8737:7;-1:-1:-1::0;;;;;8732:20:118::1;;8746:5;8732:20;;;;714:25:192::0;;702:2;687:18;;568:177;8732:20:118::1;;;;;;;;8547:212:::0;;:::o;3874:127::-;3926:7;3952:22;:20;:22::i;:::-;:42;;;3945:49;;3874:127;:::o;6926:259::-;700:65;1505:22;:20;:22::i;:::-;:28;-1:-1:-1;;;;;1505:28:118;1494:48;1543:4;966:10:5;1494:68:118;;-1:-1:-1;;;;;;1494:68:118;;;;;;;;;;7385:25:192;;;;-1:-1:-1;;;;;7446:32:192;7426:18;;;7419:60;7358:18;;1494:68:118;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1489:118;;1585:11;;-1:-1:-1;;;1585:11:118;;;;;;;;;;;1489:118;7095:22:::1;7050;:20;:22::i;:::-;:42;;:67:::0;7132:46:::1;::::0;714:25:192;;;7132:46:118::1;::::0;702:2:192;687:18;7132:46:118::1;;;;;;;;6926:259:::0;;:::o;7499:377::-;7579:29;7611:22;:20;:22::i;:::-;7579:54;;7655:1;:17;;;7647:5;:25;7643:116;;;7730:17;;;;7695:53;;-1:-1:-1;;;7695:53:118;;;;7723:5;;7695:53;;9489:25:192;;;9545:2;9530:18;;9523:34;9477:2;9462:18;;9315:248;7643:116:118;7789:5;7768:1;:17;;;:26;;;;;;;:::i;:::-;;;;-1:-1:-1;7809:30:118;;-1:-1:-1;7824:14:118;7832:5;7824:14;:::i;:::-;7809:30;;714:25:192;;;702:2;687:18;7809:30:118;;;;;;;7849:20;7854:7;7863:5;7849:4;:20::i;:::-;7569:307;7499:377;;:::o;536:118:115:-;590:7;-1:-1:-1;;;;;;;;;;;616:18:115;2225:170;6306:104:118;6374:7;:5;:7::i;:::-;6361:42;;-1:-1:-1;;;6361:42:118;;-1:-1:-1;;;;;6286:32:192;;;6361:42:118;;;6268:51:192;6361:33:118;;;;;;;6241:18:192;;6361:42:118;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2261:141;2317:7;2369:26;2387:7;2369:17;:26::i;:::-;2343:23;2358:7;2343:14;:23::i;:::-;:52;;;;:::i;6705:181::-;402:49;1505:22;:20;:22::i;:::-;:28;-1:-1:-1;;;;;1505:28:118;1494:48;1543:4;966:10:5;1494:68:118;;-1:-1:-1;;;;;;1494:68:118;;;;;;;;;;7385:25:192;;;;-1:-1:-1;;;;;7446:32:192;7426:18;;;7419:60;7358:18;;1494:68:118;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1489:118;;1585:11;;-1:-1:-1;;;1585:11:118;;;;;;;;;;;1489:118;6816:37:::1;6851:1;6816:34;:37::i;:::-;6785:22;:20;:22::i;:::-;:28;;:68:::0;6868:11:::1;::::0;::::1;::::0;::::1;::::0;6877:1;;6868:11:::1;:::i;3201:99::-:0;3239:7;3265:22;:20;:22::i;:::-;:28;-1:-1:-1;;;;;3265:28:118;;3201:99;-1:-1:-1;3201:99:118:o;8965:197::-;9075:24;;8965:197::o;1902:154:64:-;1993:4;2045;2016:25;2029:5;2036:4;2016:12;:25::i;:::-;:33;;1902:154;-1:-1:-1;;;;1902:154:64:o;1094:468:115:-;-1:-1:-1;;;;;1179:21:115;;1175:104;;1223:45;;-1:-1:-1;;;1223:45:115;;1265:1;1223:45;;;6268:51:192;6241:18;;1223:45:115;6122:203:192;1175:104:115;1288:33;1309:1;1313:7;1288:12;:33::i;:::-;1401:14;:23;;-1:-1:-1;;;;;;;;;;;2359:20:115;1419:5;;1331:39;;1401:23;;1419:5;;1401:23;:::i;:::-;;;;-1:-1:-1;;;;;;;1458:20:115;;:11;:20;;;;;;;;;;;:29;;;;;;1512:43;714:25:192;;;1512:43:115;;687:18:192;1512:43:115;;;;;;;1165:397;1094:468;;:::o;9071:205:3:-;9129:30;;3147:66;9186:27;8819:122;8792:167:118;6929:20:3;:18;:20::i;:::-;8932::118::1;8887:22;:20;:22::i;:::-;:42;;:65:::0;-1:-1:-1;8792:167:118:o;1568:651:115:-;-1:-1:-1;;;;;1653:21:115;;1649:102;;1697:43;;-1:-1:-1;;;1697:43:115;;1737:1;1697:43;;;6268:51:192;6241:18;;1697:43:115;6122:203:192;1649:102:115;1760:33;1773:7;1790:1;1760:12;:33::i;:::-;-1:-1:-1;;;;;1891:20:115;;1803:39;1891:20;;;-1:-1:-1;;;;;;;;;;;1891:20:115;;;;;;;;;1925:15;;;1921:115;;;1963:62;;-1:-1:-1;;;1963:62:115;;-1:-1:-1;;;;;11242:32:192;;1963:62:115;;;11224:51:192;11291:18;;;11284:34;;;11334:18;;;11327:34;;;11197:18;;1963:62:115;11022:345:192;1921:115:115;-1:-1:-1;;;;;2069:20:115;;:11;:20;;;;;;;;;;;2092:15;;;2069:38;;2121:14;;;:23;;;;;;;2169:43;714:25:192;;;2069:11:115;;:20;2169:43;;687:18:192;2169:43:115;;;;;;;1639:580;;1568:651;;:::o;2636:346:112:-;2711:7;2972:2;2951:16;;;;;;;;:::i;:::-;2943:25;;:31;;2925:1;2906:14;;;;;;;;:::i;:::-;2898:23;;:28;;2862:22;;;;;;;;:::i;:::-;:31;;2892:1;2862:31;;;2887:2;2862:31;2835:14;;;;;;;;:::i;:::-;:22;;2856:1;2835:22;;;2852:1;2835:22;2792:18;;;;;;;;:::i;:::-;:26;;2817:1;2792:26;;;2813:1;2792:26;2765:14;;;;;;;;:::i;:::-;:22;;2786:1;2765:22;;;2782:1;2765:22;2738:14;;;;:1;:14;:::i;:::-;:22;;2759:1;2738:22;;;2755:1;2738:22;2737:51;:82;:121;:157;:190;;;:238;2730:245;;2636:346;;;:::o;2457:308:64:-;2540:7;2582:4;2540:7;2596:134;2620:5;:12;2616:1;:16;2596:134;;;2668:51;2696:12;2710:5;2716:1;2710:8;;;;;;;;:::i;:::-;;;;;;;2668:27;:51::i;:::-;2653:66;-1:-1:-1;2634:3:64;;2596:134;;;-1:-1:-1;2746:12:64;2457:308;-1:-1:-1;;;2457:308:64:o;7082:141:3:-;7149:17;:15;:17::i;:::-;7144:73;;7189:17;;-1:-1:-1;;;7189:17:3;;;;;;;;;;;7144:73;7082:141::o;504:167:63:-;579:7;609:1;605;:5;:59;;864:13;928:15;;;963:4;956:15;;;1009:4;993:21;;605:59;;;-1:-1:-1;864:13:63;928:15;;;963:4;956:15;1009:4;993:21;;;504:167::o;8485:120:3:-;8535:4;8558:26;:24;:26::i;:::-;:40;-1:-1:-1;;;8558:40:3;;;;;;-1:-1:-1;8485:120:3:o;14:180:192:-;73:6;126:2;114:9;105:7;101:23;97:32;94:52;;;142:1;139;132:12;94:52;-1:-1:-1;165:23:192;;14:180;-1:-1:-1;14:180:192:o;199:173::-;267:20;;-1:-1:-1;;;;;316:31:192;;306:42;;296:70;;362:1;359;352:12;296:70;199:173;;;:::o;377:186::-;436:6;489:2;477:9;468:7;464:23;460:32;457:52;;;505:1;502;495:12;457:52;528:29;547:9;528:29;:::i;750:118::-;836:5;829:13;822:21;815:5;812:32;802:60;;858:1;855;848:12;802:60;750:118;:::o;873:128::-;938:20;;967:28;938:20;967:28;:::i;1006:163::-;1073:20;;1133:10;1122:22;;1112:33;;1102:61;;1159:1;1156;1149:12;1174:908;1272:6;1280;1324:9;1315:7;1311:23;1354:3;1350:2;1346:12;1343:32;;;1371:1;1368;1361:12;1343:32;1394:29;1413:9;1394:29;:::i;:::-;1384:39;-1:-1:-1;1457:4:192;-1:-1:-1;;1439:16:192;;1435:27;1432:47;;;1475:1;1472;1465:12;1432:47;;1508:2;1502:9;1550:4;1542:6;1538:17;1621:6;1609:10;1606:22;1585:18;1573:10;1570:34;1567:62;1564:185;;;1671:10;1666:3;1662:20;1659:1;1652:31;1706:4;1703:1;1696:15;1734:4;1731:1;1724:15;1564:185;1765:2;1758:22;1804:35;1835:2;1820:18;;1804:35;:::i;:::-;1796:6;1789:51;1873:35;1904:2;1893:9;1889:18;1873:35;:::i;:::-;1868:2;1860:6;1856:15;1849:60;1942:35;1973:2;1962:9;1958:18;1942:35;:::i;:::-;1937:2;1929:6;1925:15;1918:60;2011:39;2044:4;2033:9;2029:20;2011:39;:::i;:::-;2006:2;1998:6;1994:15;1987:64;2070:6;2060:16;;;1174:908;;;;;:::o;2087:689::-;2182:6;2190;2198;2251:2;2239:9;2230:7;2226:23;2222:32;2219:52;;;2267:1;2264;2257:12;2219:52;2290:29;2309:9;2290:29;:::i;:::-;2280:39;;2370:2;2359:9;2355:18;2342:32;2393:18;2434:2;2426:6;2423:14;2420:34;;;2450:1;2447;2440:12;2420:34;2488:6;2477:9;2473:22;2463:32;;2533:7;2526:4;2522:2;2518:13;2514:27;2504:55;;2555:1;2552;2545:12;2504:55;2595:2;2582:16;2621:2;2613:6;2610:14;2607:34;;;2637:1;2634;2627:12;2607:34;2690:7;2685:2;2675:6;2672:1;2668:14;2664:2;2660:23;2656:32;2653:45;2650:65;;;2711:1;2708;2701:12;2650:65;2742:2;2738;2734:11;2724:21;;2764:6;2754:16;;;;;2087:689;;;;;:::o;3069:254::-;3137:6;3145;3198:2;3186:9;3177:7;3173:23;3169:32;3166:52;;;3214:1;3211;3204:12;3166:52;3237:29;3256:9;3237:29;:::i;:::-;3227:39;3313:2;3298:18;;;;3285:32;;-1:-1:-1;;;3069:254:192:o;3328:591::-;3398:6;3406;3459:2;3447:9;3438:7;3434:23;3430:32;3427:52;;;3475:1;3472;3465:12;3427:52;3515:9;3502:23;3544:18;3585:2;3577:6;3574:14;3571:34;;;3601:1;3598;3591:12;3571:34;3639:6;3628:9;3624:22;3614:32;;3684:7;3677:4;3673:2;3669:13;3665:27;3655:55;;3706:1;3703;3696:12;3655:55;3746:2;3733:16;3772:2;3764:6;3761:14;3758:34;;;3788:1;3785;3778:12;3758:34;3833:7;3828:2;3819:6;3815:2;3811:15;3807:24;3804:37;3801:57;;;3854:1;3851;3844:12;3801:57;3885:2;3877:11;;;;;3907:6;;-1:-1:-1;3328:591:192;;-1:-1:-1;;;;3328:591:192:o;4709:260::-;4777:6;4785;4838:2;4826:9;4817:7;4813:23;4809:32;4806:52;;;4854:1;4851;4844:12;4806:52;4877:29;4896:9;4877:29;:::i;:::-;4867:39;;4925:38;4959:2;4948:9;4944:18;4925:38;:::i;:::-;4915:48;;4709:260;;;;;:::o;4974:760::-;5114:4;5156:3;5145:9;5141:19;5133:27;;5207:6;5201:13;5194:21;5187:29;5176:9;5169:48;5287:4;5279:6;5275:17;5269:24;5262:32;5255:40;5248:4;5237:9;5233:20;5226:70;5366:4;5358:6;5354:17;5348:24;5341:32;5334:40;5327:4;5316:9;5312:20;5305:70;5445:4;5437:6;5433:17;5427:24;5420:32;5413:40;5406:4;5395:9;5391:20;5384:70;5524:4;5516:6;5512:17;5506:24;5499:32;5492:40;5485:4;5474:9;5470:20;5463:70;5601:10;5593:4;5585:6;5581:17;5575:24;5571:41;5564:4;5553:9;5549:20;5542:71;5660:4;5652:6;5648:17;5642:24;5675:53;5722:4;5711:9;5707:20;5693:12;4182:10;4171:22;4159:35;;4106:94;5675:53;;4974:760;;;;:::o;5924:193::-;6009:6;6062:3;6050:9;6041:7;6037:23;6033:33;6030:53;;;6079:1;6076;6069:12;6030:53;-1:-1:-1;6102:9:192;5924:193;-1:-1:-1;5924:193:192:o;6330:245::-;6397:6;6450:2;6438:9;6429:7;6425:23;6421:32;6418:52;;;6466:1;6463;6456:12;6418:52;6498:9;6492:16;6517:28;6539:5;6517:28;:::i;6580:127::-;6641:10;6636:3;6632:20;6629:1;6622:31;6672:4;6669:1;6662:15;6696:4;6693:1;6686:15;6712:125;6777:9;;;6798:10;;;6795:36;;;6811:18;;:::i;7022:184::-;7092:6;7145:2;7133:9;7124:7;7120:23;7116:32;7113:52;;;7161:1;7158;7151:12;7113:52;-1:-1:-1;7184:16:192;;7022:184;-1:-1:-1;7022:184:192:o;7677:172::-;7744:10;7774;;;7786;;;7770:27;;7809:11;;;7806:37;;;7823:18;;:::i;8399:388::-;8556:2;8545:9;8538:21;8595:6;8590:2;8579:9;8575:18;8568:34;8652:6;8644;8639:2;8628:9;8624:18;8611:48;8708:1;8679:22;;;8703:2;8675:31;;;8668:42;;;;8771:2;8750:15;;;-1:-1:-1;;8746:29:192;8731:45;8727:54;;8399:388;-1:-1:-1;8399:388:192:o;9568:128::-;9635:9;;;9656:11;;;9653:37;;;9670:18;;:::i;9701:136::-;9736:3;-1:-1:-1;;;9757:22:192;;9754:48;;9782:18;;:::i;:::-;-1:-1:-1;9822:1:192;9818:13;;9701:136::o;9842:1175::-;10026:3;10011:19;;10052:20;;10081:28;10052:20;10081:28;:::i;:::-;10143:13;10136:21;10118:40;;10207:4;10195:17;;10182:31;10222:30;10182:31;10222:30;:::i;:::-;10297:15;10290:23;10283:4;10268:20;;10261:53;10363:4;10351:17;;10338:31;10378:30;10338:31;10378:30;:::i;:::-;10453:15;10446:23;10439:4;10424:20;;10417:53;10519:4;10507:17;;10494:31;10534:30;10494:31;10534:30;:::i;:::-;10609:15;10602:23;10595:4;10580:20;;10573:53;10655:34;10683:4;10671:17;;10655:34;:::i;:::-;2851:13;2844:21;10743:4;10728:20;;2832:34;10780:36;10810:4;10798:17;;10780:36;:::i;:::-;4182:10;4171:22;10874:4;10859:20;;4159:35;10911:36;10941:4;10929:17;;10911:36;:::i;:::-;4182:10;4171:22;;11005:4;10990:20;;4159:35;10956:55;4106:94;11372:184;11430:6;11483:2;11471:9;11462:7;11458:23;11454:32;11451:52;;;11499:1;11496;11489:12;11451:52;11522:28;11540:9;11522:28;:::i;11561:241::-;11617:6;11670:2;11658:9;11649:7;11645:23;11641:32;11638:52;;;11686:1;11683;11676:12;11638:52;11725:9;11712:23;11744:28;11766:5;11744:28;:::i;11807:127::-;11868:10;11863:3;11859:20;11856:1;11849:31;11899:4;11896:1;11889:15;11923:4;11920:1;11913:15", "linkReferences": {}, "immutableReferences": {"64865": [{"start": 5361, "length": 32}]}}, "methodIdentifiers": {"SET_ACCOUNT_INFO_ROLE()": "837b820c", "SET_FLAGS_ROLE()": "46d261ae", "SET_WHITELIST_MERKLE_ROOT_ROLE()": "8027e64d", "accounts(address)": "5e5c06e2", "activeShares()": "bfefcd7b", "activeSharesOf(address)": "9d66201b", "allocateShares(uint256)": "141caa0e", "allocatedShares()": "8c4429e9", "burn(address,uint256)": "9dc29fac", "claimShares(address)": "f31cb0c6", "claimableSharesOf(address)": "1c14724f", "flags()": "64cc4aa5", "initialize(bytes)": "439fab91", "isDepositorWhitelisted(address,bytes32[])": "31d3a224", "mint(address,uint256)": "40c10f19", "mintAllocatedShares(address,uint256)": "bdef952f", "setAccountInfo(address,(bool,bool,bool,uint32))": "21896ab1", "setFlags((bool,bool,bool,bool,bool,uint32,uint32))": "f8d21fed", "setVault(address)": "6817031b", "setWhitelistMerkleRoot(bytes32)": "bd32fb66", "sharesOf(address)": "f5eb42dc", "totalShares()": "3a98ef39", "updateChecks(address,address)": "6279d8af", "vault()": "fbfa77cf", "whitelistMerkleRoot()": "aa98e0c6"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name_\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"version_\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Blacklisted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"BurnPaused\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidReceiver\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSender\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Forbidden\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"globalLockup\",\"type\":\"uint32\"}],\"name\":\"GlobalLockupNotExpired\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"allocated\",\"type\":\"uint256\"}],\"name\":\"InsufficientAllocatedShares\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"limit\",\"type\":\"uint256\"}],\"name\":\"LimitExceeded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MintPaused\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"NotWhitelisted\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"targetedLockup\",\"type\":\"uint32\"}],\"name\":\"TargetedLockupNotExpired\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"TransferNotAllowed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"TransferPaused\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZeroValue\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"value\",\"type\":\"int256\"}],\"name\":\"AllocateShares\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"Burn\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"initParams\",\"type\":\"bytes\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"lockedUntil\",\"type\":\"uint32\"}],\"name\":\"Mint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"bool\",\"name\":\"canDeposit\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"canTransfer\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isBlacklisted\",\"type\":\"bool\"},{\"internalType\":\"uint32\",\"name\":\"lockedUntil\",\"type\":\"uint32\"}],\"indexed\":false,\"internalType\":\"struct IShareManager.AccountInfo\",\"name\":\"info\",\"type\":\"tuple\"}],\"name\":\"SetAccountInfo\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"hasMintPause\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"hasBurnPause\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"hasTransferPause\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"hasWhitelist\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"hasTransferWhitelist\",\"type\":\"bool\"},{\"internalType\":\"uint32\",\"name\":\"globalLockup\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"targetedLockup\",\"type\":\"uint32\"}],\"indexed\":false,\"internalType\":\"struct IShareManager.Flags\",\"name\":\"flags\",\"type\":\"tuple\"}],\"name\":\"SetFlags\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"SetVault\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"newWhitelistMerkleRoot\",\"type\":\"bytes32\"}],\"name\":\"SetWhitelistMerkleRoot\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"SET_ACCOUNT_INFO_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SET_FLAGS_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SET_WHITELIST_MERKLE_ROOT_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"accounts\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"canDeposit\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"canTransfer\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isBlacklisted\",\"type\":\"bool\"},{\"internalType\":\"uint32\",\"name\":\"lockedUntil\",\"type\":\"uint32\"}],\"internalType\":\"struct IShareManager.AccountInfo\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"activeShares\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"activeSharesOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"allocateShares\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"allocatedShares\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"burn\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"claimShares\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"claimableSharesOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"flags\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"hasMintPause\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"hasBurnPause\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"hasTransferPause\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"hasWhitelist\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"hasTransferWhitelist\",\"type\":\"bool\"},{\"internalType\":\"uint32\",\"name\":\"globalLockup\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"targetedLockup\",\"type\":\"uint32\"}],\"internalType\":\"struct IShareManager.Flags\",\"name\":\"f\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"bytes32[]\",\"name\":\"merkleProof\",\"type\":\"bytes32[]\"}],\"name\":\"isDepositorWhitelisted\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"mint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"mintAllocatedShares\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"bool\",\"name\":\"canDeposit\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"canTransfer\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isBlacklisted\",\"type\":\"bool\"},{\"internalType\":\"uint32\",\"name\":\"lockedUntil\",\"type\":\"uint32\"}],\"internalType\":\"struct IShareManager.AccountInfo\",\"name\":\"info\",\"type\":\"tuple\"}],\"name\":\"setAccountInfo\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"hasMintPause\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"hasBurnPause\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"hasTransferPause\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"hasWhitelist\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"hasTransferWhitelist\",\"type\":\"bool\"},{\"internalType\":\"uint32\",\"name\":\"globalLockup\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"targetedLockup\",\"type\":\"uint32\"}],\"internalType\":\"struct IShareManager.Flags\",\"name\":\"f\",\"type\":\"tuple\"}],\"name\":\"setFlags\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault_\",\"type\":\"address\"}],\"name\":\"setVault\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"newWhitelistMerkleRoot\",\"type\":\"bytes32\"}],\"name\":\"setWhitelistMerkleRoot\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"sharesOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalShares\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"updateChecks\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"vault\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"whitelistMerkleRoot\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"ERC20InsufficientBalance(address,uint256,uint256)\":[{\"details\":\"Indicates an error related to the current `balance` of a `sender`. Used in transfers.\",\"params\":{\"balance\":\"Current balance for the interacting account.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidReceiver(address)\":[{\"details\":\"Indicates a failure with the token `receiver`. Used in transfers.\",\"params\":{\"receiver\":\"Address to which tokens are being transferred.\"}}],\"ERC20InvalidSender(address)\":[{\"details\":\"Indicates a failure with the token `sender`. Used in transfers.\",\"params\":{\"sender\":\"Address whose tokens are being transferred.\"}}],\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}]},\"events\":{\"Initialized(bytes)\":{\"params\":{\"initParams\":\"The initialization parameters.\"}},\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"}},\"kind\":\"dev\",\"methods\":{\"accounts(address)\":{\"returns\":{\"_0\":\"Returns account-specific configuration and permissions\"}},\"activeShares()\":{\"returns\":{\"_0\":\"Returns total active shares across the vault\"}},\"activeSharesOf(address)\":{\"returns\":{\"_0\":\"Returns active shares for an account\"}},\"allocatedShares()\":{\"returns\":{\"_0\":\"uint256 Total allocated shares\"}},\"claimableSharesOf(address)\":{\"returns\":{\"_0\":\"Returns claimable shares for an account\"}},\"flags()\":{\"returns\":{\"f\":\"Returns current flag structure\"}},\"initialize(bytes)\":{\"params\":{\"initParams\":\"The initialization parameters.\"}},\"isDepositorWhitelisted(address,bytes32[])\":{\"returns\":{\"_0\":\"bool Returns true whether depositor is allowed under current Merkle root and flag settings\"}},\"sharesOf(address)\":{\"returns\":{\"_0\":\"Returns total shares (active + claimable) for an account\"}},\"totalShares()\":{\"returns\":{\"_0\":\"Total shares including active and claimable\"}},\"vault()\":{\"returns\":{\"_0\":\"address Returns address of the vault using this ShareManager\"}},\"whitelistMerkleRoot()\":{\"returns\":{\"_0\":\"bytes32 Returns Merkle root used for deposit whitelist verification\"}}},\"version\":1},\"userdoc\":{\"errors\":{\"Blacklisted(address)\":[{\"notice\":\"Blacklisted account tried to interact\"}],\"BurnPaused()\":[{\"notice\":\"Burning is currently paused\"}],\"Forbidden()\":[{\"notice\":\"Unauthorized call\"}],\"GlobalLockupNotExpired(uint256,uint32)\":[{\"notice\":\"Global lockup not yet expired\"}],\"InsufficientAllocatedShares(uint256,uint256)\":[{\"notice\":\"Attempted to mint more shares than pre-allocated\"}],\"LimitExceeded(uint256,uint256)\":[{\"notice\":\"Mint would exceed share limit\"}],\"MintPaused()\":[{\"notice\":\"Minting is currently paused\"}],\"NotWhitelisted(address)\":[{\"notice\":\"Account is not whitelisted to deposit\"}],\"TargetedLockupNotExpired(uint256,uint32)\":[{\"notice\":\"Targeted lockup not yet expired\"}],\"TransferNotAllowed(address,address)\":[{\"notice\":\"Transfer between accounts not allowed by whitelist\"}],\"TransferPaused()\":[{\"notice\":\"Transfers are currently paused\"}],\"ZeroValue()\":[{\"notice\":\"Provided value was zero\"}]},\"events\":{\"AllocateShares(int256)\":{\"notice\":\"Emitted when shares are allocated or removed (positive/negative)\"},\"Burn(address,uint256)\":{\"notice\":\"Emitted when shares are burned\"},\"Initialized(bytes)\":{\"notice\":\"Emitted once the entity has been initialized.\"},\"Mint(address,uint256,uint32)\":{\"notice\":\"Emitted when new shares are minted\"},\"SetAccountInfo(address,(bool,bool,bool,uint32))\":{\"notice\":\"Emitted when a user account is updated\"},\"SetFlags((bool,bool,bool,bool,bool,uint32,uint32))\":{\"notice\":\"Emitted when global flag configuration is changed\"},\"SetVault(address)\":{\"notice\":\"Emitted when vault is set\"},\"SetWhitelistMerkleRoot(bytes32)\":{\"notice\":\"Emitted when whitelist merkle root is changed\"}},\"kind\":\"user\",\"methods\":{\"allocateShares(uint256)\":{\"notice\":\"Allocates `shares` that can be later minted via `mintAllocatedShares`\"},\"burn(address,uint256)\":{\"notice\":\"Burns user's shares\"},\"claimShares(address)\":{\"notice\":\"Triggers share claiming from queue to user\"},\"initialize(bytes)\":{\"notice\":\"Initializes the factory-created entity with arbitrary initialization data.\"},\"mint(address,uint256)\":{\"notice\":\"Mints new shares to a user directly\"},\"mintAllocatedShares(address,uint256)\":{\"notice\":\"Mints shares from the allocated pool\"},\"setAccountInfo(address,(bool,bool,bool,uint32))\":{\"notice\":\"Sets permissions and flags for a specific account\"},\"setFlags((bool,bool,bool,bool,bool,uint32,uint32))\":{\"notice\":\"Sets global flag bitmask controlling mints, burns, lockups, etc.\"},\"setVault(address)\":{\"notice\":\"One-time vault assignment during initialization\"},\"setWhitelistMerkleRoot(bytes32)\":{\"notice\":\"Sets new whitelist merkle root\"},\"updateChecks(address,address)\":{\"notice\":\"Internal checks for mint/burn/transfer under flags, lockups, blacklists, etc.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/managers/BasicShareManager.sol\":\"BasicShareManager\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/contracts/access/AccessControlUpgradeable.sol\":{\"keccak256\":\"0x0d4d7d19a052a2ef9d17b28450133631188b895e1755747fa8ad0280aadfb534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://838551e10cc07e570b70aee1078e490658d3a0ab8f4adfd4f3f2a565200753da\",\"dweb:/ipfs/QmceT7R3Pqzywyxr3MSEA7VXU4axZAPS6BLy92iV97rGdG\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol\":{\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6\",\"dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/access/extensions/AccessControlEnumerableUpgradeable.sol\":{\"keccak256\":\"0xaec38804089a16494f7d45ebbbeab78fe05e5dc4bc2eae1d2af458fe999fb43c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://04796408e2c4a1051afb23319e9505edb595a3d9c663ff5d6fcfb8e185ab1d93\",\"dweb:/ipfs/Qma8ZTi4kGyqg45SyNibbJSqMF7broiQnJPHhEGLcZyF9U\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/token/ERC20/ERC20Upgradeable.sol\":{\"keccak256\":\"0xa0e86b70fa5a7bd63795161c0882676fde6a18bbb1991938ef014fb0184b4b13\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1727a5d693a56ed6af48bace20d8ec26232f0a8f78ff482c5a691cc5c348a019\",\"dweb:/ipfs/QmdXaYmoETBqAv4YBnSmZyuoovjQMsDjfjnCnBFqXhhQEB\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol\":{\"keccak256\":\"0x361126a17677994081cd9cb69c3f50cffff6e920d25cb7e428acdb1ae41d1866\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://19ae787a7dd001269cd60a394b1a5261b78925a0fc3a6f927beb2986a9aa56cf\",\"dweb:/ipfs/QmYLfXiuKmcRgTDBEDXMMjXU8t6JxsspUmjxYzqWS55oEv\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/introspection/ERC165Upgradeable.sol\":{\"keccak256\":\"0xc8ed8d2056934b7675b695dec032f2920c2f5c6cf33a17ca85650940675323ab\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3c8ccc75d1cd792d192aa09e54dd49ea35fe85baa9fcd17486f29227d9f29b89\",\"dweb:/ipfs/QmbboSbFUEiM9tdEgBwuTRb7bykFoJXZ7dsSr1PSREJXMr\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0\",\"dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/extensions/IAccessControlEnumerable.sol\":{\"keccak256\":\"0xca774fbe0568762efdc1a7cba31f09549c7fa96dbe97410f4843fa2f0bc000a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0187ffdbf3d61b6d86cba4fcd9826e53d876987d620533ee84c681bdaf0f3ba3\",\"dweb:/ipfs/QmVJDqdJv6uzHY7ifncfv2QJep8XTzS3bGb4s5Exhuv86m\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol\":{\"keccak256\":\"0xb25a4f11fa80c702bf5cd85adec90e6f6f507f32f4a8e6f5dbc31e8c10029486\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6917f8a323e7811f041aecd4d9fd6e92455a6fba38a797ac6f6e208c7912b79d\",\"dweb:/ipfs/QmShuYv55wYHGi4EFkDB8QfF7ZCHoKk2efyz3AWY1ExSq7\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x880da465c203cec76b10d72dbd87c80f387df4102274f23eea1f9c9b0918792b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://399594cd8bb0143bc9e55e0f1d071d0d8c850a394fb7a319d50edd55d9ed822b\",\"dweb:/ipfs/QmbPZzgtT6LEm9CMqWfagQFwETbV1ztpECBB1DtQHrKiRz\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol\":{\"keccak256\":\"0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049\",\"dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol\":{\"keccak256\":\"0x8decfa54cec979c824b044b8128cd91d713f72c71fd7dfa54974624d8c949898\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://271f914261a19d87117a777e0924ada545c16191ef9b00cc40b0134fc14ebc70\",\"dweb:/ipfs/QmdvVNWHGHQrGGPonZJs5NuzTevTjZRM2zayKrDJf7WBA2\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/Proxy.sol\":{\"keccak256\":\"0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac\",\"dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol\":{\"keccak256\":\"0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa\",\"dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol\":{\"keccak256\":\"0x46f86003755f50eff00a7c5aaf493ae62e024142b8aec4493a313851d3c14872\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://462c770cb667cc75ab22df5a29c50873b24b267274edf87ac5cfe0112bf4c3f7\",\"dweb:/ipfs/QmTfKk32AkQ2yyYZCYJ44V69EM5t9ryBFC6bRF7FVMHXvj\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol\":{\"keccak256\":\"0x92579f452fe663595a898cbac85d80bb3868a6c9f034f19ba7fbebdfa3b65a4d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d07a888cd4a26c7077a30dd99e9ac30fbe2752bfb37e58c65f06a3b192079ef3\",\"dweb:/ipfs/QmZCQY4FC4ynBv9dha4BBWM1vYxTUJPBdFBS4HEFe6XzVB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://df6f0c459663c9858b6cba2cda1d14a7d05a985bed6d2de72bd8e78c25ee79db\",\"dweb:/ipfs/QmeTTxZ7qVk9rjEv2R4CpCwdf8UMCcRqDNMvzNxHc3Fnn9\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0x70f2f713b13b7ce4610bcd0ac9fec0f3cc43693b043abcb8dc40a42a726eb330\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c13d13304ac79a83ab1c30168967d19e2203342ebbd6a9bbce4db7550522dcbf\",\"dweb:/ipfs/QmeN5jKMN2vw5bhacr6tkg78afbTTZUeaacNHqjWt4Ew1r\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol\":{\"keccak256\":\"0xb5afb8e8eebc4d1c6404df2f5e1e6d2c3d24fd01e5dfc855314951ecfaae462d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://78586466c424f076c6a2a551d848cfbe3f7c49e723830807598484a1047b3b34\",\"dweb:/ipfs/Qmb717ovcFxm7qgNKEShiV6M9SPR3v1qnNpAGH84D6w29p\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0xaaa1d17c1129b127a4a401db2fbd72960e2671474be3d08cae71ccdc42f7624c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cb2f27cd3952aa667e198fba0d9b7bcec52fbb12c16f013c25fe6fb52b29cc0e\",\"dweb:/ipfs/QmeuohBFoeyDPZA9JNCTEDz3VBfBD4EABWuWXVhHAuEpKR\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol\":{\"keccak256\":\"0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd\",\"dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol\":{\"keccak256\":\"0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd\",\"dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Errors.sol\":{\"keccak256\":\"0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf\",\"dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol\":{\"keccak256\":\"0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2\",\"dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/Hashes.sol\":{\"keccak256\":\"0x8cbd338f083224b4b6f0ff42cbda641a0a6c31ffcdca197452b97fe4d0918269\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f517dec5ba0c6491395acbf7f1d621f4e89e8f218bf5303c867b1c5ad70c6b11\",\"dweb:/ipfs/QmWmXHRLEw8W6ckth7NyYTU88YfvuS7xSsfae5ksL8qNUe\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/MerkleProof.sol\":{\"keccak256\":\"0x36a0c409c437a753cac9b92b75f93b0fbe92803bf2c8ff1517e54b247f166134\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f91ba472de411aa557cdbf6560c40750d87bd11c9060bc04d2ba7119af9d5a6\",\"dweb:/ipfs/QmQjtYo2i7dDvzCEzZ67bDoNSG4RrwMoxPWuqFmX5Xzpuw\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621\",\"dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol\":{\"keccak256\":\"0x93ce66a307d66cb14e95dbc8f20641cedf48b2d060bf3bda431d62a4493e6e8d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://10d4122ee13fd8dcd2f468a86031665345d2dfdda76993d1f51201bdeb746f9d\",\"dweb:/ipfs/QmWxbbYEgyDCjwT4ZtNwujJwB4vdnR2qEKwL2Zrz9JoSDU\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableMap.sol\":{\"keccak256\":\"0x5d0c69ef32eae686acc22acfb1a54df9c91356e69adec593aa69eba83ae8f6f4\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f7a9fca2cb3d802c4f8a2d0fda84ccc7f880264885801c7df08373501dd22282\",\"dweb:/ipfs/QmdtM2Loz9NzNTuQ3RXNQCPBjoPUJw35Ukpt1MSM9XH3cD\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol\":{\"keccak256\":\"0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231\",\"dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y\"]},\"src/interfaces/factories/IFactory.sol\":{\"keccak256\":\"0x838a308272620b2ae6fdf1f5b425f8ba1439ed24a7baad50599d3fb34eb0bfa7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7a801db63324ec50da072b69c67ca98eb89d026f01a4aa7e8f60d757bc82f86d\",\"dweb:/ipfs/QmaJ6WHAe8vfwX3A8yHyLW2aJKqkbQCKuthRg1QU2f3TuY\"]},\"src/interfaces/factories/IFactoryEntity.sol\":{\"keccak256\":\"0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812\",\"dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx\"]},\"src/interfaces/hooks/IHook.sol\":{\"keccak256\":\"0xf414604a4cbf1feb04f976a806eb2825fa7dcce6815e209fc7aa50c6fe845bb3\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://a727a899804756179d294bfb0975487e8c6ca11f68aa741604d400ec6dfbf241\",\"dweb:/ipfs/QmRqsAigFe71aHAG5SgyKQKVpnHoiVyiP6VmnkxFK9KRz9\"]},\"src/interfaces/hooks/IRedeemHook.sol\":{\"keccak256\":\"0xd3df4e8013847ea38f603529d1062e6fccfc757ea62339bfbdd72719d2f7fbb1\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cddd2d10f020fa96f03b728dfcf8c7874942a19d871414010437ca3cdaef2974\",\"dweb:/ipfs/QmUZwodA14vQg3ntpzF6YxVQ2JCDmjXgQKrzE6HzJ19Mq8\"]},\"src/interfaces/managers/IFeeManager.sol\":{\"keccak256\":\"0x37d3c06ed6b23e367216c2dfc14f99f3fd6d686c984eca124406c2002e4f1516\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://5b3b1b974856f76c6fe2347895ab05e973c84c963fdf8f5483c37e2e4d434cad\",\"dweb:/ipfs/QmVeLCrCWgeQ3aS4V38y9FszFvZdSkbPHKKCMWKKM3V4qw\"]},\"src/interfaces/managers/IRiskManager.sol\":{\"keccak256\":\"0xb0dda9606a25e8187fb20cff7eb99dfecde18fbea30bae52a2124fd22afda97a\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://fee1bae75e2154b21e5ea2382ff21cd3e2ff2ea3a5e67828cfe4b563ee86e14e\",\"dweb:/ipfs/QmUtFozDVfCzehRMYxhJwZAbLJ43afzzVQDA89JD1HG1YH\"]},\"src/interfaces/managers/IShareManager.sol\":{\"keccak256\":\"0xb75fcb890e9a3e706c0d87781700965aa24faa8461967ee62b140d45f4c399e8\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ae50d1dda35eb9f6fa035fc39c2424f2e2eb2d41e6b0ffed296719818b1d9502\",\"dweb:/ipfs/Qme5jPDZG3WTLYtT2s8VFs3MKJVXgL9T5AdAzoXuuqM79z\"]},\"src/interfaces/modules/IACLModule.sol\":{\"keccak256\":\"0xf6ce9b4f4af5475d152845d6afa6f57b2d376c3a6d3eec2b5ac9650901a78243\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://f851cef38b6188b20be98c95047c8bd9d8651d37421a54e1d02b60f9f372c5ed\",\"dweb:/ipfs/QmaymGDd2RoKweuASXCiV3rPuhwvLXUvb6wBgi8xXoiYT8\"]},\"src/interfaces/modules/IBaseModule.sol\":{\"keccak256\":\"0x9c5c95ceb39f4c39f81f2afc7ddb55389a21315e76047a9cf66521db4dea68c3\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ccfb0453b14b105cb5990978a49c1e08f812563a046e17c354f4f1c648f95fe7\",\"dweb:/ipfs/QmYMAKaVdRq3NoTMfqadxgMMmUq6ejzTw2Lpsa3e9a3eyj\"]},\"src/interfaces/modules/IShareModule.sol\":{\"keccak256\":\"0x65ef35a38441080ad4e0c77c7668d6717fe9699df679ed4d2c775f47d1dce57a\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://e28e0e9e2dd2097df748fa2015ce4582f489615040b5323094880a618c54edac\",\"dweb:/ipfs/QmXNvxJDQ6edM3ExmmiTEmS5UreWYTDBgaAygU2kM6mgC6\"]},\"src/interfaces/modules/ISubvaultModule.sol\":{\"keccak256\":\"0x8db6f1a3bd2506dd0db82f4138f0436602ed37487717095a804dee81e44f5e0e\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://2547472f59c19612916f3f54e6fa69644ec31f14c669aa66dea3c006bc71bccc\",\"dweb:/ipfs/QmZ9VRCsofVntdB7siM3pfypUpgJnmaJAC1n8HviQb3K3U\"]},\"src/interfaces/modules/IVaultModule.sol\":{\"keccak256\":\"0x7696792b852bc9b435e6388a19f4284854259df2f50b45e2b833b6f9dd1b1648\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4364151c043fc5e56bc87c2cdbffc169b80cd745618a4b0da14e271752de6637\",\"dweb:/ipfs/Qmdfx3L8goy8eWFGimtsh58Se1vuLsukUw2mXo5chCbTy2\"]},\"src/interfaces/modules/IVerifierModule.sol\":{\"keccak256\":\"0x8b8ad9ea43b139afccc77ceb0fdcc34615c7401cc13e99c723895302049065a5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://0d578b1a55a180c652ee24e1b35f6d70d65bc067770dd791db44d0bcfa7a5d38\",\"dweb:/ipfs/QmexSPCfixS9spdjCydR4FUaoN4J188sfLkRmJaia14yk6\"]},\"src/interfaces/oracles/IOracle.sol\":{\"keccak256\":\"0xcbf84560c1fb9f7d3445cbbca4f5f3406606496f30867222d8e3deabad741296\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c90107edcd30e29044a5a8b05e71e728b93a485e7e7ef1a5b4ff695430e33a5e\",\"dweb:/ipfs/QmfHGNxtRQWeq7JENynxZNfhfTBytwbRwyekUyuHWrUyjK\"]},\"src/interfaces/permissions/ICustomVerifier.sol\":{\"keccak256\":\"0xdb343366ed86f63f8a94ff478c5c8e1d127888e4993c27fcb3bb3b51ae0cb8c7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://12407e89b20982a00961ccc03258e353962607747f214940d18b0989c11547c9\",\"dweb:/ipfs/Qmay19iWpwiV8LuvWccPjsazVU5Kkhme9QykFfLKfBmvSr\"]},\"src/interfaces/permissions/IMellowACL.sol\":{\"keccak256\":\"0xcc86b0ce4e4a53c1c4fa16ad79f9de0e2a1f1a77a1f256c0856120a9d6ea36df\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://6942df2ffadacae5563e01ec60697f16116362ba84a856a346b36bc7b9dd1ec2\",\"dweb:/ipfs/QmNYhqZGpS98UP89CS86CjoG4ZWbMHzTHRtd7bZzQqn3UY\"]},\"src/interfaces/permissions/IVerifier.sol\":{\"keccak256\":\"0x4b42731d76efaffcb017577bd2a478d00a3058c69220d6ea8e26f30f74476be7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ff2a6f09a946dd8a470dfe495b2a64d24b849c6d071dc859589140fb1b983362\",\"dweb:/ipfs/Qmb6UoqkmaC8tJMqkMfjcWR7pn1WCwPwuKKvUbLBQbPcJS\"]},\"src/interfaces/queues/IDepositQueue.sol\":{\"keccak256\":\"0x4ef38cb59d419010f453e62fe101efec25bec85a2ce1a24048098034060db47b\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://20e51dd02001309f23f46abea409edf77a39522bf21eeffb858ac888e4200e72\",\"dweb:/ipfs/QmWm9gfMuXz8nwuHb3he7abxTRgQw35x2viwMVuiNYAY5b\"]},\"src/interfaces/queues/IQueue.sol\":{\"keccak256\":\"0xd12e34631b204821c77eaa5b2e104902d740e3530cd716f7fc991af01a73b9ab\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://1f2a4d664244be639da1a5025ebccc8c1127e087a81ce6d0fc76af5134fec311\",\"dweb:/ipfs/QmS1GJwxGCV89myCAzLBAbmSrY6YL31G3nGj542dDM4QmT\"]},\"src/interfaces/queues/IRedeemQueue.sol\":{\"keccak256\":\"0xf8f95b95de259c8ccf29976f34d97a0ef31c9827f41ebcd5e8de54c10e646076\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://45ae0d496e865d846bf0c980593c1d16c038abf2b9359906224aa94cccf1c73f\",\"dweb:/ipfs/QmYTDXSTpUqhzJDXSKjoSKxwwQYee7wSTtf617wDsnnCC5\"]},\"src/libraries/FenwickTreeLibrary.sol\":{\"keccak256\":\"0xfd159ed27b96f288c4220febac4bfe286e4545b8ca7f523ae6e63d123341f8a3\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://58f0cc202896722786140571b111bf5e589730358a24b2940a450c4c75d864fe\",\"dweb:/ipfs/QmeRSjvcYURUSpvFe4r4VRwwpHcb1NmqiEUDEJ5rBxAxuf\"]},\"src/libraries/ShareManagerFlagLibrary.sol\":{\"keccak256\":\"0xa9e87a282a825831eac2bce34a80ac6f09b366e539e82b73e03aa878cb37777a\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://a08d19e7c740def72b930d138ded20e9955a603be032f0e733b86fc75dca79cf\",\"dweb:/ipfs/QmQwgoyT6pGbEStT3ELUcFDrt3bxJLY93UTentMs7awDt3\"]},\"src/libraries/SlotLibrary.sol\":{\"keccak256\":\"0x35dcf16feec83c772add30250b0987ce29ccfbf308d04159701ebf9243444eeb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://97658d0c93ecc8174169e643d5a24c515b55905a2fcaaa6d6ec0188a3dd71a10\",\"dweb:/ipfs/QmS1z7ac8RbMvz15gVQ9YA1cCT5bs6KMHCiDhgcG8Pq8oK\"]},\"src/managers/BasicShareManager.sol\":{\"keccak256\":\"0x7c2c7e0ac3de3b140b50617163ca1b6cd896b6698633c36da296e30af05eab40\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://220a9dcbd0cce7d870826f0931ee51e3faffe4f381d6bc9a16759e3731e3f0d9\",\"dweb:/ipfs/QmRiJZHJzz8rDk8VGY8EwS86pDHhSADWdJVzYw2Z3ZjP9d\"]},\"src/managers/ShareManager.sol\":{\"keccak256\":\"0x9fb432d1538b21f969e9701f726d720dc34fe695f9d64f04ea9d3e30d1a0398c\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://f11a6862c1256a5f33b157e0b58de7411a043ef86597228e09ba7591d0b55526\",\"dweb:/ipfs/QmT6Np94nTTzjWvVHryYm7UPqyCc7eogu5NqTmzyiLeSts\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "uint256", "name": "version_", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "Blacklisted"}, {"inputs": [], "type": "error", "name": "BurnPaused"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientBalance"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "type": "error", "name": "ERC20InvalidReceiver"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "type": "error", "name": "ERC20InvalidSender"}, {"inputs": [], "type": "error", "name": "Forbidden"}, {"inputs": [{"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint32", "name": "globalLockup", "type": "uint32"}], "type": "error", "name": "GlobalLockupNotExpired"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "allocated", "type": "uint256"}], "type": "error", "name": "InsufficientAllocatedShares"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "limit", "type": "uint256"}], "type": "error", "name": "LimitExceeded"}, {"inputs": [], "type": "error", "name": "MintPaused"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"inputs": [{"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint32", "name": "targetedLockup", "type": "uint32"}], "type": "error", "name": "TargetedLockupNotExpired"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}], "type": "error", "name": "TransferNotAllowed"}, {"inputs": [], "type": "error", "name": "TransferPaused"}, {"inputs": [], "type": "error", "name": "ZeroValue"}, {"inputs": [{"internalType": "int256", "name": "value", "type": "int256", "indexed": false}], "type": "event", "name": "AllocateShares", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "shares", "type": "uint256", "indexed": false}], "type": "event", "name": "Burn", "anonymous": false}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "initParams", "type": "bytes", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "shares", "type": "uint256", "indexed": false}, {"internalType": "uint32", "name": "lockedUntil", "type": "uint32", "indexed": false}], "type": "event", "name": "Mint", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "struct IShareManager.AccountInfo", "name": "info", "type": "tuple", "components": [{"internalType": "bool", "name": "canDeposit", "type": "bool"}, {"internalType": "bool", "name": "canTransfer", "type": "bool"}, {"internalType": "bool", "name": "isBlacklisted", "type": "bool"}, {"internalType": "uint32", "name": "lockedUntil", "type": "uint32"}], "indexed": false}], "type": "event", "name": "SetAccountInfo", "anonymous": false}, {"inputs": [{"internalType": "struct IShareManager.Flags", "name": "flags", "type": "tuple", "components": [{"internalType": "bool", "name": "hasMintPause", "type": "bool"}, {"internalType": "bool", "name": "hasBurnPause", "type": "bool"}, {"internalType": "bool", "name": "hasTransferPause", "type": "bool"}, {"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool"}, {"internalType": "bool", "name": "hasTransferW<PERSON><PERSON>st", "type": "bool"}, {"internalType": "uint32", "name": "globalLockup", "type": "uint32"}, {"internalType": "uint32", "name": "targetedLockup", "type": "uint32"}], "indexed": false}], "type": "event", "name": "SetFlags", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address", "indexed": true}], "type": "event", "name": "<PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "newWhitelistMerkleRoot", "type": "bytes32", "indexed": false}], "type": "event", "name": "SetWhitelistMerkleRoot", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SET_ACCOUNT_INFO_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SET_FLAGS_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SET_WHITELIST_MERKLE_ROOT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "accounts", "outputs": [{"internalType": "struct IShareManager.AccountInfo", "name": "", "type": "tuple", "components": [{"internalType": "bool", "name": "canDeposit", "type": "bool"}, {"internalType": "bool", "name": "canTransfer", "type": "bool"}, {"internalType": "bool", "name": "isBlacklisted", "type": "bool"}, {"internalType": "uint32", "name": "lockedUntil", "type": "uint32"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "activeShares", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "activeSharesOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "allocateShares"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "allocatedShares", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "burn"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "claimShares"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "claimableSharesOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "flags", "outputs": [{"internalType": "struct IShareManager.Flags", "name": "f", "type": "tuple", "components": [{"internalType": "bool", "name": "hasMintPause", "type": "bool"}, {"internalType": "bool", "name": "hasBurnPause", "type": "bool"}, {"internalType": "bool", "name": "hasTransferPause", "type": "bool"}, {"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool"}, {"internalType": "bool", "name": "hasTransferW<PERSON><PERSON>st", "type": "bool"}, {"internalType": "uint32", "name": "globalLockup", "type": "uint32"}, {"internalType": "uint32", "name": "targetedLockup", "type": "uint32"}]}]}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32[]", "name": "merkleProof", "type": "bytes32[]"}], "stateMutability": "view", "type": "function", "name": "isDep<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "mint"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "mintAllocatedShares"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "struct IShareManager.AccountInfo", "name": "info", "type": "tuple", "components": [{"internalType": "bool", "name": "canDeposit", "type": "bool"}, {"internalType": "bool", "name": "canTransfer", "type": "bool"}, {"internalType": "bool", "name": "isBlacklisted", "type": "bool"}, {"internalType": "uint32", "name": "lockedUntil", "type": "uint32"}]}], "stateMutability": "nonpayable", "type": "function", "name": "setAccountInfo"}, {"inputs": [{"internalType": "struct IShareManager.Flags", "name": "f", "type": "tuple", "components": [{"internalType": "bool", "name": "hasMintPause", "type": "bool"}, {"internalType": "bool", "name": "hasBurnPause", "type": "bool"}, {"internalType": "bool", "name": "hasTransferPause", "type": "bool"}, {"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool"}, {"internalType": "bool", "name": "hasTransferW<PERSON><PERSON>st", "type": "bool"}, {"internalType": "uint32", "name": "globalLockup", "type": "uint32"}, {"internalType": "uint32", "name": "targetedLockup", "type": "uint32"}]}], "stateMutability": "nonpayable", "type": "function", "name": "setFlags"}, {"inputs": [{"internalType": "address", "name": "vault_", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "<PERSON><PERSON><PERSON>"}, {"inputs": [{"internalType": "bytes32", "name": "newWhitelistMerkleRoot", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "setWhitelistMerkleRoot"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "sharesOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalShares", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}], "stateMutability": "view", "type": "function", "name": "updateChecks"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "vault", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "whitelist<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}], "devdoc": {"kind": "dev", "methods": {"accounts(address)": {"returns": {"_0": "Returns account-specific configuration and permissions"}}, "activeShares()": {"returns": {"_0": "Returns total active shares across the vault"}}, "activeSharesOf(address)": {"returns": {"_0": "Returns active shares for an account"}}, "allocatedShares()": {"returns": {"_0": "uint256 Total allocated shares"}}, "claimableSharesOf(address)": {"returns": {"_0": "Returns claimable shares for an account"}}, "flags()": {"returns": {"f": "Returns current flag structure"}}, "initialize(bytes)": {"params": {"initParams": "The initialization parameters."}}, "isDepositorWhitelisted(address,bytes32[])": {"returns": {"_0": "bool Returns true whether depositor is allowed under current Merkle root and flag settings"}}, "sharesOf(address)": {"returns": {"_0": "Returns total shares (active + claimable) for an account"}}, "totalShares()": {"returns": {"_0": "Total shares including active and claimable"}}, "vault()": {"returns": {"_0": "address Returns address of the vault using this ShareManager"}}, "whitelistMerkleRoot()": {"returns": {"_0": "bytes32 Returns Merkle root used for deposit whitelist verification"}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"allocateShares(uint256)": {"notice": "Allocates `shares` that can be later minted via `mintAllocatedShares`"}, "burn(address,uint256)": {"notice": "Burns user's shares"}, "claimShares(address)": {"notice": "Triggers share claiming from queue to user"}, "initialize(bytes)": {"notice": "Initializes the factory-created entity with arbitrary initialization data."}, "mint(address,uint256)": {"notice": "Mints new shares to a user directly"}, "mintAllocatedShares(address,uint256)": {"notice": "Mints shares from the allocated pool"}, "setAccountInfo(address,(bool,bool,bool,uint32))": {"notice": "Sets permissions and flags for a specific account"}, "setFlags((bool,bool,bool,bool,bool,uint32,uint32))": {"notice": "Sets global flag bitmask controlling mints, burns, lockups, etc."}, "setVault(address)": {"notice": "One-time vault assignment during initialization"}, "setWhitelistMerkleRoot(bytes32)": {"notice": "Sets new whitelist merkle root"}, "updateChecks(address,address)": {"notice": "Internal checks for mint/burn/transfer under flags, lockups, blacklists, etc."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/managers/BasicShareManager.sol": "BasicShareManager"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/contracts/access/AccessControlUpgradeable.sol": {"keccak256": "0x0d4d7d19a052a2ef9d17b28450133631188b895e1755747fa8ad0280aadfb534", "urls": ["bzz-raw://838551e10cc07e570b70aee1078e490658d3a0ab8f4adfd4f3f2a565200753da", "dweb:/ipfs/QmceT7R3Pqzywyxr3MSEA7VXU4axZAPS6BLy92iV97rGdG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"keccak256": "0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a", "urls": ["bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6", "dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/access/extensions/AccessControlEnumerableUpgradeable.sol": {"keccak256": "0xaec38804089a16494f7d45ebbbeab78fe05e5dc4bc2eae1d2af458fe999fb43c", "urls": ["bzz-raw://04796408e2c4a1051afb23319e9505edb595a3d9c663ff5d6fcfb8e185ab1d93", "dweb:/ipfs/Qma8ZTi4kGyqg45SyNibbJSqMF7broiQnJPHhEGLcZyF9U"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05", "urls": ["bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08", "dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/token/ERC20/ERC20Upgradeable.sol": {"keccak256": "0xa0e86b70fa5a7bd63795161c0882676fde6a18bbb1991938ef014fb0184b4b13", "urls": ["bzz-raw://1727a5d693a56ed6af48bace20d8ec26232f0a8f78ff482c5a691cc5c348a019", "dweb:/ipfs/QmdXaYmoETBqAv4YBnSmZyuoovjQMsDjfjnCnBFqXhhQEB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol": {"keccak256": "0x361126a17677994081cd9cb69c3f50cffff6e920d25cb7e428acdb1ae41d1866", "urls": ["bzz-raw://19ae787a7dd001269cd60a394b1a5261b78925a0fc3a6f927beb2986a9aa56cf", "dweb:/ipfs/QmYLfXiuKmcRgTDBEDXMMjXU8t6JxsspUmjxYzqWS55oEv"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/introspection/ERC165Upgradeable.sol": {"keccak256": "0xc8ed8d2056934b7675b695dec032f2920c2f5c6cf33a17ca85650940675323ab", "urls": ["bzz-raw://3c8ccc75d1cd792d192aa09e54dd49ea35fe85baa9fcd17486f29227d9f29b89", "dweb:/ipfs/QmbboSbFUEiM9tdEgBwuTRb7bykFoJXZ7dsSr1PSREJXMr"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530", "urls": ["bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0", "dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/extensions/IAccessControlEnumerable.sol": {"keccak256": "0xca774fbe0568762efdc1a7cba31f09549c7fa96dbe97410f4843fa2f0bc000a3", "urls": ["bzz-raw://0187ffdbf3d61b6d86cba4fcd9826e53d876987d620533ee84c681bdaf0f3ba3", "dweb:/ipfs/QmVJDqdJv6uzHY7ifncfv2QJep8XTzS3bGb4s5Exhuv86m"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol": {"keccak256": "0xb25a4f11fa80c702bf5cd85adec90e6f6f507f32f4a8e6f5dbc31e8c10029486", "urls": ["bzz-raw://6917f8a323e7811f041aecd4d9fd6e92455a6fba38a797ac6f6e208c7912b79d", "dweb:/ipfs/QmShuYv55wYHGi4EFkDB8QfF7ZCHoKk2efyz3AWY1ExSq7"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x880da465c203cec76b10d72dbd87c80f387df4102274f23eea1f9c9b0918792b", "urls": ["bzz-raw://399594cd8bb0143bc9e55e0f1d071d0d8c850a394fb7a319d50edd55d9ed822b", "dweb:/ipfs/QmbPZzgtT6LEm9CMqWfagQFwETbV1ztpECBB1DtQHrKiRz"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol": {"keccak256": "0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e", "urls": ["bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049", "dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol": {"keccak256": "0x8decfa54cec979c824b044b8128cd91d713f72c71fd7dfa54974624d8c949898", "urls": ["bzz-raw://271f914261a19d87117a777e0924ada545c16191ef9b00cc40b0134fc14ebc70", "dweb:/ipfs/QmdvVNWHGHQrGGPonZJs5NuzTevTjZRM2zayKrDJf7WBA2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/Proxy.sol": {"keccak256": "0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd", "urls": ["bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac", "dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol": {"keccak256": "0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c", "urls": ["bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa", "dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol": {"keccak256": "0x46f86003755f50eff00a7c5aaf493ae62e024142b8aec4493a313851d3c14872", "urls": ["bzz-raw://462c770cb667cc75ab22df5a29c50873b24b267274edf87ac5cfe0112bf4c3f7", "dweb:/ipfs/QmTfKk32AkQ2yyYZCYJ44V69EM5t9ryBFC6bRF7FVMHXvj"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol": {"keccak256": "0x92579f452fe663595a898cbac85d80bb3868a6c9f034f19ba7fbebdfa3b65a4d", "urls": ["bzz-raw://d07a888cd4a26c7077a30dd99e9ac30fbe2752bfb37e58c65f06a3b192079ef3", "dweb:/ipfs/QmZCQY4FC4ynBv9dha4BBWM1vYxTUJPBdFBS4HEFe6XzVB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7", "urls": ["bzz-raw://df6f0c459663c9858b6cba2cda1d14a7d05a985bed6d2de72bd8e78c25ee79db", "dweb:/ipfs/QmeTTxZ7qVk9rjEv2R4CpCwdf8UMCcRqDNMvzNxHc3Fnn9"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0x70f2f713b13b7ce4610bcd0ac9fec0f3cc43693b043abcb8dc40a42a726eb330", "urls": ["bzz-raw://c13d13304ac79a83ab1c30168967d19e2203342ebbd6a9bbce4db7550522dcbf", "dweb:/ipfs/QmeN5jKMN2vw5bhacr6tkg78afbTTZUeaacNHqjWt4Ew1r"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol": {"keccak256": "0xb5afb8e8eebc4d1c6404df2f5e1e6d2c3d24fd01e5dfc855314951ecfaae462d", "urls": ["bzz-raw://78586466c424f076c6a2a551d848cfbe3f7c49e723830807598484a1047b3b34", "dweb:/ipfs/Qmb717ovcFxm7qgNKEShiV6M9SPR3v1qnNpAGH84D6w29p"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0xaaa1d17c1129b127a4a401db2fbd72960e2671474be3d08cae71ccdc42f7624c", "urls": ["bzz-raw://cb2f27cd3952aa667e198fba0d9b7bcec52fbb12c16f013c25fe6fb52b29cc0e", "dweb:/ipfs/QmeuohBFoeyDPZA9JNCTEDz3VBfBD4EABWuWXVhHAuEpKR"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol": {"keccak256": "0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6", "urls": ["bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd", "dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol": {"keccak256": "0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58", "urls": ["bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd", "dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Errors.sol": {"keccak256": "0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123", "urls": ["bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf", "dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol": {"keccak256": "0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26", "urls": ["bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2", "dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/Hashes.sol": {"keccak256": "0x8cbd338f083224b4b6f0ff42cbda641a0a6c31ffcdca197452b97fe4d0918269", "urls": ["bzz-raw://f517dec5ba0c6491395acbf7f1d621f4e89e8f218bf5303c867b1c5ad70c6b11", "dweb:/ipfs/QmWmXHRLEw8W6ckth7NyYTU88YfvuS7xSsfae5ksL8qNUe"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/MerkleProof.sol": {"keccak256": "0x36a0c409c437a753cac9b92b75f93b0fbe92803bf2c8ff1517e54b247f166134", "urls": ["bzz-raw://0f91ba472de411aa557cdbf6560c40750d87bd11c9060bc04d2ba7119af9d5a6", "dweb:/ipfs/QmQjtYo2i7dDvzCEzZ67bDoNSG4RrwMoxPWuqFmX5Xzpuw"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8", "urls": ["bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621", "dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol": {"keccak256": "0x93ce66a307d66cb14e95dbc8f20641cedf48b2d060bf3bda431d62a4493e6e8d", "urls": ["bzz-raw://10d4122ee13fd8dcd2f468a86031665345d2dfdda76993d1f51201bdeb746f9d", "dweb:/ipfs/QmWxbbYEgyDCjwT4ZtNwujJwB4vdnR2qEKwL2Zrz9JoSDU"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableMap.sol": {"keccak256": "0x5d0c69ef32eae686acc22acfb1a54df9c91356e69adec593aa69eba83ae8f6f4", "urls": ["bzz-raw://f7a9fca2cb3d802c4f8a2d0fda84ccc7f880264885801c7df08373501dd22282", "dweb:/ipfs/QmdtM2Loz9NzNTuQ3RXNQCPBjoPUJw35Ukpt1MSM9XH3cD"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol": {"keccak256": "0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06", "urls": ["bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231", "dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y"], "license": "MIT"}, "src/interfaces/factories/IFactory.sol": {"keccak256": "0x838a308272620b2ae6fdf1f5b425f8ba1439ed24a7baad50599d3fb34eb0bfa7", "urls": ["bzz-raw://7a801db63324ec50da072b69c67ca98eb89d026f01a4aa7e8f60d757bc82f86d", "dweb:/ipfs/QmaJ6WHAe8vfwX3A8yHyLW2aJKqkbQCKuthRg1QU2f3TuY"], "license": "BUSL-1.1"}, "src/interfaces/factories/IFactoryEntity.sol": {"keccak256": "0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5", "urls": ["bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812", "dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx"], "license": "BUSL-1.1"}, "src/interfaces/hooks/IHook.sol": {"keccak256": "0xf414604a4cbf1feb04f976a806eb2825fa7dcce6815e209fc7aa50c6fe845bb3", "urls": ["bzz-raw://a727a899804756179d294bfb0975487e8c6ca11f68aa741604d400ec6dfbf241", "dweb:/ipfs/QmRqsAigFe71aHAG5SgyKQKVpnHoiVyiP6VmnkxFK9KRz9"], "license": "BUSL-1.1"}, "src/interfaces/hooks/IRedeemHook.sol": {"keccak256": "0xd3df4e8013847ea38f603529d1062e6fccfc757ea62339bfbdd72719d2f7fbb1", "urls": ["bzz-raw://cddd2d10f020fa96f03b728dfcf8c7874942a19d871414010437ca3cdaef2974", "dweb:/ipfs/QmUZwodA14vQg3ntpzF6YxVQ2JCDmjXgQKrzE6HzJ19Mq8"], "license": "BUSL-1.1"}, "src/interfaces/managers/IFeeManager.sol": {"keccak256": "0x37d3c06ed6b23e367216c2dfc14f99f3fd6d686c984eca124406c2002e4f1516", "urls": ["bzz-raw://5b3b1b974856f76c6fe2347895ab05e973c84c963fdf8f5483c37e2e4d434cad", "dweb:/ipfs/QmVeLCrCWgeQ3aS4V38y9FszFvZdSkbPHKKCMWKKM3V4qw"], "license": "BUSL-1.1"}, "src/interfaces/managers/IRiskManager.sol": {"keccak256": "0xb0dda9606a25e8187fb20cff7eb99dfecde18fbea30bae52a2124fd22afda97a", "urls": ["bzz-raw://fee1bae75e2154b21e5ea2382ff21cd3e2ff2ea3a5e67828cfe4b563ee86e14e", "dweb:/ipfs/QmUtFozDVfCzehRMYxhJwZAbLJ43afzzVQDA89JD1HG1YH"], "license": "BUSL-1.1"}, "src/interfaces/managers/IShareManager.sol": {"keccak256": "0xb75fcb890e9a3e706c0d87781700965aa24faa8461967ee62b140d45f4c399e8", "urls": ["bzz-raw://ae50d1dda35eb9f6fa035fc39c2424f2e2eb2d41e6b0ffed296719818b1d9502", "dweb:/ipfs/Qme5jPDZG3WTLYtT2s8VFs3MKJVXgL9T5AdAzoXuuqM79z"], "license": "BUSL-1.1"}, "src/interfaces/modules/IACLModule.sol": {"keccak256": "0xf6ce9b4f4af5475d152845d6afa6f57b2d376c3a6d3eec2b5ac9650901a78243", "urls": ["bzz-raw://f851cef38b6188b20be98c95047c8bd9d8651d37421a54e1d02b60f9f372c5ed", "dweb:/ipfs/QmaymGDd2RoKweuASXCiV3rPuhwvLXUvb6wBgi8xXoiYT8"], "license": "BUSL-1.1"}, "src/interfaces/modules/IBaseModule.sol": {"keccak256": "0x9c5c95ceb39f4c39f81f2afc7ddb55389a21315e76047a9cf66521db4dea68c3", "urls": ["bzz-raw://ccfb0453b14b105cb5990978a49c1e08f812563a046e17c354f4f1c648f95fe7", "dweb:/ipfs/QmYMAKaVdRq3NoTMfqadxgMMmUq6ejzTw2Lpsa3e9a3eyj"], "license": "BUSL-1.1"}, "src/interfaces/modules/IShareModule.sol": {"keccak256": "0x65ef35a38441080ad4e0c77c7668d6717fe9699df679ed4d2c775f47d1dce57a", "urls": ["bzz-raw://e28e0e9e2dd2097df748fa2015ce4582f489615040b5323094880a618c54edac", "dweb:/ipfs/QmXNvxJDQ6edM3ExmmiTEmS5UreWYTDBgaAygU2kM6mgC6"], "license": "BUSL-1.1"}, "src/interfaces/modules/ISubvaultModule.sol": {"keccak256": "0x8db6f1a3bd2506dd0db82f4138f0436602ed37487717095a804dee81e44f5e0e", "urls": ["bzz-raw://2547472f59c19612916f3f54e6fa69644ec31f14c669aa66dea3c006bc71bccc", "dweb:/ipfs/QmZ9VRCsofVntdB7siM3pfypUpgJnmaJAC1n8HviQb3K3U"], "license": "BUSL-1.1"}, "src/interfaces/modules/IVaultModule.sol": {"keccak256": "0x7696792b852bc9b435e6388a19f4284854259df2f50b45e2b833b6f9dd1b1648", "urls": ["bzz-raw://4364151c043fc5e56bc87c2cdbffc169b80cd745618a4b0da14e271752de6637", "dweb:/ipfs/Qmdfx3L8goy8eWFGimtsh58Se1vuLsukUw2mXo5chCbTy2"], "license": "BUSL-1.1"}, "src/interfaces/modules/IVerifierModule.sol": {"keccak256": "0x8b8ad9ea43b139afccc77ceb0fdcc34615c7401cc13e99c723895302049065a5", "urls": ["bzz-raw://0d578b1a55a180c652ee24e1b35f6d70d65bc067770dd791db44d0bcfa7a5d38", "dweb:/ipfs/QmexSPCfixS9spdjCydR4FUaoN4J188sfLkRmJaia14yk6"], "license": "BUSL-1.1"}, "src/interfaces/oracles/IOracle.sol": {"keccak256": "0xcbf84560c1fb9f7d3445cbbca4f5f3406606496f30867222d8e3deabad741296", "urls": ["bzz-raw://c90107edcd30e29044a5a8b05e71e728b93a485e7e7ef1a5b4ff695430e33a5e", "dweb:/ipfs/QmfHGNxtRQWeq7JENynxZNfhfTBytwbRwyekUyuHWrUyjK"], "license": "BUSL-1.1"}, "src/interfaces/permissions/ICustomVerifier.sol": {"keccak256": "0xdb343366ed86f63f8a94ff478c5c8e1d127888e4993c27fcb3bb3b51ae0cb8c7", "urls": ["bzz-raw://12407e89b20982a00961ccc03258e353962607747f214940d18b0989c11547c9", "dweb:/ipfs/Qmay19iWpwiV8LuvWccPjsazVU5Kkhme9QykFfLKfBmvSr"], "license": "BUSL-1.1"}, "src/interfaces/permissions/IMellowACL.sol": {"keccak256": "0xcc86b0ce4e4a53c1c4fa16ad79f9de0e2a1f1a77a1f256c0856120a9d6ea36df", "urls": ["bzz-raw://6942df2ffadacae5563e01ec60697f16116362ba84a856a346b36bc7b9dd1ec2", "dweb:/ipfs/QmNYhqZGpS98UP89CS86CjoG4ZWbMHzTHRtd7bZzQqn3UY"], "license": "BUSL-1.1"}, "src/interfaces/permissions/IVerifier.sol": {"keccak256": "0x4b42731d76efaffcb017577bd2a478d00a3058c69220d6ea8e26f30f74476be7", "urls": ["bzz-raw://ff2a6f09a946dd8a470dfe495b2a64d24b849c6d071dc859589140fb1b983362", "dweb:/ipfs/Qmb6UoqkmaC8tJMqkMfjcWR7pn1WCwPwuKKvUbLBQbPcJS"], "license": "BUSL-1.1"}, "src/interfaces/queues/IDepositQueue.sol": {"keccak256": "0x4ef38cb59d419010f453e62fe101efec25bec85a2ce1a24048098034060db47b", "urls": ["bzz-raw://20e51dd02001309f23f46abea409edf77a39522bf21eeffb858ac888e4200e72", "dweb:/ipfs/QmWm9gfMuXz8nwuHb3he7abxTRgQw35x2viwMVuiNYAY5b"], "license": "BUSL-1.1"}, "src/interfaces/queues/IQueue.sol": {"keccak256": "0xd12e34631b204821c77eaa5b2e104902d740e3530cd716f7fc991af01a73b9ab", "urls": ["bzz-raw://1f2a4d664244be639da1a5025ebccc8c1127e087a81ce6d0fc76af5134fec311", "dweb:/ipfs/QmS1GJwxGCV89myCAzLBAbmSrY6YL31G3nGj542dDM4QmT"], "license": "BUSL-1.1"}, "src/interfaces/queues/IRedeemQueue.sol": {"keccak256": "0xf8f95b95de259c8ccf29976f34d97a0ef31c9827f41ebcd5e8de54c10e646076", "urls": ["bzz-raw://45ae0d496e865d846bf0c980593c1d16c038abf2b9359906224aa94cccf1c73f", "dweb:/ipfs/QmYTDXSTpUqhzJDXSKjoSKxwwQYee7wSTtf617wDsnnCC5"], "license": "BUSL-1.1"}, "src/libraries/FenwickTreeLibrary.sol": {"keccak256": "0xfd159ed27b96f288c4220febac4bfe286e4545b8ca7f523ae6e63d123341f8a3", "urls": ["bzz-raw://58f0cc202896722786140571b111bf5e589730358a24b2940a450c4c75d864fe", "dweb:/ipfs/QmeRSjvcYURUSpvFe4r4VRwwpHcb1NmqiEUDEJ5rBxAxuf"], "license": "BUSL-1.1"}, "src/libraries/ShareManagerFlagLibrary.sol": {"keccak256": "0xa9e87a282a825831eac2bce34a80ac6f09b366e539e82b73e03aa878cb37777a", "urls": ["bzz-raw://a08d19e7c740def72b930d138ded20e9955a603be032f0e733b86fc75dca79cf", "dweb:/ipfs/QmQwgoyT6pGbEStT3ELUcFDrt3bxJLY93UTentMs7awDt3"], "license": "BUSL-1.1"}, "src/libraries/SlotLibrary.sol": {"keccak256": "0x35dcf16feec83c772add30250b0987ce29ccfbf308d04159701ebf9243444eeb", "urls": ["bzz-raw://97658d0c93ecc8174169e643d5a24c515b55905a2fcaaa6d6ec0188a3dd71a10", "dweb:/ipfs/QmS1z7ac8RbMvz15gVQ9YA1cCT5bs6KMHCiDhgcG8Pq8oK"], "license": "BUSL-1.1"}, "src/managers/BasicShareManager.sol": {"keccak256": "0x7c2c7e0ac3de3b140b50617163ca1b6cd896b6698633c36da296e30af05eab40", "urls": ["bzz-raw://220a9dcbd0cce7d870826f0931ee51e3faffe4f381d6bc9a16759e3731e3f0d9", "dweb:/ipfs/QmRiJZHJzz8rDk8VGY8EwS86pDHhSADWdJVzYw2Z3ZjP9d"], "license": "BUSL-1.1"}, "src/managers/ShareManager.sol": {"keccak256": "0x9fb432d1538b21f969e9701f726d720dc34fe695f9d64f04ea9d3e30d1a0398c", "urls": ["bzz-raw://f11a6862c1256a5f33b157e0b58de7411a043ef86597228e09ba7591d0b55526", "dweb:/ipfs/QmT6Np94nTTzjWvVHryYm7UPqyCc7eogu5NqTmzyiLeSts"], "license": "BUSL-1.1"}}, "version": 1}, "id": 115}