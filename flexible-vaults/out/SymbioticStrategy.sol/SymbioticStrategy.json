{"abi": [{"type": "constructor", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "claim", "inputs": [{"name": "subvault", "type": "address", "internalType": "address"}, {"name": "symbioticVault", "type": "address", "internalType": "address"}, {"name": "epoch", "type": "uint256", "internalType": "uint256"}, {"name": "verificationPayload", "type": "tuple", "internalType": "struct IVerifier.VerificationPayload", "components": [{"name": "verificationType", "type": "uint8", "internalType": "enum IVerifier.VerificationType"}, {"name": "verificationData", "type": "bytes", "internalType": "bytes"}, {"name": "proof", "type": "bytes32[]", "internalType": "bytes32[]"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "deposit", "inputs": [{"name": "subvault", "type": "address", "internalType": "address"}, {"name": "symbioticVault", "type": "address", "internalType": "address"}, {"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "verificationPayload", "type": "tuple[2]", "internalType": "struct IVerifier.VerificationPayload[2]", "components": [{"name": "verificationType", "type": "uint8", "internalType": "enum IVerifier.VerificationType"}, {"name": "verificationData", "type": "bytes", "internalType": "bytes"}, {"name": "proof", "type": "bytes32[]", "internalType": "bytes32[]"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "subvault", "type": "address", "internalType": "address"}, {"name": "symbioticVault", "type": "address", "internalType": "address"}, {"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "verificationPayload", "type": "tuple", "internalType": "struct IVerifier.VerificationPayload", "components": [{"name": "verificationType", "type": "uint8", "internalType": "enum IVerifier.VerificationType"}, {"name": "verificationData", "type": "bytes", "internalType": "bytes"}, {"name": "proof", "type": "bytes32[]", "internalType": "bytes32[]"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "ApproveCallFailed", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "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", "sourceMap": "708:1604:142:-:0;;;784:44;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;819:5;-1:-1:-1;;;;;1273:26:33;;1269:95;;1322:31;;-1:-1:-1;;;1322:31:33;;1350:1;1322:31;;;455:51:192;428:18;;1322:31:33;;;;;;;1269:95;1373:32;1392:12;1373:18;:32::i;:::-;1225:187;784:44:142;708:1604;;2912:187:33;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:33;;;-1:-1:-1;;;;;;3020:17:33;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;14:290:192:-;84:6;137:2;125:9;116:7;112:23;108:32;105:52;;;153:1;150;143:12;105:52;179:16;;-1:-1:-1;;;;;224:31:192;;214:42;;204:70;;270:1;267;260:12;204:70;293:5;14:290;-1:-1:-1;;;14:290:192:o;309:203::-;708:1604:142;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "708:1604:142:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1921:352;;;;;;:::i;:::-;;:::i;:::-;;1555:360;;;;;;:::i;:::-;;:::i;2293:101:33:-;;;:::i;1638:85::-;1684:7;1710:6;1638:85;;;-1:-1:-1;;;;;1710:6:33;;;1045:51:192;;1638:85:33;;;;;1033:2:192;1638:85:33;;;834:715:142;;;;;;:::i;:::-;;:::i;2543:215:33:-;;;;;;:::i;:::-;;:::i;1921:352:142:-;1531:13:33;:11;:13::i;:::-;2179:56:142::1;::::0;;-1:-1:-1;;;;;2120:26:142;::::1;2179:56;::::0;::::1;2306:51:192::0;;;2373:18;;;;2366:34;;;2179:56:142;;;;;;;;;;2279:18:192;;;;2179:56:142;;::::1;::::0;::::1;::::0;;-1:-1:-1;;;;;2179:56:142::1;-1:-1:-1::0;;;2179:56:142::1;::::0;;2120:146;;-1:-1:-1;;;2120:146:142;;:26:::1;::::0;:146:::1;::::0;2160:14;;-1:-1:-1;;2237:19:142;;2120:146:::1;;;:::i;:::-;;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;::::0;;::::1;-1:-1:-1::0;;2120:146:142::1;::::0;::::1;;::::0;::::1;::::0;;;::::1;::::0;::::1;:::i;:::-;;1921:352:::0;;;;:::o;1555:360::-;1531:13:33;:11;:13::i;:::-;1817:60:142::1;::::0;;-1:-1:-1;;;;;1758:26:142;::::1;1817:60;::::0;::::1;2306:51:192::0;;;2373:18;;;;2366:34;;;1817:60:142;;;;;;;;;;2279:18:192;;;;1817:60:142;;::::1;::::0;::::1;::::0;;-1:-1:-1;;;;;1817:60:142::1;-1:-1:-1::0;;;1817:60:142::1;::::0;;1758:150;;-1:-1:-1;;;1758:150:142;;:26:::1;::::0;:150:::1;::::0;1798:14;;-1:-1:-1;;1879:19:142;;1758:150:::1;;;:::i;2293:101:33:-:0;1531:13;:11;:13::i;:::-;2357:30:::1;2384:1;2357:18;:30::i;:::-;2293:101::o:0;834:715:142:-;1531:13:33;:11;:13::i;:::-;1039::142::1;1071:14;-1:-1:-1::0;;;;;1055:42:142::1;;:44;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1039:60;;1109:27;1151:8;-1:-1:-1::0;;;;;1139:26:142::1;;1179:5;1186:1;1221:14;1237:6;1189:56;;;;;;;;-1:-1:-1::0;;;;;2324:32:192;;;;2306:51;;2388:2;2373:18;;2366:34;2294:2;2279:18;;2132:274;1189:56:142::1;;::::0;;-1:-1:-1;;1189:56:142;;::::1;::::0;;;;;;::::1;::::0;::::1;::::0;;-1:-1:-1;;;;;1189:56:142::1;-1:-1:-1::0;;;1189:56:142::1;::::0;;1247:22:::1;:19:::0;;:22:::1;:::i;:::-;1139:140;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;::::0;;::::1;-1:-1:-1::0;;1139:140:142::1;::::0;::::1;;::::0;::::1;::::0;;;::::1;::::0;::::1;:::i;:::-;1109:170;;1305:14;1294:34;;;;;;;;;;;;:::i;:::-;1289:92;;1351:19;;-1:-1:-1::0;;;1351:19:142::1;;;;;;;;;;;1289:92;1449:59;::::0;-1:-1:-1;;;;;1390:26:142;::::1;1449:59;::::0;::::1;2306:51:192::0;;;2373:18;;;2366:34;;;1390:26:142;::::1;::::0;1430:14;;1446:1:::1;::::0;2279:18:192;;1449:59:142::1;::::0;;;;::::1;-1:-1:-1::0;;1449:59:142;;;;;;::::1;::::0;;::::1;::::0;;-1:-1:-1;;;;;;;;1449:59:142;;::::1;;::::0;;1510:22:::1;::::0;;::::1;:19:::0;:22:::1;:::i;:::-;1390:152;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;::::0;;::::1;-1:-1:-1::0;;1390:152:142::1;::::0;::::1;;::::0;::::1;::::0;;;::::1;::::0;::::1;:::i;:::-;;1029:520;;834:715:::0;;;;:::o;2543:215:33:-;1531:13;:11;:13::i;:::-;-1:-1:-1;;;;;2627:22:33;::::1;2623:91;;2672:31;::::0;-1:-1:-1;;;2672:31:33;;2700:1:::1;2672:31;::::0;::::1;1045:51:192::0;1018:18;;2672:31:33::1;;;;;;;;2623:91;2723:28;2742:8;2723:18;:28::i;:::-;2543:215:::0;:::o;1796:162::-;1684:7;1710:6;-1:-1:-1;;;;;1710:6:33;735:10:56;1855:23:33;1851:101;;1901:40;;-1:-1:-1;;;1901:40:33;;735:10:56;1901:40:33;;;1045:51:192;1018:18;;1901:40:33;899:203:192;2912:187:33;2985:16;3004:6;;-1:-1:-1;;;;;3020:17:33;;;-1:-1:-1;;;;;;3020:17:33;;;;;;3052:40;;3004:6;;;;;;;3052:40;;2985:16;3052:40;2975:124;2912:187;:::o;14:131:192:-;-1:-1:-1;;;;;89:31:192;;79:42;;69:70;;135:1;132;125:12;150:744;276:6;284;292;300;353:3;341:9;332:7;328:23;324:33;321:53;;;370:1;367;360:12;321:53;409:9;396:23;428:31;453:5;428:31;:::i;:::-;478:5;-1:-1:-1;535:2:192;520:18;;507:32;548:33;507:32;548:33;:::i;:::-;600:7;-1:-1:-1;654:2:192;639:18;;626:32;;-1:-1:-1;709:2:192;694:18;;681:32;736:18;725:30;;722:50;;;768:1;765;758:12;722:50;791:22;;847:2;829:16;;;825:25;822:45;;;863:1;860;853:12;822:45;150:744;;;;-1:-1:-1;150:744:192;;-1:-1:-1;;150:744:192:o;1107:768::-;1258:6;1266;1274;1282;1335:3;1323:9;1314:7;1310:23;1306:33;1303:53;;;1352:1;1349;1342:12;1303:53;1391:9;1378:23;1410:31;1435:5;1410:31;:::i;:::-;1460:5;-1:-1:-1;1517:2:192;1502:18;;1489:32;1530:33;1489:32;1530:33;:::i;:::-;1582:7;-1:-1:-1;1636:2:192;1621:18;;1608:32;;-1:-1:-1;1691:2:192;1676:18;;1663:32;1718:18;1707:30;;1704:50;;;1750:1;1747;1740:12;1704:50;1773:22;;1818:2;1810:11;;1807:24;-1:-1:-1;1804:44:192;;;1844:1;1841;1834:12;1880:247;1939:6;1992:2;1980:9;1971:7;1967:23;1963:32;1960:52;;;2008:1;2005;1998:12;1960:52;2047:9;2034:23;2066:31;2091:5;2066:31;:::i;:::-;2116:5;1880:247;-1:-1:-1;;;1880:247:192:o;2411:266::-;2499:6;2494:3;2487:19;2551:6;2544:5;2537:4;2532:3;2528:14;2515:43;-1:-1:-1;2603:1:192;2578:16;;;2596:4;2574:27;;;2567:38;;;;2659:2;2638:15;;;-1:-1:-1;;2634:29:192;2625:39;;;2621:50;;2411:266::o;2682:520::-;2752:5;2759:6;2819:3;2806:17;2905:2;2901:7;2890:8;2874:14;2870:29;2866:43;2846:18;2842:68;2832:96;;2924:1;2921;2914:12;2832:96;2952:33;;3056:4;3043:18;;;-1:-1:-1;3004:21:192;;-1:-1:-1;3084:18:192;3073:30;;3070:50;;;3116:1;3113;3106:12;3070:50;3170:6;3167:1;3163:14;3147;3143:35;3136:5;3132:47;3129:67;;;3192:1;3189;3182:12;3129:67;2682:520;;;;;:::o;3207:311::-;3295:19;;;3277:3;-1:-1:-1;;;;;3326:31:192;;3323:51;;;3370:1;3367;3360:12;3323:51;3406:6;3403:1;3399:14;3458:8;3451:5;3444:4;3439:3;3435:14;3422:45;3487:18;;;;3507:4;3483:29;;3207:311;-1:-1:-1;;;3207:311:192:o;3523:1703::-;3869:1;3865;3860:3;3856:11;3852:19;3844:6;3840:32;3829:9;3822:51;3909:6;3904:2;3893:9;3889:18;3882:34;3952:3;3947:2;3936:9;3932:18;3925:31;3803:4;3985:6;3979:13;4029:6;4023:3;4012:9;4008:19;4001:35;4089:6;4084:2;4076:6;4072:15;4066:3;4055:9;4051:19;4045:51;4146:1;4140:3;4131:6;4120:9;4116:22;4112:32;4105:43;4207:2;4203:7;4198:2;4190:6;4186:15;4182:29;4171:9;4167:45;4157:55;;4272:3;4260:9;4256:2;4252:18;4248:28;4243:2;4232:9;4228:18;4221:56;4312:6;4299:20;4348:1;4341:5;4338:12;4328:40;;4364:1;4361;4354:12;4328:40;4392:3;4384:12;;4377:27;4464:2;4452:15;;4439:29;4519:14;4515:27;;;-1:-1:-1;;4511:41:192;4487:66;;4477:94;;4567:1;4564;4557:12;4477:94;4595:31;;4709:2;4696:16;;;4651:21;4737:18;4724:32;;4721:52;;;4769:1;4766;4759:12;4721:52;4818:8;4802:14;4798:29;4789:7;4785:43;4782:63;;;4841:1;4838;4831:12;4782:63;4875:2;4869:3;4865:2;4861:12;4854:24;4901:58;4954:3;4950:2;4946:12;4936:8;4927:7;4901:58;:::i;:::-;4887:72;;;5002:67;5065:2;5057:6;5053:15;5045:6;5002:67;:::i;:::-;5103:15;;;-1:-1:-1;;5099:30:192;5093:3;5085:12;;5078:52;5147:73;5107:6;5199:12;5185;5147:73;:::i;:::-;5139:81;3523:1703;-1:-1:-1;;;;;;;;;;3523:1703:192:o;5231:127::-;5292:10;5287:3;5283:20;5280:1;5273:31;5323:4;5320:1;5313:15;5347:4;5344:1;5337:15;5363:911;5442:6;5495:2;5483:9;5474:7;5470:23;5466:32;5463:52;;;5511:1;5508;5501:12;5463:52;5544:9;5538:16;5573:18;5614:2;5606:6;5603:14;5600:34;;;5630:1;5627;5620:12;5600:34;5668:6;5657:9;5653:22;5643:32;;5713:7;5706:4;5702:2;5698:13;5694:27;5684:55;;5735:1;5732;5725:12;5684:55;5764:2;5758:9;5786:2;5782;5779:10;5776:36;;;5792:18;;:::i;:::-;5867:2;5861:9;5835:2;5921:13;;-1:-1:-1;;5917:22:192;;;5941:2;5913:31;5909:40;5897:53;;;5965:18;;;5985:22;;;5962:46;5959:72;;;6011:18;;:::i;:::-;6051:10;6047:2;6040:22;6086:2;6078:6;6071:18;6126:7;6121:2;6116;6112;6108:11;6104:20;6101:33;6098:53;;;6147:1;6144;6137:12;6098:53;6196:2;6191;6187;6183:11;6178:2;6170:6;6166:15;6160:39;6241:1;6219:15;;;6236:2;6215:24;6208:35;;;;-1:-1:-1;6223:6:192;5363:911;-1:-1:-1;;;;;5363:911:192:o;6279:251::-;6349:6;6402:2;6390:9;6381:7;6377:23;6373:32;6370:52;;;6418:1;6415;6408:12;6370:52;6450:9;6444:16;6469:31;6494:5;6469:31;:::i;6667:337::-;6773:4;6831:11;6818:25;6925:2;6921:7;6910:8;6894:14;6890:29;6886:43;6866:18;6862:68;6852:96;;6944:1;6941;6934:12;6852:96;6965:33;;;;;6667:337;-1:-1:-1;;6667:337:192:o;7009:277::-;7076:6;7129:2;7117:9;7108:7;7104:23;7100:32;7097:52;;;7145:1;7142;7135:12;7097:52;7177:9;7171:16;7230:5;7223:13;7216:21;7209:5;7206:32;7196:60;;7252:1;7249;7242:12", "linkReferences": {}}, "methodIdentifiers": {"claim(address,address,uint256,(uint8,bytes,bytes32[]))": "023042b9", "deposit(address,address,uint256,(uint8,bytes,bytes32[])[2])": "c6f70721", "owner()": "8da5cb5b", "renounceOwnership()": "715018a6", "transferOwnership(address)": "f2fde38b", "withdraw(address,address,uint256,(uint8,bytes,bytes32[]))": "60ac59c1"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"ApproveCallFailed\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subvault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"symbioticVault\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"epoch\",\"type\":\"uint256\"},{\"components\":[{\"internalType\":\"enum IVerifier.VerificationType\",\"name\":\"verificationType\",\"type\":\"uint8\"},{\"internalType\":\"bytes\",\"name\":\"verificationData\",\"type\":\"bytes\"},{\"internalType\":\"bytes32[]\",\"name\":\"proof\",\"type\":\"bytes32[]\"}],\"internalType\":\"struct IVerifier.VerificationPayload\",\"name\":\"verificationPayload\",\"type\":\"tuple\"}],\"name\":\"claim\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subvault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"symbioticVault\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"components\":[{\"internalType\":\"enum IVerifier.VerificationType\",\"name\":\"verificationType\",\"type\":\"uint8\"},{\"internalType\":\"bytes\",\"name\":\"verificationData\",\"type\":\"bytes\"},{\"internalType\":\"bytes32[]\",\"name\":\"proof\",\"type\":\"bytes32[]\"}],\"internalType\":\"struct IVerifier.VerificationPayload[2]\",\"name\":\"verificationPayload\",\"type\":\"tuple[2]\"}],\"name\":\"deposit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subvault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"symbioticVault\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"components\":[{\"internalType\":\"enum IVerifier.VerificationType\",\"name\":\"verificationType\",\"type\":\"uint8\"},{\"internalType\":\"bytes\",\"name\":\"verificationData\",\"type\":\"bytes\"},{\"internalType\":\"bytes32[]\",\"name\":\"proof\",\"type\":\"bytes32[]\"}],\"internalType\":\"struct IVerifier.VerificationPayload\",\"name\":\"verificationPayload\",\"type\":\"tuple\"}],\"name\":\"withdraw\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}]},\"kind\":\"dev\",\"methods\":{\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"Out of scope! Used as an example only.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/strategies/SymbioticStrategy.sol\":\"SymbioticStrategy\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol\":{\"keccak256\":\"0x361126a17677994081cd9cb69c3f50cffff6e920d25cb7e428acdb1ae41d1866\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://19ae787a7dd001269cd60a394b1a5261b78925a0fc3a6f927beb2986a9aa56cf\",\"dweb:/ipfs/QmYLfXiuKmcRgTDBEDXMMjXU8t6JxsspUmjxYzqWS55oEv\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0\",\"dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://df6f0c459663c9858b6cba2cda1d14a7d05a985bed6d2de72bd8e78c25ee79db\",\"dweb:/ipfs/QmeTTxZ7qVk9rjEv2R4CpCwdf8UMCcRqDNMvzNxHc3Fnn9\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol\":{\"keccak256\":\"0xb5afb8e8eebc4d1c6404df2f5e1e6d2c3d24fd01e5dfc855314951ecfaae462d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://78586466c424f076c6a2a551d848cfbe3f7c49e723830807598484a1047b3b34\",\"dweb:/ipfs/Qmb717ovcFxm7qgNKEShiV6M9SPR3v1qnNpAGH84D6w29p\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0xaaa1d17c1129b127a4a401db2fbd72960e2671474be3d08cae71ccdc42f7624c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cb2f27cd3952aa667e198fba0d9b7bcec52fbb12c16f013c25fe6fb52b29cc0e\",\"dweb:/ipfs/QmeuohBFoeyDPZA9JNCTEDz3VBfBD4EABWuWXVhHAuEpKR\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol\":{\"keccak256\":\"0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd\",\"dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol\":{\"keccak256\":\"0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd\",\"dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Errors.sol\":{\"keccak256\":\"0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf\",\"dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol\":{\"keccak256\":\"0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2\",\"dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/Hashes.sol\":{\"keccak256\":\"0x8cbd338f083224b4b6f0ff42cbda641a0a6c31ffcdca197452b97fe4d0918269\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f517dec5ba0c6491395acbf7f1d621f4e89e8f218bf5303c867b1c5ad70c6b11\",\"dweb:/ipfs/QmWmXHRLEw8W6ckth7NyYTU88YfvuS7xSsfae5ksL8qNUe\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/MerkleProof.sol\":{\"keccak256\":\"0x36a0c409c437a753cac9b92b75f93b0fbe92803bf2c8ff1517e54b247f166134\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f91ba472de411aa557cdbf6560c40750d87bd11c9060bc04d2ba7119af9d5a6\",\"dweb:/ipfs/QmQjtYo2i7dDvzCEzZ67bDoNSG4RrwMoxPWuqFmX5Xzpuw\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol\":{\"keccak256\":\"0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231\",\"dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y\"]},\"src/interfaces/factories/IFactoryEntity.sol\":{\"keccak256\":\"0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812\",\"dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx\"]},\"src/interfaces/modules/IBaseModule.sol\":{\"keccak256\":\"0x9c5c95ceb39f4c39f81f2afc7ddb55389a21315e76047a9cf66521db4dea68c3\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ccfb0453b14b105cb5990978a49c1e08f812563a046e17c354f4f1c648f95fe7\",\"dweb:/ipfs/QmYMAKaVdRq3NoTMfqadxgMMmUq6ejzTw2Lpsa3e9a3eyj\"]},\"src/interfaces/modules/ICallModule.sol\":{\"keccak256\":\"0x2dd8e5fed284449aedfc18547aa791652a30cead3c679e3bfe46a2fb649a9eb4\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://3be434df4279be297e073ae3313cb871e693ff7bc77140e6679983ea2b715d9d\",\"dweb:/ipfs/QmUDcY2QMELxX8nqarV5qUuDaMVs592LHcCS95j7RYoSE1\"]},\"src/interfaces/modules/IVerifierModule.sol\":{\"keccak256\":\"0x8b8ad9ea43b139afccc77ceb0fdcc34615c7401cc13e99c723895302049065a5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://0d578b1a55a180c652ee24e1b35f6d70d65bc067770dd791db44d0bcfa7a5d38\",\"dweb:/ipfs/QmexSPCfixS9spdjCydR4FUaoN4J188sfLkRmJaia14yk6\"]},\"src/interfaces/permissions/ICustomVerifier.sol\":{\"keccak256\":\"0xdb343366ed86f63f8a94ff478c5c8e1d127888e4993c27fcb3bb3b51ae0cb8c7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://12407e89b20982a00961ccc03258e353962607747f214940d18b0989c11547c9\",\"dweb:/ipfs/Qmay19iWpwiV8LuvWccPjsazVU5Kkhme9QykFfLKfBmvSr\"]},\"src/interfaces/permissions/IVerifier.sol\":{\"keccak256\":\"0x4b42731d76efaffcb017577bd2a478d00a3058c69220d6ea8e26f30f74476be7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ff2a6f09a946dd8a470dfe495b2a64d24b849c6d071dc859589140fb1b983362\",\"dweb:/ipfs/Qmb6UoqkmaC8tJMqkMfjcWR7pn1WCwPwuKKvUbLBQbPcJS\"]},\"src/strategies/SymbioticStrategy.sol\":{\"keccak256\":\"0x3ee05a15a441904416a3ec6594ff1c415f76ae33ba48de77baed903b83522c70\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://64385695c1f44ed2b1a7e2383e7267615609451648109cedd0bd7b03411fac60\",\"dweb:/ipfs/QmaS2oqFncziPb7kxyofKM3TzBycbeU5BNjnyqkZhtghAF\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "ApproveCallFailed"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "subvault", "type": "address"}, {"internalType": "address", "name": "symbioticVault", "type": "address"}, {"internalType": "uint256", "name": "epoch", "type": "uint256"}, {"internalType": "struct IVerifier.VerificationPayload", "name": "verificationPayload", "type": "tuple", "components": [{"internalType": "enum IVerifier.VerificationType", "name": "verificationType", "type": "uint8"}, {"internalType": "bytes", "name": "verificationData", "type": "bytes"}, {"internalType": "bytes32[]", "name": "proof", "type": "bytes32[]"}]}], "stateMutability": "nonpayable", "type": "function", "name": "claim"}, {"inputs": [{"internalType": "address", "name": "subvault", "type": "address"}, {"internalType": "address", "name": "symbioticVault", "type": "address"}, {"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "struct IVerifier.VerificationPayload[2]", "name": "verificationPayload", "type": "tuple[2]", "components": [{"internalType": "enum IVerifier.VerificationType", "name": "verificationType", "type": "uint8"}, {"internalType": "bytes", "name": "verificationData", "type": "bytes"}, {"internalType": "bytes32[]", "name": "proof", "type": "bytes32[]"}]}], "stateMutability": "nonpayable", "type": "function", "name": "deposit"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}, {"inputs": [{"internalType": "address", "name": "subvault", "type": "address"}, {"internalType": "address", "name": "symbioticVault", "type": "address"}, {"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "struct IVerifier.VerificationPayload", "name": "verificationPayload", "type": "tuple", "components": [{"internalType": "enum IVerifier.VerificationType", "name": "verificationType", "type": "uint8"}, {"internalType": "bytes", "name": "verificationData", "type": "bytes"}, {"internalType": "bytes32[]", "name": "proof", "type": "bytes32[]"}]}], "stateMutability": "nonpayable", "type": "function", "name": "withdraw"}], "devdoc": {"kind": "dev", "methods": {"owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/strategies/SymbioticStrategy.sol": "SymbioticStrategy"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05", "urls": ["bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08", "dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol": {"keccak256": "0x361126a17677994081cd9cb69c3f50cffff6e920d25cb7e428acdb1ae41d1866", "urls": ["bzz-raw://19ae787a7dd001269cd60a394b1a5261b78925a0fc3a6f927beb2986a9aa56cf", "dweb:/ipfs/QmYLfXiuKmcRgTDBEDXMMjXU8t6JxsspUmjxYzqWS55oEv"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530", "urls": ["bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0", "dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7", "urls": ["bzz-raw://df6f0c459663c9858b6cba2cda1d14a7d05a985bed6d2de72bd8e78c25ee79db", "dweb:/ipfs/QmeTTxZ7qVk9rjEv2R4CpCwdf8UMCcRqDNMvzNxHc3Fnn9"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol": {"keccak256": "0xb5afb8e8eebc4d1c6404df2f5e1e6d2c3d24fd01e5dfc855314951ecfaae462d", "urls": ["bzz-raw://78586466c424f076c6a2a551d848cfbe3f7c49e723830807598484a1047b3b34", "dweb:/ipfs/Qmb717ovcFxm7qgNKEShiV6M9SPR3v1qnNpAGH84D6w29p"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0xaaa1d17c1129b127a4a401db2fbd72960e2671474be3d08cae71ccdc42f7624c", "urls": ["bzz-raw://cb2f27cd3952aa667e198fba0d9b7bcec52fbb12c16f013c25fe6fb52b29cc0e", "dweb:/ipfs/QmeuohBFoeyDPZA9JNCTEDz3VBfBD4EABWuWXVhHAuEpKR"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol": {"keccak256": "0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6", "urls": ["bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd", "dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol": {"keccak256": "0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58", "urls": ["bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd", "dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Errors.sol": {"keccak256": "0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123", "urls": ["bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf", "dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol": {"keccak256": "0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26", "urls": ["bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2", "dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/Hashes.sol": {"keccak256": "0x8cbd338f083224b4b6f0ff42cbda641a0a6c31ffcdca197452b97fe4d0918269", "urls": ["bzz-raw://f517dec5ba0c6491395acbf7f1d621f4e89e8f218bf5303c867b1c5ad70c6b11", "dweb:/ipfs/QmWmXHRLEw8W6ckth7NyYTU88YfvuS7xSsfae5ksL8qNUe"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/MerkleProof.sol": {"keccak256": "0x36a0c409c437a753cac9b92b75f93b0fbe92803bf2c8ff1517e54b247f166134", "urls": ["bzz-raw://0f91ba472de411aa557cdbf6560c40750d87bd11c9060bc04d2ba7119af9d5a6", "dweb:/ipfs/QmQjtYo2i7dDvzCEzZ67bDoNSG4RrwMoxPWuqFmX5Xzpuw"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol": {"keccak256": "0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06", "urls": ["bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231", "dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y"], "license": "MIT"}, "src/interfaces/factories/IFactoryEntity.sol": {"keccak256": "0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5", "urls": ["bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812", "dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx"], "license": "BUSL-1.1"}, "src/interfaces/modules/IBaseModule.sol": {"keccak256": "0x9c5c95ceb39f4c39f81f2afc7ddb55389a21315e76047a9cf66521db4dea68c3", "urls": ["bzz-raw://ccfb0453b14b105cb5990978a49c1e08f812563a046e17c354f4f1c648f95fe7", "dweb:/ipfs/QmYMAKaVdRq3NoTMfqadxgMMmUq6ejzTw2Lpsa3e9a3eyj"], "license": "BUSL-1.1"}, "src/interfaces/modules/ICallModule.sol": {"keccak256": "0x2dd8e5fed284449aedfc18547aa791652a30cead3c679e3bfe46a2fb649a9eb4", "urls": ["bzz-raw://3be434df4279be297e073ae3313cb871e693ff7bc77140e6679983ea2b715d9d", "dweb:/ipfs/QmUDcY2QMELxX8nqarV5qUuDaMVs592LHcCS95j7RYoSE1"], "license": "BUSL-1.1"}, "src/interfaces/modules/IVerifierModule.sol": {"keccak256": "0x8b8ad9ea43b139afccc77ceb0fdcc34615c7401cc13e99c723895302049065a5", "urls": ["bzz-raw://0d578b1a55a180c652ee24e1b35f6d70d65bc067770dd791db44d0bcfa7a5d38", "dweb:/ipfs/QmexSPCfixS9spdjCydR4FUaoN4J188sfLkRmJaia14yk6"], "license": "BUSL-1.1"}, "src/interfaces/permissions/ICustomVerifier.sol": {"keccak256": "0xdb343366ed86f63f8a94ff478c5c8e1d127888e4993c27fcb3bb3b51ae0cb8c7", "urls": ["bzz-raw://12407e89b20982a00961ccc03258e353962607747f214940d18b0989c11547c9", "dweb:/ipfs/Qmay19iWpwiV8LuvWccPjsazVU5Kkhme9QykFfLKfBmvSr"], "license": "BUSL-1.1"}, "src/interfaces/permissions/IVerifier.sol": {"keccak256": "0x4b42731d76efaffcb017577bd2a478d00a3058c69220d6ea8e26f30f74476be7", "urls": ["bzz-raw://ff2a6f09a946dd8a470dfe495b2a64d24b849c6d071dc859589140fb1b983362", "dweb:/ipfs/Qmb6UoqkmaC8tJMqkMfjcWR7pn1WCwPwuKKvUbLBQbPcJS"], "license": "BUSL-1.1"}, "src/strategies/SymbioticStrategy.sol": {"keccak256": "0x3ee05a15a441904416a3ec6594ff1c415f76ae33ba48de77baed903b83522c70", "urls": ["bzz-raw://64385695c1f44ed2b1a7e2383e7267615609451648109cedd0bd7b03411fac60", "dweb:/ipfs/QmaS2oqFncziPb7kxyofKM3TzBycbeU5BNjnyqkZhtghAF"], "license": "BUSL-1.1"}}, "version": 1}, "id": 142}