{"abi": [{"type": "function", "name": "accesses", "inputs": [{"name": "target", "type": "address", "internalType": "address"}], "outputs": [{"name": "readSlots", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "writeSlots", "type": "bytes32[]", "internalType": "bytes32[]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "addr", "inputs": [{"name": "privateKey", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "keyAddr", "type": "address", "internalType": "address"}], "stateMutability": "pure"}, {"type": "function", "name": "assertApproxEqAbs", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "max<PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertApproxEqAbs", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "max<PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertApproxEqAbs", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "max<PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertApproxEqAbs", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "max<PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertApproxEqAbsDecimal", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "max<PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertApproxEqAbsDecimal", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "max<PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertApproxEqAbsDecimal", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "max<PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertApproxEqAbsDecimal", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "max<PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertApproxEqRel", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "maxP<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertApproxEqRel", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "maxP<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertApproxEqRel", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "maxP<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertApproxEqRel", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "maxP<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertApproxEqRelDecimal", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "maxP<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertApproxEqRelDecimal", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "maxP<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertApproxEqRelDecimal", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "maxP<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertApproxEqRelDecimal", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "maxP<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "right", "type": "bytes32[]", "internalType": "bytes32[]"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "int256[]", "internalType": "int256[]"}, {"name": "right", "type": "int256[]", "internalType": "int256[]"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "address", "internalType": "address"}, {"name": "right", "type": "address", "internalType": "address"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "string", "internalType": "string"}, {"name": "right", "type": "string", "internalType": "string"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "address[]", "internalType": "address[]"}, {"name": "right", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "address[]", "internalType": "address[]"}, {"name": "right", "type": "address[]", "internalType": "address[]"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "bool", "internalType": "bool"}, {"name": "right", "type": "bool", "internalType": "bool"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "address", "internalType": "address"}, {"name": "right", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "right", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "bool[]", "internalType": "bool[]"}, {"name": "right", "type": "bool[]", "internalType": "bool[]"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "int256[]", "internalType": "int256[]"}, {"name": "right", "type": "int256[]", "internalType": "int256[]"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "bytes32", "internalType": "bytes32"}, {"name": "right", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "right", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "bytes", "internalType": "bytes"}, {"name": "right", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "bytes32", "internalType": "bytes32"}, {"name": "right", "type": "bytes32", "internalType": "bytes32"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "string[]", "internalType": "string[]"}, {"name": "right", "type": "string[]", "internalType": "string[]"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "right", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "bytes", "internalType": "bytes"}, {"name": "right", "type": "bytes", "internalType": "bytes"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "bool[]", "internalType": "bool[]"}, {"name": "right", "type": "bool[]", "internalType": "bool[]"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "right", "type": "bytes[]", "internalType": "bytes[]"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "string[]", "internalType": "string[]"}, {"name": "right", "type": "string[]", "internalType": "string[]"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "string", "internalType": "string"}, {"name": "right", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "right", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "bool", "internalType": "bool"}, {"name": "right", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEq", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEqDecimal", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEqDecimal", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEqDecimal", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertEqDecimal", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertFalse", "inputs": [{"name": "condition", "type": "bool", "internalType": "bool"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertFalse", "inputs": [{"name": "condition", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertGe", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertGe", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertGe", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertGe", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertGeDecimal", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertGeDecimal", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertGeDecimal", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertGeDecimal", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertGt", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertGt", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertGt", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertGt", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertGtDecimal", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertGtDecimal", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertGtDecimal", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertGtDecimal", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertLe", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertLe", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertLe", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertLe", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertLeDecimal", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertLeDecimal", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertLeDecimal", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertLeDecimal", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertLt", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertLt", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertLt", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertLt", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertLtDecimal", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertLtDecimal", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertLtDecimal", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertLtDecimal", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "right", "type": "bytes32[]", "internalType": "bytes32[]"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "int256[]", "internalType": "int256[]"}, {"name": "right", "type": "int256[]", "internalType": "int256[]"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "bool", "internalType": "bool"}, {"name": "right", "type": "bool", "internalType": "bool"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "right", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "bool", "internalType": "bool"}, {"name": "right", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "bool[]", "internalType": "bool[]"}, {"name": "right", "type": "bool[]", "internalType": "bool[]"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "bytes", "internalType": "bytes"}, {"name": "right", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "address[]", "internalType": "address[]"}, {"name": "right", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "right", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "bool[]", "internalType": "bool[]"}, {"name": "right", "type": "bool[]", "internalType": "bool[]"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "string", "internalType": "string"}, {"name": "right", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "address[]", "internalType": "address[]"}, {"name": "right", "type": "address[]", "internalType": "address[]"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "string", "internalType": "string"}, {"name": "right", "type": "string", "internalType": "string"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "address", "internalType": "address"}, {"name": "right", "type": "address", "internalType": "address"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "bytes32", "internalType": "bytes32"}, {"name": "right", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "bytes", "internalType": "bytes"}, {"name": "right", "type": "bytes", "internalType": "bytes"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "right", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "address", "internalType": "address"}, {"name": "right", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "bytes32", "internalType": "bytes32"}, {"name": "right", "type": "bytes32", "internalType": "bytes32"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "string[]", "internalType": "string[]"}, {"name": "right", "type": "string[]", "internalType": "string[]"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "right", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "string[]", "internalType": "string[]"}, {"name": "right", "type": "string[]", "internalType": "string[]"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "int256[]", "internalType": "int256[]"}, {"name": "right", "type": "int256[]", "internalType": "int256[]"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "right", "type": "bytes[]", "internalType": "bytes[]"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEq", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEqDecimal", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEqDecimal", "inputs": [{"name": "left", "type": "int256", "internalType": "int256"}, {"name": "right", "type": "int256", "internalType": "int256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEqDecimal", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertNotEqDecimal", "inputs": [{"name": "left", "type": "uint256", "internalType": "uint256"}, {"name": "right", "type": "uint256", "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "internalType": "uint256"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertTrue", "inputs": [{"name": "condition", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assertTrue", "inputs": [{"name": "condition", "type": "bool", "internalType": "bool"}, {"name": "error", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assume", "inputs": [{"name": "condition", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "assumeNoRevert", "inputs": [], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "breakpoint", "inputs": [{"name": "char", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "breakpoint", "inputs": [{"name": "char", "type": "string", "internalType": "string"}, {"name": "value", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "broadcast", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "broadcast", "inputs": [{"name": "signer", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "broadcast", "inputs": [{"name": "privateKey", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "broadcastRawTransaction", "inputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "closeFile", "inputs": [{"name": "path", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "computeCreate2Address", "inputs": [{"name": "salt", "type": "bytes32", "internalType": "bytes32"}, {"name": "initCodeHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "pure"}, {"type": "function", "name": "computeCreate2Address", "inputs": [{"name": "salt", "type": "bytes32", "internalType": "bytes32"}, {"name": "initCodeHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "deployer", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "pure"}, {"type": "function", "name": "computeCreate<PERSON>ddress", "inputs": [{"name": "deployer", "type": "address", "internalType": "address"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "pure"}, {"type": "function", "name": "copyFile", "inputs": [{"name": "from", "type": "string", "internalType": "string"}, {"name": "to", "type": "string", "internalType": "string"}], "outputs": [{"name": "copied", "type": "uint64", "internalType": "uint64"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "copyStorage", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "createDir", "inputs": [{"name": "path", "type": "string", "internalType": "string"}, {"name": "recursive", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "createWallet", "inputs": [{"name": "walletLabel", "type": "string", "internalType": "string"}], "outputs": [{"name": "wallet", "type": "tuple", "internalType": "struct VmSafe.Wallet", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "publicKeyX", "type": "uint256", "internalType": "uint256"}, {"name": "publicKeyY", "type": "uint256", "internalType": "uint256"}, {"name": "privateKey", "type": "uint256", "internalType": "uint256"}]}], "stateMutability": "nonpayable"}, {"type": "function", "name": "createWallet", "inputs": [{"name": "privateKey", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "wallet", "type": "tuple", "internalType": "struct VmSafe.Wallet", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "publicKeyX", "type": "uint256", "internalType": "uint256"}, {"name": "publicKeyY", "type": "uint256", "internalType": "uint256"}, {"name": "privateKey", "type": "uint256", "internalType": "uint256"}]}], "stateMutability": "nonpayable"}, {"type": "function", "name": "createWallet", "inputs": [{"name": "privateKey", "type": "uint256", "internalType": "uint256"}, {"name": "walletLabel", "type": "string", "internalType": "string"}], "outputs": [{"name": "wallet", "type": "tuple", "internalType": "struct VmSafe.Wallet", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "publicKeyX", "type": "uint256", "internalType": "uint256"}, {"name": "publicKeyY", "type": "uint256", "internalType": "uint256"}, {"name": "privateKey", "type": "uint256", "internalType": "uint256"}]}], "stateMutability": "nonpayable"}, {"type": "function", "name": "deployCode", "inputs": [{"name": "artifactPath", "type": "string", "internalType": "string"}, {"name": "constructorArgs", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "deployedAddress", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "deployCode", "inputs": [{"name": "artifactPath", "type": "string", "internalType": "string"}], "outputs": [{"name": "deployedAddress", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON>", "inputs": [{"name": "mnemonic", "type": "string", "internalType": "string"}, {"name": "derivationPath", "type": "string", "internalType": "string"}, {"name": "index", "type": "uint32", "internalType": "uint32"}, {"name": "language", "type": "string", "internalType": "string"}], "outputs": [{"name": "privateKey", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "function", "name": "<PERSON><PERSON><PERSON>", "inputs": [{"name": "mnemonic", "type": "string", "internalType": "string"}, {"name": "index", "type": "uint32", "internalType": "uint32"}, {"name": "language", "type": "string", "internalType": "string"}], "outputs": [{"name": "privateKey", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "function", "name": "<PERSON><PERSON><PERSON>", "inputs": [{"name": "mnemonic", "type": "string", "internalType": "string"}, {"name": "index", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "privateKey", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "function", "name": "<PERSON><PERSON><PERSON>", "inputs": [{"name": "mnemonic", "type": "string", "internalType": "string"}, {"name": "derivationPath", "type": "string", "internalType": "string"}, {"name": "index", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "privateKey", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "function", "name": "ens<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "name", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "envAddress", "inputs": [{"name": "name", "type": "string", "internalType": "string"}], "outputs": [{"name": "value", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "envAddress", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "delim", "type": "string", "internalType": "string"}], "outputs": [{"name": "value", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "envBool", "inputs": [{"name": "name", "type": "string", "internalType": "string"}], "outputs": [{"name": "value", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "envBool", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "delim", "type": "string", "internalType": "string"}], "outputs": [{"name": "value", "type": "bool[]", "internalType": "bool[]"}], "stateMutability": "view"}, {"type": "function", "name": "envBytes", "inputs": [{"name": "name", "type": "string", "internalType": "string"}], "outputs": [{"name": "value", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "envBytes", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "delim", "type": "string", "internalType": "string"}], "outputs": [{"name": "value", "type": "bytes[]", "internalType": "bytes[]"}], "stateMutability": "view"}, {"type": "function", "name": "envBytes32", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "delim", "type": "string", "internalType": "string"}], "outputs": [{"name": "value", "type": "bytes32[]", "internalType": "bytes32[]"}], "stateMutability": "view"}, {"type": "function", "name": "envBytes32", "inputs": [{"name": "name", "type": "string", "internalType": "string"}], "outputs": [{"name": "value", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "envExists", "inputs": [{"name": "name", "type": "string", "internalType": "string"}], "outputs": [{"name": "result", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "envInt", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "delim", "type": "string", "internalType": "string"}], "outputs": [{"name": "value", "type": "int256[]", "internalType": "int256[]"}], "stateMutability": "view"}, {"type": "function", "name": "envInt", "inputs": [{"name": "name", "type": "string", "internalType": "string"}], "outputs": [{"name": "value", "type": "int256", "internalType": "int256"}], "stateMutability": "view"}, {"type": "function", "name": "envOr", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "delim", "type": "string", "internalType": "string"}, {"name": "defaultValue", "type": "bytes32[]", "internalType": "bytes32[]"}], "outputs": [{"name": "value", "type": "bytes32[]", "internalType": "bytes32[]"}], "stateMutability": "view"}, {"type": "function", "name": "envOr", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "delim", "type": "string", "internalType": "string"}, {"name": "defaultValue", "type": "int256[]", "internalType": "int256[]"}], "outputs": [{"name": "value", "type": "int256[]", "internalType": "int256[]"}], "stateMutability": "view"}, {"type": "function", "name": "envOr", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "defaultValue", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "value", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "envOr", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "defaultValue", "type": "address", "internalType": "address"}], "outputs": [{"name": "value", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "envOr", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "defaultValue", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "value", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "envOr", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "delim", "type": "string", "internalType": "string"}, {"name": "defaultValue", "type": "bytes[]", "internalType": "bytes[]"}], "outputs": [{"name": "value", "type": "bytes[]", "internalType": "bytes[]"}], "stateMutability": "view"}, {"type": "function", "name": "envOr", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "delim", "type": "string", "internalType": "string"}, {"name": "defaultValue", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [{"name": "value", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "envOr", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "delim", "type": "string", "internalType": "string"}, {"name": "defaultValue", "type": "string[]", "internalType": "string[]"}], "outputs": [{"name": "value", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "envOr", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "defaultValue", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "value", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "envOr", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "defaultValue", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "value", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "envOr", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "defaultValue", "type": "int256", "internalType": "int256"}], "outputs": [{"name": "value", "type": "int256", "internalType": "int256"}], "stateMutability": "view"}, {"type": "function", "name": "envOr", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "delim", "type": "string", "internalType": "string"}, {"name": "defaultValue", "type": "address[]", "internalType": "address[]"}], "outputs": [{"name": "value", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "envOr", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "defaultValue", "type": "string", "internalType": "string"}], "outputs": [{"name": "value", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "envOr", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "delim", "type": "string", "internalType": "string"}, {"name": "defaultValue", "type": "bool[]", "internalType": "bool[]"}], "outputs": [{"name": "value", "type": "bool[]", "internalType": "bool[]"}], "stateMutability": "view"}, {"type": "function", "name": "envString", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "delim", "type": "string", "internalType": "string"}], "outputs": [{"name": "value", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "envString", "inputs": [{"name": "name", "type": "string", "internalType": "string"}], "outputs": [{"name": "value", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "envUint", "inputs": [{"name": "name", "type": "string", "internalType": "string"}], "outputs": [{"name": "value", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "envUint", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "delim", "type": "string", "internalType": "string"}], "outputs": [{"name": "value", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "eth_getLogs", "inputs": [{"name": "fromBlock", "type": "uint256", "internalType": "uint256"}, {"name": "toBlock", "type": "uint256", "internalType": "uint256"}, {"name": "target", "type": "address", "internalType": "address"}, {"name": "topics", "type": "bytes32[]", "internalType": "bytes32[]"}], "outputs": [{"name": "logs", "type": "tuple[]", "internalType": "struct VmSafe.EthGetLogs[]", "components": [{"name": "emitter", "type": "address", "internalType": "address"}, {"name": "topics", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "data", "type": "bytes", "internalType": "bytes"}, {"name": "blockHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "blockNumber", "type": "uint64", "internalType": "uint64"}, {"name": "transactionHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "transactionIndex", "type": "uint64", "internalType": "uint64"}, {"name": "logIndex", "type": "uint256", "internalType": "uint256"}, {"name": "removed", "type": "bool", "internalType": "bool"}]}], "stateMutability": "nonpayable"}, {"type": "function", "name": "exists", "inputs": [{"name": "path", "type": "string", "internalType": "string"}], "outputs": [{"name": "result", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "ffi", "inputs": [{"name": "commandInput", "type": "string[]", "internalType": "string[]"}], "outputs": [{"name": "result", "type": "bytes", "internalType": "bytes"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "fsMetadata", "inputs": [{"name": "path", "type": "string", "internalType": "string"}], "outputs": [{"name": "metadata", "type": "tuple", "internalType": "struct VmSafe.FsMetadata", "components": [{"name": "isDir", "type": "bool", "internalType": "bool"}, {"name": "isSymlink", "type": "bool", "internalType": "bool"}, {"name": "length", "type": "uint256", "internalType": "uint256"}, {"name": "readOnly", "type": "bool", "internalType": "bool"}, {"name": "modified", "type": "uint256", "internalType": "uint256"}, {"name": "accessed", "type": "uint256", "internalType": "uint256"}, {"name": "created", "type": "uint256", "internalType": "uint256"}]}], "stateMutability": "view"}, {"type": "function", "name": "getArtifactPathByCode", "inputs": [{"name": "code", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "path", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "getArtifactPathByDeployedCode", "inputs": [{"name": "deployedCode", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "path", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "getBlobBaseFee", "inputs": [], "outputs": [{"name": "blobBaseFee", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getBlockNumber", "inputs": [], "outputs": [{"name": "height", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getBlockTimestamp", "inputs": [], "outputs": [{"name": "timestamp", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getCode", "inputs": [{"name": "artifactPath", "type": "string", "internalType": "string"}], "outputs": [{"name": "creationBytecode", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "getDeployedCode", "inputs": [{"name": "artifactPath", "type": "string", "internalType": "string"}], "outputs": [{"name": "runtimeBytecode", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "getFoundryVersion", "inputs": [], "outputs": [{"name": "version", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "get<PERSON><PERSON><PERSON>", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "current<PERSON><PERSON><PERSON>", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "getMappingKeyAndParentOf", "inputs": [{"name": "target", "type": "address", "internalType": "address"}, {"name": "elementSlot", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "found", "type": "bool", "internalType": "bool"}, {"name": "key", "type": "bytes32", "internalType": "bytes32"}, {"name": "parent", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getMapping<PERSON>ength", "inputs": [{"name": "target", "type": "address", "internalType": "address"}, {"name": "mappingSlot", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "length", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getMappingSlotAt", "inputs": [{"name": "target", "type": "address", "internalType": "address"}, {"name": "mappingSlot", "type": "bytes32", "internalType": "bytes32"}, {"name": "idx", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "value", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getNonce", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "nonce", "type": "uint64", "internalType": "uint64"}], "stateMutability": "view"}, {"type": "function", "name": "getNonce", "inputs": [{"name": "wallet", "type": "tuple", "internalType": "struct VmSafe.Wallet", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "publicKeyX", "type": "uint256", "internalType": "uint256"}, {"name": "publicKeyY", "type": "uint256", "internalType": "uint256"}, {"name": "privateKey", "type": "uint256", "internalType": "uint256"}]}], "outputs": [{"name": "nonce", "type": "uint64", "internalType": "uint64"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getRecordedLogs", "inputs": [], "outputs": [{"name": "logs", "type": "tuple[]", "internalType": "struct VmSafe.Log[]", "components": [{"name": "topics", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "data", "type": "bytes", "internalType": "bytes"}, {"name": "emitter", "type": "address", "internalType": "address"}]}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getScriptWallets", "inputs": [], "outputs": [{"name": "wallets", "type": "address[]", "internalType": "address[]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getWallets", "inputs": [], "outputs": [{"name": "wallets", "type": "address[]", "internalType": "address[]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "indexOf", "inputs": [{"name": "input", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "function", "name": "isContext", "inputs": [{"name": "context", "type": "uint8", "internalType": "enum VmSafe.ForgeContext"}], "outputs": [{"name": "result", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isDir", "inputs": [{"name": "path", "type": "string", "internalType": "string"}], "outputs": [{"name": "result", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "isFile", "inputs": [{"name": "path", "type": "string", "internalType": "string"}], "outputs": [{"name": "result", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "keyExists", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "keyExists<PERSON>son", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "keyExistsToml", "inputs": [{"name": "toml", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "label", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "new<PERSON>abel", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "lastCallGas", "inputs": [], "outputs": [{"name": "gas", "type": "tuple", "internalType": "struct VmSafe.Gas", "components": [{"name": "gasLimit", "type": "uint64", "internalType": "uint64"}, {"name": "gasTotalUsed", "type": "uint64", "internalType": "uint64"}, {"name": "gasMemoryUsed", "type": "uint64", "internalType": "uint64"}, {"name": "gasRefunded", "type": "int64", "internalType": "int64"}, {"name": "gasRemaining", "type": "uint64", "internalType": "uint64"}]}], "stateMutability": "view"}, {"type": "function", "name": "load", "inputs": [{"name": "target", "type": "address", "internalType": "address"}, {"name": "slot", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "data", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "parseAddress", "inputs": [{"name": "stringifiedValue", "type": "string", "internalType": "string"}], "outputs": [{"name": "parsedValue", "type": "address", "internalType": "address"}], "stateMutability": "pure"}, {"type": "function", "name": "parseBool", "inputs": [{"name": "stringifiedValue", "type": "string", "internalType": "string"}], "outputs": [{"name": "parsedValue", "type": "bool", "internalType": "bool"}], "stateMutability": "pure"}, {"type": "function", "name": "parseBytes", "inputs": [{"name": "stringifiedValue", "type": "string", "internalType": "string"}], "outputs": [{"name": "parsedValue", "type": "bytes", "internalType": "bytes"}], "stateMutability": "pure"}, {"type": "function", "name": "parseBytes32", "inputs": [{"name": "stringifiedValue", "type": "string", "internalType": "string"}], "outputs": [{"name": "parsedValue", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "parseInt", "inputs": [{"name": "stringifiedValue", "type": "string", "internalType": "string"}], "outputs": [{"name": "parsedValue", "type": "int256", "internalType": "int256"}], "stateMutability": "pure"}, {"type": "function", "name": "parseJson", "inputs": [{"name": "json", "type": "string", "internalType": "string"}], "outputs": [{"name": "abiEncodedData", "type": "bytes", "internalType": "bytes"}], "stateMutability": "pure"}, {"type": "function", "name": "parseJson", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "abiEncodedData", "type": "bytes", "internalType": "bytes"}], "stateMutability": "pure"}, {"type": "function", "name": "parseJson<PERSON>dd<PERSON>", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "pure"}, {"type": "function", "name": "parseJsonAddressArray", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "pure"}, {"type": "function", "name": "parseJsonBool", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "pure"}, {"type": "function", "name": "parseJsonBoolArray", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bool[]", "internalType": "bool[]"}], "stateMutability": "pure"}, {"type": "function", "name": "parseJsonBytes", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "pure"}, {"type": "function", "name": "parseJsonBytes32", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "parseJsonBytes32Array", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bytes32[]", "internalType": "bytes32[]"}], "stateMutability": "pure"}, {"type": "function", "name": "parseJsonBytesArray", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bytes[]", "internalType": "bytes[]"}], "stateMutability": "pure"}, {"type": "function", "name": "parseJsonInt", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "int256", "internalType": "int256"}], "stateMutability": "pure"}, {"type": "function", "name": "parseJsonIntArray", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "int256[]", "internalType": "int256[]"}], "stateMutability": "pure"}, {"type": "function", "name": "parseJsonKeys", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "keys", "type": "string[]", "internalType": "string[]"}], "stateMutability": "pure"}, {"type": "function", "name": "parseJsonString", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "pure"}, {"type": "function", "name": "parseJsonStringArray", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "string[]", "internalType": "string[]"}], "stateMutability": "pure"}, {"type": "function", "name": "parseJsonType", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "typeDescription", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "pure"}, {"type": "function", "name": "parseJsonType", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}, {"name": "typeDescription", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "pure"}, {"type": "function", "name": "parseJsonTypeArray", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}, {"name": "typeDescription", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "pure"}, {"type": "function", "name": "parseJsonUint", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "function", "name": "parseJsonUintArray", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "pure"}, {"type": "function", "name": "parseToml", "inputs": [{"name": "toml", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "abiEncodedData", "type": "bytes", "internalType": "bytes"}], "stateMutability": "pure"}, {"type": "function", "name": "parseToml", "inputs": [{"name": "toml", "type": "string", "internalType": "string"}], "outputs": [{"name": "abiEncodedData", "type": "bytes", "internalType": "bytes"}], "stateMutability": "pure"}, {"type": "function", "name": "parseTomlAddress", "inputs": [{"name": "toml", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "pure"}, {"type": "function", "name": "parseTomlAddressArray", "inputs": [{"name": "toml", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "pure"}, {"type": "function", "name": "parseTomlBool", "inputs": [{"name": "toml", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "pure"}, {"type": "function", "name": "parseTomlBoolArray", "inputs": [{"name": "toml", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bool[]", "internalType": "bool[]"}], "stateMutability": "pure"}, {"type": "function", "name": "parseTomlBytes", "inputs": [{"name": "toml", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "pure"}, {"type": "function", "name": "parseTomlBytes32", "inputs": [{"name": "toml", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "parseTomlBytes32Array", "inputs": [{"name": "toml", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bytes32[]", "internalType": "bytes32[]"}], "stateMutability": "pure"}, {"type": "function", "name": "parseTomlBytesArray", "inputs": [{"name": "toml", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bytes[]", "internalType": "bytes[]"}], "stateMutability": "pure"}, {"type": "function", "name": "parseTomlInt", "inputs": [{"name": "toml", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "int256", "internalType": "int256"}], "stateMutability": "pure"}, {"type": "function", "name": "parseTomlIntArray", "inputs": [{"name": "toml", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "int256[]", "internalType": "int256[]"}], "stateMutability": "pure"}, {"type": "function", "name": "parseTomlKeys", "inputs": [{"name": "toml", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "keys", "type": "string[]", "internalType": "string[]"}], "stateMutability": "pure"}, {"type": "function", "name": "parseTomlString", "inputs": [{"name": "toml", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "pure"}, {"type": "function", "name": "parseTomlStringArray", "inputs": [{"name": "toml", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "string[]", "internalType": "string[]"}], "stateMutability": "pure"}, {"type": "function", "name": "parseTomlType", "inputs": [{"name": "toml", "type": "string", "internalType": "string"}, {"name": "typeDescription", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "pure"}, {"type": "function", "name": "parseTomlType", "inputs": [{"name": "toml", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}, {"name": "typeDescription", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "pure"}, {"type": "function", "name": "parseTomlTypeArray", "inputs": [{"name": "toml", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}, {"name": "typeDescription", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "pure"}, {"type": "function", "name": "parseTomlUint", "inputs": [{"name": "toml", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "function", "name": "parseTomlUintArray", "inputs": [{"name": "toml", "type": "string", "internalType": "string"}, {"name": "key", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "pure"}, {"type": "function", "name": "parseUint", "inputs": [{"name": "stringifiedValue", "type": "string", "internalType": "string"}], "outputs": [{"name": "parsedValue", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "function", "name": "pauseGasMetering", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "pauseTracing", "inputs": [], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "projectRoot", "inputs": [], "outputs": [{"name": "path", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "prompt", "inputs": [{"name": "promptText", "type": "string", "internalType": "string"}], "outputs": [{"name": "input", "type": "string", "internalType": "string"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "promptAddress", "inputs": [{"name": "promptText", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "promptSecret", "inputs": [{"name": "promptText", "type": "string", "internalType": "string"}], "outputs": [{"name": "input", "type": "string", "internalType": "string"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "promptSecretUint", "inputs": [{"name": "promptText", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "promptUint", "inputs": [{"name": "promptText", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "publicKeyP256", "inputs": [{"name": "privateKey", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "publicKeyX", "type": "uint256", "internalType": "uint256"}, {"name": "publicKeyY", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "function", "name": "random<PERSON>ddress", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "randomBool", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "randomBytes", "inputs": [{"name": "len", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "randomBytes4", "inputs": [], "outputs": [{"name": "", "type": "bytes4", "internalType": "bytes4"}], "stateMutability": "view"}, {"type": "function", "name": "randomBytes8", "inputs": [], "outputs": [{"name": "", "type": "bytes8", "internalType": "bytes8"}], "stateMutability": "view"}, {"type": "function", "name": "randomInt", "inputs": [], "outputs": [{"name": "", "type": "int256", "internalType": "int256"}], "stateMutability": "view"}, {"type": "function", "name": "randomInt", "inputs": [{"name": "bits", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "int256", "internalType": "int256"}], "stateMutability": "view"}, {"type": "function", "name": "randomUint", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "randomUint", "inputs": [{"name": "bits", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "randomUint", "inputs": [{"name": "min", "type": "uint256", "internalType": "uint256"}, {"name": "max", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "readDir", "inputs": [{"name": "path", "type": "string", "internalType": "string"}, {"name": "max<PERSON><PERSON><PERSON>", "type": "uint64", "internalType": "uint64"}], "outputs": [{"name": "entries", "type": "tuple[]", "internalType": "struct VmSafe.DirEntry[]", "components": [{"name": "errorMessage", "type": "string", "internalType": "string"}, {"name": "path", "type": "string", "internalType": "string"}, {"name": "depth", "type": "uint64", "internalType": "uint64"}, {"name": "isDir", "type": "bool", "internalType": "bool"}, {"name": "isSymlink", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "readDir", "inputs": [{"name": "path", "type": "string", "internalType": "string"}, {"name": "max<PERSON><PERSON><PERSON>", "type": "uint64", "internalType": "uint64"}, {"name": "followLinks", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "entries", "type": "tuple[]", "internalType": "struct VmSafe.DirEntry[]", "components": [{"name": "errorMessage", "type": "string", "internalType": "string"}, {"name": "path", "type": "string", "internalType": "string"}, {"name": "depth", "type": "uint64", "internalType": "uint64"}, {"name": "isDir", "type": "bool", "internalType": "bool"}, {"name": "isSymlink", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "readDir", "inputs": [{"name": "path", "type": "string", "internalType": "string"}], "outputs": [{"name": "entries", "type": "tuple[]", "internalType": "struct VmSafe.DirEntry[]", "components": [{"name": "errorMessage", "type": "string", "internalType": "string"}, {"name": "path", "type": "string", "internalType": "string"}, {"name": "depth", "type": "uint64", "internalType": "uint64"}, {"name": "isDir", "type": "bool", "internalType": "bool"}, {"name": "isSymlink", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "readFile", "inputs": [{"name": "path", "type": "string", "internalType": "string"}], "outputs": [{"name": "data", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "readFileBinary", "inputs": [{"name": "path", "type": "string", "internalType": "string"}], "outputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "readLine", "inputs": [{"name": "path", "type": "string", "internalType": "string"}], "outputs": [{"name": "line", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "readLink", "inputs": [{"name": "linkPath", "type": "string", "internalType": "string"}], "outputs": [{"name": "targetPath", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "record", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "recordLogs", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON>", "inputs": [{"name": "privateKey", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "keyAddr", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "mnemonic", "type": "string", "internalType": "string"}, {"name": "derivationPath", "type": "string", "internalType": "string"}, {"name": "count", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "keyAddrs", "type": "address[]", "internalType": "address[]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "mnemonic", "type": "string", "internalType": "string"}, {"name": "derivationPath", "type": "string", "internalType": "string"}, {"name": "language", "type": "string", "internalType": "string"}, {"name": "count", "type": "uint32", "internalType": "uint32"}], "outputs": [{"name": "keyAddrs", "type": "address[]", "internalType": "address[]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "removeDir", "inputs": [{"name": "path", "type": "string", "internalType": "string"}, {"name": "recursive", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "removeFile", "inputs": [{"name": "path", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "replace", "inputs": [{"name": "input", "type": "string", "internalType": "string"}, {"name": "from", "type": "string", "internalType": "string"}, {"name": "to", "type": "string", "internalType": "string"}], "outputs": [{"name": "output", "type": "string", "internalType": "string"}], "stateMutability": "pure"}, {"type": "function", "name": "resetGasMetering", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "resumeGasMetering", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "resumeTracing", "inputs": [], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "rpc", "inputs": [{"name": "url<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string", "internalType": "string"}, {"name": "method", "type": "string", "internalType": "string"}, {"name": "params", "type": "string", "internalType": "string"}], "outputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "rpc", "inputs": [{"name": "method", "type": "string", "internalType": "string"}, {"name": "params", "type": "string", "internalType": "string"}], "outputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "rpcUrl", "inputs": [{"name": "rpcAlias", "type": "string", "internalType": "string"}], "outputs": [{"name": "json", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "rpcUrlStructs", "inputs": [], "outputs": [{"name": "urls", "type": "tuple[]", "internalType": "struct VmSafe.Rpc[]", "components": [{"name": "key", "type": "string", "internalType": "string"}, {"name": "url", "type": "string", "internalType": "string"}]}], "stateMutability": "view"}, {"type": "function", "name": "rpcUrls", "inputs": [], "outputs": [{"name": "urls", "type": "string[2][]", "internalType": "string[2][]"}], "stateMutability": "view"}, {"type": "function", "name": "serializeAddress", "inputs": [{"name": "object<PERSON>ey", "type": "string", "internalType": "string"}, {"name": "valueKey", "type": "string", "internalType": "string"}, {"name": "values", "type": "address[]", "internalType": "address[]"}], "outputs": [{"name": "json", "type": "string", "internalType": "string"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "serializeAddress", "inputs": [{"name": "object<PERSON>ey", "type": "string", "internalType": "string"}, {"name": "valueKey", "type": "string", "internalType": "string"}, {"name": "value", "type": "address", "internalType": "address"}], "outputs": [{"name": "json", "type": "string", "internalType": "string"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "serializeBool", "inputs": [{"name": "object<PERSON>ey", "type": "string", "internalType": "string"}, {"name": "valueKey", "type": "string", "internalType": "string"}, {"name": "values", "type": "bool[]", "internalType": "bool[]"}], "outputs": [{"name": "json", "type": "string", "internalType": "string"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "serializeBool", "inputs": [{"name": "object<PERSON>ey", "type": "string", "internalType": "string"}, {"name": "valueKey", "type": "string", "internalType": "string"}, {"name": "value", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "json", "type": "string", "internalType": "string"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "serializeBytes", "inputs": [{"name": "object<PERSON>ey", "type": "string", "internalType": "string"}, {"name": "valueKey", "type": "string", "internalType": "string"}, {"name": "values", "type": "bytes[]", "internalType": "bytes[]"}], "outputs": [{"name": "json", "type": "string", "internalType": "string"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "serializeBytes", "inputs": [{"name": "object<PERSON>ey", "type": "string", "internalType": "string"}, {"name": "valueKey", "type": "string", "internalType": "string"}, {"name": "value", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "json", "type": "string", "internalType": "string"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "serializeBytes32", "inputs": [{"name": "object<PERSON>ey", "type": "string", "internalType": "string"}, {"name": "valueKey", "type": "string", "internalType": "string"}, {"name": "values", "type": "bytes32[]", "internalType": "bytes32[]"}], "outputs": [{"name": "json", "type": "string", "internalType": "string"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "serializeBytes32", "inputs": [{"name": "object<PERSON>ey", "type": "string", "internalType": "string"}, {"name": "valueKey", "type": "string", "internalType": "string"}, {"name": "value", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "json", "type": "string", "internalType": "string"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "serializeInt", "inputs": [{"name": "object<PERSON>ey", "type": "string", "internalType": "string"}, {"name": "valueKey", "type": "string", "internalType": "string"}, {"name": "value", "type": "int256", "internalType": "int256"}], "outputs": [{"name": "json", "type": "string", "internalType": "string"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "serializeInt", "inputs": [{"name": "object<PERSON>ey", "type": "string", "internalType": "string"}, {"name": "valueKey", "type": "string", "internalType": "string"}, {"name": "values", "type": "int256[]", "internalType": "int256[]"}], "outputs": [{"name": "json", "type": "string", "internalType": "string"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "serializeJ<PERSON>", "inputs": [{"name": "object<PERSON>ey", "type": "string", "internalType": "string"}, {"name": "value", "type": "string", "internalType": "string"}], "outputs": [{"name": "json", "type": "string", "internalType": "string"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "serializeJsonType", "inputs": [{"name": "typeDescription", "type": "string", "internalType": "string"}, {"name": "value", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "json", "type": "string", "internalType": "string"}], "stateMutability": "pure"}, {"type": "function", "name": "serializeJsonType", "inputs": [{"name": "object<PERSON>ey", "type": "string", "internalType": "string"}, {"name": "valueKey", "type": "string", "internalType": "string"}, {"name": "typeDescription", "type": "string", "internalType": "string"}, {"name": "value", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "json", "type": "string", "internalType": "string"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "serializeString", "inputs": [{"name": "object<PERSON>ey", "type": "string", "internalType": "string"}, {"name": "valueKey", "type": "string", "internalType": "string"}, {"name": "values", "type": "string[]", "internalType": "string[]"}], "outputs": [{"name": "json", "type": "string", "internalType": "string"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "serializeString", "inputs": [{"name": "object<PERSON>ey", "type": "string", "internalType": "string"}, {"name": "valueKey", "type": "string", "internalType": "string"}, {"name": "value", "type": "string", "internalType": "string"}], "outputs": [{"name": "json", "type": "string", "internalType": "string"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "serializeUint", "inputs": [{"name": "object<PERSON>ey", "type": "string", "internalType": "string"}, {"name": "valueKey", "type": "string", "internalType": "string"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "json", "type": "string", "internalType": "string"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "serializeUint", "inputs": [{"name": "object<PERSON>ey", "type": "string", "internalType": "string"}, {"name": "valueKey", "type": "string", "internalType": "string"}, {"name": "values", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [{"name": "json", "type": "string", "internalType": "string"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "serializeUintToHex", "inputs": [{"name": "object<PERSON>ey", "type": "string", "internalType": "string"}, {"name": "valueKey", "type": "string", "internalType": "string"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "json", "type": "string", "internalType": "string"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "setArbitraryStorage", "inputs": [{"name": "target", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setEnv", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "value", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "sign", "inputs": [{"name": "digest", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "v", "type": "uint8", "internalType": "uint8"}, {"name": "r", "type": "bytes32", "internalType": "bytes32"}, {"name": "s", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "sign", "inputs": [{"name": "signer", "type": "address", "internalType": "address"}, {"name": "digest", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "v", "type": "uint8", "internalType": "uint8"}, {"name": "r", "type": "bytes32", "internalType": "bytes32"}, {"name": "s", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "sign", "inputs": [{"name": "wallet", "type": "tuple", "internalType": "struct VmSafe.Wallet", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "publicKeyX", "type": "uint256", "internalType": "uint256"}, {"name": "publicKeyY", "type": "uint256", "internalType": "uint256"}, {"name": "privateKey", "type": "uint256", "internalType": "uint256"}]}, {"name": "digest", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "v", "type": "uint8", "internalType": "uint8"}, {"name": "r", "type": "bytes32", "internalType": "bytes32"}, {"name": "s", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "sign", "inputs": [{"name": "privateKey", "type": "uint256", "internalType": "uint256"}, {"name": "digest", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "v", "type": "uint8", "internalType": "uint8"}, {"name": "r", "type": "bytes32", "internalType": "bytes32"}, {"name": "s", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "signCompact", "inputs": [{"name": "wallet", "type": "tuple", "internalType": "struct VmSafe.Wallet", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "publicKeyX", "type": "uint256", "internalType": "uint256"}, {"name": "publicKeyY", "type": "uint256", "internalType": "uint256"}, {"name": "privateKey", "type": "uint256", "internalType": "uint256"}]}, {"name": "digest", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "r", "type": "bytes32", "internalType": "bytes32"}, {"name": "vs", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "signCompact", "inputs": [{"name": "signer", "type": "address", "internalType": "address"}, {"name": "digest", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "r", "type": "bytes32", "internalType": "bytes32"}, {"name": "vs", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "signCompact", "inputs": [{"name": "digest", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "r", "type": "bytes32", "internalType": "bytes32"}, {"name": "vs", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "signCompact", "inputs": [{"name": "privateKey", "type": "uint256", "internalType": "uint256"}, {"name": "digest", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "r", "type": "bytes32", "internalType": "bytes32"}, {"name": "vs", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "signP256", "inputs": [{"name": "privateKey", "type": "uint256", "internalType": "uint256"}, {"name": "digest", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "r", "type": "bytes32", "internalType": "bytes32"}, {"name": "s", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "sleep", "inputs": [{"name": "duration", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "split", "inputs": [{"name": "input", "type": "string", "internalType": "string"}, {"name": "delimiter", "type": "string", "internalType": "string"}], "outputs": [{"name": "outputs", "type": "string[]", "internalType": "string[]"}], "stateMutability": "pure"}, {"type": "function", "name": "startBroadcast", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "startBroadcast", "inputs": [{"name": "signer", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "startBroadcast", "inputs": [{"name": "privateKey", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "startDebugTraceRecording", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "startMappingRecording", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "startStateDiffRecording", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "stopAndReturnDebugTraceRecording", "inputs": [], "outputs": [{"name": "step", "type": "tuple[]", "internalType": "struct VmSafe.DebugStep[]", "components": [{"name": "stack", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "memoryInput", "type": "bytes", "internalType": "bytes"}, {"name": "opcode", "type": "uint8", "internalType": "uint8"}, {"name": "depth", "type": "uint64", "internalType": "uint64"}, {"name": "isOutOfGas", "type": "bool", "internalType": "bool"}, {"name": "contractAddr", "type": "address", "internalType": "address"}]}], "stateMutability": "nonpayable"}, {"type": "function", "name": "stopAndReturnStateDiff", "inputs": [], "outputs": [{"name": "accountAccesses", "type": "tuple[]", "internalType": "struct VmSafe.AccountAccess[]", "components": [{"name": "chainInfo", "type": "tuple", "internalType": "struct VmSafe.ChainInfo", "components": [{"name": "forkId", "type": "uint256", "internalType": "uint256"}, {"name": "chainId", "type": "uint256", "internalType": "uint256"}]}, {"name": "kind", "type": "uint8", "internalType": "enum VmSafe.AccountAccessKind"}, {"name": "account", "type": "address", "internalType": "address"}, {"name": "accessor", "type": "address", "internalType": "address"}, {"name": "initialized", "type": "bool", "internalType": "bool"}, {"name": "oldBalance", "type": "uint256", "internalType": "uint256"}, {"name": "newBalance", "type": "uint256", "internalType": "uint256"}, {"name": "deployedCode", "type": "bytes", "internalType": "bytes"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}, {"name": "reverted", "type": "bool", "internalType": "bool"}, {"name": "storageAccesses", "type": "tuple[]", "internalType": "struct VmSafe.StorageAccess[]", "components": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "slot", "type": "bytes32", "internalType": "bytes32"}, {"name": "isWrite", "type": "bool", "internalType": "bool"}, {"name": "previousValue", "type": "bytes32", "internalType": "bytes32"}, {"name": "newValue", "type": "bytes32", "internalType": "bytes32"}, {"name": "reverted", "type": "bool", "internalType": "bool"}]}, {"name": "depth", "type": "uint64", "internalType": "uint64"}]}], "stateMutability": "nonpayable"}, {"type": "function", "name": "stopBroadcast", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "stopMappingRecording", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "toBase64", "inputs": [{"name": "data", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "pure"}, {"type": "function", "name": "toBase64", "inputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "pure"}, {"type": "function", "name": "toBase64URL", "inputs": [{"name": "data", "type": "string", "internalType": "string"}], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "pure"}, {"type": "function", "name": "toBase64URL", "inputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "pure"}, {"type": "function", "name": "toLowercase", "inputs": [{"name": "input", "type": "string", "internalType": "string"}], "outputs": [{"name": "output", "type": "string", "internalType": "string"}], "stateMutability": "pure"}, {"type": "function", "name": "toString", "inputs": [{"name": "value", "type": "address", "internalType": "address"}], "outputs": [{"name": "stringifiedValue", "type": "string", "internalType": "string"}], "stateMutability": "pure"}, {"type": "function", "name": "toString", "inputs": [{"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "stringifiedValue", "type": "string", "internalType": "string"}], "stateMutability": "pure"}, {"type": "function", "name": "toString", "inputs": [{"name": "value", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "stringifiedValue", "type": "string", "internalType": "string"}], "stateMutability": "pure"}, {"type": "function", "name": "toString", "inputs": [{"name": "value", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "stringifiedValue", "type": "string", "internalType": "string"}], "stateMutability": "pure"}, {"type": "function", "name": "toString", "inputs": [{"name": "value", "type": "int256", "internalType": "int256"}], "outputs": [{"name": "stringifiedValue", "type": "string", "internalType": "string"}], "stateMutability": "pure"}, {"type": "function", "name": "toString", "inputs": [{"name": "value", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "stringifiedValue", "type": "string", "internalType": "string"}], "stateMutability": "pure"}, {"type": "function", "name": "toUppercase", "inputs": [{"name": "input", "type": "string", "internalType": "string"}], "outputs": [{"name": "output", "type": "string", "internalType": "string"}], "stateMutability": "pure"}, {"type": "function", "name": "trim", "inputs": [{"name": "input", "type": "string", "internalType": "string"}], "outputs": [{"name": "output", "type": "string", "internalType": "string"}], "stateMutability": "pure"}, {"type": "function", "name": "tryFfi", "inputs": [{"name": "commandInput", "type": "string[]", "internalType": "string[]"}], "outputs": [{"name": "result", "type": "tuple", "internalType": "struct VmSafe.FfiResult", "components": [{"name": "exitCode", "type": "int32", "internalType": "int32"}, {"name": "stdout", "type": "bytes", "internalType": "bytes"}, {"name": "stderr", "type": "bytes", "internalType": "bytes"}]}], "stateMutability": "nonpayable"}, {"type": "function", "name": "unixTime", "inputs": [], "outputs": [{"name": "milliseconds", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "writeFile", "inputs": [{"name": "path", "type": "string", "internalType": "string"}, {"name": "data", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "writeFileBinary", "inputs": [{"name": "path", "type": "string", "internalType": "string"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "writeJson", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "path", "type": "string", "internalType": "string"}, {"name": "valueKey", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "writeJson", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "path", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "writeLine", "inputs": [{"name": "path", "type": "string", "internalType": "string"}, {"name": "data", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "writeToml", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "path", "type": "string", "internalType": "string"}, {"name": "valueKey", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "writeToml", "inputs": [{"name": "json", "type": "string", "internalType": "string"}, {"name": "path", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"accesses(address)": "65bc9481", "addr(uint256)": "ffa18649", "assertApproxEqAbs(int256,int256,uint256)": "240f839d", "assertApproxEqAbs(int256,int256,uint256,string)": "8289e621", "assertApproxEqAbs(uint256,uint256,uint256)": "16d207c6", "assertApproxEqAbs(uint256,uint256,uint256,string)": "f710b062", "assertApproxEqAbsDecimal(int256,int256,uint256,uint256)": "3d5bc8bc", "assertApproxEqAbsDecimal(int256,int256,uint256,uint256,string)": "6a5066d4", "assertApproxEqAbsDecimal(uint256,uint256,uint256,uint256)": "045c55ce", "assertApproxEqAbsDecimal(uint256,uint256,uint256,uint256,string)": "60429eb2", "assertApproxEqRel(int256,int256,uint256)": "fea2d14f", "assertApproxEqRel(int256,int256,uint256,string)": "ef277d72", "assertApproxEqRel(uint256,uint256,uint256)": "8cf25ef4", "assertApproxEqRel(uint256,uint256,uint256,string)": "1ecb7d33", "assertApproxEqRelDecimal(int256,int256,uint256,uint256)": "abbf21cc", "assertApproxEqRelDecimal(int256,int256,uint256,uint256,string)": "fccc11c4", "assertApproxEqRelDecimal(uint256,uint256,uint256,uint256)": "21ed2977", "assertApproxEqRelDecimal(uint256,uint256,uint256,uint256,string)": "82d6c8fd", "assertEq(address,address)": "515361f6", "assertEq(address,address,string)": "2f2769d1", "assertEq(address[],address[])": "3868ac34", "assertEq(address[],address[],string)": "3e9173c5", "assertEq(bool,bool)": "f7fe3477", "assertEq(bool,bool,string)": "4db19e7e", "assertEq(bool[],bool[])": "707df785", "assertEq(bool[],bool[],string)": "e48a8f8d", "assertEq(bytes,bytes)": "97624631", "assertEq(bytes,bytes,string)": "e24fed00", "assertEq(bytes32,bytes32)": "7c84c69b", "assertEq(bytes32,bytes32,string)": "c1fa1ed0", "assertEq(bytes32[],bytes32[])": "0cc9ee84", "assertEq(bytes32[],bytes32[],string)": "e03e9177", "assertEq(bytes[],bytes[])": "e5fb9b4a", "assertEq(bytes[],bytes[],string)": "f413f0b6", "assertEq(int256,int256)": "fe74f05b", "assertEq(int256,int256,string)": "714a2f13", "assertEq(int256[],int256[])": "711043ac", "assertEq(int256[],int256[],string)": "191f1b30", "assertEq(string,string)": "f320d963", "assertEq(string,string,string)": "36f656d8", "assertEq(string[],string[])": "cf1c049c", "assertEq(string[],string[],string)": "eff6b27d", "assertEq(uint256,uint256)": "98296c54", "assertEq(uint256,uint256,string)": "88b44c85", "assertEq(uint256[],uint256[])": "975d5a12", "assertEq(uint256[],uint256[],string)": "5d18c73a", "assertEqDecimal(int256,int256,uint256)": "48016c04", "assertEqDecimal(int256,int256,uint256,string)": "7e77b0c5", "assertEqDecimal(uint256,uint256,uint256)": "27af7d9c", "assertEqDecimal(uint256,uint256,uint256,string)": "d0cbbdef", "assertFalse(bool)": "a5982885", "assertFalse(bool,string)": "7ba04809", "assertGe(int256,int256)": "0a30b771", "assertGe(int256,int256,string)": "a84328dd", "assertGe(uint256,uint256)": "a8d4d1d9", "assertGe(uint256,uint256,string)": "e25242c0", "assertGeDecimal(int256,int256,uint256)": "dc28c0f1", "assertGeDecimal(int256,int256,uint256,string)": "5df93c9b", "assertGeDecimal(uint256,uint256,uint256)": "3d1fe08a", "assertGeDecimal(uint256,uint256,uint256,string)": "8bff9133", "assertGt(int256,int256)": "5a362d45", "assertGt(int256,int256,string)": "f8d33b9b", "assertGt(uint256,uint256)": "db07fcd2", "assertGt(uint256,uint256,string)": "d9a3c4d2", "assertGtDecimal(int256,int256,uint256)": "78611f0e", "assertGtDecimal(int256,int256,uint256,string)": "04a5c7ab", "assertGtDecimal(uint256,uint256,uint256)": "eccd2437", "assertGtDecimal(uint256,uint256,uint256,string)": "64949a8d", "assertLe(int256,int256)": "95fd154e", "assertLe(int256,int256,string)": "4dfe692c", "assertLe(uint256,uint256)": "8466f415", "assertLe(uint256,uint256,string)": "d17d4b0d", "assertLeDecimal(int256,int256,uint256)": "11d1364a", "assertLeDecimal(int256,int256,uint256,string)": "aa5cf788", "assertLeDecimal(uint256,uint256,uint256)": "c304aab7", "assertLeDecimal(uint256,uint256,uint256,string)": "7fefbbe0", "assertLt(int256,int256)": "3e914080", "assertLt(int256,int256,string)": "9ff531e3", "assertLt(uint256,uint256)": "b12fc005", "assertLt(uint256,uint256,string)": "65d5c135", "assertLtDecimal(int256,int256,uint256)": "dbe8d88b", "assertLtDecimal(int256,int256,uint256,string)": "40f0b4e0", "assertLtDecimal(uint256,uint256,uint256)": "2077337e", "assertLtDecimal(uint256,uint256,uint256,string)": "a972d037", "assertNotEq(address,address)": "b12e1694", "assertNotEq(address,address,string)": "8775a591", "assertNotEq(address[],address[])": "46d0b252", "assertNotEq(address[],address[],string)": "72c7e0b5", "assertNotEq(bool,bool)": "236e4d66", "assertNotEq(bool,bool,string)": "1091a261", "assertNotEq(bool[],bool[])": "286fafea", "assertNotEq(bool[],bool[],string)": "62c6f9fb", "assertNotEq(bytes,bytes)": "3cf78e28", "assertNotEq(bytes,bytes,string)": "9507540e", "assertNotEq(bytes32,bytes32)": "898e83fc", "assertNotEq(bytes32,bytes32,string)": "b2332f51", "assertNotEq(bytes32[],bytes32[])": "0603ea68", "assertNotEq(bytes32[],bytes32[],string)": "b873634c", "assertNotEq(bytes[],bytes[])": "edecd035", "assertNotEq(bytes[],bytes[],string)": "1dcd1f68", "assertNotEq(int256,int256)": "f4c004e3", "assertNotEq(int256,int256,string)": "4724c5b9", "assertNotEq(int256[],int256[])": "0b72f4ef", "assertNotEq(int256[],int256[],string)": "d3977322", "assertNotEq(string,string)": "6a8237b3", "assertNotEq(string,string,string)": "78bdcea7", "assertNotEq(string[],string[])": "bdfacbe8", "assertNotEq(string[],string[],string)": "b67187f3", "assertNotEq(uint256,uint256)": "b7909320", "assertNotEq(uint256,uint256,string)": "98f9bdbd", "assertNotEq(uint256[],uint256[])": "56f29cba", "assertNotEq(uint256[],uint256[],string)": "9a7fbd8f", "assertNotEqDecimal(int256,int256,uint256)": "14e75680", "assertNotEqDecimal(int256,int256,uint256,string)": "33949f0b", "assertNotEqDecimal(uint256,uint256,uint256)": "669efca7", "assertNotEqDecimal(uint256,uint256,uint256,string)": "f5a55558", "assertTrue(bool)": "0c9fd581", "assertTrue(bool,string)": "a34edc03", "assume(bool)": "4c63e562", "assumeNoRevert()": "285b366a", "breakpoint(string)": "f0259e92", "breakpoint(string,bool)": "f7d39a8d", "broadcast()": "afc98040", "broadcast(address)": "e6962cdb", "broadcast(uint256)": "f67a965b", "broadcastRawTransaction(bytes)": "8c0c72e0", "closeFile(string)": "48c3241f", "computeCreate2Address(bytes32,bytes32)": "890c283b", "computeCreate2Address(bytes32,bytes32,address)": "d323826a", "computeCreateAddress(address,uint256)": "74637a7a", "copyFile(string,string)": "a54a87d8", "copyStorage(address,address)": "203dac0d", "createDir(string,bool)": "168b64d3", "createWallet(string)": "7404f1d2", "createWallet(uint256)": "7a675bb6", "createWallet(uint256,string)": "ed7c5462", "deployCode(string)": "9a8325a0", "deployCode(string,bytes)": "29ce9dde", "deriveKey(string,string,uint32)": "6bcb2c1b", "deriveKey(string,string,uint32,string)": "29233b1f", "deriveKey(string,uint32)": "6229498b", "deriveKey(string,uint32,string)": "32c8176d", "ensNamehash(string)": "8c374c65", "envAddress(string)": "350d56bf", "envAddress(string,string)": "ad31b9fa", "envBool(string)": "7ed1ec7d", "envBool(string,string)": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "envBytes(string)": "4d7baf06", "envBytes(string,string)": "ddc2651b", "envBytes32(string)": "97949042", "envBytes32(string,string)": "5af231c1", "envExists(string)": "ce8365f9", "envInt(string)": "892a0c61", "envInt(string,string)": "42181150", "envOr(string,address)": "561fe540", "envOr(string,bool)": "4777f3cf", "envOr(string,bytes)": "b3e47705", "envOr(string,bytes32)": "b4a85892", "envOr(string,int256)": "bbcb713e", "envOr(string,string)": "d145736c", "envOr(string,string,address[])": "c74e9deb", "envOr(string,string,bool[])": "eb85e83b", "envOr(string,string,bytes32[])": "2281f367", "envOr(string,string,bytes[])": "64bc3e64", "envOr(string,string,int256[])": "4700d74b", "envOr(string,string,string[])": "859216bc", "envOr(string,string,uint256[])": "74318528", "envOr(string,uint256)": "5e97348f", "envString(string)": "f877cb19", "envString(string,string)": "14b02bc9", "envUint(string)": "c1978d1f", "envUint(string,string)": "f3dec099", "eth_getLogs(uint256,uint256,address,bytes32[])": "35e1349b", "exists(string)": "261a323e", "ffi(string[])": "89160467", "fsMetadata(string)": "af368a08", "getArtifactPathByCode(bytes)": "eb74848c", "getArtifactPathByDeployedCode(bytes)": "6d853ba5", "getBlobBaseFee()": "1f6d6ef7", "getBlockNumber()": "42cbb15c", "getBlockTimestamp()": "796b89b9", "getCode(string)": "8d1cc925", "getDeployedCode(string)": "3ebf73b4", "getFoundryVersion()": "ea991bb5", "getLabel(address)": "28a249b0", "getMappingKeyAndParentOf(address,bytes32)": "876e24e6", "getMappingLength(address,bytes32)": "2f2fd63f", "getMappingSlotAt(address,bytes32,uint256)": "ebc73ab4", "getNonce((address,uint256,uint256,uint256))": "a5748aad", "getNonce(address)": "2d0335ab", "getRecordedLogs()": "191553a4", "getScriptWallets()": "7c49aa1f", "getWallets()": "db7a4605", "indexOf(string,string)": "8a0807b7", "isContext(uint8)": "64af255d", "isDir(string)": "7d15d019", "isFile(string)": "e0eb04d4", "keyExists(string,string)": "528a683c", "keyExistsJson(string,string)": "db4235f6", "keyExistsToml(string,string)": "600903ad", "label(address,string)": "c657c718", "lastCallGas()": "2b589b28", "load(address,bytes32)": "667f9d70", "parseAddress(string)": "c6ce059d", "parseBool(string)": "974ef924", "parseBytes(string)": "8f5d232d", "parseBytes32(string)": "087e6e81", "parseInt(string)": "42346c5e", "parseJson(string)": "6a82600a", "parseJson(string,string)": "85940ef1", "parseJsonAddress(string,string)": "1e19e657", "parseJsonAddressArray(string,string)": "2fce7883", "parseJsonBool(string,string)": "9f86dc91", "parseJsonBoolArray(string,string)": "91f3b94f", "parseJsonBytes(string,string)": "fd921be8", "parseJsonBytes32(string,string)": "1777e59d", "parseJsonBytes32Array(string,string)": "91c75bc3", "parseJsonBytesArray(string,string)": "6631aa99", "parseJsonInt(string,string)": "7b048ccd", "parseJsonIntArray(string,string)": "9983c28a", "parseJsonKeys(string,string)": "213e4198", "parseJsonString(string,string)": "49c4fac8", "parseJsonStringArray(string,string)": "498fdcf4", "parseJsonType(string,string)": "a9da313b", "parseJsonType(string,string,string)": "e3f5ae33", "parseJsonTypeArray(string,string,string)": "0175d535", "parseJsonUint(string,string)": "addde2b6", "parseJsonUintArray(string,string)": "522074ab", "parseToml(string)": "592151f0", "parseToml(string,string)": "37736e08", "parseTomlAddress(string,string)": "65e7c844", "parseTomlAddressArray(string,string)": "65c428e7", "parseTomlBool(string,string)": "d30dced6", "parseTomlBoolArray(string,string)": "127cfe9a", "parseTomlBytes(string,string)": "d77bfdb9", "parseTomlBytes32(string,string)": "8e214810", "parseTomlBytes32Array(string,string)": "3e716f81", "parseTomlBytesArray(string,string)": "b197c247", "parseTomlInt(string,string)": "c1350739", "parseTomlIntArray(string,string)": "d3522ae6", "parseTomlKeys(string,string)": "812a44b2", "parseTomlString(string,string)": "8bb8dd43", "parseTomlStringArray(string,string)": "9f629281", "parseTomlType(string,string)": "47fa5e11", "parseTomlType(string,string,string)": "f9fa5cdb", "parseTomlTypeArray(string,string,string)": "49be3743", "parseTomlUint(string,string)": "cc7b0487", "parseTomlUintArray(string,string)": "b5df27c8", "parseUint(string)": "fa91454d", "pauseGasMetering()": "d1a5b36f", "pauseTracing()": "c94d1f90", "projectRoot()": "d930a0e6", "prompt(string)": "47eaf474", "promptAddress(string)": "62ee05f4", "promptSecret(string)": "1e279d41", "promptSecretUint(string)": "69ca02b7", "promptUint(string)": "652fd489", "publicKeyP256(uint256)": "c453949e", "randomAddress()": "d5bee9f5", "randomBool()": "cdc126bd", "randomBytes(uint256)": "6c5d32a9", "randomBytes4()": "9b7cd579", "randomBytes8()": "0497b0a5", "randomInt()": "111f1202", "randomInt(uint256)": "12845966", "randomUint()": "25124730", "randomUint(uint256)": "cf81e69c", "randomUint(uint256,uint256)": "d61b051b", "readDir(string)": "c4bc59e0", "readDir(string,uint64)": "1497876c", "readDir(string,uint64,bool)": "8102d70d", "readFile(string)": "60f9bb11", "readFileBinary(string)": "16ed7bc4", "readLine(string)": "70f55728", "readLink(string)": "9f5684a2", "record()": "266cf109", "recordLogs()": "41af2f52", "rememberKey(uint256)": "22100064", "rememberKeys(string,string,string,uint32)": "f8d58eaf", "rememberKeys(string,string,uint32)": "97cb9189", "removeDir(string,bool)": "45c62011", "removeFile(string)": "f1afe04d", "replace(string,string,string)": "e00ad03e", "resetGasMetering()": "be367dd3", "resumeGasMetering()": "2bcd50e0", "resumeTracing()": "72a09ccb", "rpc(string,string)": "1206c8a8", "rpc(string,string,string)": "0199a220", "rpcUrl(string)": "975a6ce9", "rpcUrlStructs()": "9d2ad72a", "rpcUrls()": "a85a8418", "serializeAddress(string,string,address)": "972c6062", "serializeAddress(string,string,address[])": "1e356e1a", "serializeBool(string,string,bool)": "ac22e971", "serializeBool(string,string,bool[])": "92925aa1", "serializeBytes(string,string,bytes)": "f21d52c7", "serializeBytes(string,string,bytes[])": "9884b232", "serializeBytes32(string,string,bytes32)": "2d812b44", "serializeBytes32(string,string,bytes32[])": "201e43e2", "serializeInt(string,string,int256)": "3f33db60", "serializeInt(string,string,int256[])": "7676e127", "serializeJson(string,string)": "9b3358b0", "serializeJsonType(string,bytes)": "6d4f96a6", "serializeJsonType(string,string,string,bytes)": "6f93bccb", "serializeString(string,string,string)": "88da6d35", "serializeString(string,string,string[])": "561cd6f3", "serializeUint(string,string,uint256)": "129e9002", "serializeUint(string,string,uint256[])": "fee9a469", "serializeUintToHex(string,string,uint256)": "ae5a2ae8", "setArbitraryStorage(address)": "e1631837", "setEnv(string,string)": "3d5923ee", "sign((address,uint256,uint256,uint256),bytes32)": "b25c5a25", "sign(address,bytes32)": "8c1aa205", "sign(bytes32)": "799cd333", "sign(uint256,bytes32)": "e341eaa4", "signCompact((address,uint256,uint256,uint256),bytes32)": "3d0e292f", "signCompact(address,bytes32)": "8e2f97bf", "signCompact(bytes32)": "a282dc4b", "signCompact(uint256,bytes32)": "cc2a781f", "signP256(uint256,bytes32)": "83211b40", "sleep(uint256)": "fa9d8713", "split(string,string)": "8bb75533", "startBroadcast()": "7fb5297f", "startBroadcast(address)": "7fec2a8d", "startBroadcast(uint256)": "ce817d47", "startDebugTraceRecording()": "419c8832", "startMappingRecording()": "3e9705c0", "startStateDiffRecording()": "cf22e3c9", "stopAndReturnDebugTraceRecording()": "ced398a2", "stopAndReturnStateDiff()": "aa5cf90e", "stopBroadcast()": "76eadd36", "stopMappingRecording()": "0d4aae9b", "toBase64(bytes)": "a5cbfe65", "toBase64(string)": "3f8be2c8", "toBase64URL(bytes)": "c8bd0e4a", "toBase64URL(string)": "ae3165b3", "toLowercase(string)": "50bb0884", "toString(address)": "56ca623e", "toString(bool)": "71dce7da", "toString(bytes)": "71aad10d", "toString(bytes32)": "b11a19e8", "toString(int256)": "a322c40e", "toString(uint256)": "6900a3ae", "toUppercase(string)": "074ae3d7", "trim(string)": "b2dad155", "tryFfi(string[])": "f45c1ce7", "unixTime()": "625387dc", "writeFile(string,string)": "897e0a97", "writeFileBinary(string,bytes)": "1f21fc80", "writeJson(string,string)": "e23cd19f", "writeJson(string,string,string)": "35d6ad46", "writeLine(string,string)": "619d897f", "writeToml(string,string)": "c0865ba7", "writeToml(string,string,string)": "51ac6a33"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"}],\"name\":\"accesses\",\"outputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"readSlots\",\"type\":\"bytes32[]\"},{\"internalType\":\"bytes32[]\",\"name\":\"writeSlots\",\"type\":\"bytes32[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"privateKey\",\"type\":\"uint256\"}],\"name\":\"addr\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"keyAddr\",\"type\":\"address\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"maxDelta\",\"type\":\"uint256\"}],\"name\":\"assertApproxEqAbs\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"maxDelta\",\"type\":\"uint256\"}],\"name\":\"assertApproxEqAbs\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"maxDelta\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertApproxEqAbs\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"maxDelta\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertApproxEqAbs\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"maxDelta\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"assertApproxEqAbsDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"maxDelta\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"assertApproxEqAbsDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"maxDelta\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertApproxEqAbsDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"maxDelta\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertApproxEqAbsDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"maxPercentDelta\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertApproxEqRel\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"maxPercentDelta\",\"type\":\"uint256\"}],\"name\":\"assertApproxEqRel\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"maxPercentDelta\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertApproxEqRel\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"maxPercentDelta\",\"type\":\"uint256\"}],\"name\":\"assertApproxEqRel\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"maxPercentDelta\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"assertApproxEqRelDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"maxPercentDelta\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertApproxEqRelDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"maxPercentDelta\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"assertApproxEqRelDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"maxPercentDelta\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertApproxEqRelDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"left\",\"type\":\"bytes32[]\"},{\"internalType\":\"bytes32[]\",\"name\":\"right\",\"type\":\"bytes32[]\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256[]\",\"name\":\"left\",\"type\":\"int256[]\"},{\"internalType\":\"int256[]\",\"name\":\"right\",\"type\":\"int256[]\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"left\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"right\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"left\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"right\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"left\",\"type\":\"address[]\"},{\"internalType\":\"address[]\",\"name\":\"right\",\"type\":\"address[]\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"left\",\"type\":\"address[]\"},{\"internalType\":\"address[]\",\"name\":\"right\",\"type\":\"address[]\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"left\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"right\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"left\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"right\",\"type\":\"address\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256[]\",\"name\":\"left\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"right\",\"type\":\"uint256[]\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool[]\",\"name\":\"left\",\"type\":\"bool[]\"},{\"internalType\":\"bool[]\",\"name\":\"right\",\"type\":\"bool[]\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256[]\",\"name\":\"left\",\"type\":\"int256[]\"},{\"internalType\":\"int256[]\",\"name\":\"right\",\"type\":\"int256[]\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"left\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"right\",\"type\":\"bytes32\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256[]\",\"name\":\"left\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"right\",\"type\":\"uint256[]\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"left\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"right\",\"type\":\"bytes\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"left\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"right\",\"type\":\"bytes32\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string[]\",\"name\":\"left\",\"type\":\"string[]\"},{\"internalType\":\"string[]\",\"name\":\"right\",\"type\":\"string[]\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"left\",\"type\":\"bytes32[]\"},{\"internalType\":\"bytes32[]\",\"name\":\"right\",\"type\":\"bytes32[]\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"left\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"right\",\"type\":\"bytes\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool[]\",\"name\":\"left\",\"type\":\"bool[]\"},{\"internalType\":\"bool[]\",\"name\":\"right\",\"type\":\"bool[]\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes[]\",\"name\":\"left\",\"type\":\"bytes[]\"},{\"internalType\":\"bytes[]\",\"name\":\"right\",\"type\":\"bytes[]\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string[]\",\"name\":\"left\",\"type\":\"string[]\"},{\"internalType\":\"string[]\",\"name\":\"right\",\"type\":\"string[]\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"left\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"right\",\"type\":\"string\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes[]\",\"name\":\"left\",\"type\":\"bytes[]\"},{\"internalType\":\"bytes[]\",\"name\":\"right\",\"type\":\"bytes[]\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"left\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"right\",\"type\":\"bool\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"}],\"name\":\"assertEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"assertEqDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"assertEqDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertEqDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertEqDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"condition\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertFalse\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"condition\",\"type\":\"bool\"}],\"name\":\"assertFalse\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"}],\"name\":\"assertGe\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertGe\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"}],\"name\":\"assertGe\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertGe\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"assertGeDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertGeDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertGeDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"assertGeDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"}],\"name\":\"assertGt\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertGt\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"}],\"name\":\"assertGt\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertGt\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertGtDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertGtDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"assertGtDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"assertGtDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertLe\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"}],\"name\":\"assertLe\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"}],\"name\":\"assertLe\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertLe\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"assertLeDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertLeDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertLeDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"assertLeDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"}],\"name\":\"assertLt\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertLt\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertLt\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"}],\"name\":\"assertLt\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"assertLtDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertLtDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertLtDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"assertLtDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"left\",\"type\":\"bytes32[]\"},{\"internalType\":\"bytes32[]\",\"name\":\"right\",\"type\":\"bytes32[]\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256[]\",\"name\":\"left\",\"type\":\"int256[]\"},{\"internalType\":\"int256[]\",\"name\":\"right\",\"type\":\"int256[]\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"left\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"right\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes[]\",\"name\":\"left\",\"type\":\"bytes[]\"},{\"internalType\":\"bytes[]\",\"name\":\"right\",\"type\":\"bytes[]\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"left\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"right\",\"type\":\"bool\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool[]\",\"name\":\"left\",\"type\":\"bool[]\"},{\"internalType\":\"bool[]\",\"name\":\"right\",\"type\":\"bool[]\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"left\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"right\",\"type\":\"bytes\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"left\",\"type\":\"address[]\"},{\"internalType\":\"address[]\",\"name\":\"right\",\"type\":\"address[]\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256[]\",\"name\":\"left\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"right\",\"type\":\"uint256[]\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool[]\",\"name\":\"left\",\"type\":\"bool[]\"},{\"internalType\":\"bool[]\",\"name\":\"right\",\"type\":\"bool[]\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"left\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"right\",\"type\":\"string\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"left\",\"type\":\"address[]\"},{\"internalType\":\"address[]\",\"name\":\"right\",\"type\":\"address[]\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"left\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"right\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"left\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"right\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"left\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"right\",\"type\":\"bytes32\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"left\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"right\",\"type\":\"bytes\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256[]\",\"name\":\"left\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"right\",\"type\":\"uint256[]\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"left\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"right\",\"type\":\"address\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"left\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"right\",\"type\":\"bytes32\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string[]\",\"name\":\"left\",\"type\":\"string[]\"},{\"internalType\":\"string[]\",\"name\":\"right\",\"type\":\"string[]\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"left\",\"type\":\"bytes32[]\"},{\"internalType\":\"bytes32[]\",\"name\":\"right\",\"type\":\"bytes32[]\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string[]\",\"name\":\"left\",\"type\":\"string[]\"},{\"internalType\":\"string[]\",\"name\":\"right\",\"type\":\"string[]\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256[]\",\"name\":\"left\",\"type\":\"int256[]\"},{\"internalType\":\"int256[]\",\"name\":\"right\",\"type\":\"int256[]\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes[]\",\"name\":\"left\",\"type\":\"bytes[]\"},{\"internalType\":\"bytes[]\",\"name\":\"right\",\"type\":\"bytes[]\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"}],\"name\":\"assertNotEq\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"assertNotEqDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"left\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"right\",\"type\":\"int256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertNotEqDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"assertNotEqDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"left\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"right\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertNotEqDecimal\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"condition\",\"type\":\"bool\"}],\"name\":\"assertTrue\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"condition\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"error\",\"type\":\"string\"}],\"name\":\"assertTrue\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"condition\",\"type\":\"bool\"}],\"name\":\"assume\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"assumeNoRevert\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"char\",\"type\":\"string\"}],\"name\":\"breakpoint\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"char\",\"type\":\"string\"},{\"internalType\":\"bool\",\"name\":\"value\",\"type\":\"bool\"}],\"name\":\"breakpoint\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"broadcast\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"}],\"name\":\"broadcast\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"privateKey\",\"type\":\"uint256\"}],\"name\":\"broadcast\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"broadcastRawTransaction\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"}],\"name\":\"closeFile\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"salt\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"initCodeHash\",\"type\":\"bytes32\"}],\"name\":\"computeCreate2Address\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"salt\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"initCodeHash\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"deployer\",\"type\":\"address\"}],\"name\":\"computeCreate2Address\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"deployer\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"}],\"name\":\"computeCreateAddress\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"from\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"to\",\"type\":\"string\"}],\"name\":\"copyFile\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"copied\",\"type\":\"uint64\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"copyStorage\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"},{\"internalType\":\"bool\",\"name\":\"recursive\",\"type\":\"bool\"}],\"name\":\"createDir\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"walletLabel\",\"type\":\"string\"}],\"name\":\"createWallet\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"publicKeyX\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"publicKeyY\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"privateKey\",\"type\":\"uint256\"}],\"internalType\":\"struct VmSafe.Wallet\",\"name\":\"wallet\",\"type\":\"tuple\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"privateKey\",\"type\":\"uint256\"}],\"name\":\"createWallet\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"publicKeyX\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"publicKeyY\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"privateKey\",\"type\":\"uint256\"}],\"internalType\":\"struct VmSafe.Wallet\",\"name\":\"wallet\",\"type\":\"tuple\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"privateKey\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"walletLabel\",\"type\":\"string\"}],\"name\":\"createWallet\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"publicKeyX\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"publicKeyY\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"privateKey\",\"type\":\"uint256\"}],\"internalType\":\"struct VmSafe.Wallet\",\"name\":\"wallet\",\"type\":\"tuple\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"artifactPath\",\"type\":\"string\"},{\"internalType\":\"bytes\",\"name\":\"constructorArgs\",\"type\":\"bytes\"}],\"name\":\"deployCode\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"deployedAddress\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"artifactPath\",\"type\":\"string\"}],\"name\":\"deployCode\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"deployedAddress\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"mnemonic\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"derivationPath\",\"type\":\"string\"},{\"internalType\":\"uint32\",\"name\":\"index\",\"type\":\"uint32\"},{\"internalType\":\"string\",\"name\":\"language\",\"type\":\"string\"}],\"name\":\"deriveKey\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"privateKey\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"mnemonic\",\"type\":\"string\"},{\"internalType\":\"uint32\",\"name\":\"index\",\"type\":\"uint32\"},{\"internalType\":\"string\",\"name\":\"language\",\"type\":\"string\"}],\"name\":\"deriveKey\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"privateKey\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"mnemonic\",\"type\":\"string\"},{\"internalType\":\"uint32\",\"name\":\"index\",\"type\":\"uint32\"}],\"name\":\"deriveKey\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"privateKey\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"mnemonic\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"derivationPath\",\"type\":\"string\"},{\"internalType\":\"uint32\",\"name\":\"index\",\"type\":\"uint32\"}],\"name\":\"deriveKey\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"privateKey\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"}],\"name\":\"ensNamehash\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"}],\"name\":\"envAddress\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"value\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"delim\",\"type\":\"string\"}],\"name\":\"envAddress\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"value\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"}],\"name\":\"envBool\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"value\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"delim\",\"type\":\"string\"}],\"name\":\"envBool\",\"outputs\":[{\"internalType\":\"bool[]\",\"name\":\"value\",\"type\":\"bool[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"}],\"name\":\"envBytes\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"value\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"delim\",\"type\":\"string\"}],\"name\":\"envBytes\",\"outputs\":[{\"internalType\":\"bytes[]\",\"name\":\"value\",\"type\":\"bytes[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"delim\",\"type\":\"string\"}],\"name\":\"envBytes32\",\"outputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"value\",\"type\":\"bytes32[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"}],\"name\":\"envBytes32\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"value\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"}],\"name\":\"envExists\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"result\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"delim\",\"type\":\"string\"}],\"name\":\"envInt\",\"outputs\":[{\"internalType\":\"int256[]\",\"name\":\"value\",\"type\":\"int256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"}],\"name\":\"envInt\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"value\",\"type\":\"int256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"delim\",\"type\":\"string\"},{\"internalType\":\"bytes32[]\",\"name\":\"defaultValue\",\"type\":\"bytes32[]\"}],\"name\":\"envOr\",\"outputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"value\",\"type\":\"bytes32[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"delim\",\"type\":\"string\"},{\"internalType\":\"int256[]\",\"name\":\"defaultValue\",\"type\":\"int256[]\"}],\"name\":\"envOr\",\"outputs\":[{\"internalType\":\"int256[]\",\"name\":\"value\",\"type\":\"int256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"bool\",\"name\":\"defaultValue\",\"type\":\"bool\"}],\"name\":\"envOr\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"value\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"address\",\"name\":\"defaultValue\",\"type\":\"address\"}],\"name\":\"envOr\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"value\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"defaultValue\",\"type\":\"uint256\"}],\"name\":\"envOr\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"delim\",\"type\":\"string\"},{\"internalType\":\"bytes[]\",\"name\":\"defaultValue\",\"type\":\"bytes[]\"}],\"name\":\"envOr\",\"outputs\":[{\"internalType\":\"bytes[]\",\"name\":\"value\",\"type\":\"bytes[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"delim\",\"type\":\"string\"},{\"internalType\":\"uint256[]\",\"name\":\"defaultValue\",\"type\":\"uint256[]\"}],\"name\":\"envOr\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"value\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"delim\",\"type\":\"string\"},{\"internalType\":\"string[]\",\"name\":\"defaultValue\",\"type\":\"string[]\"}],\"name\":\"envOr\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"value\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"bytes\",\"name\":\"defaultValue\",\"type\":\"bytes\"}],\"name\":\"envOr\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"value\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"bytes32\",\"name\":\"defaultValue\",\"type\":\"bytes32\"}],\"name\":\"envOr\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"value\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"int256\",\"name\":\"defaultValue\",\"type\":\"int256\"}],\"name\":\"envOr\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"value\",\"type\":\"int256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"delim\",\"type\":\"string\"},{\"internalType\":\"address[]\",\"name\":\"defaultValue\",\"type\":\"address[]\"}],\"name\":\"envOr\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"value\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"defaultValue\",\"type\":\"string\"}],\"name\":\"envOr\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"value\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"delim\",\"type\":\"string\"},{\"internalType\":\"bool[]\",\"name\":\"defaultValue\",\"type\":\"bool[]\"}],\"name\":\"envOr\",\"outputs\":[{\"internalType\":\"bool[]\",\"name\":\"value\",\"type\":\"bool[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"delim\",\"type\":\"string\"}],\"name\":\"envString\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"value\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"}],\"name\":\"envString\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"value\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"}],\"name\":\"envUint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"delim\",\"type\":\"string\"}],\"name\":\"envUint\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"value\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"fromBlock\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"toBlock\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"bytes32[]\",\"name\":\"topics\",\"type\":\"bytes32[]\"}],\"name\":\"eth_getLogs\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"emitter\",\"type\":\"address\"},{\"internalType\":\"bytes32[]\",\"name\":\"topics\",\"type\":\"bytes32[]\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"},{\"internalType\":\"bytes32\",\"name\":\"blockHash\",\"type\":\"bytes32\"},{\"internalType\":\"uint64\",\"name\":\"blockNumber\",\"type\":\"uint64\"},{\"internalType\":\"bytes32\",\"name\":\"transactionHash\",\"type\":\"bytes32\"},{\"internalType\":\"uint64\",\"name\":\"transactionIndex\",\"type\":\"uint64\"},{\"internalType\":\"uint256\",\"name\":\"logIndex\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"removed\",\"type\":\"bool\"}],\"internalType\":\"struct VmSafe.EthGetLogs[]\",\"name\":\"logs\",\"type\":\"tuple[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"}],\"name\":\"exists\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"result\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string[]\",\"name\":\"commandInput\",\"type\":\"string[]\"}],\"name\":\"ffi\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"result\",\"type\":\"bytes\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"}],\"name\":\"fsMetadata\",\"outputs\":[{\"components\":[{\"internalType\":\"bool\",\"name\":\"isDir\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isSymlink\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"length\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"readOnly\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"modified\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"accessed\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"created\",\"type\":\"uint256\"}],\"internalType\":\"struct VmSafe.FsMetadata\",\"name\":\"metadata\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"code\",\"type\":\"bytes\"}],\"name\":\"getArtifactPathByCode\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"deployedCode\",\"type\":\"bytes\"}],\"name\":\"getArtifactPathByDeployedCode\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getBlobBaseFee\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"blobBaseFee\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getBlockNumber\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"height\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getBlockTimestamp\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"artifactPath\",\"type\":\"string\"}],\"name\":\"getCode\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"creationBytecode\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"artifactPath\",\"type\":\"string\"}],\"name\":\"getDeployedCode\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"runtimeBytecode\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getFoundryVersion\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"version\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"getLabel\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"currentLabel\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"elementSlot\",\"type\":\"bytes32\"}],\"name\":\"getMappingKeyAndParentOf\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"found\",\"type\":\"bool\"},{\"internalType\":\"bytes32\",\"name\":\"key\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"parent\",\"type\":\"bytes32\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"mappingSlot\",\"type\":\"bytes32\"}],\"name\":\"getMappingLength\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"length\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"mappingSlot\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"idx\",\"type\":\"uint256\"}],\"name\":\"getMappingSlotAt\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"value\",\"type\":\"bytes32\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"getNonce\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"nonce\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"publicKeyX\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"publicKeyY\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"privateKey\",\"type\":\"uint256\"}],\"internalType\":\"struct VmSafe.Wallet\",\"name\":\"wallet\",\"type\":\"tuple\"}],\"name\":\"getNonce\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"nonce\",\"type\":\"uint64\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getRecordedLogs\",\"outputs\":[{\"components\":[{\"internalType\":\"bytes32[]\",\"name\":\"topics\",\"type\":\"bytes32[]\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"},{\"internalType\":\"address\",\"name\":\"emitter\",\"type\":\"address\"}],\"internalType\":\"struct VmSafe.Log[]\",\"name\":\"logs\",\"type\":\"tuple[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getScriptWallets\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"wallets\",\"type\":\"address[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getWallets\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"wallets\",\"type\":\"address[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"input\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"indexOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"enum VmSafe.ForgeContext\",\"name\":\"context\",\"type\":\"uint8\"}],\"name\":\"isContext\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"result\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"}],\"name\":\"isDir\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"result\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"}],\"name\":\"isFile\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"result\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"keyExists\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"keyExistsJson\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"toml\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"keyExistsToml\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"newLabel\",\"type\":\"string\"}],\"name\":\"label\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"lastCallGas\",\"outputs\":[{\"components\":[{\"internalType\":\"uint64\",\"name\":\"gasLimit\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"gasTotalUsed\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"gasMemoryUsed\",\"type\":\"uint64\"},{\"internalType\":\"int64\",\"name\":\"gasRefunded\",\"type\":\"int64\"},{\"internalType\":\"uint64\",\"name\":\"gasRemaining\",\"type\":\"uint64\"}],\"internalType\":\"struct VmSafe.Gas\",\"name\":\"gas\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"slot\",\"type\":\"bytes32\"}],\"name\":\"load\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"data\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"stringifiedValue\",\"type\":\"string\"}],\"name\":\"parseAddress\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"parsedValue\",\"type\":\"address\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"stringifiedValue\",\"type\":\"string\"}],\"name\":\"parseBool\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"parsedValue\",\"type\":\"bool\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"stringifiedValue\",\"type\":\"string\"}],\"name\":\"parseBytes\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"parsedValue\",\"type\":\"bytes\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"stringifiedValue\",\"type\":\"string\"}],\"name\":\"parseBytes32\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"parsedValue\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"stringifiedValue\",\"type\":\"string\"}],\"name\":\"parseInt\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"parsedValue\",\"type\":\"int256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"}],\"name\":\"parseJson\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"abiEncodedData\",\"type\":\"bytes\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseJson\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"abiEncodedData\",\"type\":\"bytes\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseJsonAddress\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseJsonAddressArray\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseJsonBool\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseJsonBoolArray\",\"outputs\":[{\"internalType\":\"bool[]\",\"name\":\"\",\"type\":\"bool[]\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseJsonBytes\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseJsonBytes32\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseJsonBytes32Array\",\"outputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"\",\"type\":\"bytes32[]\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseJsonBytesArray\",\"outputs\":[{\"internalType\":\"bytes[]\",\"name\":\"\",\"type\":\"bytes[]\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseJsonInt\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseJsonIntArray\",\"outputs\":[{\"internalType\":\"int256[]\",\"name\":\"\",\"type\":\"int256[]\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseJsonKeys\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"keys\",\"type\":\"string[]\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseJsonString\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseJsonStringArray\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"\",\"type\":\"string[]\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"typeDescription\",\"type\":\"string\"}],\"name\":\"parseJsonType\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"typeDescription\",\"type\":\"string\"}],\"name\":\"parseJsonType\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"typeDescription\",\"type\":\"string\"}],\"name\":\"parseJsonTypeArray\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseJsonUint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseJsonUintArray\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"\",\"type\":\"uint256[]\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"toml\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseToml\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"abiEncodedData\",\"type\":\"bytes\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"toml\",\"type\":\"string\"}],\"name\":\"parseToml\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"abiEncodedData\",\"type\":\"bytes\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"toml\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseTomlAddress\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"toml\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseTomlAddressArray\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"toml\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseTomlBool\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"toml\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseTomlBoolArray\",\"outputs\":[{\"internalType\":\"bool[]\",\"name\":\"\",\"type\":\"bool[]\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"toml\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseTomlBytes\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"toml\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseTomlBytes32\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"toml\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseTomlBytes32Array\",\"outputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"\",\"type\":\"bytes32[]\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"toml\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseTomlBytesArray\",\"outputs\":[{\"internalType\":\"bytes[]\",\"name\":\"\",\"type\":\"bytes[]\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"toml\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseTomlInt\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"toml\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseTomlIntArray\",\"outputs\":[{\"internalType\":\"int256[]\",\"name\":\"\",\"type\":\"int256[]\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"toml\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseTomlKeys\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"keys\",\"type\":\"string[]\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"toml\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseTomlString\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"toml\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseTomlStringArray\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"\",\"type\":\"string[]\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"toml\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"typeDescription\",\"type\":\"string\"}],\"name\":\"parseTomlType\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"toml\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"typeDescription\",\"type\":\"string\"}],\"name\":\"parseTomlType\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"toml\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"typeDescription\",\"type\":\"string\"}],\"name\":\"parseTomlTypeArray\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"toml\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseTomlUint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"toml\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"}],\"name\":\"parseTomlUintArray\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"\",\"type\":\"uint256[]\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"stringifiedValue\",\"type\":\"string\"}],\"name\":\"parseUint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"parsedValue\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pauseGasMetering\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pauseTracing\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"projectRoot\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"promptText\",\"type\":\"string\"}],\"name\":\"prompt\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"input\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"promptText\",\"type\":\"string\"}],\"name\":\"promptAddress\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"promptText\",\"type\":\"string\"}],\"name\":\"promptSecret\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"input\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"promptText\",\"type\":\"string\"}],\"name\":\"promptSecretUint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"promptText\",\"type\":\"string\"}],\"name\":\"promptUint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"privateKey\",\"type\":\"uint256\"}],\"name\":\"publicKeyP256\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"publicKeyX\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"publicKeyY\",\"type\":\"uint256\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"randomAddress\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"randomBool\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"len\",\"type\":\"uint256\"}],\"name\":\"randomBytes\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"randomBytes4\",\"outputs\":[{\"internalType\":\"bytes4\",\"name\":\"\",\"type\":\"bytes4\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"randomBytes8\",\"outputs\":[{\"internalType\":\"bytes8\",\"name\":\"\",\"type\":\"bytes8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"randomInt\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"bits\",\"type\":\"uint256\"}],\"name\":\"randomInt\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"randomUint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"bits\",\"type\":\"uint256\"}],\"name\":\"randomUint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"min\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"max\",\"type\":\"uint256\"}],\"name\":\"randomUint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"},{\"internalType\":\"uint64\",\"name\":\"maxDepth\",\"type\":\"uint64\"}],\"name\":\"readDir\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"errorMessage\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"},{\"internalType\":\"uint64\",\"name\":\"depth\",\"type\":\"uint64\"},{\"internalType\":\"bool\",\"name\":\"isDir\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isSymlink\",\"type\":\"bool\"}],\"internalType\":\"struct VmSafe.DirEntry[]\",\"name\":\"entries\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"},{\"internalType\":\"uint64\",\"name\":\"maxDepth\",\"type\":\"uint64\"},{\"internalType\":\"bool\",\"name\":\"followLinks\",\"type\":\"bool\"}],\"name\":\"readDir\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"errorMessage\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"},{\"internalType\":\"uint64\",\"name\":\"depth\",\"type\":\"uint64\"},{\"internalType\":\"bool\",\"name\":\"isDir\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isSymlink\",\"type\":\"bool\"}],\"internalType\":\"struct VmSafe.DirEntry[]\",\"name\":\"entries\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"}],\"name\":\"readDir\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"errorMessage\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"},{\"internalType\":\"uint64\",\"name\":\"depth\",\"type\":\"uint64\"},{\"internalType\":\"bool\",\"name\":\"isDir\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isSymlink\",\"type\":\"bool\"}],\"internalType\":\"struct VmSafe.DirEntry[]\",\"name\":\"entries\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"}],\"name\":\"readFile\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"data\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"}],\"name\":\"readFileBinary\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"}],\"name\":\"readLine\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"line\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"linkPath\",\"type\":\"string\"}],\"name\":\"readLink\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"targetPath\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"record\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"recordLogs\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"privateKey\",\"type\":\"uint256\"}],\"name\":\"rememberKey\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"keyAddr\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"mnemonic\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"derivationPath\",\"type\":\"string\"},{\"internalType\":\"uint32\",\"name\":\"count\",\"type\":\"uint32\"}],\"name\":\"rememberKeys\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"keyAddrs\",\"type\":\"address[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"mnemonic\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"derivationPath\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"language\",\"type\":\"string\"},{\"internalType\":\"uint32\",\"name\":\"count\",\"type\":\"uint32\"}],\"name\":\"rememberKeys\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"keyAddrs\",\"type\":\"address[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"},{\"internalType\":\"bool\",\"name\":\"recursive\",\"type\":\"bool\"}],\"name\":\"removeDir\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"}],\"name\":\"removeFile\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"input\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"from\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"to\",\"type\":\"string\"}],\"name\":\"replace\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"output\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"resetGasMetering\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"resumeGasMetering\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"resumeTracing\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"urlOrAlias\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"method\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"params\",\"type\":\"string\"}],\"name\":\"rpc\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"method\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"params\",\"type\":\"string\"}],\"name\":\"rpc\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"rpcAlias\",\"type\":\"string\"}],\"name\":\"rpcUrl\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rpcUrlStructs\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"url\",\"type\":\"string\"}],\"internalType\":\"struct VmSafe.Rpc[]\",\"name\":\"urls\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rpcUrls\",\"outputs\":[{\"internalType\":\"string[2][]\",\"name\":\"urls\",\"type\":\"string[2][]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"objectKey\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"valueKey\",\"type\":\"string\"},{\"internalType\":\"address[]\",\"name\":\"values\",\"type\":\"address[]\"}],\"name\":\"serializeAddress\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"objectKey\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"valueKey\",\"type\":\"string\"},{\"internalType\":\"address\",\"name\":\"value\",\"type\":\"address\"}],\"name\":\"serializeAddress\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"objectKey\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"valueKey\",\"type\":\"string\"},{\"internalType\":\"bool[]\",\"name\":\"values\",\"type\":\"bool[]\"}],\"name\":\"serializeBool\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"objectKey\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"valueKey\",\"type\":\"string\"},{\"internalType\":\"bool\",\"name\":\"value\",\"type\":\"bool\"}],\"name\":\"serializeBool\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"objectKey\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"valueKey\",\"type\":\"string\"},{\"internalType\":\"bytes[]\",\"name\":\"values\",\"type\":\"bytes[]\"}],\"name\":\"serializeBytes\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"objectKey\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"valueKey\",\"type\":\"string\"},{\"internalType\":\"bytes\",\"name\":\"value\",\"type\":\"bytes\"}],\"name\":\"serializeBytes\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"objectKey\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"valueKey\",\"type\":\"string\"},{\"internalType\":\"bytes32[]\",\"name\":\"values\",\"type\":\"bytes32[]\"}],\"name\":\"serializeBytes32\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"objectKey\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"valueKey\",\"type\":\"string\"},{\"internalType\":\"bytes32\",\"name\":\"value\",\"type\":\"bytes32\"}],\"name\":\"serializeBytes32\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"objectKey\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"valueKey\",\"type\":\"string\"},{\"internalType\":\"int256\",\"name\":\"value\",\"type\":\"int256\"}],\"name\":\"serializeInt\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"objectKey\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"valueKey\",\"type\":\"string\"},{\"internalType\":\"int256[]\",\"name\":\"values\",\"type\":\"int256[]\"}],\"name\":\"serializeInt\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"objectKey\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"value\",\"type\":\"string\"}],\"name\":\"serializeJson\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"typeDescription\",\"type\":\"string\"},{\"internalType\":\"bytes\",\"name\":\"value\",\"type\":\"bytes\"}],\"name\":\"serializeJsonType\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"objectKey\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"valueKey\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"typeDescription\",\"type\":\"string\"},{\"internalType\":\"bytes\",\"name\":\"value\",\"type\":\"bytes\"}],\"name\":\"serializeJsonType\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"objectKey\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"valueKey\",\"type\":\"string\"},{\"internalType\":\"string[]\",\"name\":\"values\",\"type\":\"string[]\"}],\"name\":\"serializeString\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"objectKey\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"valueKey\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"value\",\"type\":\"string\"}],\"name\":\"serializeString\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"objectKey\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"valueKey\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"serializeUint\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"objectKey\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"valueKey\",\"type\":\"string\"},{\"internalType\":\"uint256[]\",\"name\":\"values\",\"type\":\"uint256[]\"}],\"name\":\"serializeUint\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"objectKey\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"valueKey\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"serializeUintToHex\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"}],\"name\":\"setArbitraryStorage\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"value\",\"type\":\"string\"}],\"name\":\"setEnv\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"digest\",\"type\":\"bytes32\"}],\"name\":\"sign\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"v\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"r\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"digest\",\"type\":\"bytes32\"}],\"name\":\"sign\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"v\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"r\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"publicKeyX\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"publicKeyY\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"privateKey\",\"type\":\"uint256\"}],\"internalType\":\"struct VmSafe.Wallet\",\"name\":\"wallet\",\"type\":\"tuple\"},{\"internalType\":\"bytes32\",\"name\":\"digest\",\"type\":\"bytes32\"}],\"name\":\"sign\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"v\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"r\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"privateKey\",\"type\":\"uint256\"},{\"internalType\":\"bytes32\",\"name\":\"digest\",\"type\":\"bytes32\"}],\"name\":\"sign\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"v\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"r\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"publicKeyX\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"publicKeyY\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"privateKey\",\"type\":\"uint256\"}],\"internalType\":\"struct VmSafe.Wallet\",\"name\":\"wallet\",\"type\":\"tuple\"},{\"internalType\":\"bytes32\",\"name\":\"digest\",\"type\":\"bytes32\"}],\"name\":\"signCompact\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"r\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"vs\",\"type\":\"bytes32\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"digest\",\"type\":\"bytes32\"}],\"name\":\"signCompact\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"r\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"vs\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"digest\",\"type\":\"bytes32\"}],\"name\":\"signCompact\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"r\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"vs\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"privateKey\",\"type\":\"uint256\"},{\"internalType\":\"bytes32\",\"name\":\"digest\",\"type\":\"bytes32\"}],\"name\":\"signCompact\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"r\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"vs\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"privateKey\",\"type\":\"uint256\"},{\"internalType\":\"bytes32\",\"name\":\"digest\",\"type\":\"bytes32\"}],\"name\":\"signP256\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"r\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"duration\",\"type\":\"uint256\"}],\"name\":\"sleep\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"input\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"delimiter\",\"type\":\"string\"}],\"name\":\"split\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"outputs\",\"type\":\"string[]\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"startBroadcast\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"}],\"name\":\"startBroadcast\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"privateKey\",\"type\":\"uint256\"}],\"name\":\"startBroadcast\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"startDebugTraceRecording\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"startMappingRecording\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"startStateDiffRecording\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"stopAndReturnDebugTraceRecording\",\"outputs\":[{\"components\":[{\"internalType\":\"uint256[]\",\"name\":\"stack\",\"type\":\"uint256[]\"},{\"internalType\":\"bytes\",\"name\":\"memoryInput\",\"type\":\"bytes\"},{\"internalType\":\"uint8\",\"name\":\"opcode\",\"type\":\"uint8\"},{\"internalType\":\"uint64\",\"name\":\"depth\",\"type\":\"uint64\"},{\"internalType\":\"bool\",\"name\":\"isOutOfGas\",\"type\":\"bool\"},{\"internalType\":\"address\",\"name\":\"contractAddr\",\"type\":\"address\"}],\"internalType\":\"struct VmSafe.DebugStep[]\",\"name\":\"step\",\"type\":\"tuple[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"stopAndReturnStateDiff\",\"outputs\":[{\"components\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"forkId\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"chainId\",\"type\":\"uint256\"}],\"internalType\":\"struct VmSafe.ChainInfo\",\"name\":\"chainInfo\",\"type\":\"tuple\"},{\"internalType\":\"enum VmSafe.AccountAccessKind\",\"name\":\"kind\",\"type\":\"uint8\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"accessor\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"initialized\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"oldBalance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"newBalance\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"deployedCode\",\"type\":\"bytes\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"},{\"internalType\":\"bool\",\"name\":\"reverted\",\"type\":\"bool\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"slot\",\"type\":\"bytes32\"},{\"internalType\":\"bool\",\"name\":\"isWrite\",\"type\":\"bool\"},{\"internalType\":\"bytes32\",\"name\":\"previousValue\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"newValue\",\"type\":\"bytes32\"},{\"internalType\":\"bool\",\"name\":\"reverted\",\"type\":\"bool\"}],\"internalType\":\"struct VmSafe.StorageAccess[]\",\"name\":\"storageAccesses\",\"type\":\"tuple[]\"},{\"internalType\":\"uint64\",\"name\":\"depth\",\"type\":\"uint64\"}],\"internalType\":\"struct VmSafe.AccountAccess[]\",\"name\":\"accountAccesses\",\"type\":\"tuple[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"stopBroadcast\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"stopMappingRecording\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"data\",\"type\":\"string\"}],\"name\":\"toBase64\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"toBase64\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"data\",\"type\":\"string\"}],\"name\":\"toBase64URL\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"toBase64URL\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"input\",\"type\":\"string\"}],\"name\":\"toLowercase\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"output\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"value\",\"type\":\"address\"}],\"name\":\"toString\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"stringifiedValue\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"toString\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"stringifiedValue\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"value\",\"type\":\"bytes\"}],\"name\":\"toString\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"stringifiedValue\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"value\",\"type\":\"bool\"}],\"name\":\"toString\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"stringifiedValue\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"value\",\"type\":\"int256\"}],\"name\":\"toString\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"stringifiedValue\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"value\",\"type\":\"bytes32\"}],\"name\":\"toString\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"stringifiedValue\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"input\",\"type\":\"string\"}],\"name\":\"toUppercase\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"output\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"input\",\"type\":\"string\"}],\"name\":\"trim\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"output\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string[]\",\"name\":\"commandInput\",\"type\":\"string[]\"}],\"name\":\"tryFfi\",\"outputs\":[{\"components\":[{\"internalType\":\"int32\",\"name\":\"exitCode\",\"type\":\"int32\"},{\"internalType\":\"bytes\",\"name\":\"stdout\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"stderr\",\"type\":\"bytes\"}],\"internalType\":\"struct VmSafe.FfiResult\",\"name\":\"result\",\"type\":\"tuple\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"unixTime\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"milliseconds\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"data\",\"type\":\"string\"}],\"name\":\"writeFile\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"writeFileBinary\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"valueKey\",\"type\":\"string\"}],\"name\":\"writeJson\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"}],\"name\":\"writeJson\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"data\",\"type\":\"string\"}],\"name\":\"writeLine\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"valueKey\",\"type\":\"string\"}],\"name\":\"writeToml\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"json\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"path\",\"type\":\"string\"}],\"name\":\"writeToml\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"accesses(address)\":{\"notice\":\"Gets all accessed reads and write slot from a `vm.record` session, for a given address.\"},\"addr(uint256)\":{\"notice\":\"Gets the address for a given private key.\"},\"assertApproxEqAbs(int256,int256,uint256)\":{\"notice\":\"Compares two `int256` values. Expects difference to be less than or equal to `maxDelta`.\"},\"assertApproxEqAbs(int256,int256,uint256,string)\":{\"notice\":\"Compares two `int256` values. Expects difference to be less than or equal to `maxDelta`. Includes error message into revert string on failure.\"},\"assertApproxEqAbs(uint256,uint256,uint256)\":{\"notice\":\"Compares two `uint256` values. Expects difference to be less than or equal to `maxDelta`.\"},\"assertApproxEqAbs(uint256,uint256,uint256,string)\":{\"notice\":\"Compares two `uint256` values. Expects difference to be less than or equal to `maxDelta`. Includes error message into revert string on failure.\"},\"assertApproxEqAbsDecimal(int256,int256,uint256,uint256)\":{\"notice\":\"Compares two `int256` values. Expects difference to be less than or equal to `maxDelta`. Formats values with decimals in failure message.\"},\"assertApproxEqAbsDecimal(int256,int256,uint256,uint256,string)\":{\"notice\":\"Compares two `int256` values. Expects difference to be less than or equal to `maxDelta`. Formats values with decimals in failure message. Includes error message into revert string on failure.\"},\"assertApproxEqAbsDecimal(uint256,uint256,uint256,uint256)\":{\"notice\":\"Compares two `uint256` values. Expects difference to be less than or equal to `maxDelta`. Formats values with decimals in failure message.\"},\"assertApproxEqAbsDecimal(uint256,uint256,uint256,uint256,string)\":{\"notice\":\"Compares two `uint256` values. Expects difference to be less than or equal to `maxDelta`. Formats values with decimals in failure message. Includes error message into revert string on failure.\"},\"assertApproxEqRel(int256,int256,uint256)\":{\"notice\":\"Compares two `int256` values. Expects relative difference in percents to be less than or equal to `maxPercentDelta`. `maxPercentDelta` is an 18 decimal fixed point number, where 1e18 == 100%\"},\"assertApproxEqRel(int256,int256,uint256,string)\":{\"notice\":\"Compares two `int256` values. Expects relative difference in percents to be less than or equal to `maxPercentDelta`. `maxPercentDelta` is an 18 decimal fixed point number, where 1e18 == 100% Includes error message into revert string on failure.\"},\"assertApproxEqRel(uint256,uint256,uint256)\":{\"notice\":\"Compares two `uint256` values. Expects relative difference in percents to be less than or equal to `maxPercentDelta`. `maxPercentDelta` is an 18 decimal fixed point number, where 1e18 == 100%\"},\"assertApproxEqRel(uint256,uint256,uint256,string)\":{\"notice\":\"Compares two `uint256` values. Expects relative difference in percents to be less than or equal to `maxPercentDelta`. `maxPercentDelta` is an 18 decimal fixed point number, where 1e18 == 100% Includes error message into revert string on failure.\"},\"assertApproxEqRelDecimal(int256,int256,uint256,uint256)\":{\"notice\":\"Compares two `int256` values. Expects relative difference in percents to be less than or equal to `maxPercentDelta`. `maxPercentDelta` is an 18 decimal fixed point number, where 1e18 == 100% Formats values with decimals in failure message.\"},\"assertApproxEqRelDecimal(int256,int256,uint256,uint256,string)\":{\"notice\":\"Compares two `int256` values. Expects relative difference in percents to be less than or equal to `maxPercentDelta`. `maxPercentDelta` is an 18 decimal fixed point number, where 1e18 == 100% Formats values with decimals in failure message. Includes error message into revert string on failure.\"},\"assertApproxEqRelDecimal(uint256,uint256,uint256,uint256)\":{\"notice\":\"Compares two `uint256` values. Expects relative difference in percents to be less than or equal to `maxPercentDelta`. `maxPercentDelta` is an 18 decimal fixed point number, where 1e18 == 100% Formats values with decimals in failure message.\"},\"assertApproxEqRelDecimal(uint256,uint256,uint256,uint256,string)\":{\"notice\":\"Compares two `uint256` values. Expects relative difference in percents to be less than or equal to `maxPercentDelta`. `maxPercentDelta` is an 18 decimal fixed point number, where 1e18 == 100% Formats values with decimals in failure message. Includes error message into revert string on failure.\"},\"assertEq(address,address)\":{\"notice\":\"Asserts that two `address` values are equal.\"},\"assertEq(address,address,string)\":{\"notice\":\"Asserts that two `address` values are equal and includes error message into revert string on failure.\"},\"assertEq(address[],address[])\":{\"notice\":\"Asserts that two arrays of `address` values are equal.\"},\"assertEq(address[],address[],string)\":{\"notice\":\"Asserts that two arrays of `address` values are equal and includes error message into revert string on failure.\"},\"assertEq(bool,bool)\":{\"notice\":\"Asserts that two `bool` values are equal.\"},\"assertEq(bool,bool,string)\":{\"notice\":\"Asserts that two `bool` values are equal and includes error message into revert string on failure.\"},\"assertEq(bool[],bool[])\":{\"notice\":\"Asserts that two arrays of `bool` values are equal.\"},\"assertEq(bool[],bool[],string)\":{\"notice\":\"Asserts that two arrays of `bool` values are equal and includes error message into revert string on failure.\"},\"assertEq(bytes,bytes)\":{\"notice\":\"Asserts that two `bytes` values are equal.\"},\"assertEq(bytes,bytes,string)\":{\"notice\":\"Asserts that two `bytes` values are equal and includes error message into revert string on failure.\"},\"assertEq(bytes32,bytes32)\":{\"notice\":\"Asserts that two `bytes32` values are equal.\"},\"assertEq(bytes32,bytes32,string)\":{\"notice\":\"Asserts that two `bytes32` values are equal and includes error message into revert string on failure.\"},\"assertEq(bytes32[],bytes32[])\":{\"notice\":\"Asserts that two arrays of `bytes32` values are equal.\"},\"assertEq(bytes32[],bytes32[],string)\":{\"notice\":\"Asserts that two arrays of `bytes32` values are equal and includes error message into revert string on failure.\"},\"assertEq(bytes[],bytes[])\":{\"notice\":\"Asserts that two arrays of `bytes` values are equal.\"},\"assertEq(bytes[],bytes[],string)\":{\"notice\":\"Asserts that two arrays of `bytes` values are equal and includes error message into revert string on failure.\"},\"assertEq(int256,int256)\":{\"notice\":\"Asserts that two `int256` values are equal.\"},\"assertEq(int256,int256,string)\":{\"notice\":\"Asserts that two `int256` values are equal and includes error message into revert string on failure.\"},\"assertEq(int256[],int256[])\":{\"notice\":\"Asserts that two arrays of `int256` values are equal.\"},\"assertEq(int256[],int256[],string)\":{\"notice\":\"Asserts that two arrays of `int256` values are equal and includes error message into revert string on failure.\"},\"assertEq(string,string)\":{\"notice\":\"Asserts that two `string` values are equal.\"},\"assertEq(string,string,string)\":{\"notice\":\"Asserts that two `string` values are equal and includes error message into revert string on failure.\"},\"assertEq(string[],string[])\":{\"notice\":\"Asserts that two arrays of `string` values are equal.\"},\"assertEq(string[],string[],string)\":{\"notice\":\"Asserts that two arrays of `string` values are equal and includes error message into revert string on failure.\"},\"assertEq(uint256,uint256)\":{\"notice\":\"Asserts that two `uint256` values are equal.\"},\"assertEq(uint256,uint256,string)\":{\"notice\":\"Asserts that two `uint256` values are equal and includes error message into revert string on failure.\"},\"assertEq(uint256[],uint256[])\":{\"notice\":\"Asserts that two arrays of `uint256 values are equal.\"},\"assertEq(uint256[],uint256[],string)\":{\"notice\":\"Asserts that two arrays of `uint256` values are equal and includes error message into revert string on failure.\"},\"assertEqDecimal(int256,int256,uint256)\":{\"notice\":\"Asserts that two `int256` values are equal, formatting them with decimals in failure message.\"},\"assertEqDecimal(int256,int256,uint256,string)\":{\"notice\":\"Asserts that two `int256` values are equal, formatting them with decimals in failure message. Includes error message into revert string on failure.\"},\"assertEqDecimal(uint256,uint256,uint256)\":{\"notice\":\"Asserts that two `uint256` values are equal, formatting them with decimals in failure message.\"},\"assertEqDecimal(uint256,uint256,uint256,string)\":{\"notice\":\"Asserts that two `uint256` values are equal, formatting them with decimals in failure message. Includes error message into revert string on failure.\"},\"assertFalse(bool)\":{\"notice\":\"Asserts that the given condition is false.\"},\"assertFalse(bool,string)\":{\"notice\":\"Asserts that the given condition is false and includes error message into revert string on failure.\"},\"assertGe(int256,int256)\":{\"notice\":\"Compares two `int256` values. Expects first value to be greater than or equal to second.\"},\"assertGe(int256,int256,string)\":{\"notice\":\"Compares two `int256` values. Expects first value to be greater than or equal to second. Includes error message into revert string on failure.\"},\"assertGe(uint256,uint256)\":{\"notice\":\"Compares two `uint256` values. Expects first value to be greater than or equal to second.\"},\"assertGe(uint256,uint256,string)\":{\"notice\":\"Compares two `uint256` values. Expects first value to be greater than or equal to second. Includes error message into revert string on failure.\"},\"assertGeDecimal(int256,int256,uint256)\":{\"notice\":\"Compares two `int256` values. Expects first value to be greater than or equal to second. Formats values with decimals in failure message.\"},\"assertGeDecimal(int256,int256,uint256,string)\":{\"notice\":\"Compares two `int256` values. Expects first value to be greater than or equal to second. Formats values with decimals in failure message. Includes error message into revert string on failure.\"},\"assertGeDecimal(uint256,uint256,uint256)\":{\"notice\":\"Compares two `uint256` values. Expects first value to be greater than or equal to second. Formats values with decimals in failure message.\"},\"assertGeDecimal(uint256,uint256,uint256,string)\":{\"notice\":\"Compares two `uint256` values. Expects first value to be greater than or equal to second. Formats values with decimals in failure message. Includes error message into revert string on failure.\"},\"assertGt(int256,int256)\":{\"notice\":\"Compares two `int256` values. Expects first value to be greater than second.\"},\"assertGt(int256,int256,string)\":{\"notice\":\"Compares two `int256` values. Expects first value to be greater than second. Includes error message into revert string on failure.\"},\"assertGt(uint256,uint256)\":{\"notice\":\"Compares two `uint256` values. Expects first value to be greater than second.\"},\"assertGt(uint256,uint256,string)\":{\"notice\":\"Compares two `uint256` values. Expects first value to be greater than second. Includes error message into revert string on failure.\"},\"assertGtDecimal(int256,int256,uint256)\":{\"notice\":\"Compares two `int256` values. Expects first value to be greater than second. Formats values with decimals in failure message.\"},\"assertGtDecimal(int256,int256,uint256,string)\":{\"notice\":\"Compares two `int256` values. Expects first value to be greater than second. Formats values with decimals in failure message. Includes error message into revert string on failure.\"},\"assertGtDecimal(uint256,uint256,uint256)\":{\"notice\":\"Compares two `uint256` values. Expects first value to be greater than second. Formats values with decimals in failure message.\"},\"assertGtDecimal(uint256,uint256,uint256,string)\":{\"notice\":\"Compares two `uint256` values. Expects first value to be greater than second. Formats values with decimals in failure message. Includes error message into revert string on failure.\"},\"assertLe(int256,int256)\":{\"notice\":\"Compares two `int256` values. Expects first value to be less than or equal to second.\"},\"assertLe(int256,int256,string)\":{\"notice\":\"Compares two `int256` values. Expects first value to be less than or equal to second. Includes error message into revert string on failure.\"},\"assertLe(uint256,uint256)\":{\"notice\":\"Compares two `uint256` values. Expects first value to be less than or equal to second.\"},\"assertLe(uint256,uint256,string)\":{\"notice\":\"Compares two `uint256` values. Expects first value to be less than or equal to second. Includes error message into revert string on failure.\"},\"assertLeDecimal(int256,int256,uint256)\":{\"notice\":\"Compares two `int256` values. Expects first value to be less than or equal to second. Formats values with decimals in failure message.\"},\"assertLeDecimal(int256,int256,uint256,string)\":{\"notice\":\"Compares two `int256` values. Expects first value to be less than or equal to second. Formats values with decimals in failure message. Includes error message into revert string on failure.\"},\"assertLeDecimal(uint256,uint256,uint256)\":{\"notice\":\"Compares two `uint256` values. Expects first value to be less than or equal to second. Formats values with decimals in failure message.\"},\"assertLeDecimal(uint256,uint256,uint256,string)\":{\"notice\":\"Compares two `uint256` values. Expects first value to be less than or equal to second. Formats values with decimals in failure message. Includes error message into revert string on failure.\"},\"assertLt(int256,int256)\":{\"notice\":\"Compares two `int256` values. Expects first value to be less than second.\"},\"assertLt(int256,int256,string)\":{\"notice\":\"Compares two `int256` values. Expects first value to be less than second. Includes error message into revert string on failure.\"},\"assertLt(uint256,uint256)\":{\"notice\":\"Compares two `uint256` values. Expects first value to be less than second.\"},\"assertLt(uint256,uint256,string)\":{\"notice\":\"Compares two `uint256` values. Expects first value to be less than second. Includes error message into revert string on failure.\"},\"assertLtDecimal(int256,int256,uint256)\":{\"notice\":\"Compares two `int256` values. Expects first value to be less than second. Formats values with decimals in failure message.\"},\"assertLtDecimal(int256,int256,uint256,string)\":{\"notice\":\"Compares two `int256` values. Expects first value to be less than second. Formats values with decimals in failure message. Includes error message into revert string on failure.\"},\"assertLtDecimal(uint256,uint256,uint256)\":{\"notice\":\"Compares two `uint256` values. Expects first value to be less than second. Formats values with decimals in failure message.\"},\"assertLtDecimal(uint256,uint256,uint256,string)\":{\"notice\":\"Compares two `uint256` values. Expects first value to be less than second. Formats values with decimals in failure message. Includes error message into revert string on failure.\"},\"assertNotEq(address,address)\":{\"notice\":\"Asserts that two `address` values are not equal.\"},\"assertNotEq(address,address,string)\":{\"notice\":\"Asserts that two `address` values are not equal and includes error message into revert string on failure.\"},\"assertNotEq(address[],address[])\":{\"notice\":\"Asserts that two arrays of `address` values are not equal.\"},\"assertNotEq(address[],address[],string)\":{\"notice\":\"Asserts that two arrays of `address` values are not equal and includes error message into revert string on failure.\"},\"assertNotEq(bool,bool)\":{\"notice\":\"Asserts that two `bool` values are not equal.\"},\"assertNotEq(bool,bool,string)\":{\"notice\":\"Asserts that two `bool` values are not equal and includes error message into revert string on failure.\"},\"assertNotEq(bool[],bool[])\":{\"notice\":\"Asserts that two arrays of `bool` values are not equal.\"},\"assertNotEq(bool[],bool[],string)\":{\"notice\":\"Asserts that two arrays of `bool` values are not equal and includes error message into revert string on failure.\"},\"assertNotEq(bytes,bytes)\":{\"notice\":\"Asserts that two `bytes` values are not equal.\"},\"assertNotEq(bytes,bytes,string)\":{\"notice\":\"Asserts that two `bytes` values are not equal and includes error message into revert string on failure.\"},\"assertNotEq(bytes32,bytes32)\":{\"notice\":\"Asserts that two `bytes32` values are not equal.\"},\"assertNotEq(bytes32,bytes32,string)\":{\"notice\":\"Asserts that two `bytes32` values are not equal and includes error message into revert string on failure.\"},\"assertNotEq(bytes32[],bytes32[])\":{\"notice\":\"Asserts that two arrays of `bytes32` values are not equal.\"},\"assertNotEq(bytes32[],bytes32[],string)\":{\"notice\":\"Asserts that two arrays of `bytes32` values are not equal and includes error message into revert string on failure.\"},\"assertNotEq(bytes[],bytes[])\":{\"notice\":\"Asserts that two arrays of `bytes` values are not equal.\"},\"assertNotEq(bytes[],bytes[],string)\":{\"notice\":\"Asserts that two arrays of `bytes` values are not equal and includes error message into revert string on failure.\"},\"assertNotEq(int256,int256)\":{\"notice\":\"Asserts that two `int256` values are not equal.\"},\"assertNotEq(int256,int256,string)\":{\"notice\":\"Asserts that two `int256` values are not equal and includes error message into revert string on failure.\"},\"assertNotEq(int256[],int256[])\":{\"notice\":\"Asserts that two arrays of `int256` values are not equal.\"},\"assertNotEq(int256[],int256[],string)\":{\"notice\":\"Asserts that two arrays of `int256` values are not equal and includes error message into revert string on failure.\"},\"assertNotEq(string,string)\":{\"notice\":\"Asserts that two `string` values are not equal.\"},\"assertNotEq(string,string,string)\":{\"notice\":\"Asserts that two `string` values are not equal and includes error message into revert string on failure.\"},\"assertNotEq(string[],string[])\":{\"notice\":\"Asserts that two arrays of `string` values are not equal.\"},\"assertNotEq(string[],string[],string)\":{\"notice\":\"Asserts that two arrays of `string` values are not equal and includes error message into revert string on failure.\"},\"assertNotEq(uint256,uint256)\":{\"notice\":\"Asserts that two `uint256` values are not equal.\"},\"assertNotEq(uint256,uint256,string)\":{\"notice\":\"Asserts that two `uint256` values are not equal and includes error message into revert string on failure.\"},\"assertNotEq(uint256[],uint256[])\":{\"notice\":\"Asserts that two arrays of `uint256` values are not equal.\"},\"assertNotEq(uint256[],uint256[],string)\":{\"notice\":\"Asserts that two arrays of `uint256` values are not equal and includes error message into revert string on failure.\"},\"assertNotEqDecimal(int256,int256,uint256)\":{\"notice\":\"Asserts that two `int256` values are not equal, formatting them with decimals in failure message.\"},\"assertNotEqDecimal(int256,int256,uint256,string)\":{\"notice\":\"Asserts that two `int256` values are not equal, formatting them with decimals in failure message. Includes error message into revert string on failure.\"},\"assertNotEqDecimal(uint256,uint256,uint256)\":{\"notice\":\"Asserts that two `uint256` values are not equal, formatting them with decimals in failure message.\"},\"assertNotEqDecimal(uint256,uint256,uint256,string)\":{\"notice\":\"Asserts that two `uint256` values are not equal, formatting them with decimals in failure message. Includes error message into revert string on failure.\"},\"assertTrue(bool)\":{\"notice\":\"Asserts that the given condition is true.\"},\"assertTrue(bool,string)\":{\"notice\":\"Asserts that the given condition is true and includes error message into revert string on failure.\"},\"assume(bool)\":{\"notice\":\"If the condition is false, discard this run's fuzz inputs and generate new ones.\"},\"assumeNoRevert()\":{\"notice\":\"Discard this run's fuzz inputs and generate new ones if next call reverted.\"},\"breakpoint(string)\":{\"notice\":\"Writes a breakpoint to jump to in the debugger.\"},\"breakpoint(string,bool)\":{\"notice\":\"Writes a conditional breakpoint to jump to in the debugger.\"},\"broadcast()\":{\"notice\":\"Has the next call (at this call depth only) create transactions that can later be signed and sent onchain. Broadcasting address is determined by checking the following in order: 1. If `--sender` argument was provided, that address is used. 2. If exactly one signer (e.g. private key, hw wallet, keystore) is set when `forge broadcast` is invoked, that signer is used. 3. Otherwise, default foundry sender (1804c8AB1F12E6bbf3894d4083f33e07309d1f38) is used.\"},\"broadcast(address)\":{\"notice\":\"Has the next call (at this call depth only) create a transaction with the address provided as the sender that can later be signed and sent onchain.\"},\"broadcast(uint256)\":{\"notice\":\"Has the next call (at this call depth only) create a transaction with the private key provided as the sender that can later be signed and sent onchain.\"},\"broadcastRawTransaction(bytes)\":{\"notice\":\"Takes a signed transaction and broadcasts it to the network.\"},\"closeFile(string)\":{\"notice\":\"Closes file for reading, resetting the offset and allowing to read it from beginning with readLine. `path` is relative to the project root.\"},\"computeCreate2Address(bytes32,bytes32)\":{\"notice\":\"Compute the address of a contract created with CREATE2 using the default CREATE2 deployer.\"},\"computeCreate2Address(bytes32,bytes32,address)\":{\"notice\":\"Compute the address of a contract created with CREATE2 using the given CREATE2 deployer.\"},\"computeCreateAddress(address,uint256)\":{\"notice\":\"Compute the address a contract will be deployed at for a given deployer address and nonce.\"},\"copyFile(string,string)\":{\"notice\":\"Copies the contents of one file to another. This function will **overwrite** the contents of `to`. On success, the total number of bytes copied is returned and it is equal to the length of the `to` file as reported by `metadata`. Both `from` and `to` are relative to the project root.\"},\"copyStorage(address,address)\":{\"notice\":\"Utility cheatcode to copy storage of `from` contract to another `to` contract.\"},\"createDir(string,bool)\":{\"notice\":\"Creates a new, empty directory at the provided path. This cheatcode will revert in the following situations, but is not limited to just these cases: - User lacks permissions to modify `path`. - A parent of the given path doesn't exist and `recursive` is false. - `path` already exists and `recursive` is false. `path` is relative to the project root.\"},\"createWallet(string)\":{\"notice\":\"Derives a private key from the name, labels the account with that name, and returns the wallet.\"},\"createWallet(uint256)\":{\"notice\":\"Generates a wallet from the private key and returns the wallet.\"},\"createWallet(uint256,string)\":{\"notice\":\"Generates a wallet from the private key, labels the account with that name, and returns the wallet.\"},\"deployCode(string)\":{\"notice\":\"Deploys a contract from an artifact file. Takes in the relative path to the json file or the path to the artifact in the form of <path>:<contract>:<version> where <contract> and <version> parts are optional.\"},\"deployCode(string,bytes)\":{\"notice\":\"Deploys a contract from an artifact file. Takes in the relative path to the json file or the path to the artifact in the form of <path>:<contract>:<version> where <contract> and <version> parts are optional. Additionally accepts abi-encoded constructor arguments.\"},\"deriveKey(string,string,uint32)\":{\"notice\":\"Derive a private key from a provided mnenomic string (or mnenomic file path) at `{derivationPath}{index}`.\"},\"deriveKey(string,string,uint32,string)\":{\"notice\":\"Derive a private key from a provided mnenomic string (or mnenomic file path) in the specified language at `{derivationPath}{index}`.\"},\"deriveKey(string,uint32)\":{\"notice\":\"Derive a private key from a provided mnenomic string (or mnenomic file path) at the derivation path `m/44'/60'/0'/0/{index}`.\"},\"deriveKey(string,uint32,string)\":{\"notice\":\"Derive a private key from a provided mnenomic string (or mnenomic file path) in the specified language at the derivation path `m/44'/60'/0'/0/{index}`.\"},\"ensNamehash(string)\":{\"notice\":\"Returns ENS namehash for provided string.\"},\"envAddress(string)\":{\"notice\":\"Gets the environment variable `name` and parses it as `address`. Reverts if the variable was not found or could not be parsed.\"},\"envAddress(string,string)\":{\"notice\":\"Gets the environment variable `name` and parses it as an array of `address`, delimited by `delim`. Reverts if the variable was not found or could not be parsed.\"},\"envBool(string)\":{\"notice\":\"Gets the environment variable `name` and parses it as `bool`. Reverts if the variable was not found or could not be parsed.\"},\"envBool(string,string)\":{\"notice\":\"Gets the environment variable `name` and parses it as an array of `bool`, delimited by `delim`. Reverts if the variable was not found or could not be parsed.\"},\"envBytes(string)\":{\"notice\":\"Gets the environment variable `name` and parses it as `bytes`. Reverts if the variable was not found or could not be parsed.\"},\"envBytes(string,string)\":{\"notice\":\"Gets the environment variable `name` and parses it as an array of `bytes`, delimited by `delim`. Reverts if the variable was not found or could not be parsed.\"},\"envBytes32(string)\":{\"notice\":\"Gets the environment variable `name` and parses it as `bytes32`. Reverts if the variable was not found or could not be parsed.\"},\"envBytes32(string,string)\":{\"notice\":\"Gets the environment variable `name` and parses it as an array of `bytes32`, delimited by `delim`. Reverts if the variable was not found or could not be parsed.\"},\"envExists(string)\":{\"notice\":\"Gets the environment variable `name` and returns true if it exists, else returns false.\"},\"envInt(string)\":{\"notice\":\"Gets the environment variable `name` and parses it as `int256`. Reverts if the variable was not found or could not be parsed.\"},\"envInt(string,string)\":{\"notice\":\"Gets the environment variable `name` and parses it as an array of `int256`, delimited by `delim`. Reverts if the variable was not found or could not be parsed.\"},\"envOr(string,address)\":{\"notice\":\"Gets the environment variable `name` and parses it as `address`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found.\"},\"envOr(string,bool)\":{\"notice\":\"Gets the environment variable `name` and parses it as `bool`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found.\"},\"envOr(string,bytes)\":{\"notice\":\"Gets the environment variable `name` and parses it as `bytes`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found.\"},\"envOr(string,bytes32)\":{\"notice\":\"Gets the environment variable `name` and parses it as `bytes32`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found.\"},\"envOr(string,int256)\":{\"notice\":\"Gets the environment variable `name` and parses it as `int256`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found.\"},\"envOr(string,string)\":{\"notice\":\"Gets the environment variable `name` and parses it as `string`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found.\"},\"envOr(string,string,address[])\":{\"notice\":\"Gets the environment variable `name` and parses it as an array of `address`, delimited by `delim`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found.\"},\"envOr(string,string,bool[])\":{\"notice\":\"Gets the environment variable `name` and parses it as an array of `bool`, delimited by `delim`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found.\"},\"envOr(string,string,bytes32[])\":{\"notice\":\"Gets the environment variable `name` and parses it as an array of `bytes32`, delimited by `delim`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found.\"},\"envOr(string,string,bytes[])\":{\"notice\":\"Gets the environment variable `name` and parses it as an array of `bytes`, delimited by `delim`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found.\"},\"envOr(string,string,int256[])\":{\"notice\":\"Gets the environment variable `name` and parses it as an array of `int256`, delimited by `delim`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found.\"},\"envOr(string,string,string[])\":{\"notice\":\"Gets the environment variable `name` and parses it as an array of `string`, delimited by `delim`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found.\"},\"envOr(string,string,uint256[])\":{\"notice\":\"Gets the environment variable `name` and parses it as an array of `uint256`, delimited by `delim`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found.\"},\"envOr(string,uint256)\":{\"notice\":\"Gets the environment variable `name` and parses it as `uint256`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found.\"},\"envString(string)\":{\"notice\":\"Gets the environment variable `name` and parses it as `string`. Reverts if the variable was not found or could not be parsed.\"},\"envString(string,string)\":{\"notice\":\"Gets the environment variable `name` and parses it as an array of `string`, delimited by `delim`. Reverts if the variable was not found or could not be parsed.\"},\"envUint(string)\":{\"notice\":\"Gets the environment variable `name` and parses it as `uint256`. Reverts if the variable was not found or could not be parsed.\"},\"envUint(string,string)\":{\"notice\":\"Gets the environment variable `name` and parses it as an array of `uint256`, delimited by `delim`. Reverts if the variable was not found or could not be parsed.\"},\"eth_getLogs(uint256,uint256,address,bytes32[])\":{\"notice\":\"Gets all the logs according to specified filter.\"},\"exists(string)\":{\"notice\":\"Returns true if the given path points to an existing entity, else returns false.\"},\"ffi(string[])\":{\"notice\":\"Performs a foreign function call via the terminal.\"},\"fsMetadata(string)\":{\"notice\":\"Given a path, query the file system to get information about a file, directory, etc.\"},\"getArtifactPathByCode(bytes)\":{\"notice\":\"Gets the artifact path from code (aka. creation code).\"},\"getArtifactPathByDeployedCode(bytes)\":{\"notice\":\"Gets the artifact path from deployed code (aka. runtime code).\"},\"getBlobBaseFee()\":{\"notice\":\"Gets the current `block.blobbasefee`. You should use this instead of `block.blobbasefee` if you use `vm.blobBaseFee`, as `block.blobbasefee` is assumed to be constant across a transaction, and as a result will get optimized out by the compiler. See https://github.com/foundry-rs/foundry/issues/6180\"},\"getBlockNumber()\":{\"notice\":\"Gets the current `block.number`. You should use this instead of `block.number` if you use `vm.roll`, as `block.number` is assumed to be constant across a transaction, and as a result will get optimized out by the compiler. See https://github.com/foundry-rs/foundry/issues/6180\"},\"getBlockTimestamp()\":{\"notice\":\"Gets the current `block.timestamp`. You should use this instead of `block.timestamp` if you use `vm.warp`, as `block.timestamp` is assumed to be constant across a transaction, and as a result will get optimized out by the compiler. See https://github.com/foundry-rs/foundry/issues/6180\"},\"getCode(string)\":{\"notice\":\"Gets the creation bytecode from an artifact file. Takes in the relative path to the json file or the path to the artifact in the form of <path>:<contract>:<version> where <contract> and <version> parts are optional.\"},\"getDeployedCode(string)\":{\"notice\":\"Gets the deployed bytecode from an artifact file. Takes in the relative path to the json file or the path to the artifact in the form of <path>:<contract>:<version> where <contract> and <version> parts are optional.\"},\"getFoundryVersion()\":{\"notice\":\"Returns the Foundry version. Format: <cargo_version>+<git_sha>+<build_timestamp> Sample output: 0.2.0+faa94c384+202407110019 Note: Build timestamps may vary slightly across platforms due to separate CI jobs. For reliable version comparisons, use YYYYMMDD0000 format (e.g., >= 202407110000) to compare timestamps while ignoring minor time differences.\"},\"getLabel(address)\":{\"notice\":\"Gets the label for the specified address.\"},\"getMappingKeyAndParentOf(address,bytes32)\":{\"notice\":\"Gets the map key and parent of a mapping at a given slot, for a given address.\"},\"getMappingLength(address,bytes32)\":{\"notice\":\"Gets the number of elements in the mapping at the given slot, for a given address.\"},\"getMappingSlotAt(address,bytes32,uint256)\":{\"notice\":\"Gets the elements at index idx of the mapping at the given slot, for a given address. The index must be less than the length of the mapping (i.e. the number of keys in the mapping).\"},\"getNonce((address,uint256,uint256,uint256))\":{\"notice\":\"Get the nonce of a `Wallet`.\"},\"getNonce(address)\":{\"notice\":\"Gets the nonce of an account.\"},\"getRecordedLogs()\":{\"notice\":\"Gets all the recorded logs.\"},\"getScriptWallets()\":{\"notice\":\"Returns addresses of available unlocked wallets in the script environment.\"},\"getWallets()\":{\"notice\":\"Returns addresses of available unlocked wallets in the script environment.\"},\"indexOf(string,string)\":{\"notice\":\"Returns the index of the first occurrence of a `key` in an `input` string. Returns `NOT_FOUND` (i.e. `type(uint256).max`) if the `key` is not found. Returns 0 in case of an empty `key`.\"},\"isContext(uint8)\":{\"notice\":\"Returns true if `forge` command was executed in given context.\"},\"isDir(string)\":{\"notice\":\"Returns true if the path exists on disk and is pointing at a directory, else returns false.\"},\"isFile(string)\":{\"notice\":\"Returns true if the path exists on disk and is pointing at a regular file, else returns false.\"},\"keyExists(string,string)\":{\"notice\":\"Checks if `key` exists in a JSON object `keyExists` is being deprecated in favor of `keyExistsJson`. It will be removed in future versions.\"},\"keyExistsJson(string,string)\":{\"notice\":\"Checks if `key` exists in a JSON object.\"},\"keyExistsToml(string,string)\":{\"notice\":\"Checks if `key` exists in a TOML table.\"},\"label(address,string)\":{\"notice\":\"Labels an address in call traces.\"},\"lastCallGas()\":{\"notice\":\"Gets the gas used in the last call from the callee perspective.\"},\"load(address,bytes32)\":{\"notice\":\"Loads a storage slot from an address.\"},\"parseAddress(string)\":{\"notice\":\"Parses the given `string` into an `address`.\"},\"parseBool(string)\":{\"notice\":\"Parses the given `string` into a `bool`.\"},\"parseBytes(string)\":{\"notice\":\"Parses the given `string` into `bytes`.\"},\"parseBytes32(string)\":{\"notice\":\"Parses the given `string` into a `bytes32`.\"},\"parseInt(string)\":{\"notice\":\"Parses the given `string` into a `int256`.\"},\"parseJson(string)\":{\"notice\":\"ABI-encodes a JSON object.\"},\"parseJson(string,string)\":{\"notice\":\"ABI-encodes a JSON object at `key`.\"},\"parseJsonAddress(string,string)\":{\"notice\":\"Parses a string of JSON data at `key` and coerces it to `address`.\"},\"parseJsonAddressArray(string,string)\":{\"notice\":\"Parses a string of JSON data at `key` and coerces it to `address[]`.\"},\"parseJsonBool(string,string)\":{\"notice\":\"Parses a string of JSON data at `key` and coerces it to `bool`.\"},\"parseJsonBoolArray(string,string)\":{\"notice\":\"Parses a string of JSON data at `key` and coerces it to `bool[]`.\"},\"parseJsonBytes(string,string)\":{\"notice\":\"Parses a string of JSON data at `key` and coerces it to `bytes`.\"},\"parseJsonBytes32(string,string)\":{\"notice\":\"Parses a string of JSON data at `key` and coerces it to `bytes32`.\"},\"parseJsonBytes32Array(string,string)\":{\"notice\":\"Parses a string of JSON data at `key` and coerces it to `bytes32[]`.\"},\"parseJsonBytesArray(string,string)\":{\"notice\":\"Parses a string of JSON data at `key` and coerces it to `bytes[]`.\"},\"parseJsonInt(string,string)\":{\"notice\":\"Parses a string of JSON data at `key` and coerces it to `int256`.\"},\"parseJsonIntArray(string,string)\":{\"notice\":\"Parses a string of JSON data at `key` and coerces it to `int256[]`.\"},\"parseJsonKeys(string,string)\":{\"notice\":\"Returns an array of all the keys in a JSON object.\"},\"parseJsonString(string,string)\":{\"notice\":\"Parses a string of JSON data at `key` and coerces it to `string`.\"},\"parseJsonStringArray(string,string)\":{\"notice\":\"Parses a string of JSON data at `key` and coerces it to `string[]`.\"},\"parseJsonType(string,string)\":{\"notice\":\"Parses a string of JSON data and coerces it to type corresponding to `typeDescription`.\"},\"parseJsonType(string,string,string)\":{\"notice\":\"Parses a string of JSON data at `key` and coerces it to type corresponding to `typeDescription`.\"},\"parseJsonTypeArray(string,string,string)\":{\"notice\":\"Parses a string of JSON data at `key` and coerces it to type array corresponding to `typeDescription`.\"},\"parseJsonUint(string,string)\":{\"notice\":\"Parses a string of JSON data at `key` and coerces it to `uint256`.\"},\"parseJsonUintArray(string,string)\":{\"notice\":\"Parses a string of JSON data at `key` and coerces it to `uint256[]`.\"},\"parseToml(string)\":{\"notice\":\"ABI-encodes a TOML table.\"},\"parseToml(string,string)\":{\"notice\":\"ABI-encodes a TOML table at `key`.\"},\"parseTomlAddress(string,string)\":{\"notice\":\"Parses a string of TOML data at `key` and coerces it to `address`.\"},\"parseTomlAddressArray(string,string)\":{\"notice\":\"Parses a string of TOML data at `key` and coerces it to `address[]`.\"},\"parseTomlBool(string,string)\":{\"notice\":\"Parses a string of TOML data at `key` and coerces it to `bool`.\"},\"parseTomlBoolArray(string,string)\":{\"notice\":\"Parses a string of TOML data at `key` and coerces it to `bool[]`.\"},\"parseTomlBytes(string,string)\":{\"notice\":\"Parses a string of TOML data at `key` and coerces it to `bytes`.\"},\"parseTomlBytes32(string,string)\":{\"notice\":\"Parses a string of TOML data at `key` and coerces it to `bytes32`.\"},\"parseTomlBytes32Array(string,string)\":{\"notice\":\"Parses a string of TOML data at `key` and coerces it to `bytes32[]`.\"},\"parseTomlBytesArray(string,string)\":{\"notice\":\"Parses a string of TOML data at `key` and coerces it to `bytes[]`.\"},\"parseTomlInt(string,string)\":{\"notice\":\"Parses a string of TOML data at `key` and coerces it to `int256`.\"},\"parseTomlIntArray(string,string)\":{\"notice\":\"Parses a string of TOML data at `key` and coerces it to `int256[]`.\"},\"parseTomlKeys(string,string)\":{\"notice\":\"Returns an array of all the keys in a TOML table.\"},\"parseTomlString(string,string)\":{\"notice\":\"Parses a string of TOML data at `key` and coerces it to `string`.\"},\"parseTomlStringArray(string,string)\":{\"notice\":\"Parses a string of TOML data at `key` and coerces it to `string[]`.\"},\"parseTomlType(string,string)\":{\"notice\":\"Parses a string of TOML data and coerces it to type corresponding to `typeDescription`.\"},\"parseTomlType(string,string,string)\":{\"notice\":\"Parses a string of TOML data at `key` and coerces it to type corresponding to `typeDescription`.\"},\"parseTomlTypeArray(string,string,string)\":{\"notice\":\"Parses a string of TOML data at `key` and coerces it to type array corresponding to `typeDescription`.\"},\"parseTomlUint(string,string)\":{\"notice\":\"Parses a string of TOML data at `key` and coerces it to `uint256`.\"},\"parseTomlUintArray(string,string)\":{\"notice\":\"Parses a string of TOML data at `key` and coerces it to `uint256[]`.\"},\"parseUint(string)\":{\"notice\":\"Parses the given `string` into a `uint256`.\"},\"pauseGasMetering()\":{\"notice\":\"Pauses gas metering (i.e. gas usage is not counted). Noop if already paused.\"},\"pauseTracing()\":{\"notice\":\"Pauses collection of call traces. Useful in cases when you want to skip tracing of complex calls which are not useful for debugging.\"},\"projectRoot()\":{\"notice\":\"Get the path of the current project root.\"},\"prompt(string)\":{\"notice\":\"Prompts the user for a string value in the terminal.\"},\"promptAddress(string)\":{\"notice\":\"Prompts the user for an address in the terminal.\"},\"promptSecret(string)\":{\"notice\":\"Prompts the user for a hidden string value in the terminal.\"},\"promptSecretUint(string)\":{\"notice\":\"Prompts the user for hidden uint256 in the terminal (usually pk).\"},\"promptUint(string)\":{\"notice\":\"Prompts the user for uint256 in the terminal.\"},\"publicKeyP256(uint256)\":{\"notice\":\"Derives secp256r1 public key from the provided `privateKey`.\"},\"randomAddress()\":{\"notice\":\"Returns a random `address`.\"},\"randomBool()\":{\"notice\":\"Returns a random `bool`.\"},\"randomBytes(uint256)\":{\"notice\":\"Returns a random byte array value of the given length.\"},\"randomBytes4()\":{\"notice\":\"Returns a random fixed-size byte array of length 4.\"},\"randomBytes8()\":{\"notice\":\"Returns a random fixed-size byte array of length 8.\"},\"randomInt()\":{\"notice\":\"Returns a random `int256` value.\"},\"randomInt(uint256)\":{\"notice\":\"Returns a random `int256` value of given bits.\"},\"randomUint()\":{\"notice\":\"Returns a random uint256 value.\"},\"randomUint(uint256)\":{\"notice\":\"Returns a random `uint256` value of given bits.\"},\"randomUint(uint256,uint256)\":{\"notice\":\"Returns random uint256 value between the provided range (=min..=max).\"},\"readDir(string)\":{\"notice\":\"Reads the directory at the given path recursively, up to `maxDepth`. `maxDepth` defaults to 1, meaning only the direct children of the given directory will be returned. Follows symbolic links if `followLinks` is true.\"},\"readDir(string,uint64)\":{\"notice\":\"See `readDir(string)`.\"},\"readDir(string,uint64,bool)\":{\"notice\":\"See `readDir(string)`.\"},\"readFile(string)\":{\"notice\":\"Reads the entire content of file to string. `path` is relative to the project root.\"},\"readFileBinary(string)\":{\"notice\":\"Reads the entire content of file as binary. `path` is relative to the project root.\"},\"readLine(string)\":{\"notice\":\"Reads next line of file to string.\"},\"readLink(string)\":{\"notice\":\"Reads a symbolic link, returning the path that the link points to. This cheatcode will revert in the following situations, but is not limited to just these cases: - `path` is not a symbolic link. - `path` does not exist.\"},\"record()\":{\"notice\":\"Records all storage reads and writes.\"},\"recordLogs()\":{\"notice\":\"Record all the transaction logs.\"},\"rememberKey(uint256)\":{\"notice\":\"Adds a private key to the local forge wallet and returns the address.\"},\"rememberKeys(string,string,string,uint32)\":{\"notice\":\"Derive a set number of wallets from a mnemonic in the specified language at the derivation path `m/44'/60'/0'/0/{0..count}`. The respective private keys are saved to the local forge wallet for later use and their addresses are returned.\"},\"rememberKeys(string,string,uint32)\":{\"notice\":\"Derive a set number of wallets from a mnemonic at the derivation path `m/44'/60'/0'/0/{0..count}`. The respective private keys are saved to the local forge wallet for later use and their addresses are returned.\"},\"removeDir(string,bool)\":{\"notice\":\"Removes a directory at the provided path. This cheatcode will revert in the following situations, but is not limited to just these cases: - `path` doesn't exist. - `path` isn't a directory. - User lacks permissions to modify `path`. - The directory is not empty and `recursive` is false. `path` is relative to the project root.\"},\"removeFile(string)\":{\"notice\":\"Removes a file from the filesystem. This cheatcode will revert in the following situations, but is not limited to just these cases: - `path` points to a directory. - The file doesn't exist. - The user lacks permissions to remove the file. `path` is relative to the project root.\"},\"replace(string,string,string)\":{\"notice\":\"Replaces occurrences of `from` in the given `string` with `to`.\"},\"resetGasMetering()\":{\"notice\":\"Reset gas metering (i.e. gas usage is set to gas limit).\"},\"resumeGasMetering()\":{\"notice\":\"Resumes gas metering (i.e. gas usage is counted again). Noop if already on.\"},\"resumeTracing()\":{\"notice\":\"Unpauses collection of call traces.\"},\"rpc(string,string)\":{\"notice\":\"Performs an Ethereum JSON-RPC request to the current fork URL.\"},\"rpc(string,string,string)\":{\"notice\":\"Performs an Ethereum JSON-RPC request to the given endpoint.\"},\"rpcUrl(string)\":{\"notice\":\"Returns the RPC url for the given alias.\"},\"rpcUrlStructs()\":{\"notice\":\"Returns all rpc urls and their aliases as structs.\"},\"rpcUrls()\":{\"notice\":\"Returns all rpc urls and their aliases `[alias, url][]`.\"},\"serializeAddress(string,string,address)\":{\"notice\":\"See `serializeJson`.\"},\"serializeAddress(string,string,address[])\":{\"notice\":\"See `serializeJson`.\"},\"serializeBool(string,string,bool)\":{\"notice\":\"See `serializeJson`.\"},\"serializeBool(string,string,bool[])\":{\"notice\":\"See `serializeJson`.\"},\"serializeBytes(string,string,bytes)\":{\"notice\":\"See `serializeJson`.\"},\"serializeBytes(string,string,bytes[])\":{\"notice\":\"See `serializeJson`.\"},\"serializeBytes32(string,string,bytes32)\":{\"notice\":\"See `serializeJson`.\"},\"serializeBytes32(string,string,bytes32[])\":{\"notice\":\"See `serializeJson`.\"},\"serializeInt(string,string,int256)\":{\"notice\":\"See `serializeJson`.\"},\"serializeInt(string,string,int256[])\":{\"notice\":\"See `serializeJson`.\"},\"serializeJson(string,string)\":{\"notice\":\"Serializes a key and value to a JSON object stored in-memory that can be later written to a file. Returns the stringified version of the specific JSON file up to that moment.\"},\"serializeJsonType(string,bytes)\":{\"notice\":\"See `serializeJson`.\"},\"serializeJsonType(string,string,string,bytes)\":{\"notice\":\"See `serializeJson`.\"},\"serializeString(string,string,string)\":{\"notice\":\"See `serializeJson`.\"},\"serializeString(string,string,string[])\":{\"notice\":\"See `serializeJson`.\"},\"serializeUint(string,string,uint256)\":{\"notice\":\"See `serializeJson`.\"},\"serializeUint(string,string,uint256[])\":{\"notice\":\"See `serializeJson`.\"},\"serializeUintToHex(string,string,uint256)\":{\"notice\":\"See `serializeJson`.\"},\"setArbitraryStorage(address)\":{\"notice\":\"Utility cheatcode to set arbitrary storage for given target address.\"},\"setEnv(string,string)\":{\"notice\":\"Sets environment variables.\"},\"sign((address,uint256,uint256,uint256),bytes32)\":{\"notice\":\"Signs data with a `Wallet`.\"},\"sign(address,bytes32)\":{\"notice\":\"Signs `digest` with signer provided to script using the secp256k1 curve. Raises error if none of the signers passed into the script have provided address.\"},\"sign(bytes32)\":{\"notice\":\"Signs `digest` with signer provided to script using the secp256k1 curve. If `--sender` is provided, the signer with provided address is used, otherwise, if exactly one signer is provided to the script, that signer is used. Raises error if signer passed through `--sender` does not match any unlocked signers or if `--sender` is not provided and not exactly one signer is passed to the script.\"},\"sign(uint256,bytes32)\":{\"notice\":\"Signs `digest` with `privateKey` using the secp256k1 curve.\"},\"signCompact((address,uint256,uint256,uint256),bytes32)\":{\"notice\":\"Signs data with a `Wallet`. Returns a compact signature (`r`, `vs`) as per EIP-2098, where `vs` encodes both the signature's `s` value, and the recovery id `v` in a single bytes32. This format reduces the signature size from 65 to 64 bytes.\"},\"signCompact(address,bytes32)\":{\"notice\":\"Signs `digest` with signer provided to script using the secp256k1 curve. Returns a compact signature (`r`, `vs`) as per EIP-2098, where `vs` encodes both the signature's `s` value, and the recovery id `v` in a single bytes32. This format reduces the signature size from 65 to 64 bytes. Raises error if none of the signers passed into the script have provided address.\"},\"signCompact(bytes32)\":{\"notice\":\"Signs `digest` with signer provided to script using the secp256k1 curve. Returns a compact signature (`r`, `vs`) as per EIP-2098, where `vs` encodes both the signature's `s` value, and the recovery id `v` in a single bytes32. This format reduces the signature size from 65 to 64 bytes. If `--sender` is provided, the signer with provided address is used, otherwise, if exactly one signer is provided to the script, that signer is used. Raises error if signer passed through `--sender` does not match any unlocked signers or if `--sender` is not provided and not exactly one signer is passed to the script.\"},\"signCompact(uint256,bytes32)\":{\"notice\":\"Signs `digest` with `privateKey` using the secp256k1 curve. Returns a compact signature (`r`, `vs`) as per EIP-2098, where `vs` encodes both the signature's `s` value, and the recovery id `v` in a single bytes32. This format reduces the signature size from 65 to 64 bytes.\"},\"signP256(uint256,bytes32)\":{\"notice\":\"Signs `digest` with `privateKey` using the secp256r1 curve.\"},\"sleep(uint256)\":{\"notice\":\"Suspends execution of the main thread for `duration` milliseconds.\"},\"split(string,string)\":{\"notice\":\"Splits the given `string` into an array of strings divided by the `delimiter`.\"},\"startBroadcast()\":{\"notice\":\"Has all subsequent calls (at this call depth only) create transactions that can later be signed and sent onchain. Broadcasting address is determined by checking the following in order: 1. If `--sender` argument was provided, that address is used. 2. If exactly one signer (e.g. private key, hw wallet, keystore) is set when `forge broadcast` is invoked, that signer is used. 3. Otherwise, default foundry sender (1804c8AB1F12E6bbf3894d4083f33e07309d1f38) is used.\"},\"startBroadcast(address)\":{\"notice\":\"Has all subsequent calls (at this call depth only) create transactions with the address provided that can later be signed and sent onchain.\"},\"startBroadcast(uint256)\":{\"notice\":\"Has all subsequent calls (at this call depth only) create transactions with the private key provided that can later be signed and sent onchain.\"},\"startDebugTraceRecording()\":{\"notice\":\"Records the debug trace during the run.\"},\"startMappingRecording()\":{\"notice\":\"Starts recording all map SSTOREs for later retrieval.\"},\"startStateDiffRecording()\":{\"notice\":\"Record all account accesses as part of CREATE, CALL or SELFDESTRUCT opcodes in order, along with the context of the calls\"},\"stopAndReturnDebugTraceRecording()\":{\"notice\":\"Stop debug trace recording and returns the recorded debug trace.\"},\"stopAndReturnStateDiff()\":{\"notice\":\"Returns an ordered array of all account accesses from a `vm.startStateDiffRecording` session.\"},\"stopBroadcast()\":{\"notice\":\"Stops collecting onchain transactions.\"},\"stopMappingRecording()\":{\"notice\":\"Stops recording all map SSTOREs for later retrieval and clears the recorded data.\"},\"toBase64(bytes)\":{\"notice\":\"Encodes a `bytes` value to a base64 string.\"},\"toBase64(string)\":{\"notice\":\"Encodes a `string` value to a base64 string.\"},\"toBase64URL(bytes)\":{\"notice\":\"Encodes a `bytes` value to a base64url string.\"},\"toBase64URL(string)\":{\"notice\":\"Encodes a `string` value to a base64url string.\"},\"toLowercase(string)\":{\"notice\":\"Converts the given `string` value to Lowercase.\"},\"toString(address)\":{\"notice\":\"Converts the given value to a `string`.\"},\"toString(bool)\":{\"notice\":\"Converts the given value to a `string`.\"},\"toString(bytes)\":{\"notice\":\"Converts the given value to a `string`.\"},\"toString(bytes32)\":{\"notice\":\"Converts the given value to a `string`.\"},\"toString(int256)\":{\"notice\":\"Converts the given value to a `string`.\"},\"toString(uint256)\":{\"notice\":\"Converts the given value to a `string`.\"},\"toUppercase(string)\":{\"notice\":\"Converts the given `string` value to Uppercase.\"},\"trim(string)\":{\"notice\":\"Trims leading and trailing whitespace from the given `string` value.\"},\"tryFfi(string[])\":{\"notice\":\"Performs a foreign function call via terminal and returns the exit code, stdout, and stderr.\"},\"unixTime()\":{\"notice\":\"Returns the time since unix epoch in milliseconds.\"},\"writeFile(string,string)\":{\"notice\":\"Writes data to file, creating a file if it does not exist, and entirely replacing its contents if it does. `path` is relative to the project root.\"},\"writeFileBinary(string,bytes)\":{\"notice\":\"Writes binary data to a file, creating a file if it does not exist, and entirely replacing its contents if it does. `path` is relative to the project root.\"},\"writeJson(string,string)\":{\"notice\":\"Write a serialized JSON object to a file. If the file exists, it will be overwritten.\"},\"writeJson(string,string,string)\":{\"notice\":\"Write a serialized JSON object to an **existing** JSON file, replacing a value with key = <value_key.> This is useful to replace a specific value of a JSON file, without having to parse the entire thing.\"},\"writeLine(string,string)\":{\"notice\":\"Writes line to file, creating a file if it does not exist. `path` is relative to the project root.\"},\"writeToml(string,string)\":{\"notice\":\"Takes serialized JSON, converts to TOML and write a serialized TOML to a file.\"},\"writeToml(string,string,string)\":{\"notice\":\"Takes serialized JSON, converts to TOML and write a serialized TOML table to an **existing** TOML file, replacing a value with key = <value_key.> This is useful to replace a specific value of a TOML file, without having to parse the entire thing.\"}},\"notice\":\"The `VmSafe` interface does not allow manipulation of the EVM state or other actions that may result in Script simulations differing from on-chain execution. It is recommended to only use these cheats in scripts.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/Vm.sol\":\"VmSafe\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x51d822417db259b59eec2dfb7859285cef983a26b5d4fba20e1addabccc8734e\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://0e3b5abe0173ee6e479e4248eb4ee3a4374f7e08fb2848d93f5c2cf5e95214f1\",\"dweb:/ipfs/QmbUBD4NLFCuR6WHVx5unXH2uWHEhtADLcFrUwyev8iLrD\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "accesses", "outputs": [{"internalType": "bytes32[]", "name": "readSlots", "type": "bytes32[]"}, {"internalType": "bytes32[]", "name": "writeSlots", "type": "bytes32[]"}]}, {"inputs": [{"internalType": "uint256", "name": "privateKey", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "addr", "outputs": [{"internalType": "address", "name": "keyAddr", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "uint256", "name": "max<PERSON><PERSON><PERSON>", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertApproxEqAbs"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "uint256", "name": "max<PERSON><PERSON><PERSON>", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertApproxEqAbs"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "uint256", "name": "max<PERSON><PERSON><PERSON>", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertApproxEqAbs"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "uint256", "name": "max<PERSON><PERSON><PERSON>", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertApproxEqAbs"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "uint256", "name": "max<PERSON><PERSON><PERSON>", "type": "uint256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertApproxEqAbsDecimal"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "uint256", "name": "max<PERSON><PERSON><PERSON>", "type": "uint256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertApproxEqAbsDecimal"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "uint256", "name": "max<PERSON><PERSON><PERSON>", "type": "uint256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertApproxEqAbsDecimal"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "uint256", "name": "max<PERSON><PERSON><PERSON>", "type": "uint256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertApproxEqAbsDecimal"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "uint256", "name": "maxP<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertApproxEqRel"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "uint256", "name": "maxP<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertApproxEqRel"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "uint256", "name": "maxP<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertApproxEqRel"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "uint256", "name": "maxP<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertApproxEqRel"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "uint256", "name": "maxP<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertApproxEqRelDecimal"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "uint256", "name": "maxP<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertApproxEqRelDecimal"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "uint256", "name": "maxP<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertApproxEqRelDecimal"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "uint256", "name": "maxP<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertApproxEqRelDecimal"}, {"inputs": [{"internalType": "bytes32[]", "name": "left", "type": "bytes32[]"}, {"internalType": "bytes32[]", "name": "right", "type": "bytes32[]"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "int256[]", "name": "left", "type": "int256[]"}, {"internalType": "int256[]", "name": "right", "type": "int256[]"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "address", "name": "left", "type": "address"}, {"internalType": "address", "name": "right", "type": "address"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "string", "name": "left", "type": "string"}, {"internalType": "string", "name": "right", "type": "string"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "address[]", "name": "left", "type": "address[]"}, {"internalType": "address[]", "name": "right", "type": "address[]"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "address[]", "name": "left", "type": "address[]"}, {"internalType": "address[]", "name": "right", "type": "address[]"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "bool", "name": "left", "type": "bool"}, {"internalType": "bool", "name": "right", "type": "bool"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "address", "name": "left", "type": "address"}, {"internalType": "address", "name": "right", "type": "address"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "uint256[]", "name": "left", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "right", "type": "uint256[]"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "bool[]", "name": "left", "type": "bool[]"}, {"internalType": "bool[]", "name": "right", "type": "bool[]"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "int256[]", "name": "left", "type": "int256[]"}, {"internalType": "int256[]", "name": "right", "type": "int256[]"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "bytes32", "name": "left", "type": "bytes32"}, {"internalType": "bytes32", "name": "right", "type": "bytes32"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "uint256[]", "name": "left", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "right", "type": "uint256[]"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "bytes", "name": "left", "type": "bytes"}, {"internalType": "bytes", "name": "right", "type": "bytes"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "bytes32", "name": "left", "type": "bytes32"}, {"internalType": "bytes32", "name": "right", "type": "bytes32"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "string[]", "name": "left", "type": "string[]"}, {"internalType": "string[]", "name": "right", "type": "string[]"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "bytes32[]", "name": "left", "type": "bytes32[]"}, {"internalType": "bytes32[]", "name": "right", "type": "bytes32[]"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "bytes", "name": "left", "type": "bytes"}, {"internalType": "bytes", "name": "right", "type": "bytes"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "bool[]", "name": "left", "type": "bool[]"}, {"internalType": "bool[]", "name": "right", "type": "bool[]"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "bytes[]", "name": "left", "type": "bytes[]"}, {"internalType": "bytes[]", "name": "right", "type": "bytes[]"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "string[]", "name": "left", "type": "string[]"}, {"internalType": "string[]", "name": "right", "type": "string[]"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "string", "name": "left", "type": "string"}, {"internalType": "string", "name": "right", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "bytes[]", "name": "left", "type": "bytes[]"}, {"internalType": "bytes[]", "name": "right", "type": "bytes[]"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "bool", "name": "left", "type": "bool"}, {"internalType": "bool", "name": "right", "type": "bool"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}], "stateMutability": "pure", "type": "function", "name": "assertEq"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertEqDecimal"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertEqDecimal"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertEqDecimal"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertEqDecimal"}, {"inputs": [{"internalType": "bool", "name": "condition", "type": "bool"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertFalse"}, {"inputs": [{"internalType": "bool", "name": "condition", "type": "bool"}], "stateMutability": "pure", "type": "function", "name": "assertFalse"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}], "stateMutability": "pure", "type": "function", "name": "assertGe"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertGe"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertGe"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertGe"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertGeDecimal"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertGeDecimal"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertGeDecimal"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertGeDecimal"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}], "stateMutability": "pure", "type": "function", "name": "assertGt"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertGt"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertGt"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertGt"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertGtDecimal"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertGtDecimal"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertGtDecimal"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertGtDecimal"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertLe"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertLe"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}], "stateMutability": "pure", "type": "function", "name": "assertLe"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertLe"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertLeDecimal"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertLeDecimal"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertLeDecimal"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertLeDecimal"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}], "stateMutability": "pure", "type": "function", "name": "assertLt"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertLt"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertLt"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertLt"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertLtDecimal"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertLtDecimal"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertLtDecimal"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertLtDecimal"}, {"inputs": [{"internalType": "bytes32[]", "name": "left", "type": "bytes32[]"}, {"internalType": "bytes32[]", "name": "right", "type": "bytes32[]"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "int256[]", "name": "left", "type": "int256[]"}, {"internalType": "int256[]", "name": "right", "type": "int256[]"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "bool", "name": "left", "type": "bool"}, {"internalType": "bool", "name": "right", "type": "bool"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "bytes[]", "name": "left", "type": "bytes[]"}, {"internalType": "bytes[]", "name": "right", "type": "bytes[]"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "bool", "name": "left", "type": "bool"}, {"internalType": "bool", "name": "right", "type": "bool"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "bool[]", "name": "left", "type": "bool[]"}, {"internalType": "bool[]", "name": "right", "type": "bool[]"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "bytes", "name": "left", "type": "bytes"}, {"internalType": "bytes", "name": "right", "type": "bytes"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "address[]", "name": "left", "type": "address[]"}, {"internalType": "address[]", "name": "right", "type": "address[]"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "uint256[]", "name": "left", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "right", "type": "uint256[]"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "bool[]", "name": "left", "type": "bool[]"}, {"internalType": "bool[]", "name": "right", "type": "bool[]"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "string", "name": "left", "type": "string"}, {"internalType": "string", "name": "right", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "address[]", "name": "left", "type": "address[]"}, {"internalType": "address[]", "name": "right", "type": "address[]"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "string", "name": "left", "type": "string"}, {"internalType": "string", "name": "right", "type": "string"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "address", "name": "left", "type": "address"}, {"internalType": "address", "name": "right", "type": "address"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "bytes32", "name": "left", "type": "bytes32"}, {"internalType": "bytes32", "name": "right", "type": "bytes32"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "bytes", "name": "left", "type": "bytes"}, {"internalType": "bytes", "name": "right", "type": "bytes"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "uint256[]", "name": "left", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "right", "type": "uint256[]"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "address", "name": "left", "type": "address"}, {"internalType": "address", "name": "right", "type": "address"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "bytes32", "name": "left", "type": "bytes32"}, {"internalType": "bytes32", "name": "right", "type": "bytes32"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "string[]", "name": "left", "type": "string[]"}, {"internalType": "string[]", "name": "right", "type": "string[]"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "bytes32[]", "name": "left", "type": "bytes32[]"}, {"internalType": "bytes32[]", "name": "right", "type": "bytes32[]"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "string[]", "name": "left", "type": "string[]"}, {"internalType": "string[]", "name": "right", "type": "string[]"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "int256[]", "name": "left", "type": "int256[]"}, {"internalType": "int256[]", "name": "right", "type": "int256[]"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "bytes[]", "name": "left", "type": "bytes[]"}, {"internalType": "bytes[]", "name": "right", "type": "bytes[]"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}], "stateMutability": "pure", "type": "function", "name": "assertNotEq"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertNotEqDecimal"}, {"inputs": [{"internalType": "int256", "name": "left", "type": "int256"}, {"internalType": "int256", "name": "right", "type": "int256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertNotEqDecimal"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "assertNotEqDecimal"}, {"inputs": [{"internalType": "uint256", "name": "left", "type": "uint256"}, {"internalType": "uint256", "name": "right", "type": "uint256"}, {"internalType": "uint256", "name": "decimals", "type": "uint256"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertNotEqDecimal"}, {"inputs": [{"internalType": "bool", "name": "condition", "type": "bool"}], "stateMutability": "pure", "type": "function", "name": "assertTrue"}, {"inputs": [{"internalType": "bool", "name": "condition", "type": "bool"}, {"internalType": "string", "name": "error", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "assertTrue"}, {"inputs": [{"internalType": "bool", "name": "condition", "type": "bool"}], "stateMutability": "pure", "type": "function", "name": "assume"}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "assumeNoRevert"}, {"inputs": [{"internalType": "string", "name": "char", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "breakpoint"}, {"inputs": [{"internalType": "string", "name": "char", "type": "string"}, {"internalType": "bool", "name": "value", "type": "bool"}], "stateMutability": "pure", "type": "function", "name": "breakpoint"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "broadcast"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "broadcast"}, {"inputs": [{"internalType": "uint256", "name": "privateKey", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "broadcast"}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "broadcastRawTransaction"}, {"inputs": [{"internalType": "string", "name": "path", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "closeFile"}, {"inputs": [{"internalType": "bytes32", "name": "salt", "type": "bytes32"}, {"internalType": "bytes32", "name": "initCodeHash", "type": "bytes32"}], "stateMutability": "pure", "type": "function", "name": "computeCreate2Address", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "bytes32", "name": "salt", "type": "bytes32"}, {"internalType": "bytes32", "name": "initCodeHash", "type": "bytes32"}, {"internalType": "address", "name": "deployer", "type": "address"}], "stateMutability": "pure", "type": "function", "name": "computeCreate2Address", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "deployer", "type": "address"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "computeCreate<PERSON>ddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "string", "name": "from", "type": "string"}, {"internalType": "string", "name": "to", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "copyFile", "outputs": [{"internalType": "uint64", "name": "copied", "type": "uint64"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "copyStorage"}, {"inputs": [{"internalType": "string", "name": "path", "type": "string"}, {"internalType": "bool", "name": "recursive", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "createDir"}, {"inputs": [{"internalType": "string", "name": "walletLabel", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "createWallet", "outputs": [{"internalType": "struct VmSafe.Wallet", "name": "wallet", "type": "tuple", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "uint256", "name": "publicKeyX", "type": "uint256"}, {"internalType": "uint256", "name": "publicKeyY", "type": "uint256"}, {"internalType": "uint256", "name": "privateKey", "type": "uint256"}]}]}, {"inputs": [{"internalType": "uint256", "name": "privateKey", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "createWallet", "outputs": [{"internalType": "struct VmSafe.Wallet", "name": "wallet", "type": "tuple", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "uint256", "name": "publicKeyX", "type": "uint256"}, {"internalType": "uint256", "name": "publicKeyY", "type": "uint256"}, {"internalType": "uint256", "name": "privateKey", "type": "uint256"}]}]}, {"inputs": [{"internalType": "uint256", "name": "privateKey", "type": "uint256"}, {"internalType": "string", "name": "walletLabel", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "createWallet", "outputs": [{"internalType": "struct VmSafe.Wallet", "name": "wallet", "type": "tuple", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "uint256", "name": "publicKeyX", "type": "uint256"}, {"internalType": "uint256", "name": "publicKeyY", "type": "uint256"}, {"internalType": "uint256", "name": "privateKey", "type": "uint256"}]}]}, {"inputs": [{"internalType": "string", "name": "artifactPath", "type": "string"}, {"internalType": "bytes", "name": "constructorArgs", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "deployCode", "outputs": [{"internalType": "address", "name": "deployedAddress", "type": "address"}]}, {"inputs": [{"internalType": "string", "name": "artifactPath", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "deployCode", "outputs": [{"internalType": "address", "name": "deployedAddress", "type": "address"}]}, {"inputs": [{"internalType": "string", "name": "mnemonic", "type": "string"}, {"internalType": "string", "name": "derivationPath", "type": "string"}, {"internalType": "uint32", "name": "index", "type": "uint32"}, {"internalType": "string", "name": "language", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "privateKey", "type": "uint256"}]}, {"inputs": [{"internalType": "string", "name": "mnemonic", "type": "string"}, {"internalType": "uint32", "name": "index", "type": "uint32"}, {"internalType": "string", "name": "language", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "privateKey", "type": "uint256"}]}, {"inputs": [{"internalType": "string", "name": "mnemonic", "type": "string"}, {"internalType": "uint32", "name": "index", "type": "uint32"}], "stateMutability": "pure", "type": "function", "name": "<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "privateKey", "type": "uint256"}]}, {"inputs": [{"internalType": "string", "name": "mnemonic", "type": "string"}, {"internalType": "string", "name": "derivationPath", "type": "string"}, {"internalType": "uint32", "name": "index", "type": "uint32"}], "stateMutability": "pure", "type": "function", "name": "<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "privateKey", "type": "uint256"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "ens<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}], "stateMutability": "view", "type": "function", "name": "envAddress", "outputs": [{"internalType": "address", "name": "value", "type": "address"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "delim", "type": "string"}], "stateMutability": "view", "type": "function", "name": "envAddress", "outputs": [{"internalType": "address[]", "name": "value", "type": "address[]"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}], "stateMutability": "view", "type": "function", "name": "envBool", "outputs": [{"internalType": "bool", "name": "value", "type": "bool"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "delim", "type": "string"}], "stateMutability": "view", "type": "function", "name": "envBool", "outputs": [{"internalType": "bool[]", "name": "value", "type": "bool[]"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}], "stateMutability": "view", "type": "function", "name": "envBytes", "outputs": [{"internalType": "bytes", "name": "value", "type": "bytes"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "delim", "type": "string"}], "stateMutability": "view", "type": "function", "name": "envBytes", "outputs": [{"internalType": "bytes[]", "name": "value", "type": "bytes[]"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "delim", "type": "string"}], "stateMutability": "view", "type": "function", "name": "envBytes32", "outputs": [{"internalType": "bytes32[]", "name": "value", "type": "bytes32[]"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}], "stateMutability": "view", "type": "function", "name": "envBytes32", "outputs": [{"internalType": "bytes32", "name": "value", "type": "bytes32"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}], "stateMutability": "view", "type": "function", "name": "envExists", "outputs": [{"internalType": "bool", "name": "result", "type": "bool"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "delim", "type": "string"}], "stateMutability": "view", "type": "function", "name": "envInt", "outputs": [{"internalType": "int256[]", "name": "value", "type": "int256[]"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}], "stateMutability": "view", "type": "function", "name": "envInt", "outputs": [{"internalType": "int256", "name": "value", "type": "int256"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "delim", "type": "string"}, {"internalType": "bytes32[]", "name": "defaultValue", "type": "bytes32[]"}], "stateMutability": "view", "type": "function", "name": "envOr", "outputs": [{"internalType": "bytes32[]", "name": "value", "type": "bytes32[]"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "delim", "type": "string"}, {"internalType": "int256[]", "name": "defaultValue", "type": "int256[]"}], "stateMutability": "view", "type": "function", "name": "envOr", "outputs": [{"internalType": "int256[]", "name": "value", "type": "int256[]"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "bool", "name": "defaultValue", "type": "bool"}], "stateMutability": "view", "type": "function", "name": "envOr", "outputs": [{"internalType": "bool", "name": "value", "type": "bool"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "address", "name": "defaultValue", "type": "address"}], "stateMutability": "view", "type": "function", "name": "envOr", "outputs": [{"internalType": "address", "name": "value", "type": "address"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "defaultValue", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "envOr", "outputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "delim", "type": "string"}, {"internalType": "bytes[]", "name": "defaultValue", "type": "bytes[]"}], "stateMutability": "view", "type": "function", "name": "envOr", "outputs": [{"internalType": "bytes[]", "name": "value", "type": "bytes[]"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "delim", "type": "string"}, {"internalType": "uint256[]", "name": "defaultValue", "type": "uint256[]"}], "stateMutability": "view", "type": "function", "name": "envOr", "outputs": [{"internalType": "uint256[]", "name": "value", "type": "uint256[]"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "delim", "type": "string"}, {"internalType": "string[]", "name": "defaultValue", "type": "string[]"}], "stateMutability": "view", "type": "function", "name": "envOr", "outputs": [{"internalType": "string[]", "name": "value", "type": "string[]"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "bytes", "name": "defaultValue", "type": "bytes"}], "stateMutability": "view", "type": "function", "name": "envOr", "outputs": [{"internalType": "bytes", "name": "value", "type": "bytes"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "bytes32", "name": "defaultValue", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "envOr", "outputs": [{"internalType": "bytes32", "name": "value", "type": "bytes32"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "int256", "name": "defaultValue", "type": "int256"}], "stateMutability": "view", "type": "function", "name": "envOr", "outputs": [{"internalType": "int256", "name": "value", "type": "int256"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "delim", "type": "string"}, {"internalType": "address[]", "name": "defaultValue", "type": "address[]"}], "stateMutability": "view", "type": "function", "name": "envOr", "outputs": [{"internalType": "address[]", "name": "value", "type": "address[]"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "defaultValue", "type": "string"}], "stateMutability": "view", "type": "function", "name": "envOr", "outputs": [{"internalType": "string", "name": "value", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "delim", "type": "string"}, {"internalType": "bool[]", "name": "defaultValue", "type": "bool[]"}], "stateMutability": "view", "type": "function", "name": "envOr", "outputs": [{"internalType": "bool[]", "name": "value", "type": "bool[]"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "delim", "type": "string"}], "stateMutability": "view", "type": "function", "name": "envString", "outputs": [{"internalType": "string[]", "name": "value", "type": "string[]"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}], "stateMutability": "view", "type": "function", "name": "envString", "outputs": [{"internalType": "string", "name": "value", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}], "stateMutability": "view", "type": "function", "name": "envUint", "outputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "delim", "type": "string"}], "stateMutability": "view", "type": "function", "name": "envUint", "outputs": [{"internalType": "uint256[]", "name": "value", "type": "uint256[]"}]}, {"inputs": [{"internalType": "uint256", "name": "fromBlock", "type": "uint256"}, {"internalType": "uint256", "name": "toBlock", "type": "uint256"}, {"internalType": "address", "name": "target", "type": "address"}, {"internalType": "bytes32[]", "name": "topics", "type": "bytes32[]"}], "stateMutability": "nonpayable", "type": "function", "name": "eth_getLogs", "outputs": [{"internalType": "struct VmSafe.EthGetLogs[]", "name": "logs", "type": "tuple[]", "components": [{"internalType": "address", "name": "emitter", "type": "address"}, {"internalType": "bytes32[]", "name": "topics", "type": "bytes32[]"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "bytes32", "name": "blockHash", "type": "bytes32"}, {"internalType": "uint64", "name": "blockNumber", "type": "uint64"}, {"internalType": "bytes32", "name": "transactionHash", "type": "bytes32"}, {"internalType": "uint64", "name": "transactionIndex", "type": "uint64"}, {"internalType": "uint256", "name": "logIndex", "type": "uint256"}, {"internalType": "bool", "name": "removed", "type": "bool"}]}]}, {"inputs": [{"internalType": "string", "name": "path", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "exists", "outputs": [{"internalType": "bool", "name": "result", "type": "bool"}]}, {"inputs": [{"internalType": "string[]", "name": "commandInput", "type": "string[]"}], "stateMutability": "nonpayable", "type": "function", "name": "ffi", "outputs": [{"internalType": "bytes", "name": "result", "type": "bytes"}]}, {"inputs": [{"internalType": "string", "name": "path", "type": "string"}], "stateMutability": "view", "type": "function", "name": "fsMetadata", "outputs": [{"internalType": "struct VmSafe.FsMetadata", "name": "metadata", "type": "tuple", "components": [{"internalType": "bool", "name": "isDir", "type": "bool"}, {"internalType": "bool", "name": "isSymlink", "type": "bool"}, {"internalType": "uint256", "name": "length", "type": "uint256"}, {"internalType": "bool", "name": "readOnly", "type": "bool"}, {"internalType": "uint256", "name": "modified", "type": "uint256"}, {"internalType": "uint256", "name": "accessed", "type": "uint256"}, {"internalType": "uint256", "name": "created", "type": "uint256"}]}]}, {"inputs": [{"internalType": "bytes", "name": "code", "type": "bytes"}], "stateMutability": "view", "type": "function", "name": "getArtifactPathByCode", "outputs": [{"internalType": "string", "name": "path", "type": "string"}]}, {"inputs": [{"internalType": "bytes", "name": "deployedCode", "type": "bytes"}], "stateMutability": "view", "type": "function", "name": "getArtifactPathByDeployedCode", "outputs": [{"internalType": "string", "name": "path", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getBlobBaseFee", "outputs": [{"internalType": "uint256", "name": "blobBaseFee", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getBlockNumber", "outputs": [{"internalType": "uint256", "name": "height", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getBlockTimestamp", "outputs": [{"internalType": "uint256", "name": "timestamp", "type": "uint256"}]}, {"inputs": [{"internalType": "string", "name": "artifactPath", "type": "string"}], "stateMutability": "view", "type": "function", "name": "getCode", "outputs": [{"internalType": "bytes", "name": "creationBytecode", "type": "bytes"}]}, {"inputs": [{"internalType": "string", "name": "artifactPath", "type": "string"}], "stateMutability": "view", "type": "function", "name": "getDeployedCode", "outputs": [{"internalType": "bytes", "name": "runtimeBytecode", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getFoundryVersion", "outputs": [{"internalType": "string", "name": "version", "type": "string"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "get<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "string", "name": "current<PERSON><PERSON><PERSON>", "type": "string"}]}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "bytes32", "name": "elementSlot", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "getMappingKeyAndParentOf", "outputs": [{"internalType": "bool", "name": "found", "type": "bool"}, {"internalType": "bytes32", "name": "key", "type": "bytes32"}, {"internalType": "bytes32", "name": "parent", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "bytes32", "name": "mappingSlot", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "getMapping<PERSON>ength", "outputs": [{"internalType": "uint256", "name": "length", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "bytes32", "name": "mappingSlot", "type": "bytes32"}, {"internalType": "uint256", "name": "idx", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "getMappingSlotAt", "outputs": [{"internalType": "bytes32", "name": "value", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getNonce", "outputs": [{"internalType": "uint64", "name": "nonce", "type": "uint64"}]}, {"inputs": [{"internalType": "struct VmSafe.Wallet", "name": "wallet", "type": "tuple", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "uint256", "name": "publicKeyX", "type": "uint256"}, {"internalType": "uint256", "name": "publicKeyY", "type": "uint256"}, {"internalType": "uint256", "name": "privateKey", "type": "uint256"}]}], "stateMutability": "nonpayable", "type": "function", "name": "getNonce", "outputs": [{"internalType": "uint64", "name": "nonce", "type": "uint64"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "getRecordedLogs", "outputs": [{"internalType": "struct VmSafe.Log[]", "name": "logs", "type": "tuple[]", "components": [{"internalType": "bytes32[]", "name": "topics", "type": "bytes32[]"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "address", "name": "emitter", "type": "address"}]}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "getScriptWallets", "outputs": [{"internalType": "address[]", "name": "wallets", "type": "address[]"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "getWallets", "outputs": [{"internalType": "address[]", "name": "wallets", "type": "address[]"}]}, {"inputs": [{"internalType": "string", "name": "input", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "indexOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "enum VmSafe.ForgeContext", "name": "context", "type": "uint8"}], "stateMutability": "view", "type": "function", "name": "isContext", "outputs": [{"internalType": "bool", "name": "result", "type": "bool"}]}, {"inputs": [{"internalType": "string", "name": "path", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "isDir", "outputs": [{"internalType": "bool", "name": "result", "type": "bool"}]}, {"inputs": [{"internalType": "string", "name": "path", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "isFile", "outputs": [{"internalType": "bool", "name": "result", "type": "bool"}]}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "view", "type": "function", "name": "keyExists", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "view", "type": "function", "name": "keyExists<PERSON>son", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "string", "name": "toml", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "view", "type": "function", "name": "keyExistsToml", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "string", "name": "new<PERSON>abel", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "label"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "lastCallGas", "outputs": [{"internalType": "struct VmSafe.Gas", "name": "gas", "type": "tuple", "components": [{"internalType": "uint64", "name": "gasLimit", "type": "uint64"}, {"internalType": "uint64", "name": "gasTotalUsed", "type": "uint64"}, {"internalType": "uint64", "name": "gasMemoryUsed", "type": "uint64"}, {"internalType": "int64", "name": "gasRefunded", "type": "int64"}, {"internalType": "uint64", "name": "gasRemaining", "type": "uint64"}]}]}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "bytes32", "name": "slot", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "load", "outputs": [{"internalType": "bytes32", "name": "data", "type": "bytes32"}]}, {"inputs": [{"internalType": "string", "name": "stringifiedValue", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseAddress", "outputs": [{"internalType": "address", "name": "parsedValue", "type": "address"}]}, {"inputs": [{"internalType": "string", "name": "stringifiedValue", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseBool", "outputs": [{"internalType": "bool", "name": "parsedValue", "type": "bool"}]}, {"inputs": [{"internalType": "string", "name": "stringifiedValue", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseBytes", "outputs": [{"internalType": "bytes", "name": "parsedValue", "type": "bytes"}]}, {"inputs": [{"internalType": "string", "name": "stringifiedValue", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseBytes32", "outputs": [{"internalType": "bytes32", "name": "parsedValue", "type": "bytes32"}]}, {"inputs": [{"internalType": "string", "name": "stringifiedValue", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseInt", "outputs": [{"internalType": "int256", "name": "parsedValue", "type": "int256"}]}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseJson", "outputs": [{"internalType": "bytes", "name": "abiEncodedData", "type": "bytes"}]}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseJson", "outputs": [{"internalType": "bytes", "name": "abiEncodedData", "type": "bytes"}]}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseJson<PERSON>dd<PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseJsonAddressArray", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}]}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseJsonBool", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseJsonBoolArray", "outputs": [{"internalType": "bool[]", "name": "", "type": "bool[]"}]}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseJsonBytes", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseJsonBytes32", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseJsonBytes32Array", "outputs": [{"internalType": "bytes32[]", "name": "", "type": "bytes32[]"}]}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseJsonBytesArray", "outputs": [{"internalType": "bytes[]", "name": "", "type": "bytes[]"}]}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseJsonInt", "outputs": [{"internalType": "int256", "name": "", "type": "int256"}]}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseJsonIntArray", "outputs": [{"internalType": "int256[]", "name": "", "type": "int256[]"}]}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseJsonKeys", "outputs": [{"internalType": "string[]", "name": "keys", "type": "string[]"}]}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseJsonString", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseJsonStringArray", "outputs": [{"internalType": "string[]", "name": "", "type": "string[]"}]}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "typeDescription", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseJsonType", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}, {"internalType": "string", "name": "typeDescription", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseJsonType", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}, {"internalType": "string", "name": "typeDescription", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseJsonTypeArray", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseJsonUint", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseJsonUintArray", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}]}, {"inputs": [{"internalType": "string", "name": "toml", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseToml", "outputs": [{"internalType": "bytes", "name": "abiEncodedData", "type": "bytes"}]}, {"inputs": [{"internalType": "string", "name": "toml", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseToml", "outputs": [{"internalType": "bytes", "name": "abiEncodedData", "type": "bytes"}]}, {"inputs": [{"internalType": "string", "name": "toml", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseTomlAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "string", "name": "toml", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseTomlAddressArray", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}]}, {"inputs": [{"internalType": "string", "name": "toml", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseTomlBool", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "string", "name": "toml", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseTomlBoolArray", "outputs": [{"internalType": "bool[]", "name": "", "type": "bool[]"}]}, {"inputs": [{"internalType": "string", "name": "toml", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseTomlBytes", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [{"internalType": "string", "name": "toml", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseTomlBytes32", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "string", "name": "toml", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseTomlBytes32Array", "outputs": [{"internalType": "bytes32[]", "name": "", "type": "bytes32[]"}]}, {"inputs": [{"internalType": "string", "name": "toml", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseTomlBytesArray", "outputs": [{"internalType": "bytes[]", "name": "", "type": "bytes[]"}]}, {"inputs": [{"internalType": "string", "name": "toml", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseTomlInt", "outputs": [{"internalType": "int256", "name": "", "type": "int256"}]}, {"inputs": [{"internalType": "string", "name": "toml", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseTomlIntArray", "outputs": [{"internalType": "int256[]", "name": "", "type": "int256[]"}]}, {"inputs": [{"internalType": "string", "name": "toml", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseTomlKeys", "outputs": [{"internalType": "string[]", "name": "keys", "type": "string[]"}]}, {"inputs": [{"internalType": "string", "name": "toml", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseTomlString", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "toml", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseTomlStringArray", "outputs": [{"internalType": "string[]", "name": "", "type": "string[]"}]}, {"inputs": [{"internalType": "string", "name": "toml", "type": "string"}, {"internalType": "string", "name": "typeDescription", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseTomlType", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [{"internalType": "string", "name": "toml", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}, {"internalType": "string", "name": "typeDescription", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseTomlType", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [{"internalType": "string", "name": "toml", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}, {"internalType": "string", "name": "typeDescription", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseTomlTypeArray", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [{"internalType": "string", "name": "toml", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseTomlUint", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "string", "name": "toml", "type": "string"}, {"internalType": "string", "name": "key", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseTomlUintArray", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}]}, {"inputs": [{"internalType": "string", "name": "stringifiedValue", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "parseUint", "outputs": [{"internalType": "uint256", "name": "parsedValue", "type": "uint256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "pauseGasMetering"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "pauseTracing"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "projectRoot", "outputs": [{"internalType": "string", "name": "path", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "promptText", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "prompt", "outputs": [{"internalType": "string", "name": "input", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "promptText", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "promptAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "string", "name": "promptText", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "promptSecret", "outputs": [{"internalType": "string", "name": "input", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "promptText", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "promptSecretUint", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "string", "name": "promptText", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "promptUint", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "privateKey", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "publicKeyP256", "outputs": [{"internalType": "uint256", "name": "publicKeyX", "type": "uint256"}, {"internalType": "uint256", "name": "publicKeyY", "type": "uint256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "random<PERSON>ddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "randomBool", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "len", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "randomBytes", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "randomBytes4", "outputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "randomBytes8", "outputs": [{"internalType": "bytes8", "name": "", "type": "bytes8"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "randomInt", "outputs": [{"internalType": "int256", "name": "", "type": "int256"}]}, {"inputs": [{"internalType": "uint256", "name": "bits", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "randomInt", "outputs": [{"internalType": "int256", "name": "", "type": "int256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "randomUint", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "bits", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "randomUint", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "min", "type": "uint256"}, {"internalType": "uint256", "name": "max", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "randomUint", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "string", "name": "path", "type": "string"}, {"internalType": "uint64", "name": "max<PERSON><PERSON><PERSON>", "type": "uint64"}], "stateMutability": "view", "type": "function", "name": "readDir", "outputs": [{"internalType": "struct VmSafe.DirEntry[]", "name": "entries", "type": "tuple[]", "components": [{"internalType": "string", "name": "errorMessage", "type": "string"}, {"internalType": "string", "name": "path", "type": "string"}, {"internalType": "uint64", "name": "depth", "type": "uint64"}, {"internalType": "bool", "name": "isDir", "type": "bool"}, {"internalType": "bool", "name": "isSymlink", "type": "bool"}]}]}, {"inputs": [{"internalType": "string", "name": "path", "type": "string"}, {"internalType": "uint64", "name": "max<PERSON><PERSON><PERSON>", "type": "uint64"}, {"internalType": "bool", "name": "followLinks", "type": "bool"}], "stateMutability": "view", "type": "function", "name": "readDir", "outputs": [{"internalType": "struct VmSafe.DirEntry[]", "name": "entries", "type": "tuple[]", "components": [{"internalType": "string", "name": "errorMessage", "type": "string"}, {"internalType": "string", "name": "path", "type": "string"}, {"internalType": "uint64", "name": "depth", "type": "uint64"}, {"internalType": "bool", "name": "isDir", "type": "bool"}, {"internalType": "bool", "name": "isSymlink", "type": "bool"}]}]}, {"inputs": [{"internalType": "string", "name": "path", "type": "string"}], "stateMutability": "view", "type": "function", "name": "readDir", "outputs": [{"internalType": "struct VmSafe.DirEntry[]", "name": "entries", "type": "tuple[]", "components": [{"internalType": "string", "name": "errorMessage", "type": "string"}, {"internalType": "string", "name": "path", "type": "string"}, {"internalType": "uint64", "name": "depth", "type": "uint64"}, {"internalType": "bool", "name": "isDir", "type": "bool"}, {"internalType": "bool", "name": "isSymlink", "type": "bool"}]}]}, {"inputs": [{"internalType": "string", "name": "path", "type": "string"}], "stateMutability": "view", "type": "function", "name": "readFile", "outputs": [{"internalType": "string", "name": "data", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "path", "type": "string"}], "stateMutability": "view", "type": "function", "name": "readFileBinary", "outputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}]}, {"inputs": [{"internalType": "string", "name": "path", "type": "string"}], "stateMutability": "view", "type": "function", "name": "readLine", "outputs": [{"internalType": "string", "name": "line", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "linkPath", "type": "string"}], "stateMutability": "view", "type": "function", "name": "readLink", "outputs": [{"internalType": "string", "name": "targetPath", "type": "string"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "record"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "recordLogs"}, {"inputs": [{"internalType": "uint256", "name": "privateKey", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "keyAddr", "type": "address"}]}, {"inputs": [{"internalType": "string", "name": "mnemonic", "type": "string"}, {"internalType": "string", "name": "derivationPath", "type": "string"}, {"internalType": "uint32", "name": "count", "type": "uint32"}], "stateMutability": "nonpayable", "type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address[]", "name": "keyAddrs", "type": "address[]"}]}, {"inputs": [{"internalType": "string", "name": "mnemonic", "type": "string"}, {"internalType": "string", "name": "derivationPath", "type": "string"}, {"internalType": "string", "name": "language", "type": "string"}, {"internalType": "uint32", "name": "count", "type": "uint32"}], "stateMutability": "nonpayable", "type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address[]", "name": "keyAddrs", "type": "address[]"}]}, {"inputs": [{"internalType": "string", "name": "path", "type": "string"}, {"internalType": "bool", "name": "recursive", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "removeDir"}, {"inputs": [{"internalType": "string", "name": "path", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "removeFile"}, {"inputs": [{"internalType": "string", "name": "input", "type": "string"}, {"internalType": "string", "name": "from", "type": "string"}, {"internalType": "string", "name": "to", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "replace", "outputs": [{"internalType": "string", "name": "output", "type": "string"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "resetGasMetering"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "resumeGasMetering"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "resumeTracing"}, {"inputs": [{"internalType": "string", "name": "url<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string"}, {"internalType": "string", "name": "method", "type": "string"}, {"internalType": "string", "name": "params", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "rpc", "outputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}]}, {"inputs": [{"internalType": "string", "name": "method", "type": "string"}, {"internalType": "string", "name": "params", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "rpc", "outputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}]}, {"inputs": [{"internalType": "string", "name": "rpcAlias", "type": "string"}], "stateMutability": "view", "type": "function", "name": "rpcUrl", "outputs": [{"internalType": "string", "name": "json", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "rpcUrlStructs", "outputs": [{"internalType": "struct VmSafe.Rpc[]", "name": "urls", "type": "tuple[]", "components": [{"internalType": "string", "name": "key", "type": "string"}, {"internalType": "string", "name": "url", "type": "string"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "rpcUrls", "outputs": [{"internalType": "string[2][]", "name": "urls", "type": "string[2][]"}]}, {"inputs": [{"internalType": "string", "name": "object<PERSON>ey", "type": "string"}, {"internalType": "string", "name": "valueKey", "type": "string"}, {"internalType": "address[]", "name": "values", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "serializeAddress", "outputs": [{"internalType": "string", "name": "json", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "object<PERSON>ey", "type": "string"}, {"internalType": "string", "name": "valueKey", "type": "string"}, {"internalType": "address", "name": "value", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "serializeAddress", "outputs": [{"internalType": "string", "name": "json", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "object<PERSON>ey", "type": "string"}, {"internalType": "string", "name": "valueKey", "type": "string"}, {"internalType": "bool[]", "name": "values", "type": "bool[]"}], "stateMutability": "nonpayable", "type": "function", "name": "serializeBool", "outputs": [{"internalType": "string", "name": "json", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "object<PERSON>ey", "type": "string"}, {"internalType": "string", "name": "valueKey", "type": "string"}, {"internalType": "bool", "name": "value", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "serializeBool", "outputs": [{"internalType": "string", "name": "json", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "object<PERSON>ey", "type": "string"}, {"internalType": "string", "name": "valueKey", "type": "string"}, {"internalType": "bytes[]", "name": "values", "type": "bytes[]"}], "stateMutability": "nonpayable", "type": "function", "name": "serializeBytes", "outputs": [{"internalType": "string", "name": "json", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "object<PERSON>ey", "type": "string"}, {"internalType": "string", "name": "valueKey", "type": "string"}, {"internalType": "bytes", "name": "value", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "serializeBytes", "outputs": [{"internalType": "string", "name": "json", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "object<PERSON>ey", "type": "string"}, {"internalType": "string", "name": "valueKey", "type": "string"}, {"internalType": "bytes32[]", "name": "values", "type": "bytes32[]"}], "stateMutability": "nonpayable", "type": "function", "name": "serializeBytes32", "outputs": [{"internalType": "string", "name": "json", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "object<PERSON>ey", "type": "string"}, {"internalType": "string", "name": "valueKey", "type": "string"}, {"internalType": "bytes32", "name": "value", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "serializeBytes32", "outputs": [{"internalType": "string", "name": "json", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "object<PERSON>ey", "type": "string"}, {"internalType": "string", "name": "valueKey", "type": "string"}, {"internalType": "int256", "name": "value", "type": "int256"}], "stateMutability": "nonpayable", "type": "function", "name": "serializeInt", "outputs": [{"internalType": "string", "name": "json", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "object<PERSON>ey", "type": "string"}, {"internalType": "string", "name": "valueKey", "type": "string"}, {"internalType": "int256[]", "name": "values", "type": "int256[]"}], "stateMutability": "nonpayable", "type": "function", "name": "serializeInt", "outputs": [{"internalType": "string", "name": "json", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "object<PERSON>ey", "type": "string"}, {"internalType": "string", "name": "value", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "serializeJ<PERSON>", "outputs": [{"internalType": "string", "name": "json", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "typeDescription", "type": "string"}, {"internalType": "bytes", "name": "value", "type": "bytes"}], "stateMutability": "pure", "type": "function", "name": "serializeJsonType", "outputs": [{"internalType": "string", "name": "json", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "object<PERSON>ey", "type": "string"}, {"internalType": "string", "name": "valueKey", "type": "string"}, {"internalType": "string", "name": "typeDescription", "type": "string"}, {"internalType": "bytes", "name": "value", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "serializeJsonType", "outputs": [{"internalType": "string", "name": "json", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "object<PERSON>ey", "type": "string"}, {"internalType": "string", "name": "valueKey", "type": "string"}, {"internalType": "string[]", "name": "values", "type": "string[]"}], "stateMutability": "nonpayable", "type": "function", "name": "serializeString", "outputs": [{"internalType": "string", "name": "json", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "object<PERSON>ey", "type": "string"}, {"internalType": "string", "name": "valueKey", "type": "string"}, {"internalType": "string", "name": "value", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "serializeString", "outputs": [{"internalType": "string", "name": "json", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "object<PERSON>ey", "type": "string"}, {"internalType": "string", "name": "valueKey", "type": "string"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "serializeUint", "outputs": [{"internalType": "string", "name": "json", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "object<PERSON>ey", "type": "string"}, {"internalType": "string", "name": "valueKey", "type": "string"}, {"internalType": "uint256[]", "name": "values", "type": "uint256[]"}], "stateMutability": "nonpayable", "type": "function", "name": "serializeUint", "outputs": [{"internalType": "string", "name": "json", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "object<PERSON>ey", "type": "string"}, {"internalType": "string", "name": "valueKey", "type": "string"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "serializeUintToHex", "outputs": [{"internalType": "string", "name": "json", "type": "string"}]}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setArbitraryStorage"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "value", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "setEnv"}, {"inputs": [{"internalType": "bytes32", "name": "digest", "type": "bytes32"}], "stateMutability": "pure", "type": "function", "name": "sign", "outputs": [{"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "bytes32", "name": "digest", "type": "bytes32"}], "stateMutability": "pure", "type": "function", "name": "sign", "outputs": [{"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}]}, {"inputs": [{"internalType": "struct VmSafe.Wallet", "name": "wallet", "type": "tuple", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "uint256", "name": "publicKeyX", "type": "uint256"}, {"internalType": "uint256", "name": "publicKeyY", "type": "uint256"}, {"internalType": "uint256", "name": "privateKey", "type": "uint256"}]}, {"internalType": "bytes32", "name": "digest", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "sign", "outputs": [{"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}]}, {"inputs": [{"internalType": "uint256", "name": "privateKey", "type": "uint256"}, {"internalType": "bytes32", "name": "digest", "type": "bytes32"}], "stateMutability": "pure", "type": "function", "name": "sign", "outputs": [{"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}]}, {"inputs": [{"internalType": "struct VmSafe.Wallet", "name": "wallet", "type": "tuple", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "uint256", "name": "publicKeyX", "type": "uint256"}, {"internalType": "uint256", "name": "publicKeyY", "type": "uint256"}, {"internalType": "uint256", "name": "privateKey", "type": "uint256"}]}, {"internalType": "bytes32", "name": "digest", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "signCompact", "outputs": [{"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "vs", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "bytes32", "name": "digest", "type": "bytes32"}], "stateMutability": "pure", "type": "function", "name": "signCompact", "outputs": [{"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "vs", "type": "bytes32"}]}, {"inputs": [{"internalType": "bytes32", "name": "digest", "type": "bytes32"}], "stateMutability": "pure", "type": "function", "name": "signCompact", "outputs": [{"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "vs", "type": "bytes32"}]}, {"inputs": [{"internalType": "uint256", "name": "privateKey", "type": "uint256"}, {"internalType": "bytes32", "name": "digest", "type": "bytes32"}], "stateMutability": "pure", "type": "function", "name": "signCompact", "outputs": [{"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "vs", "type": "bytes32"}]}, {"inputs": [{"internalType": "uint256", "name": "privateKey", "type": "uint256"}, {"internalType": "bytes32", "name": "digest", "type": "bytes32"}], "stateMutability": "pure", "type": "function", "name": "signP256", "outputs": [{"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}]}, {"inputs": [{"internalType": "uint256", "name": "duration", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "sleep"}, {"inputs": [{"internalType": "string", "name": "input", "type": "string"}, {"internalType": "string", "name": "delimiter", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "split", "outputs": [{"internalType": "string[]", "name": "outputs", "type": "string[]"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "startBroadcast"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "startBroadcast"}, {"inputs": [{"internalType": "uint256", "name": "privateKey", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "startBroadcast"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "startDebugTraceRecording"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "startMappingRecording"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "startStateDiffRecording"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "stopAndReturnDebugTraceRecording", "outputs": [{"internalType": "struct VmSafe.DebugStep[]", "name": "step", "type": "tuple[]", "components": [{"internalType": "uint256[]", "name": "stack", "type": "uint256[]"}, {"internalType": "bytes", "name": "memoryInput", "type": "bytes"}, {"internalType": "uint8", "name": "opcode", "type": "uint8"}, {"internalType": "uint64", "name": "depth", "type": "uint64"}, {"internalType": "bool", "name": "isOutOfGas", "type": "bool"}, {"internalType": "address", "name": "contractAddr", "type": "address"}]}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "stopAndReturnStateDiff", "outputs": [{"internalType": "struct VmSafe.AccountAccess[]", "name": "accountAccesses", "type": "tuple[]", "components": [{"internalType": "struct VmSafe.ChainInfo", "name": "chainInfo", "type": "tuple", "components": [{"internalType": "uint256", "name": "forkId", "type": "uint256"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}]}, {"internalType": "enum VmSafe.AccountAccessKind", "name": "kind", "type": "uint8"}, {"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "accessor", "type": "address"}, {"internalType": "bool", "name": "initialized", "type": "bool"}, {"internalType": "uint256", "name": "oldBalance", "type": "uint256"}, {"internalType": "uint256", "name": "newBalance", "type": "uint256"}, {"internalType": "bytes", "name": "deployedCode", "type": "bytes"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "bool", "name": "reverted", "type": "bool"}, {"internalType": "struct VmSafe.StorageAccess[]", "name": "storageAccesses", "type": "tuple[]", "components": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "slot", "type": "bytes32"}, {"internalType": "bool", "name": "isWrite", "type": "bool"}, {"internalType": "bytes32", "name": "previousValue", "type": "bytes32"}, {"internalType": "bytes32", "name": "newValue", "type": "bytes32"}, {"internalType": "bool", "name": "reverted", "type": "bool"}]}, {"internalType": "uint64", "name": "depth", "type": "uint64"}]}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "stopBroadcast"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "stopMappingRecording"}, {"inputs": [{"internalType": "string", "name": "data", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "toBase64", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "pure", "type": "function", "name": "toBase64", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "data", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "toBase64URL", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "pure", "type": "function", "name": "toBase64URL", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "input", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "toLowercase", "outputs": [{"internalType": "string", "name": "output", "type": "string"}]}, {"inputs": [{"internalType": "address", "name": "value", "type": "address"}], "stateMutability": "pure", "type": "function", "name": "toString", "outputs": [{"internalType": "string", "name": "stringifiedValue", "type": "string"}]}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "pure", "type": "function", "name": "toString", "outputs": [{"internalType": "string", "name": "stringifiedValue", "type": "string"}]}, {"inputs": [{"internalType": "bytes", "name": "value", "type": "bytes"}], "stateMutability": "pure", "type": "function", "name": "toString", "outputs": [{"internalType": "string", "name": "stringifiedValue", "type": "string"}]}, {"inputs": [{"internalType": "bool", "name": "value", "type": "bool"}], "stateMutability": "pure", "type": "function", "name": "toString", "outputs": [{"internalType": "string", "name": "stringifiedValue", "type": "string"}]}, {"inputs": [{"internalType": "int256", "name": "value", "type": "int256"}], "stateMutability": "pure", "type": "function", "name": "toString", "outputs": [{"internalType": "string", "name": "stringifiedValue", "type": "string"}]}, {"inputs": [{"internalType": "bytes32", "name": "value", "type": "bytes32"}], "stateMutability": "pure", "type": "function", "name": "toString", "outputs": [{"internalType": "string", "name": "stringifiedValue", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "input", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "toUppercase", "outputs": [{"internalType": "string", "name": "output", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "input", "type": "string"}], "stateMutability": "pure", "type": "function", "name": "trim", "outputs": [{"internalType": "string", "name": "output", "type": "string"}]}, {"inputs": [{"internalType": "string[]", "name": "commandInput", "type": "string[]"}], "stateMutability": "nonpayable", "type": "function", "name": "tryFfi", "outputs": [{"internalType": "struct VmSafe.FfiResult", "name": "result", "type": "tuple", "components": [{"internalType": "int32", "name": "exitCode", "type": "int32"}, {"internalType": "bytes", "name": "stdout", "type": "bytes"}, {"internalType": "bytes", "name": "stderr", "type": "bytes"}]}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "unixTime", "outputs": [{"internalType": "uint256", "name": "milliseconds", "type": "uint256"}]}, {"inputs": [{"internalType": "string", "name": "path", "type": "string"}, {"internalType": "string", "name": "data", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "writeFile"}, {"inputs": [{"internalType": "string", "name": "path", "type": "string"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "writeFileBinary"}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "path", "type": "string"}, {"internalType": "string", "name": "valueKey", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "writeJson"}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "path", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "writeJson"}, {"inputs": [{"internalType": "string", "name": "path", "type": "string"}, {"internalType": "string", "name": "data", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "writeLine"}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "path", "type": "string"}, {"internalType": "string", "name": "valueKey", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "writeToml"}, {"inputs": [{"internalType": "string", "name": "json", "type": "string"}, {"internalType": "string", "name": "path", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "writeToml"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {"accesses(address)": {"notice": "Gets all accessed reads and write slot from a `vm.record` session, for a given address."}, "addr(uint256)": {"notice": "Gets the address for a given private key."}, "assertApproxEqAbs(int256,int256,uint256)": {"notice": "Compares two `int256` values. Expects difference to be less than or equal to `max<PERSON><PERSON><PERSON>`."}, "assertApproxEqAbs(int256,int256,uint256,string)": {"notice": "Compares two `int256` values. Expects difference to be less than or equal to `max<PERSON><PERSON><PERSON>`. Includes error message into revert string on failure."}, "assertApproxEqAbs(uint256,uint256,uint256)": {"notice": "Compares two `uint256` values. Expects difference to be less than or equal to `max<PERSON><PERSON><PERSON>`."}, "assertApproxEqAbs(uint256,uint256,uint256,string)": {"notice": "Compares two `uint256` values. Expects difference to be less than or equal to `max<PERSON><PERSON><PERSON>`. Includes error message into revert string on failure."}, "assertApproxEqAbsDecimal(int256,int256,uint256,uint256)": {"notice": "Compares two `int256` values. Expects difference to be less than or equal to `maxD<PERSON><PERSON>`. Formats values with decimals in failure message."}, "assertApproxEqAbsDecimal(int256,int256,uint256,uint256,string)": {"notice": "Compares two `int256` values. Expects difference to be less than or equal to `max<PERSON><PERSON><PERSON>`. Formats values with decimals in failure message. Includes error message into revert string on failure."}, "assertApproxEqAbsDecimal(uint256,uint256,uint256,uint256)": {"notice": "Compares two `uint256` values. Expects difference to be less than or equal to `max<PERSON><PERSON><PERSON>`. Formats values with decimals in failure message."}, "assertApproxEqAbsDecimal(uint256,uint256,uint256,uint256,string)": {"notice": "Compares two `uint256` values. Expects difference to be less than or equal to `max<PERSON><PERSON><PERSON>`. Formats values with decimals in failure message. Includes error message into revert string on failure."}, "assertApproxEqRel(int256,int256,uint256)": {"notice": "Compares two `int256` values. Expects relative difference in percents to be less than or equal to `maxPercentDelta`. `maxPercentDelta` is an 18 decimal fixed point number, where 1e18 == 100%"}, "assertApproxEqRel(int256,int256,uint256,string)": {"notice": "Compares two `int256` values. Expects relative difference in percents to be less than or equal to `maxPercentDelta`. `maxPercentDelta` is an 18 decimal fixed point number, where 1e18 == 100% Includes error message into revert string on failure."}, "assertApproxEqRel(uint256,uint256,uint256)": {"notice": "Compares two `uint256` values. Expects relative difference in percents to be less than or equal to `maxPercentDelta`. `maxPercentDelta` is an 18 decimal fixed point number, where 1e18 == 100%"}, "assertApproxEqRel(uint256,uint256,uint256,string)": {"notice": "Compares two `uint256` values. Expects relative difference in percents to be less than or equal to `maxPercentDelta`. `maxPercentDelta` is an 18 decimal fixed point number, where 1e18 == 100% Includes error message into revert string on failure."}, "assertApproxEqRelDecimal(int256,int256,uint256,uint256)": {"notice": "Compares two `int256` values. Expects relative difference in percents to be less than or equal to `maxPercentDelta`. `maxPercentDelta` is an 18 decimal fixed point number, where 1e18 == 100% Formats values with decimals in failure message."}, "assertApproxEqRelDecimal(int256,int256,uint256,uint256,string)": {"notice": "Compares two `int256` values. Expects relative difference in percents to be less than or equal to `maxPercentDelta`. `maxPercentDelta` is an 18 decimal fixed point number, where 1e18 == 100% Formats values with decimals in failure message. Includes error message into revert string on failure."}, "assertApproxEqRelDecimal(uint256,uint256,uint256,uint256)": {"notice": "Compares two `uint256` values. Expects relative difference in percents to be less than or equal to `maxPercentDelta`. `maxPercentDelta` is an 18 decimal fixed point number, where 1e18 == 100% Formats values with decimals in failure message."}, "assertApproxEqRelDecimal(uint256,uint256,uint256,uint256,string)": {"notice": "Compares two `uint256` values. Expects relative difference in percents to be less than or equal to `maxPercentDelta`. `maxPercentDelta` is an 18 decimal fixed point number, where 1e18 == 100% Formats values with decimals in failure message. Includes error message into revert string on failure."}, "assertEq(address,address)": {"notice": "Asserts that two `address` values are equal."}, "assertEq(address,address,string)": {"notice": "Asserts that two `address` values are equal and includes error message into revert string on failure."}, "assertEq(address[],address[])": {"notice": "Asserts that two arrays of `address` values are equal."}, "assertEq(address[],address[],string)": {"notice": "Asserts that two arrays of `address` values are equal and includes error message into revert string on failure."}, "assertEq(bool,bool)": {"notice": "Asserts that two `bool` values are equal."}, "assertEq(bool,bool,string)": {"notice": "Asserts that two `bool` values are equal and includes error message into revert string on failure."}, "assertEq(bool[],bool[])": {"notice": "Asserts that two arrays of `bool` values are equal."}, "assertEq(bool[],bool[],string)": {"notice": "Asserts that two arrays of `bool` values are equal and includes error message into revert string on failure."}, "assertEq(bytes,bytes)": {"notice": "Asserts that two `bytes` values are equal."}, "assertEq(bytes,bytes,string)": {"notice": "Asserts that two `bytes` values are equal and includes error message into revert string on failure."}, "assertEq(bytes32,bytes32)": {"notice": "Asserts that two `bytes32` values are equal."}, "assertEq(bytes32,bytes32,string)": {"notice": "Asserts that two `bytes32` values are equal and includes error message into revert string on failure."}, "assertEq(bytes32[],bytes32[])": {"notice": "Asserts that two arrays of `bytes32` values are equal."}, "assertEq(bytes32[],bytes32[],string)": {"notice": "Asserts that two arrays of `bytes32` values are equal and includes error message into revert string on failure."}, "assertEq(bytes[],bytes[])": {"notice": "Asserts that two arrays of `bytes` values are equal."}, "assertEq(bytes[],bytes[],string)": {"notice": "Asserts that two arrays of `bytes` values are equal and includes error message into revert string on failure."}, "assertEq(int256,int256)": {"notice": "Asserts that two `int256` values are equal."}, "assertEq(int256,int256,string)": {"notice": "Asserts that two `int256` values are equal and includes error message into revert string on failure."}, "assertEq(int256[],int256[])": {"notice": "Asserts that two arrays of `int256` values are equal."}, "assertEq(int256[],int256[],string)": {"notice": "Asserts that two arrays of `int256` values are equal and includes error message into revert string on failure."}, "assertEq(string,string)": {"notice": "Asserts that two `string` values are equal."}, "assertEq(string,string,string)": {"notice": "Asserts that two `string` values are equal and includes error message into revert string on failure."}, "assertEq(string[],string[])": {"notice": "Asserts that two arrays of `string` values are equal."}, "assertEq(string[],string[],string)": {"notice": "Asserts that two arrays of `string` values are equal and includes error message into revert string on failure."}, "assertEq(uint256,uint256)": {"notice": "Asserts that two `uint256` values are equal."}, "assertEq(uint256,uint256,string)": {"notice": "Asserts that two `uint256` values are equal and includes error message into revert string on failure."}, "assertEq(uint256[],uint256[])": {"notice": "Asserts that two arrays of `uint256 values are equal."}, "assertEq(uint256[],uint256[],string)": {"notice": "Asserts that two arrays of `uint256` values are equal and includes error message into revert string on failure."}, "assertEqDecimal(int256,int256,uint256)": {"notice": "Asserts that two `int256` values are equal, formatting them with decimals in failure message."}, "assertEqDecimal(int256,int256,uint256,string)": {"notice": "Asserts that two `int256` values are equal, formatting them with decimals in failure message. Includes error message into revert string on failure."}, "assertEqDecimal(uint256,uint256,uint256)": {"notice": "Asserts that two `uint256` values are equal, formatting them with decimals in failure message."}, "assertEqDecimal(uint256,uint256,uint256,string)": {"notice": "Asserts that two `uint256` values are equal, formatting them with decimals in failure message. Includes error message into revert string on failure."}, "assertFalse(bool)": {"notice": "Asserts that the given condition is false."}, "assertFalse(bool,string)": {"notice": "Asserts that the given condition is false and includes error message into revert string on failure."}, "assertGe(int256,int256)": {"notice": "Compares two `int256` values. Expects first value to be greater than or equal to second."}, "assertGe(int256,int256,string)": {"notice": "Compares two `int256` values. Expects first value to be greater than or equal to second. Includes error message into revert string on failure."}, "assertGe(uint256,uint256)": {"notice": "Compares two `uint256` values. Expects first value to be greater than or equal to second."}, "assertGe(uint256,uint256,string)": {"notice": "Compares two `uint256` values. Expects first value to be greater than or equal to second. Includes error message into revert string on failure."}, "assertGeDecimal(int256,int256,uint256)": {"notice": "Compares two `int256` values. Expects first value to be greater than or equal to second. Formats values with decimals in failure message."}, "assertGeDecimal(int256,int256,uint256,string)": {"notice": "Compares two `int256` values. Expects first value to be greater than or equal to second. Formats values with decimals in failure message. Includes error message into revert string on failure."}, "assertGeDecimal(uint256,uint256,uint256)": {"notice": "Compares two `uint256` values. Expects first value to be greater than or equal to second. Formats values with decimals in failure message."}, "assertGeDecimal(uint256,uint256,uint256,string)": {"notice": "Compares two `uint256` values. Expects first value to be greater than or equal to second. Formats values with decimals in failure message. Includes error message into revert string on failure."}, "assertGt(int256,int256)": {"notice": "Compares two `int256` values. Expects first value to be greater than second."}, "assertGt(int256,int256,string)": {"notice": "Compares two `int256` values. Expects first value to be greater than second. Includes error message into revert string on failure."}, "assertGt(uint256,uint256)": {"notice": "Compares two `uint256` values. Expects first value to be greater than second."}, "assertGt(uint256,uint256,string)": {"notice": "Compares two `uint256` values. Expects first value to be greater than second. Includes error message into revert string on failure."}, "assertGtDecimal(int256,int256,uint256)": {"notice": "Compares two `int256` values. Expects first value to be greater than second. Formats values with decimals in failure message."}, "assertGtDecimal(int256,int256,uint256,string)": {"notice": "Compares two `int256` values. Expects first value to be greater than second. Formats values with decimals in failure message. Includes error message into revert string on failure."}, "assertGtDecimal(uint256,uint256,uint256)": {"notice": "Compares two `uint256` values. Expects first value to be greater than second. Formats values with decimals in failure message."}, "assertGtDecimal(uint256,uint256,uint256,string)": {"notice": "Compares two `uint256` values. Expects first value to be greater than second. Formats values with decimals in failure message. Includes error message into revert string on failure."}, "assertLe(int256,int256)": {"notice": "Compares two `int256` values. Expects first value to be less than or equal to second."}, "assertLe(int256,int256,string)": {"notice": "Compares two `int256` values. Expects first value to be less than or equal to second. Includes error message into revert string on failure."}, "assertLe(uint256,uint256)": {"notice": "Compares two `uint256` values. Expects first value to be less than or equal to second."}, "assertLe(uint256,uint256,string)": {"notice": "Compares two `uint256` values. Expects first value to be less than or equal to second. Includes error message into revert string on failure."}, "assertLeDecimal(int256,int256,uint256)": {"notice": "Compares two `int256` values. Expects first value to be less than or equal to second. Formats values with decimals in failure message."}, "assertLeDecimal(int256,int256,uint256,string)": {"notice": "Compares two `int256` values. Expects first value to be less than or equal to second. Formats values with decimals in failure message. Includes error message into revert string on failure."}, "assertLeDecimal(uint256,uint256,uint256)": {"notice": "Compares two `uint256` values. Expects first value to be less than or equal to second. Formats values with decimals in failure message."}, "assertLeDecimal(uint256,uint256,uint256,string)": {"notice": "Compares two `uint256` values. Expects first value to be less than or equal to second. Formats values with decimals in failure message. Includes error message into revert string on failure."}, "assertLt(int256,int256)": {"notice": "Compares two `int256` values. Expects first value to be less than second."}, "assertLt(int256,int256,string)": {"notice": "Compares two `int256` values. Expects first value to be less than second. Includes error message into revert string on failure."}, "assertLt(uint256,uint256)": {"notice": "Compares two `uint256` values. Expects first value to be less than second."}, "assertLt(uint256,uint256,string)": {"notice": "Compares two `uint256` values. Expects first value to be less than second. Includes error message into revert string on failure."}, "assertLtDecimal(int256,int256,uint256)": {"notice": "Compares two `int256` values. Expects first value to be less than second. Formats values with decimals in failure message."}, "assertLtDecimal(int256,int256,uint256,string)": {"notice": "Compares two `int256` values. Expects first value to be less than second. Formats values with decimals in failure message. Includes error message into revert string on failure."}, "assertLtDecimal(uint256,uint256,uint256)": {"notice": "Compares two `uint256` values. Expects first value to be less than second. Formats values with decimals in failure message."}, "assertLtDecimal(uint256,uint256,uint256,string)": {"notice": "Compares two `uint256` values. Expects first value to be less than second. Formats values with decimals in failure message. Includes error message into revert string on failure."}, "assertNotEq(address,address)": {"notice": "Asserts that two `address` values are not equal."}, "assertNotEq(address,address,string)": {"notice": "Asserts that two `address` values are not equal and includes error message into revert string on failure."}, "assertNotEq(address[],address[])": {"notice": "Asserts that two arrays of `address` values are not equal."}, "assertNotEq(address[],address[],string)": {"notice": "Asserts that two arrays of `address` values are not equal and includes error message into revert string on failure."}, "assertNotEq(bool,bool)": {"notice": "Asserts that two `bool` values are not equal."}, "assertNotEq(bool,bool,string)": {"notice": "Asserts that two `bool` values are not equal and includes error message into revert string on failure."}, "assertNotEq(bool[],bool[])": {"notice": "Asserts that two arrays of `bool` values are not equal."}, "assertNotEq(bool[],bool[],string)": {"notice": "Asserts that two arrays of `bool` values are not equal and includes error message into revert string on failure."}, "assertNotEq(bytes,bytes)": {"notice": "Asserts that two `bytes` values are not equal."}, "assertNotEq(bytes,bytes,string)": {"notice": "Asserts that two `bytes` values are not equal and includes error message into revert string on failure."}, "assertNotEq(bytes32,bytes32)": {"notice": "Asserts that two `bytes32` values are not equal."}, "assertNotEq(bytes32,bytes32,string)": {"notice": "Asserts that two `bytes32` values are not equal and includes error message into revert string on failure."}, "assertNotEq(bytes32[],bytes32[])": {"notice": "Asserts that two arrays of `bytes32` values are not equal."}, "assertNotEq(bytes32[],bytes32[],string)": {"notice": "Asserts that two arrays of `bytes32` values are not equal and includes error message into revert string on failure."}, "assertNotEq(bytes[],bytes[])": {"notice": "Asserts that two arrays of `bytes` values are not equal."}, "assertNotEq(bytes[],bytes[],string)": {"notice": "Asserts that two arrays of `bytes` values are not equal and includes error message into revert string on failure."}, "assertNotEq(int256,int256)": {"notice": "Asserts that two `int256` values are not equal."}, "assertNotEq(int256,int256,string)": {"notice": "Asserts that two `int256` values are not equal and includes error message into revert string on failure."}, "assertNotEq(int256[],int256[])": {"notice": "Asserts that two arrays of `int256` values are not equal."}, "assertNotEq(int256[],int256[],string)": {"notice": "Asserts that two arrays of `int256` values are not equal and includes error message into revert string on failure."}, "assertNotEq(string,string)": {"notice": "Asserts that two `string` values are not equal."}, "assertNotEq(string,string,string)": {"notice": "Asserts that two `string` values are not equal and includes error message into revert string on failure."}, "assertNotEq(string[],string[])": {"notice": "Asserts that two arrays of `string` values are not equal."}, "assertNotEq(string[],string[],string)": {"notice": "Asserts that two arrays of `string` values are not equal and includes error message into revert string on failure."}, "assertNotEq(uint256,uint256)": {"notice": "Asserts that two `uint256` values are not equal."}, "assertNotEq(uint256,uint256,string)": {"notice": "Asserts that two `uint256` values are not equal and includes error message into revert string on failure."}, "assertNotEq(uint256[],uint256[])": {"notice": "Asserts that two arrays of `uint256` values are not equal."}, "assertNotEq(uint256[],uint256[],string)": {"notice": "Asserts that two arrays of `uint256` values are not equal and includes error message into revert string on failure."}, "assertNotEqDecimal(int256,int256,uint256)": {"notice": "Asserts that two `int256` values are not equal, formatting them with decimals in failure message."}, "assertNotEqDecimal(int256,int256,uint256,string)": {"notice": "Asserts that two `int256` values are not equal, formatting them with decimals in failure message. Includes error message into revert string on failure."}, "assertNotEqDecimal(uint256,uint256,uint256)": {"notice": "Asserts that two `uint256` values are not equal, formatting them with decimals in failure message."}, "assertNotEqDecimal(uint256,uint256,uint256,string)": {"notice": "Asserts that two `uint256` values are not equal, formatting them with decimals in failure message. Includes error message into revert string on failure."}, "assertTrue(bool)": {"notice": "Asserts that the given condition is true."}, "assertTrue(bool,string)": {"notice": "Asserts that the given condition is true and includes error message into revert string on failure."}, "assume(bool)": {"notice": "If the condition is false, discard this run's fuzz inputs and generate new ones."}, "assumeNoRevert()": {"notice": "Discard this run's fuzz inputs and generate new ones if next call reverted."}, "breakpoint(string)": {"notice": "Writes a breakpoint to jump to in the debugger."}, "breakpoint(string,bool)": {"notice": "Writes a conditional breakpoint to jump to in the debugger."}, "broadcast()": {"notice": "Has the next call (at this call depth only) create transactions that can later be signed and sent onchain. Broadcasting address is determined by checking the following in order: 1. If `--sender` argument was provided, that address is used. 2. If exactly one signer (e.g. private key, hw wallet, keystore) is set when `forge broadcast` is invoked, that signer is used. 3. Otherwise, default foundry sender (1804c8AB1F12E6bbf3894d4083f33e07309d1f38) is used."}, "broadcast(address)": {"notice": "Has the next call (at this call depth only) create a transaction with the address provided as the sender that can later be signed and sent onchain."}, "broadcast(uint256)": {"notice": "Has the next call (at this call depth only) create a transaction with the private key provided as the sender that can later be signed and sent onchain."}, "broadcastRawTransaction(bytes)": {"notice": "Takes a signed transaction and broadcasts it to the network."}, "closeFile(string)": {"notice": "Closes file for reading, resetting the offset and allowing to read it from beginning with readLine. `path` is relative to the project root."}, "computeCreate2Address(bytes32,bytes32)": {"notice": "Compute the address of a contract created with CREATE2 using the default CREATE2 deployer."}, "computeCreate2Address(bytes32,bytes32,address)": {"notice": "Compute the address of a contract created with CREATE2 using the given CREATE2 deployer."}, "computeCreateAddress(address,uint256)": {"notice": "Compute the address a contract will be deployed at for a given deployer address and nonce."}, "copyFile(string,string)": {"notice": "Copies the contents of one file to another. This function will **overwrite** the contents of `to`. On success, the total number of bytes copied is returned and it is equal to the length of the `to` file as reported by `metadata`. Both `from` and `to` are relative to the project root."}, "copyStorage(address,address)": {"notice": "Utility cheatcode to copy storage of `from` contract to another `to` contract."}, "createDir(string,bool)": {"notice": "Creates a new, empty directory at the provided path. This cheatcode will revert in the following situations, but is not limited to just these cases: - User lacks permissions to modify `path`. - A parent of the given path doesn't exist and `recursive` is false. - `path` already exists and `recursive` is false. `path` is relative to the project root."}, "createWallet(string)": {"notice": "Derives a private key from the name, labels the account with that name, and returns the wallet."}, "createWallet(uint256)": {"notice": "Generates a wallet from the private key and returns the wallet."}, "createWallet(uint256,string)": {"notice": "Generates a wallet from the private key, labels the account with that name, and returns the wallet."}, "deployCode(string)": {"notice": "Deploys a contract from an artifact file. Takes in the relative path to the json file or the path to the artifact in the form of <path>:<contract>:<version> where <contract> and <version> parts are optional."}, "deployCode(string,bytes)": {"notice": "Deploys a contract from an artifact file. Takes in the relative path to the json file or the path to the artifact in the form of <path>:<contract>:<version> where <contract> and <version> parts are optional. Additionally accepts abi-encoded constructor arguments."}, "deriveKey(string,string,uint32)": {"notice": "Derive a private key from a provided mnenomic string (or mnenomic file path) at `{derivationPath}{index}`."}, "deriveKey(string,string,uint32,string)": {"notice": "Derive a private key from a provided mnenomic string (or mnenomic file path) in the specified language at `{derivationPath}{index}`."}, "deriveKey(string,uint32)": {"notice": "Derive a private key from a provided mnenomic string (or mnenomic file path) at the derivation path `m/44'/60'/0'/0/{index}`."}, "deriveKey(string,uint32,string)": {"notice": "Derive a private key from a provided mnenomic string (or mnenomic file path) in the specified language at the derivation path `m/44'/60'/0'/0/{index}`."}, "ensNamehash(string)": {"notice": "Returns ENS namehash for provided string."}, "envAddress(string)": {"notice": "Gets the environment variable `name` and parses it as `address`. Reverts if the variable was not found or could not be parsed."}, "envAddress(string,string)": {"notice": "Gets the environment variable `name` and parses it as an array of `address`, delimited by `delim`. Reverts if the variable was not found or could not be parsed."}, "envBool(string)": {"notice": "Gets the environment variable `name` and parses it as `bool`. Reverts if the variable was not found or could not be parsed."}, "envBool(string,string)": {"notice": "Gets the environment variable `name` and parses it as an array of `bool`, delimited by `delim`. Reverts if the variable was not found or could not be parsed."}, "envBytes(string)": {"notice": "Gets the environment variable `name` and parses it as `bytes`. Reverts if the variable was not found or could not be parsed."}, "envBytes(string,string)": {"notice": "Gets the environment variable `name` and parses it as an array of `bytes`, delimited by `delim`. Reverts if the variable was not found or could not be parsed."}, "envBytes32(string)": {"notice": "Gets the environment variable `name` and parses it as `bytes32`. Reverts if the variable was not found or could not be parsed."}, "envBytes32(string,string)": {"notice": "Gets the environment variable `name` and parses it as an array of `bytes32`, delimited by `delim`. Reverts if the variable was not found or could not be parsed."}, "envExists(string)": {"notice": "Gets the environment variable `name` and returns true if it exists, else returns false."}, "envInt(string)": {"notice": "Gets the environment variable `name` and parses it as `int256`. Reverts if the variable was not found or could not be parsed."}, "envInt(string,string)": {"notice": "Gets the environment variable `name` and parses it as an array of `int256`, delimited by `delim`. Reverts if the variable was not found or could not be parsed."}, "envOr(string,address)": {"notice": "Gets the environment variable `name` and parses it as `address`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found."}, "envOr(string,bool)": {"notice": "Gets the environment variable `name` and parses it as `bool`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found."}, "envOr(string,bytes)": {"notice": "Gets the environment variable `name` and parses it as `bytes`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found."}, "envOr(string,bytes32)": {"notice": "Gets the environment variable `name` and parses it as `bytes32`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found."}, "envOr(string,int256)": {"notice": "Gets the environment variable `name` and parses it as `int256`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found."}, "envOr(string,string)": {"notice": "Gets the environment variable `name` and parses it as `string`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found."}, "envOr(string,string,address[])": {"notice": "Gets the environment variable `name` and parses it as an array of `address`, delimited by `delim`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found."}, "envOr(string,string,bool[])": {"notice": "Gets the environment variable `name` and parses it as an array of `bool`, delimited by `delim`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found."}, "envOr(string,string,bytes32[])": {"notice": "Gets the environment variable `name` and parses it as an array of `bytes32`, delimited by `delim`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found."}, "envOr(string,string,bytes[])": {"notice": "Gets the environment variable `name` and parses it as an array of `bytes`, delimited by `delim`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found."}, "envOr(string,string,int256[])": {"notice": "Gets the environment variable `name` and parses it as an array of `int256`, delimited by `delim`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found."}, "envOr(string,string,string[])": {"notice": "Gets the environment variable `name` and parses it as an array of `string`, delimited by `delim`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found."}, "envOr(string,string,uint256[])": {"notice": "Gets the environment variable `name` and parses it as an array of `uint256`, delimited by `delim`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found."}, "envOr(string,uint256)": {"notice": "Gets the environment variable `name` and parses it as `uint256`. Reverts if the variable could not be parsed. Returns `defaultValue` if the variable was not found."}, "envString(string)": {"notice": "Gets the environment variable `name` and parses it as `string`. Reverts if the variable was not found or could not be parsed."}, "envString(string,string)": {"notice": "Gets the environment variable `name` and parses it as an array of `string`, delimited by `delim`. Reverts if the variable was not found or could not be parsed."}, "envUint(string)": {"notice": "Gets the environment variable `name` and parses it as `uint256`. Reverts if the variable was not found or could not be parsed."}, "envUint(string,string)": {"notice": "Gets the environment variable `name` and parses it as an array of `uint256`, delimited by `delim`. Reverts if the variable was not found or could not be parsed."}, "eth_getLogs(uint256,uint256,address,bytes32[])": {"notice": "Gets all the logs according to specified filter."}, "exists(string)": {"notice": "Returns true if the given path points to an existing entity, else returns false."}, "ffi(string[])": {"notice": "Performs a foreign function call via the terminal."}, "fsMetadata(string)": {"notice": "Given a path, query the file system to get information about a file, directory, etc."}, "getArtifactPathByCode(bytes)": {"notice": "Gets the artifact path from code (aka. creation code)."}, "getArtifactPathByDeployedCode(bytes)": {"notice": "Gets the artifact path from deployed code (aka. runtime code)."}, "getBlobBaseFee()": {"notice": "Gets the current `block.blobbasefee`. You should use this instead of `block.blobbasefee` if you use `vm.blobBaseFee`, as `block.blobbasefee` is assumed to be constant across a transaction, and as a result will get optimized out by the compiler. See https://github.com/foundry-rs/foundry/issues/6180"}, "getBlockNumber()": {"notice": "Gets the current `block.number`. You should use this instead of `block.number` if you use `vm.roll`, as `block.number` is assumed to be constant across a transaction, and as a result will get optimized out by the compiler. See https://github.com/foundry-rs/foundry/issues/6180"}, "getBlockTimestamp()": {"notice": "Gets the current `block.timestamp`. You should use this instead of `block.timestamp` if you use `vm.warp`, as `block.timestamp` is assumed to be constant across a transaction, and as a result will get optimized out by the compiler. See https://github.com/foundry-rs/foundry/issues/6180"}, "getCode(string)": {"notice": "Gets the creation bytecode from an artifact file. Takes in the relative path to the json file or the path to the artifact in the form of <path>:<contract>:<version> where <contract> and <version> parts are optional."}, "getDeployedCode(string)": {"notice": "Gets the deployed bytecode from an artifact file. Takes in the relative path to the json file or the path to the artifact in the form of <path>:<contract>:<version> where <contract> and <version> parts are optional."}, "getFoundryVersion()": {"notice": "Returns the Foundry version. Format: <cargo_version>+<git_sha>+<build_timestamp> Sample output: 0.2.0+faa94c384+202407110019 Note: Build timestamps may vary slightly across platforms due to separate CI jobs. For reliable version comparisons, use YYYYMMDD0000 format (e.g., >= 202407110000) to compare timestamps while ignoring minor time differences."}, "getLabel(address)": {"notice": "Gets the label for the specified address."}, "getMappingKeyAndParentOf(address,bytes32)": {"notice": "Gets the map key and parent of a mapping at a given slot, for a given address."}, "getMappingLength(address,bytes32)": {"notice": "Gets the number of elements in the mapping at the given slot, for a given address."}, "getMappingSlotAt(address,bytes32,uint256)": {"notice": "Gets the elements at index idx of the mapping at the given slot, for a given address. The index must be less than the length of the mapping (i.e. the number of keys in the mapping)."}, "getNonce((address,uint256,uint256,uint256))": {"notice": "Get the nonce of a `Wallet`."}, "getNonce(address)": {"notice": "Gets the nonce of an account."}, "getRecordedLogs()": {"notice": "Gets all the recorded logs."}, "getScriptWallets()": {"notice": "Returns addresses of available unlocked wallets in the script environment."}, "getWallets()": {"notice": "Returns addresses of available unlocked wallets in the script environment."}, "indexOf(string,string)": {"notice": "Returns the index of the first occurrence of a `key` in an `input` string. Returns `NOT_FOUND` (i.e. `type(uint256).max`) if the `key` is not found. Returns 0 in case of an empty `key`."}, "isContext(uint8)": {"notice": "Returns true if `forge` command was executed in given context."}, "isDir(string)": {"notice": "Returns true if the path exists on disk and is pointing at a directory, else returns false."}, "isFile(string)": {"notice": "Returns true if the path exists on disk and is pointing at a regular file, else returns false."}, "keyExists(string,string)": {"notice": "Checks if `key` exists in a JSON object `keyExists` is being deprecated in favor of `keyExistsJson`. It will be removed in future versions."}, "keyExistsJson(string,string)": {"notice": "Checks if `key` exists in a JSON object."}, "keyExistsToml(string,string)": {"notice": "Checks if `key` exists in a TOML table."}, "label(address,string)": {"notice": "Labels an address in call traces."}, "lastCallGas()": {"notice": "Gets the gas used in the last call from the callee perspective."}, "load(address,bytes32)": {"notice": "Loads a storage slot from an address."}, "parseAddress(string)": {"notice": "Parses the given `string` into an `address`."}, "parseBool(string)": {"notice": "Parses the given `string` into a `bool`."}, "parseBytes(string)": {"notice": "Parses the given `string` into `bytes`."}, "parseBytes32(string)": {"notice": "Parses the given `string` into a `bytes32`."}, "parseInt(string)": {"notice": "Parses the given `string` into a `int256`."}, "parseJson(string)": {"notice": "ABI-encodes a JSON object."}, "parseJson(string,string)": {"notice": "ABI-encodes a JSON object at `key`."}, "parseJsonAddress(string,string)": {"notice": "Parses a string of JSON data at `key` and coerces it to `address`."}, "parseJsonAddressArray(string,string)": {"notice": "Parses a string of JSON data at `key` and coerces it to `address[]`."}, "parseJsonBool(string,string)": {"notice": "Parses a string of JSON data at `key` and coerces it to `bool`."}, "parseJsonBoolArray(string,string)": {"notice": "Parses a string of JSON data at `key` and coerces it to `bool[]`."}, "parseJsonBytes(string,string)": {"notice": "Parses a string of JSON data at `key` and coerces it to `bytes`."}, "parseJsonBytes32(string,string)": {"notice": "Parses a string of JSON data at `key` and coerces it to `bytes32`."}, "parseJsonBytes32Array(string,string)": {"notice": "Parses a string of JSON data at `key` and coerces it to `bytes32[]`."}, "parseJsonBytesArray(string,string)": {"notice": "Parses a string of JSON data at `key` and coerces it to `bytes[]`."}, "parseJsonInt(string,string)": {"notice": "Parses a string of JSON data at `key` and coerces it to `int256`."}, "parseJsonIntArray(string,string)": {"notice": "Parses a string of JSON data at `key` and coerces it to `int256[]`."}, "parseJsonKeys(string,string)": {"notice": "Returns an array of all the keys in a JSON object."}, "parseJsonString(string,string)": {"notice": "Parses a string of JSON data at `key` and coerces it to `string`."}, "parseJsonStringArray(string,string)": {"notice": "Parses a string of JSON data at `key` and coerces it to `string[]`."}, "parseJsonType(string,string)": {"notice": "Parses a string of JSON data and coerces it to type corresponding to `typeDescription`."}, "parseJsonType(string,string,string)": {"notice": "Parses a string of JSON data at `key` and coerces it to type corresponding to `typeDescription`."}, "parseJsonTypeArray(string,string,string)": {"notice": "Parses a string of JSON data at `key` and coerces it to type array corresponding to `typeDescription`."}, "parseJsonUint(string,string)": {"notice": "Parses a string of JSON data at `key` and coerces it to `uint256`."}, "parseJsonUintArray(string,string)": {"notice": "Parses a string of JSON data at `key` and coerces it to `uint256[]`."}, "parseToml(string)": {"notice": "ABI-encodes a TOML table."}, "parseToml(string,string)": {"notice": "ABI-encodes a TOML table at `key`."}, "parseTomlAddress(string,string)": {"notice": "Parses a string of TOML data at `key` and coerces it to `address`."}, "parseTomlAddressArray(string,string)": {"notice": "Parses a string of TOML data at `key` and coerces it to `address[]`."}, "parseTomlBool(string,string)": {"notice": "Parses a string of TOML data at `key` and coerces it to `bool`."}, "parseTomlBoolArray(string,string)": {"notice": "Parses a string of TOML data at `key` and coerces it to `bool[]`."}, "parseTomlBytes(string,string)": {"notice": "Parses a string of TOML data at `key` and coerces it to `bytes`."}, "parseTomlBytes32(string,string)": {"notice": "Parses a string of TOML data at `key` and coerces it to `bytes32`."}, "parseTomlBytes32Array(string,string)": {"notice": "Parses a string of TOML data at `key` and coerces it to `bytes32[]`."}, "parseTomlBytesArray(string,string)": {"notice": "Parses a string of TOML data at `key` and coerces it to `bytes[]`."}, "parseTomlInt(string,string)": {"notice": "Parses a string of TOML data at `key` and coerces it to `int256`."}, "parseTomlIntArray(string,string)": {"notice": "Parses a string of TOML data at `key` and coerces it to `int256[]`."}, "parseTomlKeys(string,string)": {"notice": "Returns an array of all the keys in a TOML table."}, "parseTomlString(string,string)": {"notice": "Parses a string of TOML data at `key` and coerces it to `string`."}, "parseTomlStringArray(string,string)": {"notice": "Parses a string of TOML data at `key` and coerces it to `string[]`."}, "parseTomlType(string,string)": {"notice": "Parses a string of TOML data and coerces it to type corresponding to `typeDescription`."}, "parseTomlType(string,string,string)": {"notice": "Parses a string of TOML data at `key` and coerces it to type corresponding to `typeDescription`."}, "parseTomlTypeArray(string,string,string)": {"notice": "Parses a string of TOML data at `key` and coerces it to type array corresponding to `typeDescription`."}, "parseTomlUint(string,string)": {"notice": "Parses a string of TOML data at `key` and coerces it to `uint256`."}, "parseTomlUintArray(string,string)": {"notice": "Parses a string of TOML data at `key` and coerces it to `uint256[]`."}, "parseUint(string)": {"notice": "Parses the given `string` into a `uint256`."}, "pauseGasMetering()": {"notice": "Pauses gas metering (i.e. gas usage is not counted). Noop if already paused."}, "pauseTracing()": {"notice": "Pauses collection of call traces. Useful in cases when you want to skip tracing of complex calls which are not useful for debugging."}, "projectRoot()": {"notice": "Get the path of the current project root."}, "prompt(string)": {"notice": "Prompts the user for a string value in the terminal."}, "promptAddress(string)": {"notice": "Prompts the user for an address in the terminal."}, "promptSecret(string)": {"notice": "Prompts the user for a hidden string value in the terminal."}, "promptSecretUint(string)": {"notice": "Prompts the user for hidden uint256 in the terminal (usually pk)."}, "promptUint(string)": {"notice": "Prompts the user for uint256 in the terminal."}, "publicKeyP256(uint256)": {"notice": "Derives secp256r1 public key from the provided `privateKey`."}, "randomAddress()": {"notice": "Returns a random `address`."}, "randomBool()": {"notice": "Returns a random `bool`."}, "randomBytes(uint256)": {"notice": "Returns a random byte array value of the given length."}, "randomBytes4()": {"notice": "Returns a random fixed-size byte array of length 4."}, "randomBytes8()": {"notice": "Returns a random fixed-size byte array of length 8."}, "randomInt()": {"notice": "Returns a random `int256` value."}, "randomInt(uint256)": {"notice": "Returns a random `int256` value of given bits."}, "randomUint()": {"notice": "Returns a random uint256 value."}, "randomUint(uint256)": {"notice": "Returns a random `uint256` value of given bits."}, "randomUint(uint256,uint256)": {"notice": "Returns random uint256 value between the provided range (=min..=max)."}, "readDir(string)": {"notice": "Reads the directory at the given path recursively, up to `maxDepth`. `maxDepth` defaults to 1, meaning only the direct children of the given directory will be returned. Follows symbolic links if `followLinks` is true."}, "readDir(string,uint64)": {"notice": "See `readDir(string)`."}, "readDir(string,uint64,bool)": {"notice": "See `readDir(string)`."}, "readFile(string)": {"notice": "Reads the entire content of file to string. `path` is relative to the project root."}, "readFileBinary(string)": {"notice": "Reads the entire content of file as binary. `path` is relative to the project root."}, "readLine(string)": {"notice": "Reads next line of file to string."}, "readLink(string)": {"notice": "Reads a symbolic link, returning the path that the link points to. This cheatcode will revert in the following situations, but is not limited to just these cases: - `path` is not a symbolic link. - `path` does not exist."}, "record()": {"notice": "Records all storage reads and writes."}, "recordLogs()": {"notice": "Record all the transaction logs."}, "rememberKey(uint256)": {"notice": "Adds a private key to the local forge wallet and returns the address."}, "rememberKeys(string,string,string,uint32)": {"notice": "Derive a set number of wallets from a mnemonic in the specified language at the derivation path `m/44'/60'/0'/0/{0..count}`. The respective private keys are saved to the local forge wallet for later use and their addresses are returned."}, "rememberKeys(string,string,uint32)": {"notice": "Derive a set number of wallets from a mnemonic at the derivation path `m/44'/60'/0'/0/{0..count}`. The respective private keys are saved to the local forge wallet for later use and their addresses are returned."}, "removeDir(string,bool)": {"notice": "Removes a directory at the provided path. This cheatcode will revert in the following situations, but is not limited to just these cases: - `path` doesn't exist. - `path` isn't a directory. - User lacks permissions to modify `path`. - The directory is not empty and `recursive` is false. `path` is relative to the project root."}, "removeFile(string)": {"notice": "Removes a file from the filesystem. This cheatcode will revert in the following situations, but is not limited to just these cases: - `path` points to a directory. - The file doesn't exist. - The user lacks permissions to remove the file. `path` is relative to the project root."}, "replace(string,string,string)": {"notice": "Replaces occurrences of `from` in the given `string` with `to`."}, "resetGasMetering()": {"notice": "Reset gas metering (i.e. gas usage is set to gas limit)."}, "resumeGasMetering()": {"notice": "Resumes gas metering (i.e. gas usage is counted again). Noop if already on."}, "resumeTracing()": {"notice": "Unpauses collection of call traces."}, "rpc(string,string)": {"notice": "Performs an Ethereum JSON-RPC request to the current fork URL."}, "rpc(string,string,string)": {"notice": "Performs an Ethereum JSON-RPC request to the given endpoint."}, "rpcUrl(string)": {"notice": "Returns the RPC url for the given alias."}, "rpcUrlStructs()": {"notice": "Returns all rpc urls and their aliases as structs."}, "rpcUrls()": {"notice": "Returns all rpc urls and their aliases `[alias, url][]`."}, "serializeAddress(string,string,address)": {"notice": "See `serializeJson`."}, "serializeAddress(string,string,address[])": {"notice": "See `serializeJson`."}, "serializeBool(string,string,bool)": {"notice": "See `serializeJson`."}, "serializeBool(string,string,bool[])": {"notice": "See `serializeJson`."}, "serializeBytes(string,string,bytes)": {"notice": "See `serializeJson`."}, "serializeBytes(string,string,bytes[])": {"notice": "See `serializeJson`."}, "serializeBytes32(string,string,bytes32)": {"notice": "See `serializeJson`."}, "serializeBytes32(string,string,bytes32[])": {"notice": "See `serializeJson`."}, "serializeInt(string,string,int256)": {"notice": "See `serializeJson`."}, "serializeInt(string,string,int256[])": {"notice": "See `serializeJson`."}, "serializeJson(string,string)": {"notice": "Serializes a key and value to a JSON object stored in-memory that can be later written to a file. Returns the stringified version of the specific JSON file up to that moment."}, "serializeJsonType(string,bytes)": {"notice": "See `serializeJson`."}, "serializeJsonType(string,string,string,bytes)": {"notice": "See `serializeJson`."}, "serializeString(string,string,string)": {"notice": "See `serializeJson`."}, "serializeString(string,string,string[])": {"notice": "See `serializeJson`."}, "serializeUint(string,string,uint256)": {"notice": "See `serializeJson`."}, "serializeUint(string,string,uint256[])": {"notice": "See `serializeJson`."}, "serializeUintToHex(string,string,uint256)": {"notice": "See `serializeJson`."}, "setArbitraryStorage(address)": {"notice": "Utility cheatcode to set arbitrary storage for given target address."}, "setEnv(string,string)": {"notice": "Sets environment variables."}, "sign((address,uint256,uint256,uint256),bytes32)": {"notice": "Signs data with a `Wallet`."}, "sign(address,bytes32)": {"notice": "Signs `digest` with signer provided to script using the secp256k1 curve. Raises error if none of the signers passed into the script have provided address."}, "sign(bytes32)": {"notice": "Signs `digest` with signer provided to script using the secp256k1 curve. If `--sender` is provided, the signer with provided address is used, otherwise, if exactly one signer is provided to the script, that signer is used. Raises error if signer passed through `--sender` does not match any unlocked signers or if `--sender` is not provided and not exactly one signer is passed to the script."}, "sign(uint256,bytes32)": {"notice": "Signs `digest` with `privateKey` using the secp256k1 curve."}, "signCompact((address,uint256,uint256,uint256),bytes32)": {"notice": "Signs data with a `Wallet`. Returns a compact signature (`r`, `vs`) as per EIP-2098, where `vs` encodes both the signature's `s` value, and the recovery id `v` in a single bytes32. This format reduces the signature size from 65 to 64 bytes."}, "signCompact(address,bytes32)": {"notice": "Signs `digest` with signer provided to script using the secp256k1 curve. Returns a compact signature (`r`, `vs`) as per EIP-2098, where `vs` encodes both the signature's `s` value, and the recovery id `v` in a single bytes32. This format reduces the signature size from 65 to 64 bytes. Raises error if none of the signers passed into the script have provided address."}, "signCompact(bytes32)": {"notice": "Signs `digest` with signer provided to script using the secp256k1 curve. Returns a compact signature (`r`, `vs`) as per EIP-2098, where `vs` encodes both the signature's `s` value, and the recovery id `v` in a single bytes32. This format reduces the signature size from 65 to 64 bytes. If `--sender` is provided, the signer with provided address is used, otherwise, if exactly one signer is provided to the script, that signer is used. Raises error if signer passed through `--sender` does not match any unlocked signers or if `--sender` is not provided and not exactly one signer is passed to the script."}, "signCompact(uint256,bytes32)": {"notice": "Signs `digest` with `private<PERSON>ey` using the secp256k1 curve. Returns a compact signature (`r`, `vs`) as per EIP-2098, where `vs` encodes both the signature's `s` value, and the recovery id `v` in a single bytes32. This format reduces the signature size from 65 to 64 bytes."}, "signP256(uint256,bytes32)": {"notice": "Signs `digest` with `privateKey` using the secp256r1 curve."}, "sleep(uint256)": {"notice": "Suspends execution of the main thread for `duration` milliseconds."}, "split(string,string)": {"notice": "Splits the given `string` into an array of strings divided by the `delimiter`."}, "startBroadcast()": {"notice": "Has all subsequent calls (at this call depth only) create transactions that can later be signed and sent onchain. Broadcasting address is determined by checking the following in order: 1. If `--sender` argument was provided, that address is used. 2. If exactly one signer (e.g. private key, hw wallet, keystore) is set when `forge broadcast` is invoked, that signer is used. 3. Otherwise, default foundry sender (1804c8AB1F12E6bbf3894d4083f33e07309d1f38) is used."}, "startBroadcast(address)": {"notice": "Has all subsequent calls (at this call depth only) create transactions with the address provided that can later be signed and sent onchain."}, "startBroadcast(uint256)": {"notice": "Has all subsequent calls (at this call depth only) create transactions with the private key provided that can later be signed and sent onchain."}, "startDebugTraceRecording()": {"notice": "Records the debug trace during the run."}, "startMappingRecording()": {"notice": "Starts recording all map SSTOREs for later retrieval."}, "startStateDiffRecording()": {"notice": "Record all account accesses as part of CREATE, CALL or SELFDESTRUCT opcodes in order, along with the context of the calls"}, "stopAndReturnDebugTraceRecording()": {"notice": "Stop debug trace recording and returns the recorded debug trace."}, "stopAndReturnStateDiff()": {"notice": "Returns an ordered array of all account accesses from a `vm.startStateDiffRecording` session."}, "stopBroadcast()": {"notice": "Stops collecting onchain transactions."}, "stopMappingRecording()": {"notice": "Stops recording all map SSTOREs for later retrieval and clears the recorded data."}, "toBase64(bytes)": {"notice": "Encodes a `bytes` value to a base64 string."}, "toBase64(string)": {"notice": "Encodes a `string` value to a base64 string."}, "toBase64URL(bytes)": {"notice": "Encodes a `bytes` value to a base64url string."}, "toBase64URL(string)": {"notice": "Encodes a `string` value to a base64url string."}, "toLowercase(string)": {"notice": "Converts the given `string` value to Lowercase."}, "toString(address)": {"notice": "Converts the given value to a `string`."}, "toString(bool)": {"notice": "Converts the given value to a `string`."}, "toString(bytes)": {"notice": "Converts the given value to a `string`."}, "toString(bytes32)": {"notice": "Converts the given value to a `string`."}, "toString(int256)": {"notice": "Converts the given value to a `string`."}, "toString(uint256)": {"notice": "Converts the given value to a `string`."}, "toUppercase(string)": {"notice": "Converts the given `string` value to Uppercase."}, "trim(string)": {"notice": "Trims leading and trailing whitespace from the given `string` value."}, "tryFfi(string[])": {"notice": "Performs a foreign function call via terminal and returns the exit code, stdout, and stderr."}, "unixTime()": {"notice": "Returns the time since unix epoch in milliseconds."}, "writeFile(string,string)": {"notice": "Writes data to file, creating a file if it does not exist, and entirely replacing its contents if it does. `path` is relative to the project root."}, "writeFileBinary(string,bytes)": {"notice": "Writes binary data to a file, creating a file if it does not exist, and entirely replacing its contents if it does. `path` is relative to the project root."}, "writeJson(string,string)": {"notice": "Write a serialized JSON object to a file. If the file exists, it will be overwritten."}, "writeJson(string,string,string)": {"notice": "Write a serialized JSON object to an **existing** JSON file, replacing a value with key = <value_key.> This is useful to replace a specific value of a JSON file, without having to parse the entire thing."}, "writeLine(string,string)": {"notice": "Writes line to file, creating a file if it does not exist. `path` is relative to the project root."}, "writeToml(string,string)": {"notice": "Takes serialized JSON, converts to TOML and write a serialized TOML to a file."}, "writeToml(string,string,string)": {"notice": "Takes serialized JSON, converts to TOML and write a serialized TOML table to an **existing** TOML file, replacing a value with key = <value_key.> This is useful to replace a specific value of a TOML file, without having to parse the entire thing."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/Vm.sol": "VmSafe"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/Vm.sol": {"keccak256": "0x51d822417db259b59eec2dfb7859285cef983a26b5d4fba20e1addabccc8734e", "urls": ["bzz-raw://0e3b5abe0173ee6e479e4248eb4ee3a4374f7e08fb2848d93f5c2cf5e95214f1", "dweb:/ipfs/QmbUBD4NLFCuR6WHVx5unXH2uWHEhtADLcFrUwyev8iLrD"], "license": "MIT OR Apache-2.0"}}, "version": 1}, "id": 22}