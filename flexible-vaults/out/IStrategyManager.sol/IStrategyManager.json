{"abi": [{"type": "function", "name": "addStrategiesToDepositWhitelist", "inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address[]", "internalType": "contract IStrategy[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "burnShares", "inputs": [{"name": "strategy", "type": "address", "internalType": "contract IStrategy"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "calculateStrategyDepositDigestHash", "inputs": [{"name": "staker", "type": "address", "internalType": "address"}, {"name": "strategy", "type": "address", "internalType": "contract IStrategy"}, {"name": "token", "type": "address", "internalType": "contract IERC20"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "expiry", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "delegation", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IDelegationManager"}], "stateMutability": "view"}, {"type": "function", "name": "depositIntoStrategy", "inputs": [{"name": "strategy", "type": "address", "internalType": "contract IStrategy"}, {"name": "token", "type": "address", "internalType": "contract IERC20"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "depositShares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "depositIntoStrategyWithSignature", "inputs": [{"name": "strategy", "type": "address", "internalType": "contract IStrategy"}, {"name": "token", "type": "address", "internalType": "contract IERC20"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "staker", "type": "address", "internalType": "address"}, {"name": "expiry", "type": "uint256", "internalType": "uint256"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "depositShares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getBurnableShares", "inputs": [{"name": "strategy", "type": "address", "internalType": "contract IStrategy"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getDeposits", "inputs": [{"name": "staker", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address[]", "internalType": "contract IStrategy[]"}, {"name": "", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "getStakerStrategyList", "inputs": [{"name": "staker", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address[]", "internalType": "contract IStrategy[]"}], "stateMutability": "view"}, {"type": "function", "name": "getStrategiesWithBurnableShares", "inputs": [], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}, {"name": "", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "initialOwner", "type": "address", "internalType": "address"}, {"name": "initialStrategyW<PERSON><PERSON>ster", "type": "address", "internalType": "address"}, {"name": "initialPausedStatus", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "removeStrategiesFromDepositWhitelist", "inputs": [{"name": "strategiesToRemoveFromWhitelist", "type": "address[]", "internalType": "contract IStrategy[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setStrategy<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "newStrategy<PERSON><PERSON><PERSON><PERSON>", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "stakerDepositShares", "inputs": [{"name": "user", "type": "address", "internalType": "address"}, {"name": "strategy", "type": "address", "internalType": "contract IStrategy"}], "outputs": [{"name": "shares", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "stakerStrategyListLength", "inputs": [{"name": "staker", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "strategyIsWhitelistedForDeposit", "inputs": [{"name": "strategy", "type": "address", "internalType": "contract IStrategy"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "strategy<PERSON><PERSON><PERSON><PERSON>", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"addStrategiesToDepositWhitelist(address[])": "5de08ff2", "burnShares(address)": "4b6d5d6e", "calculateStrategyDepositDigestHash(address,address,address,uint256,uint256,uint256)": "9ac01d61", "delegation()": "df5cf723", "depositIntoStrategy(address,address,uint256)": "e7a050aa", "depositIntoStrategyWithSignature(address,address,uint256,address,uint256,bytes)": "32e89ace", "getBurnableShares(address)": "fd980423", "getDeposits(address)": "94f649dd", "getStakerStrategyList(address)": "de44acb6", "getStrategiesWithBurnableShares()": "36a8c500", "initialize(address,address,uint256)": "1794bb3c", "removeStrategiesFromDepositWhitelist(address[])": "b5d8b5b8", "setStrategyWhitelister(address)": "c6656702", "stakerDepositShares(address,address)": "fe243a17", "stakerStrategyListLength(address)": "8b8aac3c", "strategyIsWhitelistedForDeposit(address)": "663c1de4", "strategyWhitelister()": "967fc0d2"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"contract IStrategy[]\",\"name\":\"strategiesToWhitelist\",\"type\":\"address[]\"}],\"name\":\"addStrategiesToDepositWhitelist\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract IStrategy\",\"name\":\"strategy\",\"type\":\"address\"}],\"name\":\"burnShares\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"},{\"internalType\":\"contract IStrategy\",\"name\":\"strategy\",\"type\":\"address\"},{\"internalType\":\"contract IERC20\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"expiry\",\"type\":\"uint256\"}],\"name\":\"calculateStrategyDepositDigestHash\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"delegation\",\"outputs\":[{\"internalType\":\"contract IDelegationManager\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract IStrategy\",\"name\":\"strategy\",\"type\":\"address\"},{\"internalType\":\"contract IERC20\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"depositIntoStrategy\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"depositShares\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract IStrategy\",\"name\":\"strategy\",\"type\":\"address\"},{\"internalType\":\"contract IERC20\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"expiry\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"}],\"name\":\"depositIntoStrategyWithSignature\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"depositShares\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract IStrategy\",\"name\":\"strategy\",\"type\":\"address\"}],\"name\":\"getBurnableShares\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"}],\"name\":\"getDeposits\",\"outputs\":[{\"internalType\":\"contract IStrategy[]\",\"name\":\"\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"}],\"name\":\"getStakerStrategyList\",\"outputs\":[{\"internalType\":\"contract IStrategy[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getStrategiesWithBurnableShares\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"initialOwner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"initialStrategyWhitelister\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"initialPausedStatus\",\"type\":\"uint256\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract IStrategy[]\",\"name\":\"strategiesToRemoveFromWhitelist\",\"type\":\"address[]\"}],\"name\":\"removeStrategiesFromDepositWhitelist\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newStrategyWhitelister\",\"type\":\"address\"}],\"name\":\"setStrategyWhitelister\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"internalType\":\"contract IStrategy\",\"name\":\"strategy\",\"type\":\"address\"}],\"name\":\"stakerDepositShares\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"}],\"name\":\"stakerStrategyListLength\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract IStrategy\",\"name\":\"strategy\",\"type\":\"address\"}],\"name\":\"strategyIsWhitelistedForDeposit\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"strategyWhitelister\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/external/eigen-layer/IStrategyManager.sol\":\"IStrategyManager\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://df6f0c459663c9858b6cba2cda1d14a7d05a985bed6d2de72bd8e78c25ee79db\",\"dweb:/ipfs/QmeTTxZ7qVk9rjEv2R4CpCwdf8UMCcRqDNMvzNxHc3Fnn9\"]},\"src/interfaces/external/eigen-layer/IAllocationManager.sol\":{\"keccak256\":\"0xae3e733a2af24b269f9e861eb091a70123dfb662073296af1e0404d3ef38077c\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b35c72a2bdebe1d6a1515a1215e90bb354414de570e3723bd1f764204fe51e86\",\"dweb:/ipfs/QmWibt9unFu2MYSMXmkb8rAtCEpxUxio5Rn2fM43D4cKth\"]},\"src/interfaces/external/eigen-layer/IDelegationManager.sol\":{\"keccak256\":\"0x84e8357193d083bd63c3af2ae3ee1583d1ba734a09d24a897fa6c7785a28b700\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://e4ffb431c1e5283743009f98027dcdb06a42b32789d7251fb21f6a223e3565aa\",\"dweb:/ipfs/Qme9V6VZFkTDuB3aM3XhkntjYJ6Fv3Ud7QuHgorY4iDGXL\"]},\"src/interfaces/external/eigen-layer/ISignatureUtils.sol\":{\"keccak256\":\"0xf0a4f7b91b079e85ad7b27132a751955d09694358cb6494b70a6d4217f248f03\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://0773df5dae86559e56fbced497b20cbe8def225f3f2dadf43cf7728c10cd6bc2\",\"dweb:/ipfs/QmS7YDqmyttHZ8sbX1YB21Z2rk7wsS9hneDTkAcSPbED6E\"]},\"src/interfaces/external/eigen-layer/IStrategy.sol\":{\"keccak256\":\"0x938f287999a7edf7f3a67f6c2aa3205268cf6e5ebc8dbc00302d2886a558b098\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c7e22f4a1ed1cf9fc2f255d98d5af1a85077cfb77bdfaa351348f4bb28bca872\",\"dweb:/ipfs/QmeCA5afkHVoW5FVhDmaVNUwV4tN6YzwKyHyC9dJpffRof\"]},\"src/interfaces/external/eigen-layer/IStrategyManager.sol\":{\"keccak256\":\"0xf8557b7be6db605615100f77fb6a423350afd25709c78badc077063b16db80b9\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://5220c4e8c2d2d5b50f3ea0e88d8d91660be87340ffb4621f3180a7ed8fc977eb\",\"dweb:/ipfs/QmPdgaqa7DhzmMya6wB31K1Voky7iSBF2SBEVR6VPahEuA\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "contract IStrategy[]", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "addStrategiesToDepositWhitelist"}, {"inputs": [{"internalType": "contract IStrategy", "name": "strategy", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "burnShares"}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}, {"internalType": "contract IStrategy", "name": "strategy", "type": "address"}, {"internalType": "contract IERC20", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint256", "name": "expiry", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "calculateStrategyDepositDigestHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "delegation", "outputs": [{"internalType": "contract IDelegationManager", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "contract IStrategy", "name": "strategy", "type": "address"}, {"internalType": "contract IERC20", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "depositIntoStrategy", "outputs": [{"internalType": "uint256", "name": "depositShares", "type": "uint256"}]}, {"inputs": [{"internalType": "contract IStrategy", "name": "strategy", "type": "address"}, {"internalType": "contract IERC20", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "staker", "type": "address"}, {"internalType": "uint256", "name": "expiry", "type": "uint256"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "depositIntoStrategyWithSignature", "outputs": [{"internalType": "uint256", "name": "depositShares", "type": "uint256"}]}, {"inputs": [{"internalType": "contract IStrategy", "name": "strategy", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getBurnableShares", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getDeposits", "outputs": [{"internalType": "contract IStrategy[]", "name": "", "type": "address[]"}, {"internalType": "uint256[]", "name": "", "type": "uint256[]"}]}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getStakerStrategyList", "outputs": [{"internalType": "contract IStrategy[]", "name": "", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getStrategiesWithBurnableShares", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}, {"internalType": "uint256[]", "name": "", "type": "uint256[]"}]}, {"inputs": [{"internalType": "address", "name": "initialOwner", "type": "address"}, {"internalType": "address", "name": "initialStrategyW<PERSON><PERSON>ster", "type": "address"}, {"internalType": "uint256", "name": "initialPausedStatus", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "contract IStrategy[]", "name": "strategiesToRemoveFromWhitelist", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "removeStrategiesFromDepositWhitelist"}, {"inputs": [{"internalType": "address", "name": "newStrategy<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setStrategy<PERSON><PERSON><PERSON><PERSON>"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "contract IStrategy", "name": "strategy", "type": "address"}], "stateMutability": "view", "type": "function", "name": "stakerDepositShares", "outputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "stateMutability": "view", "type": "function", "name": "stakerStrategyListLength", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "contract IStrategy", "name": "strategy", "type": "address"}], "stateMutability": "view", "type": "function", "name": "strategyIsWhitelistedForDeposit", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "strategy<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/external/eigen-layer/IStrategyManager.sol": "IStrategyManager"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7", "urls": ["bzz-raw://df6f0c459663c9858b6cba2cda1d14a7d05a985bed6d2de72bd8e78c25ee79db", "dweb:/ipfs/QmeTTxZ7qVk9rjEv2R4CpCwdf8UMCcRqDNMvzNxHc3Fnn9"], "license": "MIT"}, "src/interfaces/external/eigen-layer/IAllocationManager.sol": {"keccak256": "0xae3e733a2af24b269f9e861eb091a70123dfb662073296af1e0404d3ef38077c", "urls": ["bzz-raw://b35c72a2bdebe1d6a1515a1215e90bb354414de570e3723bd1f764204fe51e86", "dweb:/ipfs/QmWibt9unFu2MYSMXmkb8rAtCEpxUxio5Rn2fM43D4cKth"], "license": "BUSL-1.1"}, "src/interfaces/external/eigen-layer/IDelegationManager.sol": {"keccak256": "0x84e8357193d083bd63c3af2ae3ee1583d1ba734a09d24a897fa6c7785a28b700", "urls": ["bzz-raw://e4ffb431c1e5283743009f98027dcdb06a42b32789d7251fb21f6a223e3565aa", "dweb:/ipfs/Qme9V6VZFkTDuB3aM3XhkntjYJ6Fv3Ud7QuHgorY4iDGXL"], "license": "BUSL-1.1"}, "src/interfaces/external/eigen-layer/ISignatureUtils.sol": {"keccak256": "0xf0a4f7b91b079e85ad7b27132a751955d09694358cb6494b70a6d4217f248f03", "urls": ["bzz-raw://0773df5dae86559e56fbced497b20cbe8def225f3f2dadf43cf7728c10cd6bc2", "dweb:/ipfs/QmS7YDqmyttHZ8sbX1YB21Z2rk7wsS9hneDTkAcSPbED6E"], "license": "BUSL-1.1"}, "src/interfaces/external/eigen-layer/IStrategy.sol": {"keccak256": "0x938f287999a7edf7f3a67f6c2aa3205268cf6e5ebc8dbc00302d2886a558b098", "urls": ["bzz-raw://c7e22f4a1ed1cf9fc2f255d98d5af1a85077cfb77bdfaa351348f4bb28bca872", "dweb:/ipfs/QmeCA5afkHVoW5FVhDmaVNUwV4tN6YzwKyHyC9dJpffRof"], "license": "BUSL-1.1"}, "src/interfaces/external/eigen-layer/IStrategyManager.sol": {"keccak256": "0xf8557b7be6db605615100f77fb6a423350afd25709c78badc077063b16db80b9", "urls": ["bzz-raw://5220c4e8c2d2d5b50f3ea0e88d8d91660be87340ffb4621f3180a7ed8fc977eb", "dweb:/ipfs/QmPdgaqa7DhzmMya6wB31K1Voky7iSBF2SBEVR6VPahEuA"], "license": "BUSL-1.1"}}, "version": 1}, "id": 82}