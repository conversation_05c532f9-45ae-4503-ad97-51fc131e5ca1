{"abi": [{"type": "constructor", "inputs": [{"name": "name_", "type": "string", "internalType": "string"}, {"name": "version_", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "acceptProposedImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "create", "inputs": [{"name": "version", "type": "uint256", "internalType": "uint256"}, {"name": "owner", "type": "address", "internalType": "address"}, {"name": "initParams", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "instance", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "entities", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "entityAt", "inputs": [{"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "implementationAt", "inputs": [{"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "implementations", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isBlacklisted", "inputs": [{"name": "version", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isEntity", "inputs": [{"name": "entity", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "proposalAt", "inputs": [{"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "proposals", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "proposeImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setBlacklistStatus", "inputs": [{"name": "version", "type": "uint256", "internalType": "uint256"}, {"name": "flag", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "AcceptProposedImplementation", "inputs": [{"name": "implementation", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Created", "inputs": [{"name": "instance", "type": "address", "indexed": true, "internalType": "address"}, {"name": "version", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "initParams", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "initParams", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "ProposeImplementation", "inputs": [{"name": "implementation", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetBlacklistStatus", "inputs": [{"name": "version", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "flag", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "error", "name": "BlacklistedVersion", "inputs": [{"name": "version", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ImplementationAlreadyAccepted", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ImplementationAlreadyProposed", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ImplementationNotProposed", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "OutOfBounds", "inputs": [{"name": "index", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "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", "sourceMap": "150:4316:73:-:0;;;312:169;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;395:47;;;;;;;;;;;;-1:-1:-1;;;395:47:73;;;;;;426:5;433:8;395:19;:47::i;:::-;373:69;;452:22;:20;:22::i;:::-;312:169;;150:4316;;656:343:113;761:7;986:4;970:22;;969:23;941:1;908:12;922:4;928:7;856:80;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;846:91;;;;;;838:100;;:104;;;;:::i;:::-;810:146;;;;;;2258:25:192;;2246:2;2231:18;;2112:177;810:146:113;;;;;;;;;;;;;787:179;;;;;;:205;780:212;;656:343;;;;;:::o;7709:422:3:-;7824:30;7857:26;:24;:26::i;:::-;7898:15;;;;-1:-1:-1;7898:15:3;;;;;7894:76;;;7936:23;;-1:-1:-1;;;7936:23:3;;;;;;;;;;;7894:76;7983:14;;-1:-1:-1;;;;;7983:14:3;;;:34;7979:146;;8033:33;;-1:-1:-1;;;;;;8033:33:3;-1:-1:-1;;;;;8033:33:3;;;;;8085:29;;2438:50:192;;;8085:29:3;;2426:2:192;2411:18;8085:29:3;;;;;;;7979:146;7758:373;7709:422::o;9071:205::-;9129:30;;3147:66;9186:27;9171:42;9071:205;-1:-1:-1;;9071:205:3:o;14:127:192:-;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:983;235:6;243;296:2;284:9;275:7;271:23;267:32;264:52;;;312:1;309;302:12;264:52;339:16;;-1:-1:-1;;;;;404:14:192;;;401:34;;;431:1;428;421:12;401:34;469:6;458:9;454:22;444:32;;514:7;507:4;503:2;499:13;495:27;485:55;;536:1;533;526:12;485:55;565:2;559:9;587:2;583;580:10;577:36;;;593:18;;:::i;:::-;668:2;662:9;636:2;722:13;;-1:-1:-1;;718:22:192;;;742:2;714:31;710:40;698:53;;;766:18;;;786:22;;;763:46;760:72;;;812:18;;:::i;:::-;852:10;848:2;841:22;887:2;879:6;872:18;929:7;922:4;917:2;913;909:11;905:22;902:35;899:55;;;950:1;947;940:12;899:55;1003:2;996:4;992:2;988:13;981:4;973:6;969:17;963:43;1050:1;1043:4;1038:2;1030:6;1026:15;1022:26;1015:37;1071:6;1061:16;;;;;;;1117:4;1106:9;1102:20;1096:27;1086:37;;146:983;;;;;:::o;1134:212::-;1176:3;1214:5;1208:12;1258:6;1251:4;1244:5;1240:16;1235:3;1229:36;1320:1;1284:16;;1309:13;;;-1:-1:-1;1284:16:192;;1134:212;-1:-1:-1;1134:212:192:o;1351:526::-;1689:33;1684:3;1677:46;1659:3;1745:66;1771:39;1806:2;1801:3;1797:12;1789:6;1771:39;:::i;:::-;1763:6;1745:66;:::i;:::-;1820:21;;;-1:-1:-1;;1868:2:192;1857:14;;1351:526;-1:-1:-1;;1351:526:192:o;1882:225::-;1949:9;;;1970:11;;;1967:134;;;2023:10;2018:3;2014:20;2011:1;2004:31;2058:4;2055:1;2048:15;2086:4;2083:1;2076:15;2294:200;150:4316:73;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "150:4316:73:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;845:130;;;;;;:::i;:::-;;:::i;:::-;;;567:14:192;;560:22;542:41;;530:2;515:18;845:130:73;;;;;;;;539:111;;;:::i;:::-;;;740:25:192;;;728:2;713:18;539:111:73;594:177:192;1010:125:73;;;:::i;1856:187::-;;;;;;:::i;:::-;;:::i;:::-;;3409:838;;;;;;:::i;:::-;;:::i;:::-;;;-1:-1:-1;;;;;2323:32:192;;;2305:51;;2293:2;2278:18;3409:838:73;2159:203:192;1656:133:73;;;;;;:::i;:::-;1719:4;1742:40;;;:31;4381:19;1742:31;:40;;;;;;;;;1656:133;1345:113;;;:::i;1493:128::-;;;;;;:::i;:::-;;:::i;2957:417::-;;;;;;:::i;:::-;;:::i;3155:101:1:-;;;:::i;2441:144::-;1313:22;2570:8;-1:-1:-1;;;;;2570:8:1;2441:144;;1170:140:73;;;;;;:::i;:::-;;:::i;2442:480::-;;;;;;:::i;:::-;;:::i;685:125::-;;;;;;:::i;:::-;;:::i;2078:329::-;;;;;;:::i;:::-;;:::i;3405:215:1:-;;;;;;:::i;:::-;;:::i;845:130:73:-;902:4;925:43;4381:19;961:6;925:35;:43::i;:::-;918:50;845:130;-1:-1:-1;;845:130:73:o;539:111::-;582:7;608:35;4381:19;608:33;:35::i;:::-;601:42;;539:111;:::o;1010:125::-;1060:7;1086:42;4381:19;1086:33;;:40;:42::i;1856:187::-;4158:30:3;4191:26;:24;:26::i;:::-;4302:15;;4158:59;;-1:-1:-1;4302:15:3;-1:-1:-1;;;4302:15:3;;;4301:16;;4348:14;;4279:19;4724:16;;:34;;;;;4744:14;4724:34;4704:54;;4768:17;4788:11;:16;;4803:1;4788:16;:50;;;;-1:-1:-1;4816:4:3;4808:25;:30;4788:50;4768:70;;4854:12;4853:13;:30;;;;;4871:12;4870:13;4853:30;4849:91;;;4906:23;;-1:-1:-1;;;4906:23:3;;;;;;;;;;;4849:91;4949:18;;-1:-1:-1;;4949:18:3;4966:1;4949:18;;;4977:67;;;;5011:22;;-1:-1:-1;;;;5011:22:3;-1:-1:-1;;;5011:22:3;;;4977:67;1928:14:73::1;1945:27;::::0;;::::1;1956:4:::0;1945:27:::1;:::i;:::-;1928:44;;1982:22;1997:6;1982:14;:22::i;:::-;2019:17;2031:4;;2019:17;;;;;;;:::i;:::-;;;;;;;;1918:125;5068:14:3::0;5064:101;;;5098:23;;-1:-1:-1;;;;5098:23:3;;;5140:14;;-1:-1:-1;3704:50:192;;5140:14:3;;3692:2:192;3677:18;5140:14:3;;;;;;;5064:101;4092:1079;;;;;1856:187:73;;:::o;3409:838::-;3502:16;4381:19;3599:26;:17;;;:24;:26::i;:::-;3588:7;:37;3584:95;;3648:20;;-1:-1:-1;;;3648:20:73;;;;;740:25:192;;;713:18;;3648:20:73;;;;;;;;3584:95;3692:24;;;;:15;;;:24;;;;;;;;3688:89;;;3739:27;;-1:-1:-1;;;3739:27:73;;;;;740:25:192;;;713:18;;3739:27:73;594:177:192;3688:89:73;3786:22;3811:29;:17;;;3832:7;3811:20;:29::i;:::-;3786:54;-1:-1:-1;3850:12:73;3892:7;3901:5;3908:10;;3920:19;:1;:17;:19::i;:::-;3875:65;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;3865:76;;;;;;3850:91;;4021:4;4044:14;4060:5;4110:10;;4067:55;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;4067:55:73;;;;;;;;;;;;;;-1:-1:-1;;;;;4067:55:73;-1:-1:-1;;;4067:55:73;;;3983:153;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;3951:195:73;-1:-1:-1;4156:24:73;:1;3951:195;4156:14;:24::i;:::-;;4222:5;-1:-1:-1;;;;;4195:45:73;4213:7;4203:8;-1:-1:-1;;;;;4195:45:73;;4229:10;;4195:45;;;;;;;:::i;:::-;;;;;;;;3520:727;;;3409:838;;;;;;:::o;1345:113::-;1389:7;1415:36;4381:19;1415:27;;:34;:36::i;1493:128::-;1551:7;1577:37;:27;4381:19;1577:27;1608:5;1577:30;:37::i;2957:417::-;2334:13:1;:11;:13::i;:::-;4381:19:73;3107:36:::1;:11;::::0;::::1;3128:14:::0;3107:20:::1;:36::i;:::-;3102:116;;3166:41;::::0;-1:-1:-1;;;3166:41:73;;-1:-1:-1;;;;;2323:32:192;;3166:41:73::1;::::0;::::1;2305:51:192::0;2278:18;;3166:41:73::1;2159:203:192::0;3102:116:73::1;3227:34;:11;::::0;::::1;3246:14:::0;3227:18:::1;:34::i;:::-;-1:-1:-1::0;3271:37:73::1;:17;::::0;::::1;3293:14:::0;3271:21:::1;:37::i;:::-;-1:-1:-1::0;3323:44:73::1;::::0;-1:-1:-1;;;;;2323:32:192;;2305:51;;3323:44:73::1;::::0;2293:2:192;2278:18;3323:44:73::1;;;;;;;;3038:336;2957:417:::0;:::o;3155:101:1:-;2334:13;:11;:13::i;:::-;3219:30:::1;3246:1;3219:18;:30::i;:::-;3155:101::o:0;1170:140:73:-;1234:7;1260:43;:33;4381:19;1260:33;1297:5;1260:36;:43::i;2442:480::-;4381:19;2574:42;:17;;;2601:14;2574:26;:42::i;:::-;2570:125;;;2639:45;;-1:-1:-1;;;2639:45:73;;-1:-1:-1;;;;;2323:32:192;;2639:45:73;;;2305:51:192;2278:18;;2639:45:73;2159:203:192;2570:125:73;2708:36;:11;;;2729:14;2708:20;:36::i;:::-;2704:119;;;2767:45;;-1:-1:-1;;;2767:45:73;;-1:-1:-1;;;;;2323:32:192;;2767:45:73;;;2305:51:192;2278:18;;2767:45:73;2159:203:192;2704:119:73;2832:31;:11;;;2848:14;2832:15;:31::i;:::-;-1:-1:-1;2878:37:73;;-1:-1:-1;;;;;2323:32:192;;2305:51;;2878:37:73;;2293:2:192;2278:18;2878:37:73;2159:203:192;685:125:73;741:7;767:36;4381:19;797:5;767:29;:36::i;2078:329::-;2334:13:1;:11;:13::i;:::-;4381:19:73;2232:26:::1;:17;::::0;::::1;:24;:26::i;:::-;2221:7;:37;2217:95;;2281:20;::::0;-1:-1:-1;;;2281:20:73;;::::1;::::0;::::1;740:25:192::0;;;713:18;;2281:20:73::1;594:177:192::0;2217:95:73::1;2321:24;::::0;;;:15:::1;::::0;::::1;:24;::::0;;;;;;;;:31;;-1:-1:-1;;2321:31:73::1;::::0;::::1;;::::0;;::::1;::::0;;;2367:33;;5059:25:192;;;5100:18;;;5093:50;2367:33:73::1;::::0;5032:18:192;2367:33:73::1;;;;;;;2153:254;2078:329:::0;;:::o;3405:215:1:-;2334:13;:11;:13::i;:::-;-1:-1:-1;;;;;3489:22:1;::::1;3485:91;;3534:31;::::0;-1:-1:-1;;;3534:31:1;;3562:1:::1;3534:31;::::0;::::1;2305:51:192::0;2278:18;;3534:31:1::1;2159:203:192::0;3485:91:1::1;3585:28;3604:8;3585:18;:28::i;:::-;3405:215:::0;:::o;10284:165:72:-;-1:-1:-1;;;;;10417:23:72;;10364:4;5006:21;;;:14;;;:21;;;;;;:26;;10387:55;10380:62;10284:165;-1:-1:-1;;;10284:165:72:o;10530:115::-;10593:7;10619:19;10627:3;5202:18;;5120:107;9071:205:3;9129:30;;3147:66;9186:27;8819:122;1847:127:1;6929:20:3;:18;:20::i;:::-;1929:38:1::1;1954:12;1929:24;:38::i;10987:156:72:-:0;11061:7;11111:22;11115:3;11127:5;11111:3;:22::i;9332:150::-;9402:4;9425:50;9430:3;-1:-1:-1;;;;;9450:23:72;;9425:4;:50::i;2658:162:1:-;966:10:5;2717:7:1;1313:22;2570:8;-1:-1:-1;;;;;2570:8:1;;2441:144;2717:7;-1:-1:-1;;;;;2717:23:1;;2713:101;;2763:40;;-1:-1:-1;;;2763:40:1;;966:10:5;2763:40:1;;;2305:51:192;2278:18;;2763:40:1;2159:203:192;9650:156:72;9723:4;9746:53;9754:3;-1:-1:-1;;;;;9774:23:72;;9746:7;:53::i;3774:248:1:-;1313:22;3923:8;;-1:-1:-1;;;;;;3941:19:1;;-1:-1:-1;;;;;3941:19:1;;;;;;;;3975:40;;3923:8;;;;;3975:40;;3847:24;;3975:40;3837:185;;3774:248;:::o;7082:141:3:-;7149:17;:15;:17::i;:::-;7144:73;;7189:17;;-1:-1:-1;;;7189:17:3;;;;;;;;;;;1980:235:1;6929:20:3;:18;:20::i;5569:118:72:-;5636:7;5662:3;:11;;5674:5;5662:18;;;;;;;;:::i;:::-;;;;;;;;;5655:25;;5569:118;;;;:::o;2336:406::-;2399:4;5006:21;;;:14;;;:21;;;;;;2415:321;;-1:-1:-1;2457:23:72;;;;;;;;:11;:23;;;;;;;;;;;;;2639:18;;2615:21;;;:14;;;:21;;;;;;:42;;;;2671:11;;2415:321;-1:-1:-1;2720:5:72;2713:12;;2910:1368;2976:4;3105:21;;;:14;;;:21;;;;;;3141:13;;3137:1135;;3508:18;3529:12;3540:1;3529:8;:12;:::i;:::-;3575:18;;3508:33;;-1:-1:-1;3555:17:72;;3575:22;;3596:1;;3575:22;:::i;:::-;3555:42;;3630:9;3616:10;:23;3612:378;;3659:17;3679:3;:11;;3691:9;3679:22;;;;;;;;:::i;:::-;;;;;;;;;3659:42;;3826:9;3800:3;:11;;3812:10;3800:23;;;;;;;;:::i;:::-;;;;;;;;;;;;:35;;;;3939:25;;;:14;;;:25;;;;;:36;;;3612:378;4068:17;;:3;;:17;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;4171:3;:14;;:21;4186:5;4171:21;;;;;;;;;;;4164:28;;;4214:4;4207:11;;;;;;;3137:1135;4256:5;4249:12;;;;;8485:120:3;8535:4;8558:26;:24;:26::i;:::-;:40;-1:-1:-1;;;8558:40:3;;;;;;-1:-1:-1;8485:120:3:o;-1:-1:-1:-;;;;;;;;:::o;14:131:192:-;-1:-1:-1;;;;;89:31:192;;79:42;;69:70;;135:1;132;125:12;150:247;209:6;262:2;250:9;241:7;237:23;233:32;230:52;;;278:1;275;268:12;230:52;317:9;304:23;336:31;361:5;336:31;:::i;776:347::-;827:8;837:6;891:3;884:4;876:6;872:17;868:27;858:55;;909:1;906;899:12;858:55;-1:-1:-1;932:20:192;;975:18;964:30;;961:50;;;1007:1;1004;997:12;961:50;1044:4;1036:6;1032:17;1020:29;;1096:3;1089:4;1080:6;1072;1068:19;1064:30;1061:39;1058:59;;;1113:1;1110;1103:12;1058:59;776:347;;;;;:::o;1128:409::-;1198:6;1206;1259:2;1247:9;1238:7;1234:23;1230:32;1227:52;;;1275:1;1272;1265:12;1227:52;1315:9;1302:23;1348:18;1340:6;1337:30;1334:50;;;1380:1;1377;1370:12;1334:50;1419:58;1469:7;1460:6;1449:9;1445:22;1419:58;:::i;:::-;1496:8;;1393:84;;-1:-1:-1;1128:409:192;-1:-1:-1;;;;1128:409:192:o;1542:612::-;1630:6;1638;1646;1654;1707:2;1695:9;1686:7;1682:23;1678:32;1675:52;;;1723:1;1720;1713:12;1675:52;1759:9;1746:23;1736:33;;1819:2;1808:9;1804:18;1791:32;1832:31;1857:5;1832:31;:::i;:::-;1882:5;-1:-1:-1;1938:2:192;1923:18;;1910:32;1965:18;1954:30;;1951:50;;;1997:1;1994;1987:12;1951:50;2036:58;2086:7;2077:6;2066:9;2062:22;2036:58;:::i;:::-;1542:612;;;;-1:-1:-1;2113:8:192;-1:-1:-1;;;;1542:612:192:o;2367:180::-;2426:6;2479:2;2467:9;2458:7;2454:23;2450:32;2447:52;;;2495:1;2492;2485:12;2447:52;-1:-1:-1;2518:23:192;;2367:180;-1:-1:-1;2367:180:192:o;2552:341::-;2617:6;2625;2678:2;2666:9;2657:7;2653:23;2649:32;2646:52;;;2694:1;2691;2684:12;2646:52;2730:9;2717:23;2707:33;;2790:2;2779:9;2775:18;2762:32;2837:5;2830:13;2823:21;2816:5;2813:32;2803:60;;2859:1;2856;2849:12;2803:60;2882:5;2872:15;;;2552:341;;;;;:::o;3158:388::-;3315:2;3304:9;3297:21;3354:6;3349:2;3338:9;3334:18;3327:34;3411:6;3403;3398:2;3387:9;3383:18;3370:48;3467:1;3438:22;;;3462:2;3434:31;;;3427:42;;;;3530:2;3509:15;;;-1:-1:-1;;3505:29:192;3490:45;3486:54;;3158:388;-1:-1:-1;3158:388:192:o;3765:499::-;4018:6;4013:3;4006:19;4080:26;4076:31;4067:6;4063:2;4059:15;4055:53;4050:2;4045:3;4041:12;4034:75;4153:6;4145;4140:2;4135:3;4131:12;4118:42;4219:2;4179:16;;4211:11;;;4204:27;4255:2;4247:11;;3765:499;-1:-1:-1;;;3765:499:192:o;4269:617::-;4435:4;4481:1;4477;4472:3;4468:11;4464:19;4522:2;4514:6;4510:15;4499:9;4492:34;4574:2;4566:6;4562:15;4557:2;4546:9;4542:18;4535:43;;4614:2;4609;4598:9;4594:18;4587:30;4646:6;4640:13;4689:6;4684:2;4673:9;4669:18;4662:34;4749:6;4744:2;4736:6;4732:15;4726:3;4715:9;4711:19;4705:51;4806:1;4800:3;4791:6;4780:9;4776:22;4772:32;4765:43;4876:3;4869:2;4865:7;4860:2;4852:6;4848:15;4844:29;4833:9;4829:45;4825:55;4817:63;;;4269:617;;;;;;:::o;5154:127::-;5215:10;5210:3;5206:20;5203:1;5196:31;5246:4;5243:1;5236:15;5270:4;5267:1;5260:15;5286:225;5353:9;;;5374:11;;;5371:134;;;5427:10;5422:3;5418:20;5415:1;5408:31;5462:4;5459:1;5452:15;5490:4;5487:1;5480:15;5516:127;5577:10;5572:3;5568:20;5565:1;5558:31;5608:4;5605:1;5598:15;5632:4;5629:1;5622:15", "linkReferences": {}, "immutableReferences": {"57022": [{"start": 411, "length": 32}, {"start": 660, "length": 32}, {"start": 709, "length": 32}, {"start": 756, "length": 32}, {"start": 1121, "length": 32}, {"start": 1553, "length": 32}, {"start": 1600, "length": 32}, {"start": 1648, "length": 32}, {"start": 1852, "length": 32}, {"start": 1892, "length": 32}, {"start": 2108, "length": 32}, {"start": 2155, "length": 32}]}}, "methodIdentifiers": {"acceptProposedImplementation(address)": "67f72122", "create(uint256,address,bytes)": "46fbcbb2", "entities()": "1615fd03", "entityAt(uint256)": "d46bd10c", "implementationAt(uint256)": "94341e49", "implementations()": "30e9012c", "initialize(bytes)": "439fab91", "isBlacklisted(uint256)": "4752f9ef", "isEntity(address)": "14887c58", "owner()": "8da5cb5b", "proposalAt(uint256)": "5bb838cb", "proposals()": "55ef20e6", "proposeImplementation(address)": "9dd18c15", "renounceOwnership()": "715018a6", "setBlacklistStatus(uint256,bool)": "e4ccf770", "transferOwnership(address)": "f2fde38b"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name_\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"version_\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"version\",\"type\":\"uint256\"}],\"name\":\"BlacklistedVersion\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"ImplementationAlreadyAccepted\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"ImplementationAlreadyProposed\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"ImplementationNotProposed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"OutOfBounds\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"AcceptProposedImplementation\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"instance\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"version\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"initParams\",\"type\":\"bytes\"}],\"name\":\"Created\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"initParams\",\"type\":\"bytes\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"ProposeImplementation\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"version\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"flag\",\"type\":\"bool\"}],\"name\":\"SetBlacklistStatus\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"acceptProposedImplementation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"version\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"initParams\",\"type\":\"bytes\"}],\"name\":\"create\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"instance\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"entities\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"entityAt\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"implementationAt\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"implementations\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"version\",\"type\":\"uint256\"}],\"name\":\"isBlacklisted\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"entity\",\"type\":\"address\"}],\"name\":\"isEntity\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"proposalAt\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"proposals\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"proposeImplementation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"version\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"flag\",\"type\":\"bool\"}],\"name\":\"setBlacklistStatus\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}]},\"events\":{\"Initialized(bytes)\":{\"params\":{\"initParams\":\"The initialization parameters.\"}},\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"}},\"kind\":\"dev\",\"methods\":{\"acceptProposedImplementation(address)\":{\"params\":{\"implementation\":\"The address of the proposed implementation to approve.\"}},\"create(uint256,address,bytes)\":{\"params\":{\"initParams\":\"Calldata to be passed for initialization of the new proxy instance.\",\"owner\":\"The address that will become the owner of the proxy.\",\"version\":\"The version index of the implementation to use.\"},\"returns\":{\"instance\":\"The address of the newly deployed proxy contract.\"}},\"initialize(bytes)\":{\"params\":{\"initParams\":\"The initialization parameters.\"}},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"proposeImplementation(address)\":{\"params\":{\"implementation\":\"The address of the proposed implementation contract.\"}},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"setBlacklistStatus(uint256,bool)\":{\"params\":{\"flag\":\"True to blacklist, false to unblacklist.\",\"version\":\"The version index to update.\"}},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"errors\":{\"BlacklistedVersion(uint256)\":[{\"notice\":\"Thrown when trying to use an implementation version that is blacklisted.\"}],\"ImplementationAlreadyAccepted(address)\":[{\"notice\":\"Thrown when an implementation is already in the accepted list.\"}],\"ImplementationAlreadyProposed(address)\":[{\"notice\":\"Thrown when an implementation has already been proposed.\"}],\"ImplementationNotProposed(address)\":[{\"notice\":\"Thrown when attempting to accept an implementation that was never proposed.\"}],\"OutOfBounds(uint256)\":[{\"notice\":\"Thrown when attempting to access an index outside the valid range.\"}]},\"events\":{\"AcceptProposedImplementation(address)\":{\"notice\":\"Emitted when a proposed implementation is accepted.\"},\"Created(address,uint256,address,bytes)\":{\"notice\":\"Emitted when a new proxy instance is successfully deployed.\"},\"Initialized(bytes)\":{\"notice\":\"Emitted once the entity has been initialized.\"},\"ProposeImplementation(address)\":{\"notice\":\"Emitted when a new implementation is proposed.\"},\"SetBlacklistStatus(uint256,bool)\":{\"notice\":\"Emitted when the blacklist status of a version is updated.\"}},\"kind\":\"user\",\"methods\":{\"acceptProposedImplementation(address)\":{\"notice\":\"Approves a previously proposed implementation, allowing it to be used for deployments.\"},\"create(uint256,address,bytes)\":{\"notice\":\"Deploys a new TransparentUpgradeableProxy using an accepted implementation.\"},\"entities()\":{\"notice\":\"Returns the total number of deployed entities (proxies).\"},\"entityAt(uint256)\":{\"notice\":\"Returns the address of the deployed entity at a given index.\"},\"implementationAt(uint256)\":{\"notice\":\"Returns the implementation address at the given index.\"},\"implementations()\":{\"notice\":\"Returns the total number of accepted implementation contracts.\"},\"initialize(bytes)\":{\"notice\":\"Initializes the factory-created entity with arbitrary initialization data.\"},\"isBlacklisted(uint256)\":{\"notice\":\"Returns whether the given implementation version is blacklisted.\"},\"isEntity(address)\":{\"notice\":\"Returns whether the given address is a deployed entity.\"},\"proposalAt(uint256)\":{\"notice\":\"Returns the address of a proposed implementation at a given index.\"},\"proposals()\":{\"notice\":\"Returns the number of currently proposed (pending) implementations.\"},\"proposeImplementation(address)\":{\"notice\":\"Proposes a new implementation for future deployment.\"},\"setBlacklistStatus(uint256,bool)\":{\"notice\":\"Updates the blacklist status for a specific implementation version.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/factories/Factory.sol\":\"Factory\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol\":{\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6\",\"dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol\":{\"keccak256\":\"0xb25a4f11fa80c702bf5cd85adec90e6f6f507f32f4a8e6f5dbc31e8c10029486\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6917f8a323e7811f041aecd4d9fd6e92455a6fba38a797ac6f6e208c7912b79d\",\"dweb:/ipfs/QmShuYv55wYHGi4EFkDB8QfF7ZCHoKk2efyz3AWY1ExSq7\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol\":{\"keccak256\":\"0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049\",\"dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol\":{\"keccak256\":\"0x8decfa54cec979c824b044b8128cd91d713f72c71fd7dfa54974624d8c949898\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://271f914261a19d87117a777e0924ada545c16191ef9b00cc40b0134fc14ebc70\",\"dweb:/ipfs/QmdvVNWHGHQrGGPonZJs5NuzTevTjZRM2zayKrDJf7WBA2\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/Proxy.sol\":{\"keccak256\":\"0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac\",\"dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol\":{\"keccak256\":\"0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa\",\"dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol\":{\"keccak256\":\"0x46f86003755f50eff00a7c5aaf493ae62e024142b8aec4493a313851d3c14872\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://462c770cb667cc75ab22df5a29c50873b24b267274edf87ac5cfe0112bf4c3f7\",\"dweb:/ipfs/QmTfKk32AkQ2yyYZCYJ44V69EM5t9ryBFC6bRF7FVMHXvj\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol\":{\"keccak256\":\"0x92579f452fe663595a898cbac85d80bb3868a6c9f034f19ba7fbebdfa3b65a4d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d07a888cd4a26c7077a30dd99e9ac30fbe2752bfb37e58c65f06a3b192079ef3\",\"dweb:/ipfs/QmZCQY4FC4ynBv9dha4BBWM1vYxTUJPBdFBS4HEFe6XzVB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0xaaa1d17c1129b127a4a401db2fbd72960e2671474be3d08cae71ccdc42f7624c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cb2f27cd3952aa667e198fba0d9b7bcec52fbb12c16f013c25fe6fb52b29cc0e\",\"dweb:/ipfs/QmeuohBFoeyDPZA9JNCTEDz3VBfBD4EABWuWXVhHAuEpKR\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol\":{\"keccak256\":\"0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd\",\"dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol\":{\"keccak256\":\"0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd\",\"dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Errors.sol\":{\"keccak256\":\"0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf\",\"dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol\":{\"keccak256\":\"0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2\",\"dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol\":{\"keccak256\":\"0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231\",\"dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y\"]},\"src/factories/Factory.sol\":{\"keccak256\":\"0x49c722d8738514e32f003ccb33ad01fdf887629ca8ad833b7ce532e12b345e9a\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://63b5f07133157f37efadc45da9e2793318d4544dc78e41ec363c6a4ed114f280\",\"dweb:/ipfs/QmfXZ2HME9npde4V3rYQy3d2sd8gXJDbUzhvKSQPqcVXYq\"]},\"src/interfaces/factories/IFactory.sol\":{\"keccak256\":\"0x838a308272620b2ae6fdf1f5b425f8ba1439ed24a7baad50599d3fb34eb0bfa7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7a801db63324ec50da072b69c67ca98eb89d026f01a4aa7e8f60d757bc82f86d\",\"dweb:/ipfs/QmaJ6WHAe8vfwX3A8yHyLW2aJKqkbQCKuthRg1QU2f3TuY\"]},\"src/interfaces/factories/IFactoryEntity.sol\":{\"keccak256\":\"0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812\",\"dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx\"]},\"src/libraries/SlotLibrary.sol\":{\"keccak256\":\"0x35dcf16feec83c772add30250b0987ce29ccfbf308d04159701ebf9243444eeb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://97658d0c93ecc8174169e643d5a24c515b55905a2fcaaa6d6ec0188a3dd71a10\",\"dweb:/ipfs/QmS1z7ac8RbMvz15gVQ9YA1cCT5bs6KMHCiDhgcG8Pq8oK\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "uint256", "name": "version_", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "uint256", "name": "version", "type": "uint256"}], "type": "error", "name": "BlacklistedVersion"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "type": "error", "name": "ImplementationAlreadyAccepted"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "type": "error", "name": "ImplementationAlreadyProposed"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "type": "error", "name": "ImplementationNotProposed"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "type": "error", "name": "OutOfBounds"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address", "indexed": false}], "type": "event", "name": "AcceptProposedImplementation", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "instance", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "version", "type": "uint256", "indexed": true}, {"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "bytes", "name": "initParams", "type": "bytes", "indexed": false}], "type": "event", "name": "Created", "anonymous": false}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "initParams", "type": "bytes", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address", "indexed": false}], "type": "event", "name": "ProposeImplementation", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "version", "type": "uint256", "indexed": false}, {"internalType": "bool", "name": "flag", "type": "bool", "indexed": false}], "type": "event", "name": "SetBlacklistStatus", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "acceptProposedImplementation"}, {"inputs": [{"internalType": "uint256", "name": "version", "type": "uint256"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "bytes", "name": "initParams", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "create", "outputs": [{"internalType": "address", "name": "instance", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "entities", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "entityAt", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "implementationAt", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "implementations", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "uint256", "name": "version", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "isBlacklisted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "entity", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isEntity", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "proposalAt", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "proposals", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "proposeImplementation"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [{"internalType": "uint256", "name": "version", "type": "uint256"}, {"internalType": "bool", "name": "flag", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setBlacklistStatus"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}], "devdoc": {"kind": "dev", "methods": {"acceptProposedImplementation(address)": {"params": {"implementation": "The address of the proposed implementation to approve."}}, "create(uint256,address,bytes)": {"params": {"initParams": "Calldata to be passed for initialization of the new proxy instance.", "owner": "The address that will become the owner of the proxy.", "version": "The version index of the implementation to use."}, "returns": {"instance": "The address of the newly deployed proxy contract."}}, "initialize(bytes)": {"params": {"initParams": "The initialization parameters."}}, "owner()": {"details": "Returns the address of the current owner."}, "proposeImplementation(address)": {"params": {"implementation": "The address of the proposed implementation contract."}}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "setBlacklistStatus(uint256,bool)": {"params": {"flag": "True to blacklist, false to unblacklist.", "version": "The version index to update."}}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"acceptProposedImplementation(address)": {"notice": "Approves a previously proposed implementation, allowing it to be used for deployments."}, "create(uint256,address,bytes)": {"notice": "Deploys a new TransparentUpgradeableProxy using an accepted implementation."}, "entities()": {"notice": "Returns the total number of deployed entities (proxies)."}, "entityAt(uint256)": {"notice": "Returns the address of the deployed entity at a given index."}, "implementationAt(uint256)": {"notice": "Returns the implementation address at the given index."}, "implementations()": {"notice": "Returns the total number of accepted implementation contracts."}, "initialize(bytes)": {"notice": "Initializes the factory-created entity with arbitrary initialization data."}, "isBlacklisted(uint256)": {"notice": "Returns whether the given implementation version is blacklisted."}, "isEntity(address)": {"notice": "Returns whether the given address is a deployed entity."}, "proposalAt(uint256)": {"notice": "Returns the address of a proposed implementation at a given index."}, "proposals()": {"notice": "Returns the number of currently proposed (pending) implementations."}, "proposeImplementation(address)": {"notice": "Proposes a new implementation for future deployment."}, "setBlacklistStatus(uint256,bool)": {"notice": "Updates the blacklist status for a specific implementation version."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/factories/Factory.sol": "Factory"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"keccak256": "0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a", "urls": ["bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6", "dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05", "urls": ["bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08", "dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol": {"keccak256": "0xb25a4f11fa80c702bf5cd85adec90e6f6f507f32f4a8e6f5dbc31e8c10029486", "urls": ["bzz-raw://6917f8a323e7811f041aecd4d9fd6e92455a6fba38a797ac6f6e208c7912b79d", "dweb:/ipfs/QmShuYv55wYHGi4EFkDB8QfF7ZCHoKk2efyz3AWY1ExSq7"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol": {"keccak256": "0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e", "urls": ["bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049", "dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol": {"keccak256": "0x8decfa54cec979c824b044b8128cd91d713f72c71fd7dfa54974624d8c949898", "urls": ["bzz-raw://271f914261a19d87117a777e0924ada545c16191ef9b00cc40b0134fc14ebc70", "dweb:/ipfs/QmdvVNWHGHQrGGPonZJs5NuzTevTjZRM2zayKrDJf7WBA2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/Proxy.sol": {"keccak256": "0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd", "urls": ["bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac", "dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol": {"keccak256": "0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c", "urls": ["bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa", "dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol": {"keccak256": "0x46f86003755f50eff00a7c5aaf493ae62e024142b8aec4493a313851d3c14872", "urls": ["bzz-raw://462c770cb667cc75ab22df5a29c50873b24b267274edf87ac5cfe0112bf4c3f7", "dweb:/ipfs/QmTfKk32AkQ2yyYZCYJ44V69EM5t9ryBFC6bRF7FVMHXvj"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol": {"keccak256": "0x92579f452fe663595a898cbac85d80bb3868a6c9f034f19ba7fbebdfa3b65a4d", "urls": ["bzz-raw://d07a888cd4a26c7077a30dd99e9ac30fbe2752bfb37e58c65f06a3b192079ef3", "dweb:/ipfs/QmZCQY4FC4ynBv9dha4BBWM1vYxTUJPBdFBS4HEFe6XzVB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0xaaa1d17c1129b127a4a401db2fbd72960e2671474be3d08cae71ccdc42f7624c", "urls": ["bzz-raw://cb2f27cd3952aa667e198fba0d9b7bcec52fbb12c16f013c25fe6fb52b29cc0e", "dweb:/ipfs/QmeuohBFoeyDPZA9JNCTEDz3VBfBD4EABWuWXVhHAuEpKR"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol": {"keccak256": "0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6", "urls": ["bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd", "dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol": {"keccak256": "0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58", "urls": ["bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd", "dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Errors.sol": {"keccak256": "0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123", "urls": ["bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf", "dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol": {"keccak256": "0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26", "urls": ["bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2", "dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol": {"keccak256": "0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06", "urls": ["bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231", "dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y"], "license": "MIT"}, "src/factories/Factory.sol": {"keccak256": "0x49c722d8738514e32f003ccb33ad01fdf887629ca8ad833b7ce532e12b345e9a", "urls": ["bzz-raw://63b5f07133157f37efadc45da9e2793318d4544dc78e41ec363c6a4ed114f280", "dweb:/ipfs/QmfXZ2HME9npde4V3rYQy3d2sd8gXJDbUzhvKSQPqcVXYq"], "license": "BUSL-1.1"}, "src/interfaces/factories/IFactory.sol": {"keccak256": "0x838a308272620b2ae6fdf1f5b425f8ba1439ed24a7baad50599d3fb34eb0bfa7", "urls": ["bzz-raw://7a801db63324ec50da072b69c67ca98eb89d026f01a4aa7e8f60d757bc82f86d", "dweb:/ipfs/QmaJ6WHAe8vfwX3A8yHyLW2aJKqkbQCKuthRg1QU2f3TuY"], "license": "BUSL-1.1"}, "src/interfaces/factories/IFactoryEntity.sol": {"keccak256": "0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5", "urls": ["bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812", "dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx"], "license": "BUSL-1.1"}, "src/libraries/SlotLibrary.sol": {"keccak256": "0x35dcf16feec83c772add30250b0987ce29ccfbf308d04159701ebf9243444eeb", "urls": ["bzz-raw://97658d0c93ecc8174169e643d5a24c515b55905a2fcaaa6d6ec0188a3dd71a10", "dweb:/ipfs/QmS1z7ac8RbMvz15gVQ9YA1cCT5bs6KMHCiDhgcG8Pq8oK"], "license": "BUSL-1.1"}}, "version": 1}, "id": 73}