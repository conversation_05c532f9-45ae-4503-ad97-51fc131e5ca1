{"abi": [{"type": "function", "name": "ACCEPT_REPORT_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "ADD_SUPPORTED_ASSETS_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "REMOVE_SUPPORTED_ASSETS_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "SET_SECURITY_PARAMS_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "SUBMIT_REPORTS_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "acceptReport", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}, {"name": "priceD18", "type": "uint256", "internalType": "uint256"}, {"name": "timestamp", "type": "uint32", "internalType": "uint32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "addSupportedAssets", "inputs": [{"name": "assets", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getReport", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IOracle.DetailedReport", "components": [{"name": "priceD18", "type": "uint224", "internalType": "uint224"}, {"name": "timestamp", "type": "uint32", "internalType": "uint32"}, {"name": "isSuspicious", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "initParams", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isSupportedAsset", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "removeSupportedAssets", "inputs": [{"name": "assets", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "securityParams", "inputs": [], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IOracle.SecurityParams", "components": [{"name": "maxAbsoluteDeviation", "type": "uint224", "internalType": "uint224"}, {"name": "suspiciousAbsoluteDeviation", "type": "uint224", "internalType": "uint224"}, {"name": "maxRelativeDeviationD18", "type": "uint64", "internalType": "uint64"}, {"name": "suspiciousRelativeDeviationD18", "type": "uint64", "internalType": "uint64"}, {"name": "timeout", "type": "uint32", "internalType": "uint32"}, {"name": "depositInterval", "type": "uint32", "internalType": "uint32"}, {"name": "redeemInterval", "type": "uint32", "internalType": "uint32"}]}], "stateMutability": "view"}, {"type": "function", "name": "setSecurityParams", "inputs": [{"name": "securityParams_", "type": "tuple", "internalType": "struct IOracle.SecurityParams", "components": [{"name": "maxAbsoluteDeviation", "type": "uint224", "internalType": "uint224"}, {"name": "suspiciousAbsoluteDeviation", "type": "uint224", "internalType": "uint224"}, {"name": "maxRelativeDeviationD18", "type": "uint64", "internalType": "uint64"}, {"name": "suspiciousRelativeDeviationD18", "type": "uint64", "internalType": "uint64"}, {"name": "timeout", "type": "uint32", "internalType": "uint32"}, {"name": "depositInterval", "type": "uint32", "internalType": "uint32"}, {"name": "redeemInterval", "type": "uint32", "internalType": "uint32"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON>", "inputs": [{"name": "vault_", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "submitReports", "inputs": [{"name": "reports", "type": "tuple[]", "internalType": "struct IOracle.Report[]", "components": [{"name": "asset", "type": "address", "internalType": "address"}, {"name": "priceD18", "type": "uint224", "internalType": "uint224"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportedAssetAt", "inputs": [{"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "supportedAssets", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "validatePrice", "inputs": [{"name": "priceD18", "type": "uint256", "internalType": "uint256"}, {"name": "asset", "type": "address", "internalType": "address"}], "outputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bool", "internalType": "bool"}, {"name": "isSuspicious", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "vault", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IShareModule"}], "stateMutability": "view"}, {"type": "event", "name": "Initialized", "inputs": [{"name": "initParams", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "ReportAccepted", "inputs": [{"name": "asset", "type": "address", "indexed": true, "internalType": "address"}, {"name": "priceD18", "type": "uint224", "indexed": true, "internalType": "uint224"}, {"name": "timestamp", "type": "uint32", "indexed": true, "internalType": "uint32"}], "anonymous": false}, {"type": "event", "name": "ReportsSubmitted", "inputs": [{"name": "reports", "type": "tuple[]", "indexed": false, "internalType": "struct IOracle.Report[]", "components": [{"name": "asset", "type": "address", "internalType": "address"}, {"name": "priceD18", "type": "uint224", "internalType": "uint224"}]}], "anonymous": false}, {"type": "event", "name": "SecurityParamsSet", "inputs": [{"name": "securityParams", "type": "tuple", "indexed": false, "internalType": "struct IOracle.SecurityParams", "components": [{"name": "maxAbsoluteDeviation", "type": "uint224", "internalType": "uint224"}, {"name": "suspiciousAbsoluteDeviation", "type": "uint224", "internalType": "uint224"}, {"name": "maxRelativeDeviationD18", "type": "uint64", "internalType": "uint64"}, {"name": "suspiciousRelativeDeviationD18", "type": "uint64", "internalType": "uint64"}, {"name": "timeout", "type": "uint32", "internalType": "uint32"}, {"name": "depositInterval", "type": "uint32", "internalType": "uint32"}, {"name": "redeemInterval", "type": "uint32", "internalType": "uint32"}]}], "anonymous": false}, {"type": "event", "name": "<PERSON><PERSON><PERSON>", "inputs": [{"name": "vault", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SupportedAssetsAdded", "inputs": [{"name": "assets", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "SupportedAssetsRemoved", "inputs": [{"name": "assets", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "error", "name": "AlreadySupportedAsset", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "Forbidden", "inputs": []}, {"type": "error", "name": "InvalidPrice", "inputs": [{"name": "priceD18", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidReport", "inputs": []}, {"type": "error", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "timestamp", "type": "uint256", "internalType": "uint256"}, {"name": "minTimestamp", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "UnsupportedAsset", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ZeroValue", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"ACCEPT_REPORT_ROLE()": "56679cf4", "ADD_SUPPORTED_ASSETS_ROLE()": "75a3eea4", "REMOVE_SUPPORTED_ASSETS_ROLE()": "9f79f613", "SET_SECURITY_PARAMS_ROLE()": "074cd353", "SUBMIT_REPORTS_ROLE()": "d4ad5f38", "acceptReport(address,uint256,uint32)": "6c038d03", "addSupportedAssets(address[])": "8fe4d996", "getReport(address)": "a3bdae3e", "initialize(bytes)": "439fab91", "isSupportedAsset(address)": "9be918e6", "removeSupportedAssets(address[])": "c663be59", "securityParams()": "6094fd57", "setSecurityParams((uint224,uint224,uint64,uint64,uint32,uint32,uint32))": "58f3d554", "setVault(address)": "6817031b", "submitReports((address,uint224)[])": "8f88cbfb", "supportedAssetAt(uint256)": "42c74b19", "supportedAssets()": "a80ce55c", "validatePrice(uint256,address)": "816c8850", "vault()": "fbfa77cf"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"}],\"name\":\"AlreadySupportedAsset\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Forbidden\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"priceD18\",\"type\":\"uint256\"}],\"name\":\"InvalidPrice\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidReport\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"minTimestamp\",\"type\":\"uint256\"}],\"name\":\"<PERSON><PERSON>ar<PERSON>\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"}],\"name\":\"UnsupportedAsset\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZeroValue\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"initParams\",\"type\":\"bytes\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint224\",\"name\":\"priceD18\",\"type\":\"uint224\"},{\"indexed\":true,\"internalType\":\"uint32\",\"name\":\"timestamp\",\"type\":\"uint32\"}],\"name\":\"ReportAccepted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"uint224\",\"name\":\"priceD18\",\"type\":\"uint224\"}],\"indexed\":false,\"internalType\":\"struct IOracle.Report[]\",\"name\":\"reports\",\"type\":\"tuple[]\"}],\"name\":\"ReportsSubmitted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"components\":[{\"internalType\":\"uint224\",\"name\":\"maxAbsoluteDeviation\",\"type\":\"uint224\"},{\"internalType\":\"uint224\",\"name\":\"suspiciousAbsoluteDeviation\",\"type\":\"uint224\"},{\"internalType\":\"uint64\",\"name\":\"maxRelativeDeviationD18\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"suspiciousRelativeDeviationD18\",\"type\":\"uint64\"},{\"internalType\":\"uint32\",\"name\":\"timeout\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"depositInterval\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"redeemInterval\",\"type\":\"uint32\"}],\"indexed\":false,\"internalType\":\"struct IOracle.SecurityParams\",\"name\":\"securityParams\",\"type\":\"tuple\"}],\"name\":\"SecurityParamsSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"SetVault\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"assets\",\"type\":\"address[]\"}],\"name\":\"SupportedAssetsAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"assets\",\"type\":\"address[]\"}],\"name\":\"SupportedAssetsRemoved\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"ACCEPT_REPORT_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"ADD_SUPPORTED_ASSETS_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"REMOVE_SUPPORTED_ASSETS_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SET_SECURITY_PARAMS_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SUBMIT_REPORTS_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"priceD18\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"timestamp\",\"type\":\"uint32\"}],\"name\":\"acceptReport\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"assets\",\"type\":\"address[]\"}],\"name\":\"addSupportedAssets\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"}],\"name\":\"getReport\",\"outputs\":[{\"components\":[{\"internalType\":\"uint224\",\"name\":\"priceD18\",\"type\":\"uint224\"},{\"internalType\":\"uint32\",\"name\":\"timestamp\",\"type\":\"uint32\"},{\"internalType\":\"bool\",\"name\":\"isSuspicious\",\"type\":\"bool\"}],\"internalType\":\"struct IOracle.DetailedReport\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"initParams\",\"type\":\"bytes\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"}],\"name\":\"isSupportedAsset\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"assets\",\"type\":\"address[]\"}],\"name\":\"removeSupportedAssets\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"securityParams\",\"outputs\":[{\"components\":[{\"internalType\":\"uint224\",\"name\":\"maxAbsoluteDeviation\",\"type\":\"uint224\"},{\"internalType\":\"uint224\",\"name\":\"suspiciousAbsoluteDeviation\",\"type\":\"uint224\"},{\"internalType\":\"uint64\",\"name\":\"maxRelativeDeviationD18\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"suspiciousRelativeDeviationD18\",\"type\":\"uint64\"},{\"internalType\":\"uint32\",\"name\":\"timeout\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"depositInterval\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"redeemInterval\",\"type\":\"uint32\"}],\"internalType\":\"struct IOracle.SecurityParams\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint224\",\"name\":\"maxAbsoluteDeviation\",\"type\":\"uint224\"},{\"internalType\":\"uint224\",\"name\":\"suspiciousAbsoluteDeviation\",\"type\":\"uint224\"},{\"internalType\":\"uint64\",\"name\":\"maxRelativeDeviationD18\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"suspiciousRelativeDeviationD18\",\"type\":\"uint64\"},{\"internalType\":\"uint32\",\"name\":\"timeout\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"depositInterval\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"redeemInterval\",\"type\":\"uint32\"}],\"internalType\":\"struct IOracle.SecurityParams\",\"name\":\"securityParams_\",\"type\":\"tuple\"}],\"name\":\"setSecurityParams\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault_\",\"type\":\"address\"}],\"name\":\"setVault\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"uint224\",\"name\":\"priceD18\",\"type\":\"uint224\"}],\"internalType\":\"struct IOracle.Report[]\",\"name\":\"reports\",\"type\":\"tuple[]\"}],\"name\":\"submitReports\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"supportedAssetAt\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"supportedAssets\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"priceD18\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"}],\"name\":\"validatePrice\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"isValid\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isSuspicious\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"vault\",\"outputs\":[{\"internalType\":\"contract IShareModule\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"The reported price is structured such that the invariant `shares = assets * price` holds true.Typically used to coordinate deposit, redemption and limit operations across queues, the vault and subvaults.\",\"errors\":{\"AlreadySupportedAsset(address)\":[{\"params\":{\"asset\":\"The address of the already supported asset.\"}}],\"InvalidPrice(uint256)\":[{\"params\":{\"priceD18\":\"The submitted price in 18-decimal fixed-point format.\"}}],\"InvalidReport()\":[{\"details\":\"This includes mismatches in price, timestamp, or incorrect `isSuspicios` flag.\"}],\"TooEarly(uint256,uint256)\":[{\"params\":{\"minTimestamp\":\"The earliest acceptable timestamp based on timeout configuration.\",\"timestamp\":\"The submitted report timestamp.\"}}],\"UnsupportedAsset(address)\":[{\"params\":{\"asset\":\"The address of the unsupported asset.\"}}]},\"events\":{\"Initialized(bytes)\":{\"params\":{\"initParams\":\"The initialization parameters.\"}}},\"kind\":\"dev\",\"methods\":{\"acceptReport(address,uint256,uint32)\":{\"details\":\"Callable only by account with `ACCEPT_REPORT_ROLE`\",\"params\":{\"asset\":\"Address of the asset\",\"priceD18\":\"Timestamp that must match existing suspicious report\",\"timestamp\":\"Timestamp that must match existing suspicious report\"}},\"addSupportedAssets(address[])\":{\"params\":{\"assets\":\"Array of asset addresses to add\"}},\"getReport(address)\":{\"params\":{\"asset\":\"Address of the asset\"}},\"initialize(bytes)\":{\"params\":{\"initParams\":\"The initialization parameters.\"}},\"isSupportedAsset(address)\":{\"params\":{\"asset\":\"Address of the asset\"}},\"removeSupportedAssets(address[])\":{\"params\":{\"assets\":\"Array of asset addresses to remove\"}},\"setSecurityParams((uint224,uint224,uint64,uint64,uint32,uint32,uint32))\":{\"params\":{\"securityParams_\":\"New security settings\"}},\"setVault(address)\":{\"params\":{\"vault_\":\"Address of the vault module\"}},\"submitReports((address,uint224)[])\":{\"details\":\"Processes pending deposit and redemption requests across DepositQueue and RedeemQueue contracts.      The core processing logic is determined by the ShareModule and Queue contracts.      Only callable by accounts with the `SUBMIT_REPORTS_ROLE`.Note: Submitted prices MUST reflect protocol and performance fee deductions, ensuring accurate share issuance.\",\"params\":{\"reports\":\"An array of price reports, each specifying the target asset and its latest price (in 18 decimals).\"}},\"supportedAssetAt(uint256)\":{\"params\":{\"index\":\"Index in the supported asset set\"}},\"validatePrice(uint256,address)\":{\"details\":\"Evaluates both absolute and relative deviation limits to determine whether the price is valid or suspicious.\",\"params\":{\"asset\":\"Address of the asset being evaluated.\",\"priceD18\":\"Price to validate, in 18-decimal fixed-point format.\"},\"returns\":{\"isSuspicious\":\"True if the price exceeds the suspicious deviation threshold.\",\"isValid\":\"True if the price is within maximum allowed deviation.\"}}},\"title\":\"IOracle\",\"version\":1},\"userdoc\":{\"errors\":{\"AlreadySupportedAsset(address)\":[{\"notice\":\"Thrown when attempting to register an asset that is already supported.\"}],\"Forbidden()\":[{\"notice\":\"Thrown when the caller lacks the necessary permission to perform the operation.\"}],\"InvalidPrice(uint256)\":[{\"notice\":\"Thrown when the submitted price violates oracle security rules.\"}],\"InvalidReport()\":[{\"notice\":\"Thrown when a suspicious report fails validation due to unexpected data.\"}],\"TooEarly(uint256,uint256)\":[{\"notice\":\"Thrown when a report is submitted before the required timeout period,         and the previous report was not marked as suspicious.\"}],\"UnsupportedAsset(address)\":[{\"notice\":\"Thrown when an asset is not supported by the oracle.\"}],\"ZeroValue()\":[{\"notice\":\"Thrown when a function receives a zero value where a non-zero value is required.\"}]},\"events\":{\"Initialized(bytes)\":{\"notice\":\"Emitted once the entity has been initialized.\"},\"ReportAccepted(address,uint224,uint32)\":{\"notice\":\"Emitted when a suspicious report is accepted\"},\"ReportsSubmitted((address,uint224)[])\":{\"notice\":\"Emitted when price reports are submitted\"},\"SecurityParamsSet((uint224,uint224,uint64,uint64,uint32,uint32,uint32))\":{\"notice\":\"Emitted when new security parameters are set\"},\"SetVault(address)\":{\"notice\":\"Emitted when vault is set\"},\"SupportedAssetsAdded(address[])\":{\"notice\":\"Emitted when new assets are added\"},\"SupportedAssetsRemoved(address[])\":{\"notice\":\"Emitted when supported assets are removed\"}},\"kind\":\"user\",\"methods\":{\"ACCEPT_REPORT_ROLE()\":{\"notice\":\"Role required to accept suspicious reports\"},\"ADD_SUPPORTED_ASSETS_ROLE()\":{\"notice\":\"Role required to add new supported assets\"},\"REMOVE_SUPPORTED_ASSETS_ROLE()\":{\"notice\":\"Role required to remove supported assets\"},\"SET_SECURITY_PARAMS_ROLE()\":{\"notice\":\"Role required to update security parameters\"},\"SUBMIT_REPORTS_ROLE()\":{\"notice\":\"Role required to submit reports\"},\"acceptReport(address,uint256,uint32)\":{\"notice\":\"Accepts a previously suspicious report\"},\"addSupportedAssets(address[])\":{\"notice\":\"Adds multiple new assets to the supported set\"},\"getReport(address)\":{\"notice\":\"Returns the most recent detailed report for an asset\"},\"initialize(bytes)\":{\"notice\":\"Initializes the factory-created entity with arbitrary initialization data.\"},\"isSupportedAsset(address)\":{\"notice\":\"Checks whether an asset is supported\"},\"removeSupportedAssets(address[])\":{\"notice\":\"Removes assets from the supported set\"},\"securityParams()\":{\"notice\":\"Returns current security parameters\"},\"setSecurityParams((uint224,uint224,uint64,uint64,uint32,uint32,uint32))\":{\"notice\":\"Updates oracle security parameters\"},\"setVault(address)\":{\"notice\":\"Sets the associated vault\"},\"submitReports((address,uint224)[])\":{\"notice\":\"Submits price reports for supported assets.\"},\"supportedAssetAt(uint256)\":{\"notice\":\"Returns the supported asset at a specific index\"},\"supportedAssets()\":{\"notice\":\"Returns total count of supported assets\"},\"validatePrice(uint256,address)\":{\"notice\":\"Validates the given price for a specific asset based on oracle security parameters.\"},\"vault()\":{\"notice\":\"Returns the connected vault module\"}},\"notice\":\"Interface for the vault price oracle responsible for submitting, validating, and propagating price reports.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/oracles/IOracle.sol\":\"IOracle\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/contracts/access/AccessControlUpgradeable.sol\":{\"keccak256\":\"0x0d4d7d19a052a2ef9d17b28450133631188b895e1755747fa8ad0280aadfb534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://838551e10cc07e570b70aee1078e490658d3a0ab8f4adfd4f3f2a565200753da\",\"dweb:/ipfs/QmceT7R3Pqzywyxr3MSEA7VXU4axZAPS6BLy92iV97rGdG\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol\":{\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6\",\"dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/access/extensions/AccessControlEnumerableUpgradeable.sol\":{\"keccak256\":\"0xaec38804089a16494f7d45ebbbeab78fe05e5dc4bc2eae1d2af458fe999fb43c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://04796408e2c4a1051afb23319e9505edb595a3d9c663ff5d6fcfb8e185ab1d93\",\"dweb:/ipfs/Qma8ZTi4kGyqg45SyNibbJSqMF7broiQnJPHhEGLcZyF9U\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol\":{\"keccak256\":\"0x361126a17677994081cd9cb69c3f50cffff6e920d25cb7e428acdb1ae41d1866\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://19ae787a7dd001269cd60a394b1a5261b78925a0fc3a6f927beb2986a9aa56cf\",\"dweb:/ipfs/QmYLfXiuKmcRgTDBEDXMMjXU8t6JxsspUmjxYzqWS55oEv\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/introspection/ERC165Upgradeable.sol\":{\"keccak256\":\"0xc8ed8d2056934b7675b695dec032f2920c2f5c6cf33a17ca85650940675323ab\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3c8ccc75d1cd792d192aa09e54dd49ea35fe85baa9fcd17486f29227d9f29b89\",\"dweb:/ipfs/QmbboSbFUEiM9tdEgBwuTRb7bykFoJXZ7dsSr1PSREJXMr\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0\",\"dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/extensions/IAccessControlEnumerable.sol\":{\"keccak256\":\"0xca774fbe0568762efdc1a7cba31f09549c7fa96dbe97410f4843fa2f0bc000a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0187ffdbf3d61b6d86cba4fcd9826e53d876987d620533ee84c681bdaf0f3ba3\",\"dweb:/ipfs/QmVJDqdJv6uzHY7ifncfv2QJep8XTzS3bGb4s5Exhuv86m\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol\":{\"keccak256\":\"0xb25a4f11fa80c702bf5cd85adec90e6f6f507f32f4a8e6f5dbc31e8c10029486\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6917f8a323e7811f041aecd4d9fd6e92455a6fba38a797ac6f6e208c7912b79d\",\"dweb:/ipfs/QmShuYv55wYHGi4EFkDB8QfF7ZCHoKk2efyz3AWY1ExSq7\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol\":{\"keccak256\":\"0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049\",\"dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol\":{\"keccak256\":\"0x8decfa54cec979c824b044b8128cd91d713f72c71fd7dfa54974624d8c949898\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://271f914261a19d87117a777e0924ada545c16191ef9b00cc40b0134fc14ebc70\",\"dweb:/ipfs/QmdvVNWHGHQrGGPonZJs5NuzTevTjZRM2zayKrDJf7WBA2\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/Proxy.sol\":{\"keccak256\":\"0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac\",\"dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol\":{\"keccak256\":\"0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa\",\"dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol\":{\"keccak256\":\"0x46f86003755f50eff00a7c5aaf493ae62e024142b8aec4493a313851d3c14872\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://462c770cb667cc75ab22df5a29c50873b24b267274edf87ac5cfe0112bf4c3f7\",\"dweb:/ipfs/QmTfKk32AkQ2yyYZCYJ44V69EM5t9ryBFC6bRF7FVMHXvj\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol\":{\"keccak256\":\"0x92579f452fe663595a898cbac85d80bb3868a6c9f034f19ba7fbebdfa3b65a4d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d07a888cd4a26c7077a30dd99e9ac30fbe2752bfb37e58c65f06a3b192079ef3\",\"dweb:/ipfs/QmZCQY4FC4ynBv9dha4BBWM1vYxTUJPBdFBS4HEFe6XzVB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol\":{\"keccak256\":\"0xb5afb8e8eebc4d1c6404df2f5e1e6d2c3d24fd01e5dfc855314951ecfaae462d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://78586466c424f076c6a2a551d848cfbe3f7c49e723830807598484a1047b3b34\",\"dweb:/ipfs/Qmb717ovcFxm7qgNKEShiV6M9SPR3v1qnNpAGH84D6w29p\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0xaaa1d17c1129b127a4a401db2fbd72960e2671474be3d08cae71ccdc42f7624c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cb2f27cd3952aa667e198fba0d9b7bcec52fbb12c16f013c25fe6fb52b29cc0e\",\"dweb:/ipfs/QmeuohBFoeyDPZA9JNCTEDz3VBfBD4EABWuWXVhHAuEpKR\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol\":{\"keccak256\":\"0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd\",\"dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol\":{\"keccak256\":\"0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd\",\"dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Errors.sol\":{\"keccak256\":\"0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf\",\"dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol\":{\"keccak256\":\"0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2\",\"dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/Hashes.sol\":{\"keccak256\":\"0x8cbd338f083224b4b6f0ff42cbda641a0a6c31ffcdca197452b97fe4d0918269\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f517dec5ba0c6491395acbf7f1d621f4e89e8f218bf5303c867b1c5ad70c6b11\",\"dweb:/ipfs/QmWmXHRLEw8W6ckth7NyYTU88YfvuS7xSsfae5ksL8qNUe\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/MerkleProof.sol\":{\"keccak256\":\"0x36a0c409c437a753cac9b92b75f93b0fbe92803bf2c8ff1517e54b247f166134\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f91ba472de411aa557cdbf6560c40750d87bd11c9060bc04d2ba7119af9d5a6\",\"dweb:/ipfs/QmQjtYo2i7dDvzCEzZ67bDoNSG4RrwMoxPWuqFmX5Xzpuw\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621\",\"dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol\":{\"keccak256\":\"0x93ce66a307d66cb14e95dbc8f20641cedf48b2d060bf3bda431d62a4493e6e8d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://10d4122ee13fd8dcd2f468a86031665345d2dfdda76993d1f51201bdeb746f9d\",\"dweb:/ipfs/QmWxbbYEgyDCjwT4ZtNwujJwB4vdnR2qEKwL2Zrz9JoSDU\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableMap.sol\":{\"keccak256\":\"0x5d0c69ef32eae686acc22acfb1a54df9c91356e69adec593aa69eba83ae8f6f4\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f7a9fca2cb3d802c4f8a2d0fda84ccc7f880264885801c7df08373501dd22282\",\"dweb:/ipfs/QmdtM2Loz9NzNTuQ3RXNQCPBjoPUJw35Ukpt1MSM9XH3cD\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol\":{\"keccak256\":\"0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231\",\"dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y\"]},\"src/interfaces/factories/IFactory.sol\":{\"keccak256\":\"0x838a308272620b2ae6fdf1f5b425f8ba1439ed24a7baad50599d3fb34eb0bfa7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7a801db63324ec50da072b69c67ca98eb89d026f01a4aa7e8f60d757bc82f86d\",\"dweb:/ipfs/QmaJ6WHAe8vfwX3A8yHyLW2aJKqkbQCKuthRg1QU2f3TuY\"]},\"src/interfaces/factories/IFactoryEntity.sol\":{\"keccak256\":\"0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812\",\"dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx\"]},\"src/interfaces/hooks/IHook.sol\":{\"keccak256\":\"0xf414604a4cbf1feb04f976a806eb2825fa7dcce6815e209fc7aa50c6fe845bb3\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://a727a899804756179d294bfb0975487e8c6ca11f68aa741604d400ec6dfbf241\",\"dweb:/ipfs/QmRqsAigFe71aHAG5SgyKQKVpnHoiVyiP6VmnkxFK9KRz9\"]},\"src/interfaces/hooks/IRedeemHook.sol\":{\"keccak256\":\"0xd3df4e8013847ea38f603529d1062e6fccfc757ea62339bfbdd72719d2f7fbb1\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cddd2d10f020fa96f03b728dfcf8c7874942a19d871414010437ca3cdaef2974\",\"dweb:/ipfs/QmUZwodA14vQg3ntpzF6YxVQ2JCDmjXgQKrzE6HzJ19Mq8\"]},\"src/interfaces/managers/IFeeManager.sol\":{\"keccak256\":\"0x37d3c06ed6b23e367216c2dfc14f99f3fd6d686c984eca124406c2002e4f1516\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://5b3b1b974856f76c6fe2347895ab05e973c84c963fdf8f5483c37e2e4d434cad\",\"dweb:/ipfs/QmVeLCrCWgeQ3aS4V38y9FszFvZdSkbPHKKCMWKKM3V4qw\"]},\"src/interfaces/managers/IRiskManager.sol\":{\"keccak256\":\"0xb0dda9606a25e8187fb20cff7eb99dfecde18fbea30bae52a2124fd22afda97a\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://fee1bae75e2154b21e5ea2382ff21cd3e2ff2ea3a5e67828cfe4b563ee86e14e\",\"dweb:/ipfs/QmUtFozDVfCzehRMYxhJwZAbLJ43afzzVQDA89JD1HG1YH\"]},\"src/interfaces/managers/IShareManager.sol\":{\"keccak256\":\"0xb75fcb890e9a3e706c0d87781700965aa24faa8461967ee62b140d45f4c399e8\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ae50d1dda35eb9f6fa035fc39c2424f2e2eb2d41e6b0ffed296719818b1d9502\",\"dweb:/ipfs/Qme5jPDZG3WTLYtT2s8VFs3MKJVXgL9T5AdAzoXuuqM79z\"]},\"src/interfaces/modules/IACLModule.sol\":{\"keccak256\":\"0xf6ce9b4f4af5475d152845d6afa6f57b2d376c3a6d3eec2b5ac9650901a78243\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://f851cef38b6188b20be98c95047c8bd9d8651d37421a54e1d02b60f9f372c5ed\",\"dweb:/ipfs/QmaymGDd2RoKweuASXCiV3rPuhwvLXUvb6wBgi8xXoiYT8\"]},\"src/interfaces/modules/IBaseModule.sol\":{\"keccak256\":\"0x9c5c95ceb39f4c39f81f2afc7ddb55389a21315e76047a9cf66521db4dea68c3\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ccfb0453b14b105cb5990978a49c1e08f812563a046e17c354f4f1c648f95fe7\",\"dweb:/ipfs/QmYMAKaVdRq3NoTMfqadxgMMmUq6ejzTw2Lpsa3e9a3eyj\"]},\"src/interfaces/modules/IShareModule.sol\":{\"keccak256\":\"0x65ef35a38441080ad4e0c77c7668d6717fe9699df679ed4d2c775f47d1dce57a\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://e28e0e9e2dd2097df748fa2015ce4582f489615040b5323094880a618c54edac\",\"dweb:/ipfs/QmXNvxJDQ6edM3ExmmiTEmS5UreWYTDBgaAygU2kM6mgC6\"]},\"src/interfaces/modules/ISubvaultModule.sol\":{\"keccak256\":\"0x8db6f1a3bd2506dd0db82f4138f0436602ed37487717095a804dee81e44f5e0e\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://2547472f59c19612916f3f54e6fa69644ec31f14c669aa66dea3c006bc71bccc\",\"dweb:/ipfs/QmZ9VRCsofVntdB7siM3pfypUpgJnmaJAC1n8HviQb3K3U\"]},\"src/interfaces/modules/IVaultModule.sol\":{\"keccak256\":\"0x7696792b852bc9b435e6388a19f4284854259df2f50b45e2b833b6f9dd1b1648\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4364151c043fc5e56bc87c2cdbffc169b80cd745618a4b0da14e271752de6637\",\"dweb:/ipfs/Qmdfx3L8goy8eWFGimtsh58Se1vuLsukUw2mXo5chCbTy2\"]},\"src/interfaces/modules/IVerifierModule.sol\":{\"keccak256\":\"0x8b8ad9ea43b139afccc77ceb0fdcc34615c7401cc13e99c723895302049065a5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://0d578b1a55a180c652ee24e1b35f6d70d65bc067770dd791db44d0bcfa7a5d38\",\"dweb:/ipfs/QmexSPCfixS9spdjCydR4FUaoN4J188sfLkRmJaia14yk6\"]},\"src/interfaces/oracles/IOracle.sol\":{\"keccak256\":\"0xcbf84560c1fb9f7d3445cbbca4f5f3406606496f30867222d8e3deabad741296\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c90107edcd30e29044a5a8b05e71e728b93a485e7e7ef1a5b4ff695430e33a5e\",\"dweb:/ipfs/QmfHGNxtRQWeq7JENynxZNfhfTBytwbRwyekUyuHWrUyjK\"]},\"src/interfaces/permissions/ICustomVerifier.sol\":{\"keccak256\":\"0xdb343366ed86f63f8a94ff478c5c8e1d127888e4993c27fcb3bb3b51ae0cb8c7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://12407e89b20982a00961ccc03258e353962607747f214940d18b0989c11547c9\",\"dweb:/ipfs/Qmay19iWpwiV8LuvWccPjsazVU5Kkhme9QykFfLKfBmvSr\"]},\"src/interfaces/permissions/IMellowACL.sol\":{\"keccak256\":\"0xcc86b0ce4e4a53c1c4fa16ad79f9de0e2a1f1a77a1f256c0856120a9d6ea36df\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://6942df2ffadacae5563e01ec60697f16116362ba84a856a346b36bc7b9dd1ec2\",\"dweb:/ipfs/QmNYhqZGpS98UP89CS86CjoG4ZWbMHzTHRtd7bZzQqn3UY\"]},\"src/interfaces/permissions/IVerifier.sol\":{\"keccak256\":\"0x4b42731d76efaffcb017577bd2a478d00a3058c69220d6ea8e26f30f74476be7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ff2a6f09a946dd8a470dfe495b2a64d24b849c6d071dc859589140fb1b983362\",\"dweb:/ipfs/Qmb6UoqkmaC8tJMqkMfjcWR7pn1WCwPwuKKvUbLBQbPcJS\"]},\"src/interfaces/queues/IDepositQueue.sol\":{\"keccak256\":\"0x4ef38cb59d419010f453e62fe101efec25bec85a2ce1a24048098034060db47b\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://20e51dd02001309f23f46abea409edf77a39522bf21eeffb858ac888e4200e72\",\"dweb:/ipfs/QmWm9gfMuXz8nwuHb3he7abxTRgQw35x2viwMVuiNYAY5b\"]},\"src/interfaces/queues/IQueue.sol\":{\"keccak256\":\"0xd12e34631b204821c77eaa5b2e104902d740e3530cd716f7fc991af01a73b9ab\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://1f2a4d664244be639da1a5025ebccc8c1127e087a81ce6d0fc76af5134fec311\",\"dweb:/ipfs/QmS1GJwxGCV89myCAzLBAbmSrY6YL31G3nGj542dDM4QmT\"]},\"src/interfaces/queues/IRedeemQueue.sol\":{\"keccak256\":\"0xf8f95b95de259c8ccf29976f34d97a0ef31c9827f41ebcd5e8de54c10e646076\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://45ae0d496e865d846bf0c980593c1d16c038abf2b9359906224aa94cccf1c73f\",\"dweb:/ipfs/QmYTDXSTpUqhzJDXSKjoSKxwwQYee7wSTtf617wDsnnCC5\"]},\"src/libraries/FenwickTreeLibrary.sol\":{\"keccak256\":\"0xfd159ed27b96f288c4220febac4bfe286e4545b8ca7f523ae6e63d123341f8a3\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://58f0cc202896722786140571b111bf5e589730358a24b2940a450c4c75d864fe\",\"dweb:/ipfs/QmeRSjvcYURUSpvFe4r4VRwwpHcb1NmqiEUDEJ5rBxAxuf\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "type": "error", "name": "AlreadySupportedAsset"}, {"inputs": [], "type": "error", "name": "Forbidden"}, {"inputs": [{"internalType": "uint256", "name": "priceD18", "type": "uint256"}], "type": "error", "name": "InvalidPrice"}, {"inputs": [], "type": "error", "name": "InvalidReport"}, {"inputs": [{"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "minTimestamp", "type": "uint256"}], "type": "error", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "type": "error", "name": "UnsupportedAsset"}, {"inputs": [], "type": "error", "name": "ZeroValue"}, {"inputs": [{"internalType": "bytes", "name": "initParams", "type": "bytes", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address", "indexed": true}, {"internalType": "uint224", "name": "priceD18", "type": "uint224", "indexed": true}, {"internalType": "uint32", "name": "timestamp", "type": "uint32", "indexed": true}], "type": "event", "name": "ReportAccepted", "anonymous": false}, {"inputs": [{"internalType": "struct IOracle.Report[]", "name": "reports", "type": "tuple[]", "components": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint224", "name": "priceD18", "type": "uint224"}], "indexed": false}], "type": "event", "name": "ReportsSubmitted", "anonymous": false}, {"inputs": [{"internalType": "struct IOracle.SecurityParams", "name": "securityParams", "type": "tuple", "components": [{"internalType": "uint224", "name": "maxAbsoluteDeviation", "type": "uint224"}, {"internalType": "uint224", "name": "suspiciousAbsoluteDeviation", "type": "uint224"}, {"internalType": "uint64", "name": "maxRelativeDeviationD18", "type": "uint64"}, {"internalType": "uint64", "name": "suspiciousRelativeDeviationD18", "type": "uint64"}, {"internalType": "uint32", "name": "timeout", "type": "uint32"}, {"internalType": "uint32", "name": "depositInterval", "type": "uint32"}, {"internalType": "uint32", "name": "redeemInterval", "type": "uint32"}], "indexed": false}], "type": "event", "name": "SecurityParamsSet", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address", "indexed": true}], "type": "event", "name": "<PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "assets", "type": "address[]", "indexed": false}], "type": "event", "name": "SupportedAssetsAdded", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "assets", "type": "address[]", "indexed": false}], "type": "event", "name": "SupportedAssetsRemoved", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "ACCEPT_REPORT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "ADD_SUPPORTED_ASSETS_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "REMOVE_SUPPORTED_ASSETS_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SET_SECURITY_PARAMS_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SUBMIT_REPORTS_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "priceD18", "type": "uint256"}, {"internalType": "uint32", "name": "timestamp", "type": "uint32"}], "stateMutability": "nonpayable", "type": "function", "name": "acceptReport"}, {"inputs": [{"internalType": "address[]", "name": "assets", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "addSupportedAssets"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getReport", "outputs": [{"internalType": "struct IOracle.DetailedReport", "name": "", "type": "tuple", "components": [{"internalType": "uint224", "name": "priceD18", "type": "uint224"}, {"internalType": "uint32", "name": "timestamp", "type": "uint32"}, {"internalType": "bool", "name": "isSuspicious", "type": "bool"}]}]}, {"inputs": [{"internalType": "bytes", "name": "initParams", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isSupportedAsset", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address[]", "name": "assets", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "removeSupportedAssets"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "securityParams", "outputs": [{"internalType": "struct IOracle.SecurityParams", "name": "", "type": "tuple", "components": [{"internalType": "uint224", "name": "maxAbsoluteDeviation", "type": "uint224"}, {"internalType": "uint224", "name": "suspiciousAbsoluteDeviation", "type": "uint224"}, {"internalType": "uint64", "name": "maxRelativeDeviationD18", "type": "uint64"}, {"internalType": "uint64", "name": "suspiciousRelativeDeviationD18", "type": "uint64"}, {"internalType": "uint32", "name": "timeout", "type": "uint32"}, {"internalType": "uint32", "name": "depositInterval", "type": "uint32"}, {"internalType": "uint32", "name": "redeemInterval", "type": "uint32"}]}]}, {"inputs": [{"internalType": "struct IOracle.SecurityParams", "name": "securityParams_", "type": "tuple", "components": [{"internalType": "uint224", "name": "maxAbsoluteDeviation", "type": "uint224"}, {"internalType": "uint224", "name": "suspiciousAbsoluteDeviation", "type": "uint224"}, {"internalType": "uint64", "name": "maxRelativeDeviationD18", "type": "uint64"}, {"internalType": "uint64", "name": "suspiciousRelativeDeviationD18", "type": "uint64"}, {"internalType": "uint32", "name": "timeout", "type": "uint32"}, {"internalType": "uint32", "name": "depositInterval", "type": "uint32"}, {"internalType": "uint32", "name": "redeemInterval", "type": "uint32"}]}], "stateMutability": "nonpayable", "type": "function", "name": "setSecurityParams"}, {"inputs": [{"internalType": "address", "name": "vault_", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "<PERSON><PERSON><PERSON>"}, {"inputs": [{"internalType": "struct IOracle.Report[]", "name": "reports", "type": "tuple[]", "components": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint224", "name": "priceD18", "type": "uint224"}]}], "stateMutability": "nonpayable", "type": "function", "name": "submitReports"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "supportedAssetAt", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "supportedAssets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "priceD18", "type": "uint256"}, {"internalType": "address", "name": "asset", "type": "address"}], "stateMutability": "view", "type": "function", "name": "validatePrice", "outputs": [{"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bool"}, {"internalType": "bool", "name": "isSuspicious", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "vault", "outputs": [{"internalType": "contract IShareModule", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {"acceptReport(address,uint256,uint32)": {"details": "Callable only by account with `ACCEPT_REPORT_ROLE`", "params": {"asset": "Address of the asset", "priceD18": "Timestamp that must match existing suspicious report", "timestamp": "Timestamp that must match existing suspicious report"}}, "addSupportedAssets(address[])": {"params": {"assets": "Array of asset addresses to add"}}, "getReport(address)": {"params": {"asset": "Address of the asset"}}, "initialize(bytes)": {"params": {"initParams": "The initialization parameters."}}, "isSupportedAsset(address)": {"params": {"asset": "Address of the asset"}}, "removeSupportedAssets(address[])": {"params": {"assets": "Array of asset addresses to remove"}}, "setSecurityParams((uint224,uint224,uint64,uint64,uint32,uint32,uint32))": {"params": {"securityParams_": "New security settings"}}, "setVault(address)": {"params": {"vault_": "Address of the vault module"}}, "submitReports((address,uint224)[])": {"details": "Processes pending deposit and redemption requests across DepositQueue and RedeemQueue contracts.      The core processing logic is determined by the ShareModule and Queue contracts.      Only callable by accounts with the `SUBMIT_REPORTS_ROLE`.Note: Submitted prices MUST reflect protocol and performance fee deductions, ensuring accurate share issuance.", "params": {"reports": "An array of price reports, each specifying the target asset and its latest price (in 18 decimals)."}}, "supportedAssetAt(uint256)": {"params": {"index": "Index in the supported asset set"}}, "validatePrice(uint256,address)": {"details": "Evaluates both absolute and relative deviation limits to determine whether the price is valid or suspicious.", "params": {"asset": "Address of the asset being evaluated.", "priceD18": "Price to validate, in 18-decimal fixed-point format."}, "returns": {"isSuspicious": "True if the price exceeds the suspicious deviation threshold.", "isValid": "True if the price is within maximum allowed deviation."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"ACCEPT_REPORT_ROLE()": {"notice": "Role required to accept suspicious reports"}, "ADD_SUPPORTED_ASSETS_ROLE()": {"notice": "Role required to add new supported assets"}, "REMOVE_SUPPORTED_ASSETS_ROLE()": {"notice": "Role required to remove supported assets"}, "SET_SECURITY_PARAMS_ROLE()": {"notice": "Role required to update security parameters"}, "SUBMIT_REPORTS_ROLE()": {"notice": "Role required to submit reports"}, "acceptReport(address,uint256,uint32)": {"notice": "Accepts a previously suspicious report"}, "addSupportedAssets(address[])": {"notice": "Adds multiple new assets to the supported set"}, "getReport(address)": {"notice": "Returns the most recent detailed report for an asset"}, "initialize(bytes)": {"notice": "Initializes the factory-created entity with arbitrary initialization data."}, "isSupportedAsset(address)": {"notice": "Checks whether an asset is supported"}, "removeSupportedAssets(address[])": {"notice": "Removes assets from the supported set"}, "securityParams()": {"notice": "Returns current security parameters"}, "setSecurityParams((uint224,uint224,uint64,uint64,uint32,uint32,uint32))": {"notice": "Updates oracle security parameters"}, "setVault(address)": {"notice": "Sets the associated vault"}, "submitReports((address,uint224)[])": {"notice": "Submits price reports for supported assets."}, "supportedAssetAt(uint256)": {"notice": "Returns the supported asset at a specific index"}, "supportedAssets()": {"notice": "Returns total count of supported assets"}, "validatePrice(uint256,address)": {"notice": "Validates the given price for a specific asset based on oracle security parameters."}, "vault()": {"notice": "Returns the connected vault module"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/oracles/IOracle.sol": "IOracle"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/contracts/access/AccessControlUpgradeable.sol": {"keccak256": "0x0d4d7d19a052a2ef9d17b28450133631188b895e1755747fa8ad0280aadfb534", "urls": ["bzz-raw://838551e10cc07e570b70aee1078e490658d3a0ab8f4adfd4f3f2a565200753da", "dweb:/ipfs/QmceT7R3Pqzywyxr3MSEA7VXU4axZAPS6BLy92iV97rGdG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"keccak256": "0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a", "urls": ["bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6", "dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/access/extensions/AccessControlEnumerableUpgradeable.sol": {"keccak256": "0xaec38804089a16494f7d45ebbbeab78fe05e5dc4bc2eae1d2af458fe999fb43c", "urls": ["bzz-raw://04796408e2c4a1051afb23319e9505edb595a3d9c663ff5d6fcfb8e185ab1d93", "dweb:/ipfs/Qma8ZTi4kGyqg45SyNibbJSqMF7broiQnJPHhEGLcZyF9U"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05", "urls": ["bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08", "dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol": {"keccak256": "0x361126a17677994081cd9cb69c3f50cffff6e920d25cb7e428acdb1ae41d1866", "urls": ["bzz-raw://19ae787a7dd001269cd60a394b1a5261b78925a0fc3a6f927beb2986a9aa56cf", "dweb:/ipfs/QmYLfXiuKmcRgTDBEDXMMjXU8t6JxsspUmjxYzqWS55oEv"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/introspection/ERC165Upgradeable.sol": {"keccak256": "0xc8ed8d2056934b7675b695dec032f2920c2f5c6cf33a17ca85650940675323ab", "urls": ["bzz-raw://3c8ccc75d1cd792d192aa09e54dd49ea35fe85baa9fcd17486f29227d9f29b89", "dweb:/ipfs/QmbboSbFUEiM9tdEgBwuTRb7bykFoJXZ7dsSr1PSREJXMr"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530", "urls": ["bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0", "dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/extensions/IAccessControlEnumerable.sol": {"keccak256": "0xca774fbe0568762efdc1a7cba31f09549c7fa96dbe97410f4843fa2f0bc000a3", "urls": ["bzz-raw://0187ffdbf3d61b6d86cba4fcd9826e53d876987d620533ee84c681bdaf0f3ba3", "dweb:/ipfs/QmVJDqdJv6uzHY7ifncfv2QJep8XTzS3bGb4s5Exhuv86m"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol": {"keccak256": "0xb25a4f11fa80c702bf5cd85adec90e6f6f507f32f4a8e6f5dbc31e8c10029486", "urls": ["bzz-raw://6917f8a323e7811f041aecd4d9fd6e92455a6fba38a797ac6f6e208c7912b79d", "dweb:/ipfs/QmShuYv55wYHGi4EFkDB8QfF7ZCHoKk2efyz3AWY1ExSq7"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol": {"keccak256": "0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e", "urls": ["bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049", "dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol": {"keccak256": "0x8decfa54cec979c824b044b8128cd91d713f72c71fd7dfa54974624d8c949898", "urls": ["bzz-raw://271f914261a19d87117a777e0924ada545c16191ef9b00cc40b0134fc14ebc70", "dweb:/ipfs/QmdvVNWHGHQrGGPonZJs5NuzTevTjZRM2zayKrDJf7WBA2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/Proxy.sol": {"keccak256": "0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd", "urls": ["bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac", "dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol": {"keccak256": "0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c", "urls": ["bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa", "dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol": {"keccak256": "0x46f86003755f50eff00a7c5aaf493ae62e024142b8aec4493a313851d3c14872", "urls": ["bzz-raw://462c770cb667cc75ab22df5a29c50873b24b267274edf87ac5cfe0112bf4c3f7", "dweb:/ipfs/QmTfKk32AkQ2yyYZCYJ44V69EM5t9ryBFC6bRF7FVMHXvj"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol": {"keccak256": "0x92579f452fe663595a898cbac85d80bb3868a6c9f034f19ba7fbebdfa3b65a4d", "urls": ["bzz-raw://d07a888cd4a26c7077a30dd99e9ac30fbe2752bfb37e58c65f06a3b192079ef3", "dweb:/ipfs/QmZCQY4FC4ynBv9dha4BBWM1vYxTUJPBdFBS4HEFe6XzVB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol": {"keccak256": "0xb5afb8e8eebc4d1c6404df2f5e1e6d2c3d24fd01e5dfc855314951ecfaae462d", "urls": ["bzz-raw://78586466c424f076c6a2a551d848cfbe3f7c49e723830807598484a1047b3b34", "dweb:/ipfs/Qmb717ovcFxm7qgNKEShiV6M9SPR3v1qnNpAGH84D6w29p"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0xaaa1d17c1129b127a4a401db2fbd72960e2671474be3d08cae71ccdc42f7624c", "urls": ["bzz-raw://cb2f27cd3952aa667e198fba0d9b7bcec52fbb12c16f013c25fe6fb52b29cc0e", "dweb:/ipfs/QmeuohBFoeyDPZA9JNCTEDz3VBfBD4EABWuWXVhHAuEpKR"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol": {"keccak256": "0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6", "urls": ["bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd", "dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol": {"keccak256": "0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58", "urls": ["bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd", "dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Errors.sol": {"keccak256": "0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123", "urls": ["bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf", "dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol": {"keccak256": "0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26", "urls": ["bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2", "dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/Hashes.sol": {"keccak256": "0x8cbd338f083224b4b6f0ff42cbda641a0a6c31ffcdca197452b97fe4d0918269", "urls": ["bzz-raw://f517dec5ba0c6491395acbf7f1d621f4e89e8f218bf5303c867b1c5ad70c6b11", "dweb:/ipfs/QmWmXHRLEw8W6ckth7NyYTU88YfvuS7xSsfae5ksL8qNUe"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/MerkleProof.sol": {"keccak256": "0x36a0c409c437a753cac9b92b75f93b0fbe92803bf2c8ff1517e54b247f166134", "urls": ["bzz-raw://0f91ba472de411aa557cdbf6560c40750d87bd11c9060bc04d2ba7119af9d5a6", "dweb:/ipfs/QmQjtYo2i7dDvzCEzZ67bDoNSG4RrwMoxPWuqFmX5Xzpuw"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8", "urls": ["bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621", "dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol": {"keccak256": "0x93ce66a307d66cb14e95dbc8f20641cedf48b2d060bf3bda431d62a4493e6e8d", "urls": ["bzz-raw://10d4122ee13fd8dcd2f468a86031665345d2dfdda76993d1f51201bdeb746f9d", "dweb:/ipfs/QmWxbbYEgyDCjwT4ZtNwujJwB4vdnR2qEKwL2Zrz9JoSDU"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableMap.sol": {"keccak256": "0x5d0c69ef32eae686acc22acfb1a54df9c91356e69adec593aa69eba83ae8f6f4", "urls": ["bzz-raw://f7a9fca2cb3d802c4f8a2d0fda84ccc7f880264885801c7df08373501dd22282", "dweb:/ipfs/QmdtM2Loz9NzNTuQ3RXNQCPBjoPUJw35Ukpt1MSM9XH3cD"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol": {"keccak256": "0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06", "urls": ["bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231", "dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y"], "license": "MIT"}, "src/interfaces/factories/IFactory.sol": {"keccak256": "0x838a308272620b2ae6fdf1f5b425f8ba1439ed24a7baad50599d3fb34eb0bfa7", "urls": ["bzz-raw://7a801db63324ec50da072b69c67ca98eb89d026f01a4aa7e8f60d757bc82f86d", "dweb:/ipfs/QmaJ6WHAe8vfwX3A8yHyLW2aJKqkbQCKuthRg1QU2f3TuY"], "license": "BUSL-1.1"}, "src/interfaces/factories/IFactoryEntity.sol": {"keccak256": "0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5", "urls": ["bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812", "dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx"], "license": "BUSL-1.1"}, "src/interfaces/hooks/IHook.sol": {"keccak256": "0xf414604a4cbf1feb04f976a806eb2825fa7dcce6815e209fc7aa50c6fe845bb3", "urls": ["bzz-raw://a727a899804756179d294bfb0975487e8c6ca11f68aa741604d400ec6dfbf241", "dweb:/ipfs/QmRqsAigFe71aHAG5SgyKQKVpnHoiVyiP6VmnkxFK9KRz9"], "license": "BUSL-1.1"}, "src/interfaces/hooks/IRedeemHook.sol": {"keccak256": "0xd3df4e8013847ea38f603529d1062e6fccfc757ea62339bfbdd72719d2f7fbb1", "urls": ["bzz-raw://cddd2d10f020fa96f03b728dfcf8c7874942a19d871414010437ca3cdaef2974", "dweb:/ipfs/QmUZwodA14vQg3ntpzF6YxVQ2JCDmjXgQKrzE6HzJ19Mq8"], "license": "BUSL-1.1"}, "src/interfaces/managers/IFeeManager.sol": {"keccak256": "0x37d3c06ed6b23e367216c2dfc14f99f3fd6d686c984eca124406c2002e4f1516", "urls": ["bzz-raw://5b3b1b974856f76c6fe2347895ab05e973c84c963fdf8f5483c37e2e4d434cad", "dweb:/ipfs/QmVeLCrCWgeQ3aS4V38y9FszFvZdSkbPHKKCMWKKM3V4qw"], "license": "BUSL-1.1"}, "src/interfaces/managers/IRiskManager.sol": {"keccak256": "0xb0dda9606a25e8187fb20cff7eb99dfecde18fbea30bae52a2124fd22afda97a", "urls": ["bzz-raw://fee1bae75e2154b21e5ea2382ff21cd3e2ff2ea3a5e67828cfe4b563ee86e14e", "dweb:/ipfs/QmUtFozDVfCzehRMYxhJwZAbLJ43afzzVQDA89JD1HG1YH"], "license": "BUSL-1.1"}, "src/interfaces/managers/IShareManager.sol": {"keccak256": "0xb75fcb890e9a3e706c0d87781700965aa24faa8461967ee62b140d45f4c399e8", "urls": ["bzz-raw://ae50d1dda35eb9f6fa035fc39c2424f2e2eb2d41e6b0ffed296719818b1d9502", "dweb:/ipfs/Qme5jPDZG3WTLYtT2s8VFs3MKJVXgL9T5AdAzoXuuqM79z"], "license": "BUSL-1.1"}, "src/interfaces/modules/IACLModule.sol": {"keccak256": "0xf6ce9b4f4af5475d152845d6afa6f57b2d376c3a6d3eec2b5ac9650901a78243", "urls": ["bzz-raw://f851cef38b6188b20be98c95047c8bd9d8651d37421a54e1d02b60f9f372c5ed", "dweb:/ipfs/QmaymGDd2RoKweuASXCiV3rPuhwvLXUvb6wBgi8xXoiYT8"], "license": "BUSL-1.1"}, "src/interfaces/modules/IBaseModule.sol": {"keccak256": "0x9c5c95ceb39f4c39f81f2afc7ddb55389a21315e76047a9cf66521db4dea68c3", "urls": ["bzz-raw://ccfb0453b14b105cb5990978a49c1e08f812563a046e17c354f4f1c648f95fe7", "dweb:/ipfs/QmYMAKaVdRq3NoTMfqadxgMMmUq6ejzTw2Lpsa3e9a3eyj"], "license": "BUSL-1.1"}, "src/interfaces/modules/IShareModule.sol": {"keccak256": "0x65ef35a38441080ad4e0c77c7668d6717fe9699df679ed4d2c775f47d1dce57a", "urls": ["bzz-raw://e28e0e9e2dd2097df748fa2015ce4582f489615040b5323094880a618c54edac", "dweb:/ipfs/QmXNvxJDQ6edM3ExmmiTEmS5UreWYTDBgaAygU2kM6mgC6"], "license": "BUSL-1.1"}, "src/interfaces/modules/ISubvaultModule.sol": {"keccak256": "0x8db6f1a3bd2506dd0db82f4138f0436602ed37487717095a804dee81e44f5e0e", "urls": ["bzz-raw://2547472f59c19612916f3f54e6fa69644ec31f14c669aa66dea3c006bc71bccc", "dweb:/ipfs/QmZ9VRCsofVntdB7siM3pfypUpgJnmaJAC1n8HviQb3K3U"], "license": "BUSL-1.1"}, "src/interfaces/modules/IVaultModule.sol": {"keccak256": "0x7696792b852bc9b435e6388a19f4284854259df2f50b45e2b833b6f9dd1b1648", "urls": ["bzz-raw://4364151c043fc5e56bc87c2cdbffc169b80cd745618a4b0da14e271752de6637", "dweb:/ipfs/Qmdfx3L8goy8eWFGimtsh58Se1vuLsukUw2mXo5chCbTy2"], "license": "BUSL-1.1"}, "src/interfaces/modules/IVerifierModule.sol": {"keccak256": "0x8b8ad9ea43b139afccc77ceb0fdcc34615c7401cc13e99c723895302049065a5", "urls": ["bzz-raw://0d578b1a55a180c652ee24e1b35f6d70d65bc067770dd791db44d0bcfa7a5d38", "dweb:/ipfs/QmexSPCfixS9spdjCydR4FUaoN4J188sfLkRmJaia14yk6"], "license": "BUSL-1.1"}, "src/interfaces/oracles/IOracle.sol": {"keccak256": "0xcbf84560c1fb9f7d3445cbbca4f5f3406606496f30867222d8e3deabad741296", "urls": ["bzz-raw://c90107edcd30e29044a5a8b05e71e728b93a485e7e7ef1a5b4ff695430e33a5e", "dweb:/ipfs/QmfHGNxtRQWeq7JENynxZNfhfTBytwbRwyekUyuHWrUyjK"], "license": "BUSL-1.1"}, "src/interfaces/permissions/ICustomVerifier.sol": {"keccak256": "0xdb343366ed86f63f8a94ff478c5c8e1d127888e4993c27fcb3bb3b51ae0cb8c7", "urls": ["bzz-raw://12407e89b20982a00961ccc03258e353962607747f214940d18b0989c11547c9", "dweb:/ipfs/Qmay19iWpwiV8LuvWccPjsazVU5Kkhme9QykFfLKfBmvSr"], "license": "BUSL-1.1"}, "src/interfaces/permissions/IMellowACL.sol": {"keccak256": "0xcc86b0ce4e4a53c1c4fa16ad79f9de0e2a1f1a77a1f256c0856120a9d6ea36df", "urls": ["bzz-raw://6942df2ffadacae5563e01ec60697f16116362ba84a856a346b36bc7b9dd1ec2", "dweb:/ipfs/QmNYhqZGpS98UP89CS86CjoG4ZWbMHzTHRtd7bZzQqn3UY"], "license": "BUSL-1.1"}, "src/interfaces/permissions/IVerifier.sol": {"keccak256": "0x4b42731d76efaffcb017577bd2a478d00a3058c69220d6ea8e26f30f74476be7", "urls": ["bzz-raw://ff2a6f09a946dd8a470dfe495b2a64d24b849c6d071dc859589140fb1b983362", "dweb:/ipfs/Qmb6UoqkmaC8tJMqkMfjcWR7pn1WCwPwuKKvUbLBQbPcJS"], "license": "BUSL-1.1"}, "src/interfaces/queues/IDepositQueue.sol": {"keccak256": "0x4ef38cb59d419010f453e62fe101efec25bec85a2ce1a24048098034060db47b", "urls": ["bzz-raw://20e51dd02001309f23f46abea409edf77a39522bf21eeffb858ac888e4200e72", "dweb:/ipfs/QmWm9gfMuXz8nwuHb3he7abxTRgQw35x2viwMVuiNYAY5b"], "license": "BUSL-1.1"}, "src/interfaces/queues/IQueue.sol": {"keccak256": "0xd12e34631b204821c77eaa5b2e104902d740e3530cd716f7fc991af01a73b9ab", "urls": ["bzz-raw://1f2a4d664244be639da1a5025ebccc8c1127e087a81ce6d0fc76af5134fec311", "dweb:/ipfs/QmS1GJwxGCV89myCAzLBAbmSrY6YL31G3nGj542dDM4QmT"], "license": "BUSL-1.1"}, "src/interfaces/queues/IRedeemQueue.sol": {"keccak256": "0xf8f95b95de259c8ccf29976f34d97a0ef31c9827f41ebcd5e8de54c10e646076", "urls": ["bzz-raw://45ae0d496e865d846bf0c980593c1d16c038abf2b9359906224aa94cccf1c73f", "dweb:/ipfs/QmYTDXSTpUqhzJDXSKjoSKxwwQYee7wSTtf617wDsnnCC5"], "license": "BUSL-1.1"}, "src/libraries/FenwickTreeLibrary.sol": {"keccak256": "0xfd159ed27b96f288c4220febac4bfe286e4545b8ca7f523ae6e63d123341f8a3", "urls": ["bzz-raw://58f0cc202896722786140571b111bf5e589730358a24b2940a450c4c75d864fe", "dweb:/ipfs/QmeRSjvcYURUSpvFe4r4VRwwpHcb1NmqiEUDEJ5rBxAxuf"], "license": "BUSL-1.1"}}, "version": 1}, "id": 102}