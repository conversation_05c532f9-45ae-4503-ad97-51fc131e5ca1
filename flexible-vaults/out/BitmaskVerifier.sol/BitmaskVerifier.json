{"abi": [{"type": "function", "name": "calculateHash", "inputs": [{"name": "bitmask", "type": "bytes", "internalType": "bytes"}, {"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "verifyCall", "inputs": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}, {"name": "verificationData", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "pure"}], "bytecode": {"object": "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", "sourceMap": "426:3256:128:-:0;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "426:3256:128:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2963:717;;;;;;:::i;:::-;;:::i;:::-;;;1649:14:192;;1642:22;1624:41;;1612:2;1597:18;2963:717:128;;;;;;;;1522:649;;;;;;:::i;:::-;;:::i;:::-;;;2762:25:192;;;2750:2;2735:18;1522:649:128;2616:177:192;2963:717:128;3125:4;3256:37;;3389:4;3360:34;;;3347:48;3318:78;;3427:15;;;;3473:18;;3514;:4;3528;3514:18;:::i;:::-;:36;3510:79;;3573:5;3566:12;;;;;;;3510:79;3626:47;3640:7;;3649:3;3654:5;3661;3668:4;;3626:13;:47::i;:::-;3605:17;:68;3598:75;;;;;2963:717;;;;;;;;;;:::o;1522:649::-;1678:7;;;-1:-1:-1;;1795:12:128;;;;1787:21;1770:13;1780:2;1678:7;1770;;:13;:::i;:::-;1762:22;;;:::i;:::-;:46;1742:67;;;;;;;;3778:19:192;;;3822:2;3813:12;;3806:28;3859:2;3850:12;;3621:247;1742:67:128;;;;-1:-1:-1;;1742:67:128;;;;;;;;;1732:78;;1742:67;1732:78;;;;;-1:-1:-1;1732:78:128;;-1:-1:-1;;1892:14:128;;;;1884:23;;1866:14;;:7;;:14;:::i;:::-;1858:23;;;:::i;:::-;:49;1838:70;;;;;;;;3778:19:192;;;3822:2;3813:12;;3806:28;3859:2;3850:12;;3621:247;1838:70:128;;;;;;;;;;;;;1828:81;;;;;;1820:89;;1950:5;1991;1983:14;;1965:7;;1973:2;1965:14;1976:2;1965:14;;;;;;;:::i;:::-;1957:23;;;:::i;:::-;:40;1937:61;;;;;;;;3778:19:192;;;3822:2;3813:12;;3806:28;3859:2;3850:12;;3621:247;1937:61:128;;;;;;;;;;;;;1927:72;;;;;;1919:80;;2014:9;2009:134;2029:15;;;2009:134;;;2096:5;2114:7;;2122:6;:1;2126:2;2122:6;:::i;:::-;2114:15;;;;;;;:::i;:::-;;;;;;;;;2104:4;;2109:1;2104:7;;;;;;;:::i;:::-;2083:48;;;;;;4160:19:192;;;;2104:7:128;;;:25;;;;-1:-1:-1;;;;;;2104:25:128;4195:12:192;;;4188:48;-1:-1:-1;4252:12:192;;2083:48:128;;;-1:-1:-1;;2083:48:128;;;;;;;;;2073:59;;2083:48;2073:59;;;;;-1:-1:-1;2046:3:128;;2009:134;;;-1:-1:-1;2159:5:128;1522:649;-1:-1:-1;;;;;;;;1522:649:128:o;14:173:192:-;82:20;;-1:-1:-1;;;;;131:31:192;;121:42;;111:70;;177:1;174;167:12;111:70;14:173;;;:::o;192:347::-;243:8;253:6;307:3;300:4;292:6;288:17;284:27;274:55;;325:1;322;315:12;274:55;-1:-1:-1;348:20:192;;391:18;380:30;;377:50;;;423:1;420;413:12;377:50;460:4;452:6;448:17;436:29;;512:3;505:4;496:6;488;484:19;480:30;477:39;474:59;;;529:1;526;519:12;474:59;192:347;;;;;:::o;544:935::-;661:6;669;677;685;693;701;709;762:3;750:9;741:7;737:23;733:33;730:53;;;779:1;776;769:12;730:53;802:29;821:9;802:29;:::i;:::-;792:39;;850:38;884:2;873:9;869:18;850:38;:::i;:::-;840:48;;935:2;924:9;920:18;907:32;897:42;;990:2;979:9;975:18;962:32;1013:18;1054:2;1046:6;1043:14;1040:34;;;1070:1;1067;1060:12;1040:34;1109:58;1159:7;1150:6;1139:9;1135:22;1109:58;:::i;:::-;1186:8;;-1:-1:-1;1083:84:192;-1:-1:-1;1274:3:192;1259:19;;1246:33;;-1:-1:-1;1291:16:192;;;1288:36;;;1320:1;1317;1310:12;1288:36;;1359:60;1411:7;1400:8;1389:9;1385:24;1359:60;:::i;:::-;544:935;;;;-1:-1:-1;544:935:192;;-1:-1:-1;544:935:192;;;;1333:86;;-1:-1:-1;;;544:935:192:o;1676:::-;1793:6;1801;1809;1817;1825;1833;1841;1894:3;1882:9;1873:7;1869:23;1865:33;1862:53;;;1911:1;1908;1901:12;1862:53;1951:9;1938:23;1980:18;2021:2;2013:6;2010:14;2007:34;;;2037:1;2034;2027:12;2007:34;2076:58;2126:7;2117:6;2106:9;2102:22;2076:58;:::i;:::-;2153:8;;-1:-1:-1;2050:84:192;-1:-1:-1;2050:84:192;;-1:-1:-1;2207:38:192;2241:2;2226:18;;2207:38;:::i;:::-;2197:48;;2264:38;2298:2;2287:9;2283:18;2264:38;:::i;:::-;2254:48;;2349:2;2338:9;2334:18;2321:32;2311:42;;2406:3;2395:9;2391:19;2378:33;2362:49;;2436:2;2426:8;2423:16;2420:36;;;2452:1;2449;2442:12;2798:222;2863:9;;;2884:10;;;2881:133;;;2936:10;2931:3;2927:20;2924:1;2917:31;2971:4;2968:1;2961:15;2999:4;2996:1;2989:15;2881:133;2798:222;;;;:::o;3025:331::-;3130:9;3141;3183:8;3171:10;3168:24;3165:44;;;3205:1;3202;3195:12;3165:44;3234:6;3224:8;3221:20;3218:40;;;3254:1;3251;3244:12;3218:40;-1:-1:-1;;3280:23:192;;;3325:25;;;;;-1:-1:-1;3025:331:192:o;3361:255::-;3481:19;;3520:2;3512:11;;3509:101;;;-1:-1:-1;;3581:2:192;3577:12;;;3574:1;3570:20;3566:33;3555:45;3361:255;;;;:::o;3873:127::-;3934:10;3929:3;3925:20;3922:1;3915:31;3965:4;3962:1;3955:15;3989:4;3986:1;3979:15", "linkReferences": {}}, "methodIdentifiers": {"calculateHash(bytes,address,address,uint256,bytes)": "d0b8c4a9", "verifyCall(address,address,uint256,bytes,bytes)": "70e46bcb"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"bitmask\",\"type\":\"bytes\"},{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"calculateHash\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"verificationData\",\"type\":\"bytes\"}],\"name\":\"verifyCall\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"pure\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Enables flexible permissioning by allowing dynamic verification of transaction intent through partial data matching.\",\"kind\":\"dev\",\"methods\":{\"calculateHash(bytes,address,address,uint256,bytes)\":{\"details\":\"The hash is computed in the following sequence: 1. Hashing `who` using `bitmask[0:32]` 2. Hashing `where` using `bitmask[32:64]` 3. Hashing `value` using `bitmask[64:96]` 4. Iteratively hashing each byte of `data` masked with `bitmask[i + 96]` This allows whitelisting specific parts of calldata or sender/target/value.\",\"params\":{\"bitmask\":\"A byte array encoding which bytes should be included in the hash.        - [0:32]    Mask for `who` (caller)        - [32:64]   Mask for `where` (target contract)        - [64:96]   Mask for `value` (ETH sent)        - [96:]     Mask for each byte of `data` (calldata)\",\"data\":\"Full calldata of the call.\",\"value\":\"ETH value to be sent with the call.\",\"where\":\"Target contract address.\",\"who\":\"Caller address.\"},\"returns\":{\"_0\":\"Hash of the masked components using `keccak256`.\"}},\"verifyCall(address,address,uint256,bytes,bytes)\":{\"details\":\"The expected hash and bitmask are ABI-encoded in `verificationData`. - `verificationData = abi.encode(expectedHash, bitmask)` - Function reverts to false if bitmask length does not match (data.length + 96).\",\"params\":{\"data\":\"Calldata for the function call.\",\"value\":\"ETH value to be sent.\",\"verificationData\":\"ABI-encoded data containing:        - bytes32 expectedHash        - bytes bitmask (variable length)\",\"where\":\"Target contract address.\",\"who\":\"Caller address of the original call.\"},\"returns\":{\"_0\":\"True if the calculated masked hash matches the expected hash, false otherwise.\"}}},\"title\":\"BitmaskVerifier\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"calculateHash(bytes,address,address,uint256,bytes)\":{\"notice\":\"Computes a hash of a call using selective masking over its fields.\"},\"verifyCall(address,address,uint256,bytes,bytes)\":{\"notice\":\"Verifies whether the provided call matches a pre-approved hash using a given bitmask.\"}},\"notice\":\"Verifier contract implementing selective calldata hashing using bitmasking rules.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/permissions/BitmaskVerifier.sol\":\"BitmaskVerifier\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0\",\"dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf\"]},\"src/interfaces/permissions/ICustomVerifier.sol\":{\"keccak256\":\"0xdb343366ed86f63f8a94ff478c5c8e1d127888e4993c27fcb3bb3b51ae0cb8c7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://12407e89b20982a00961ccc03258e353962607747f214940d18b0989c11547c9\",\"dweb:/ipfs/Qmay19iWpwiV8LuvWccPjsazVU5Kkhme9QykFfLKfBmvSr\"]},\"src/permissions/BitmaskVerifier.sol\":{\"keccak256\":\"0xefa67d5f3be22e44b52f843408fdb113587069f33c861f94b8055a7c695e3610\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7987fec61745fede54fd130dc56f6ee74391bbf618a4849cd6bcab5aef60adc4\",\"dweb:/ipfs/QmP4tVpzCYS9Shwo88TSTuy3a78Epnh6CpzqovEMDvoCF8\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "bytes", "name": "bitmask", "type": "bytes"}, {"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "pure", "type": "function", "name": "calculateHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "bytes", "name": "verificationData", "type": "bytes"}], "stateMutability": "pure", "type": "function", "name": "verifyCall", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"calculateHash(bytes,address,address,uint256,bytes)": {"details": "The hash is computed in the following sequence: 1. Hashing `who` using `bitmask[0:32]` 2. Hashing `where` using `bitmask[32:64]` 3. Hashing `value` using `bitmask[64:96]` 4. Iteratively hashing each byte of `data` masked with `bitmask[i + 96]` This allows whitelisting specific parts of calldata or sender/target/value.", "params": {"bitmask": "A byte array encoding which bytes should be included in the hash.        - [0:32]    Mask for `who` (caller)        - [32:64]   Mask for `where` (target contract)        - [64:96]   Mask for `value` (ETH sent)        - [96:]     Mask for each byte of `data` (calldata)", "data": "Full calldata of the call.", "value": "ETH value to be sent with the call.", "where": "Target contract address.", "who": "Caller address."}, "returns": {"_0": "Hash of the masked components using `keccak256`."}}, "verifyCall(address,address,uint256,bytes,bytes)": {"details": "The expected hash and bitmask are ABI-encoded in `verificationData`. - `verificationData = abi.encode(expectedHash, bitmask)` - Function reverts to false if bitmask length does not match (data.length + 96).", "params": {"data": "Calldata for the function call.", "value": "ETH value to be sent.", "verificationData": "ABI-encoded data containing:        - bytes32 expectedHash        - bytes bitmask (variable length)", "where": "Target contract address.", "who": "Caller address of the original call."}, "returns": {"_0": "True if the calculated masked hash matches the expected hash, false otherwise."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"calculateHash(bytes,address,address,uint256,bytes)": {"notice": "Computes a hash of a call using selective masking over its fields."}, "verifyCall(address,address,uint256,bytes,bytes)": {"notice": "Verifies whether the provided call matches a pre-approved hash using a given bitmask."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/permissions/BitmaskVerifier.sol": "BitmaskVerifier"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530", "urls": ["bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0", "dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf"], "license": "MIT"}, "src/interfaces/permissions/ICustomVerifier.sol": {"keccak256": "0xdb343366ed86f63f8a94ff478c5c8e1d127888e4993c27fcb3bb3b51ae0cb8c7", "urls": ["bzz-raw://12407e89b20982a00961ccc03258e353962607747f214940d18b0989c11547c9", "dweb:/ipfs/Qmay19iWpwiV8LuvWccPjsazVU5Kkhme9QykFfLKfBmvSr"], "license": "BUSL-1.1"}, "src/permissions/BitmaskVerifier.sol": {"keccak256": "0xefa67d5f3be22e44b52f843408fdb113587069f33c861f94b8055a7c695e3610", "urls": ["bzz-raw://7987fec61745fede54fd130dc56f6ee74391bbf618a4849cd6bcab5aef60adc4", "dweb:/ipfs/QmP4tVpzCYS9Shwo88TSTuy3a78Epnh6CpzqovEMDvoCF8"], "license": "BUSL-1.1"}}, "version": 1}, "id": 128}