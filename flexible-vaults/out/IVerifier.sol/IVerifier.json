{"abi": [{"type": "function", "name": "ALLOW_CALL_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "CALLER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "DISALLOW_CALL_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "SET_MERKLE_ROOT_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "allowCalls", "inputs": [{"name": "compactCalls", "type": "tuple[]", "internalType": "struct IVerifier.CompactCall[]", "components": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "selector", "type": "bytes4", "internalType": "bytes4"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "allowedCallAt", "inputs": [{"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IVerifier.CompactCall", "components": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "selector", "type": "bytes4", "internalType": "bytes4"}]}], "stateMutability": "view"}, {"type": "function", "name": "allowedCalls", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "disallowCalls", "inputs": [{"name": "compactCalls", "type": "tuple[]", "internalType": "struct IVerifier.CompactCall[]", "components": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "selector", "type": "bytes4", "internalType": "bytes4"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getVerificationResult", "inputs": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "callData", "type": "bytes", "internalType": "bytes"}, {"name": "verificationPayload", "type": "tuple", "internalType": "struct IVerifier.VerificationPayload", "components": [{"name": "verificationType", "type": "uint8", "internalType": "enum IVerifier.VerificationType"}, {"name": "verificationData", "type": "bytes", "internalType": "bytes"}, {"name": "proof", "type": "bytes32[]", "internalType": "bytes32[]"}]}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "hashCall", "inputs": [{"name": "call", "type": "tuple", "internalType": "struct IVerifier.CompactCall", "components": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "selector", "type": "bytes4", "internalType": "bytes4"}]}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "hashCall", "inputs": [{"name": "call", "type": "tuple", "internalType": "struct IVerifier.ExtendedCall", "components": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "initialize", "inputs": [{"name": "initParams", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isAllowedCall", "inputs": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "callData", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "merkleRoot", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "setMerkleRoot", "inputs": [{"name": "merkleRoot_", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "vault", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IAccessControl"}], "stateMutability": "view"}, {"type": "function", "name": "verifyCall", "inputs": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}, {"name": "verificationPayload", "type": "tuple", "internalType": "struct IVerifier.VerificationPayload", "components": [{"name": "verificationType", "type": "uint8", "internalType": "enum IVerifier.VerificationType"}, {"name": "verificationData", "type": "bytes", "internalType": "bytes"}, {"name": "proof", "type": "bytes32[]", "internalType": "bytes32[]"}]}], "outputs": [], "stateMutability": "view"}, {"type": "event", "name": "Initialized", "inputs": [{"name": "initParams", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "error", "name": "CompactCallAlreadyAllowed", "inputs": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "selector", "type": "bytes4", "internalType": "bytes4"}]}, {"type": "error", "name": "CompactCallNotFound", "inputs": [{"name": "who", "type": "address", "internalType": "address"}, {"name": "where", "type": "address", "internalType": "address"}, {"name": "selector", "type": "bytes4", "internalType": "bytes4"}]}, {"type": "error", "name": "Forbidden", "inputs": []}, {"type": "error", "name": "InvalidLength", "inputs": []}, {"type": "error", "name": "VerificationFailed", "inputs": []}, {"type": "error", "name": "ZeroValue", "inputs": []}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"ALLOW_CALL_ROLE()": "e7815cd7", "CALLER_ROLE()": "774237fc", "DISALLOW_CALL_ROLE()": "5a6ca9f9", "SET_MERKLE_ROOT_ROLE()": "a95f4754", "allowCalls((address,address,bytes4)[])": "37b893bf", "allowedCallAt(uint256)": "3ab85463", "allowedCalls()": "6218e888", "disallowCalls((address,address,bytes4)[])": "3ae314e8", "getVerificationResult(address,address,uint256,bytes,(uint8,bytes,bytes32[]))": "b27fb509", "hashCall((address,address,bytes4))": "bffeda7d", "hashCall((address,address,uint256,bytes))": "d46c2491", "initialize(bytes)": "439fab91", "isAllowedCall(address,address,bytes)": "ba3c3485", "merkleRoot()": "2eb4a7ab", "setMerkleRoot(bytes32)": "7cb64759", "vault()": "fbfa77cf", "verifyCall(address,address,uint256,bytes,(uint8,bytes,bytes32[]))": "10749ebd"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"bytes4\",\"name\":\"selector\",\"type\":\"bytes4\"}],\"name\":\"CompactCallAlreadyAllowed\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"bytes4\",\"name\":\"selector\",\"type\":\"bytes4\"}],\"name\":\"CompactCallNotFound\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Forbidden\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidLength\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"VerificationFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZeroValue\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"initParams\",\"type\":\"bytes\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"ALLOW_CALL_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"CALLER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DISALLOW_CALL_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SET_MERKLE_ROOT_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"bytes4\",\"name\":\"selector\",\"type\":\"bytes4\"}],\"internalType\":\"struct IVerifier.CompactCall[]\",\"name\":\"compactCalls\",\"type\":\"tuple[]\"}],\"name\":\"allowCalls\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"allowedCallAt\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"bytes4\",\"name\":\"selector\",\"type\":\"bytes4\"}],\"internalType\":\"struct IVerifier.CompactCall\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"allowedCalls\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"bytes4\",\"name\":\"selector\",\"type\":\"bytes4\"}],\"internalType\":\"struct IVerifier.CompactCall[]\",\"name\":\"compactCalls\",\"type\":\"tuple[]\"}],\"name\":\"disallowCalls\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"callData\",\"type\":\"bytes\"},{\"components\":[{\"internalType\":\"enum IVerifier.VerificationType\",\"name\":\"verificationType\",\"type\":\"uint8\"},{\"internalType\":\"bytes\",\"name\":\"verificationData\",\"type\":\"bytes\"},{\"internalType\":\"bytes32[]\",\"name\":\"proof\",\"type\":\"bytes32[]\"}],\"internalType\":\"struct IVerifier.VerificationPayload\",\"name\":\"verificationPayload\",\"type\":\"tuple\"}],\"name\":\"getVerificationResult\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"bytes4\",\"name\":\"selector\",\"type\":\"bytes4\"}],\"internalType\":\"struct IVerifier.CompactCall\",\"name\":\"call\",\"type\":\"tuple\"}],\"name\":\"hashCall\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"internalType\":\"struct IVerifier.ExtendedCall\",\"name\":\"call\",\"type\":\"tuple\"}],\"name\":\"hashCall\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"initParams\",\"type\":\"bytes\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"callData\",\"type\":\"bytes\"}],\"name\":\"isAllowedCall\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"merkleRoot\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"merkleRoot_\",\"type\":\"bytes32\"}],\"name\":\"setMerkleRoot\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"vault\",\"outputs\":[{\"internalType\":\"contract IAccessControl\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"who\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"where\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"},{\"components\":[{\"internalType\":\"enum IVerifier.VerificationType\",\"name\":\"verificationType\",\"type\":\"uint8\"},{\"internalType\":\"bytes\",\"name\":\"verificationData\",\"type\":\"bytes\"},{\"internalType\":\"bytes32[]\",\"name\":\"proof\",\"type\":\"bytes32[]\"}],\"internalType\":\"struct IVerifier.VerificationPayload\",\"name\":\"verificationPayload\",\"type\":\"tuple\"}],\"name\":\"verifyCall\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"events\":{\"Initialized(bytes)\":{\"params\":{\"initParams\":\"The initialization parameters.\"}}},\"kind\":\"dev\",\"methods\":{\"getVerificationResult(address,address,uint256,bytes,(uint8,bytes,bytes32[]))\":{\"returns\":{\"_0\":\"bool Returns whether a given call passes verification\"}},\"initialize(bytes)\":{\"params\":{\"initParams\":\"The initialization parameters.\"}}},\"version\":1},\"userdoc\":{\"errors\":{\"CompactCallAlreadyAllowed(address,address,bytes4)\":[{\"notice\":\"Thrown when attempting to allow an already allowed CompactCall\"}],\"CompactCallNotFound(address,address,bytes4)\":[{\"notice\":\"Thrown when attempting to disallow a non-existent CompactCall\"}],\"Forbidden()\":[{\"notice\":\"Thrown when a caller lacks necessary permissions\"}],\"InvalidLength()\":[{\"notice\":\"Thrown when input array lengths mismatch\"}],\"VerificationFailed()\":[{\"notice\":\"Thrown when a call fails verification\"}],\"ZeroValue()\":[{\"notice\":\"Thrown when a required value (e.g. address) is zero\"}]},\"events\":{\"Initialized(bytes)\":{\"notice\":\"Emitted once the entity has been initialized.\"}},\"kind\":\"user\",\"methods\":{\"ALLOW_CALL_ROLE()\":{\"notice\":\"Role identifier for allowing new CompactCalls\"},\"CALLER_ROLE()\":{\"notice\":\"Role identifier for permitted callers\"},\"DISALLOW_CALL_ROLE()\":{\"notice\":\"Role identifier for removing CompactCalls\"},\"SET_MERKLE_ROOT_ROLE()\":{\"notice\":\"Role identifier for setting Merkle root\"},\"allowCalls((address,address,bytes4)[])\":{\"notice\":\"Adds a list of CompactCalls to the allowlist\"},\"allowedCallAt(uint256)\":{\"notice\":\"Returns the compact call at a specific index\"},\"allowedCalls()\":{\"notice\":\"Returns number of currently allowed compact calls\"},\"disallowCalls((address,address,bytes4)[])\":{\"notice\":\"Removes a list of CompactCalls from the allowlist\"},\"hashCall((address,address,bytes4))\":{\"notice\":\"Computes the hash of a CompactCall\"},\"hashCall((address,address,uint256,bytes))\":{\"notice\":\"Computes the hash of an ExtendedCall\"},\"initialize(bytes)\":{\"notice\":\"Initializes the factory-created entity with arbitrary initialization data.\"},\"isAllowedCall(address,address,bytes)\":{\"notice\":\"Checks if a CompactCall is explicitly allowed\"},\"merkleRoot()\":{\"notice\":\"Returns the current Merkle root\"},\"setMerkleRoot(bytes32)\":{\"notice\":\"Sets the Merkle root used for verification\"},\"vault()\":{\"notice\":\"Returns the vault associated to this Verifier contract\"},\"verifyCall(address,address,uint256,bytes,(uint8,bytes,bytes32[]))\":{\"notice\":\"Validates a function call using the provided verification payload, reverts on failure\"}},\"notice\":\"Interface for the Verifier contract, used to validate allowed calls via multiple verification mechanisms\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/permissions/IVerifier.sol\":\"IVerifier\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0\",\"dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol\":{\"keccak256\":\"0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd\",\"dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol\":{\"keccak256\":\"0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd\",\"dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol\":{\"keccak256\":\"0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2\",\"dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/Hashes.sol\":{\"keccak256\":\"0x8cbd338f083224b4b6f0ff42cbda641a0a6c31ffcdca197452b97fe4d0918269\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f517dec5ba0c6491395acbf7f1d621f4e89e8f218bf5303c867b1c5ad70c6b11\",\"dweb:/ipfs/QmWmXHRLEw8W6ckth7NyYTU88YfvuS7xSsfae5ksL8qNUe\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/MerkleProof.sol\":{\"keccak256\":\"0x36a0c409c437a753cac9b92b75f93b0fbe92803bf2c8ff1517e54b247f166134\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f91ba472de411aa557cdbf6560c40750d87bd11c9060bc04d2ba7119af9d5a6\",\"dweb:/ipfs/QmQjtYo2i7dDvzCEzZ67bDoNSG4RrwMoxPWuqFmX5Xzpuw\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol\":{\"keccak256\":\"0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231\",\"dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y\"]},\"src/interfaces/factories/IFactoryEntity.sol\":{\"keccak256\":\"0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812\",\"dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx\"]},\"src/interfaces/permissions/ICustomVerifier.sol\":{\"keccak256\":\"0xdb343366ed86f63f8a94ff478c5c8e1d127888e4993c27fcb3bb3b51ae0cb8c7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://12407e89b20982a00961ccc03258e353962607747f214940d18b0989c11547c9\",\"dweb:/ipfs/Qmay19iWpwiV8LuvWccPjsazVU5Kkhme9QykFfLKfBmvSr\"]},\"src/interfaces/permissions/IVerifier.sol\":{\"keccak256\":\"0x4b42731d76efaffcb017577bd2a478d00a3058c69220d6ea8e26f30f74476be7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ff2a6f09a946dd8a470dfe495b2a64d24b849c6d071dc859589140fb1b983362\",\"dweb:/ipfs/Qmb6UoqkmaC8tJMqkMfjcWR7pn1WCwPwuKKvUbLBQbPcJS\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "bytes4", "name": "selector", "type": "bytes4"}], "type": "error", "name": "CompactCallAlreadyAllowed"}, {"inputs": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "bytes4", "name": "selector", "type": "bytes4"}], "type": "error", "name": "CompactCallNotFound"}, {"inputs": [], "type": "error", "name": "Forbidden"}, {"inputs": [], "type": "error", "name": "InvalidLength"}, {"inputs": [], "type": "error", "name": "VerificationFailed"}, {"inputs": [], "type": "error", "name": "ZeroValue"}, {"inputs": [{"internalType": "bytes", "name": "initParams", "type": "bytes", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "ALLOW_CALL_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "CALLER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DISALLOW_CALL_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SET_MERKLE_ROOT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "struct IVerifier.CompactCall[]", "name": "compactCalls", "type": "tuple[]", "components": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "bytes4", "name": "selector", "type": "bytes4"}]}], "stateMutability": "nonpayable", "type": "function", "name": "allowCalls"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "allowedCallAt", "outputs": [{"internalType": "struct IVerifier.CompactCall", "name": "", "type": "tuple", "components": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "bytes4", "name": "selector", "type": "bytes4"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "allowedCalls", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "struct IVerifier.CompactCall[]", "name": "compactCalls", "type": "tuple[]", "components": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "bytes4", "name": "selector", "type": "bytes4"}]}], "stateMutability": "nonpayable", "type": "function", "name": "disallowCalls"}, {"inputs": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}, {"internalType": "struct IVerifier.VerificationPayload", "name": "verificationPayload", "type": "tuple", "components": [{"internalType": "enum IVerifier.VerificationType", "name": "verificationType", "type": "uint8"}, {"internalType": "bytes", "name": "verificationData", "type": "bytes"}, {"internalType": "bytes32[]", "name": "proof", "type": "bytes32[]"}]}], "stateMutability": "view", "type": "function", "name": "getVerificationResult", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "struct IVerifier.CompactCall", "name": "call", "type": "tuple", "components": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "bytes4", "name": "selector", "type": "bytes4"}]}], "stateMutability": "pure", "type": "function", "name": "hashCall", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "struct IVerifier.ExtendedCall", "name": "call", "type": "tuple", "components": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}]}], "stateMutability": "pure", "type": "function", "name": "hashCall", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "bytes", "name": "initParams", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}], "stateMutability": "view", "type": "function", "name": "isAllowedCall", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "merkleRoot", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "bytes32", "name": "merkleRoot_", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "setMerkleRoot"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "vault", "outputs": [{"internalType": "contract IAccessControl", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "who", "type": "address"}, {"internalType": "address", "name": "where", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "struct IVerifier.VerificationPayload", "name": "verificationPayload", "type": "tuple", "components": [{"internalType": "enum IVerifier.VerificationType", "name": "verificationType", "type": "uint8"}, {"internalType": "bytes", "name": "verificationData", "type": "bytes"}, {"internalType": "bytes32[]", "name": "proof", "type": "bytes32[]"}]}], "stateMutability": "view", "type": "function", "name": "verifyCall"}], "devdoc": {"kind": "dev", "methods": {"getVerificationResult(address,address,uint256,bytes,(uint8,bytes,bytes32[]))": {"returns": {"_0": "bool Returns whether a given call passes verification"}}, "initialize(bytes)": {"params": {"initParams": "The initialization parameters."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"ALLOW_CALL_ROLE()": {"notice": "Role identifier for allowing new CompactCalls"}, "CALLER_ROLE()": {"notice": "Role identifier for permitted callers"}, "DISALLOW_CALL_ROLE()": {"notice": "Role identifier for removing CompactCalls"}, "SET_MERKLE_ROOT_ROLE()": {"notice": "Role identifier for setting <PERSON><PERSON><PERSON> root"}, "allowCalls((address,address,bytes4)[])": {"notice": "Adds a list of CompactCalls to the allowlist"}, "allowedCallAt(uint256)": {"notice": "Returns the compact call at a specific index"}, "allowedCalls()": {"notice": "Returns number of currently allowed compact calls"}, "disallowCalls((address,address,bytes4)[])": {"notice": "Removes a list of CompactCalls from the allowlist"}, "hashCall((address,address,bytes4))": {"notice": "Computes the hash of a CompactCall"}, "hashCall((address,address,uint256,bytes))": {"notice": "Computes the hash of an ExtendedCall"}, "initialize(bytes)": {"notice": "Initializes the factory-created entity with arbitrary initialization data."}, "isAllowedCall(address,address,bytes)": {"notice": "Checks if a CompactCall is explicitly allowed"}, "merkleRoot()": {"notice": "Returns the current <PERSON><PERSON><PERSON> root"}, "setMerkleRoot(bytes32)": {"notice": "Sets the <PERSON><PERSON><PERSON> root used for verification"}, "vault()": {"notice": "Returns the vault associated to this Verifier contract"}, "verifyCall(address,address,uint256,bytes,(uint8,bytes,bytes32[]))": {"notice": "Validates a function call using the provided verification payload, reverts on failure"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/permissions/IVerifier.sol": "IVerifier"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05", "urls": ["bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08", "dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530", "urls": ["bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0", "dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol": {"keccak256": "0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6", "urls": ["bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd", "dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol": {"keccak256": "0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58", "urls": ["bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd", "dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol": {"keccak256": "0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26", "urls": ["bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2", "dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/Hashes.sol": {"keccak256": "0x8cbd338f083224b4b6f0ff42cbda641a0a6c31ffcdca197452b97fe4d0918269", "urls": ["bzz-raw://f517dec5ba0c6491395acbf7f1d621f4e89e8f218bf5303c867b1c5ad70c6b11", "dweb:/ipfs/QmWmXHRLEw8W6ckth7NyYTU88YfvuS7xSsfae5ksL8qNUe"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/MerkleProof.sol": {"keccak256": "0x36a0c409c437a753cac9b92b75f93b0fbe92803bf2c8ff1517e54b247f166134", "urls": ["bzz-raw://0f91ba472de411aa557cdbf6560c40750d87bd11c9060bc04d2ba7119af9d5a6", "dweb:/ipfs/QmQjtYo2i7dDvzCEzZ67bDoNSG4RrwMoxPWuqFmX5Xzpuw"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol": {"keccak256": "0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06", "urls": ["bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231", "dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y"], "license": "MIT"}, "src/interfaces/factories/IFactoryEntity.sol": {"keccak256": "0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5", "urls": ["bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812", "dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx"], "license": "BUSL-1.1"}, "src/interfaces/permissions/ICustomVerifier.sol": {"keccak256": "0xdb343366ed86f63f8a94ff478c5c8e1d127888e4993c27fcb3bb3b51ae0cb8c7", "urls": ["bzz-raw://12407e89b20982a00961ccc03258e353962607747f214940d18b0989c11547c9", "dweb:/ipfs/Qmay19iWpwiV8LuvWccPjsazVU5Kkhme9QykFfLKfBmvSr"], "license": "BUSL-1.1"}, "src/interfaces/permissions/IVerifier.sol": {"keccak256": "0x4b42731d76efaffcb017577bd2a478d00a3058c69220d6ea8e26f30f74476be7", "urls": ["bzz-raw://ff2a6f09a946dd8a470dfe495b2a64d24b849c6d071dc859589140fb1b983362", "dweb:/ipfs/Qmb6UoqkmaC8tJMqkMfjcWR7pn1WCwPwuKKvUbLBQbPcJS"], "license": "BUSL-1.1"}}, "version": 1}, "id": 106}