{"abi": [{"type": "constructor", "inputs": [{"name": "name_", "type": "string", "internalType": "string"}, {"name": "version_", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "ACCEPT_REPORT_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "ADD_SUPPORTED_ASSETS_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "REMOVE_SUPPORTED_ASSETS_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "SET_SECURITY_PARAMS_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "SUBMIT_REPORTS_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "acceptReport", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}, {"name": "priceD18", "type": "uint256", "internalType": "uint256"}, {"name": "timestamp", "type": "uint32", "internalType": "uint32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "addSupportedAssets", "inputs": [{"name": "assets", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getReport", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IOracle.DetailedReport", "components": [{"name": "priceD18", "type": "uint224", "internalType": "uint224"}, {"name": "timestamp", "type": "uint32", "internalType": "uint32"}, {"name": "isSuspicious", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "initParams", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isSupportedAsset", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "removeSupportedAssets", "inputs": [{"name": "assets", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "securityParams", "inputs": [], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IOracle.SecurityParams", "components": [{"name": "maxAbsoluteDeviation", "type": "uint224", "internalType": "uint224"}, {"name": "suspiciousAbsoluteDeviation", "type": "uint224", "internalType": "uint224"}, {"name": "maxRelativeDeviationD18", "type": "uint64", "internalType": "uint64"}, {"name": "suspiciousRelativeDeviationD18", "type": "uint64", "internalType": "uint64"}, {"name": "timeout", "type": "uint32", "internalType": "uint32"}, {"name": "depositInterval", "type": "uint32", "internalType": "uint32"}, {"name": "redeemInterval", "type": "uint32", "internalType": "uint32"}]}], "stateMutability": "view"}, {"type": "function", "name": "setSecurityParams", "inputs": [{"name": "securityParams_", "type": "tuple", "internalType": "struct IOracle.SecurityParams", "components": [{"name": "maxAbsoluteDeviation", "type": "uint224", "internalType": "uint224"}, {"name": "suspiciousAbsoluteDeviation", "type": "uint224", "internalType": "uint224"}, {"name": "maxRelativeDeviationD18", "type": "uint64", "internalType": "uint64"}, {"name": "suspiciousRelativeDeviationD18", "type": "uint64", "internalType": "uint64"}, {"name": "timeout", "type": "uint32", "internalType": "uint32"}, {"name": "depositInterval", "type": "uint32", "internalType": "uint32"}, {"name": "redeemInterval", "type": "uint32", "internalType": "uint32"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON>", "inputs": [{"name": "vault_", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "submitReports", "inputs": [{"name": "reports", "type": "tuple[]", "internalType": "struct IOracle.Report[]", "components": [{"name": "asset", "type": "address", "internalType": "address"}, {"name": "priceD18", "type": "uint224", "internalType": "uint224"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportedAssetAt", "inputs": [{"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "supportedAssets", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "validatePrice", "inputs": [{"name": "priceD18", "type": "uint256", "internalType": "uint256"}, {"name": "asset", "type": "address", "internalType": "address"}], "outputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bool", "internalType": "bool"}, {"name": "isSuspicious", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "vault", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IShareModule"}], "stateMutability": "view"}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "initParams", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "ReportAccepted", "inputs": [{"name": "asset", "type": "address", "indexed": true, "internalType": "address"}, {"name": "priceD18", "type": "uint224", "indexed": true, "internalType": "uint224"}, {"name": "timestamp", "type": "uint32", "indexed": true, "internalType": "uint32"}], "anonymous": false}, {"type": "event", "name": "ReportsSubmitted", "inputs": [{"name": "reports", "type": "tuple[]", "indexed": false, "internalType": "struct IOracle.Report[]", "components": [{"name": "asset", "type": "address", "internalType": "address"}, {"name": "priceD18", "type": "uint224", "internalType": "uint224"}]}], "anonymous": false}, {"type": "event", "name": "SecurityParamsSet", "inputs": [{"name": "securityParams", "type": "tuple", "indexed": false, "internalType": "struct IOracle.SecurityParams", "components": [{"name": "maxAbsoluteDeviation", "type": "uint224", "internalType": "uint224"}, {"name": "suspiciousAbsoluteDeviation", "type": "uint224", "internalType": "uint224"}, {"name": "maxRelativeDeviationD18", "type": "uint64", "internalType": "uint64"}, {"name": "suspiciousRelativeDeviationD18", "type": "uint64", "internalType": "uint64"}, {"name": "timeout", "type": "uint32", "internalType": "uint32"}, {"name": "depositInterval", "type": "uint32", "internalType": "uint32"}, {"name": "redeemInterval", "type": "uint32", "internalType": "uint32"}]}], "anonymous": false}, {"type": "event", "name": "<PERSON><PERSON><PERSON>", "inputs": [{"name": "vault", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SupportedAssetsAdded", "inputs": [{"name": "assets", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "SupportedAssetsRemoved", "inputs": [{"name": "assets", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "error", "name": "AlreadySupportedAsset", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "Forbidden", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "InvalidPrice", "inputs": [{"name": "priceD18", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidReport", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "timestamp", "type": "uint256", "internalType": "uint256"}, {"name": "minTimestamp", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "UnsupportedAsset", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ZeroValue", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "147:9400:127:-:0;;;1198:167;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1280:46;;;;;;;;;;;;-1:-1:-1;;;1280:46:127;;;;;;1310:5;1317:8;1280:19;:46::i;:::-;1259:67;;1336:22;:20;:22::i;:::-;1198:167;;147:9400;;656:343:113;761:7;986:4;970:22;;969:23;941:1;908:12;922:4;928:7;856:80;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;846:91;;;;;;838:100;;:104;;;;:::i;:::-;810:146;;;;;;2258:25:192;;2246:2;2231:18;;2112:177;810:146:113;;;;;;;;;;;;;787:179;;;;;;:205;780:212;;656:343;;;;;:::o;7709:422:3:-;7824:30;7857:26;:24;:26::i;:::-;7898:15;;;;-1:-1:-1;7898:15:3;;;;;7894:76;;;7936:23;;-1:-1:-1;;;7936:23:3;;;;;;;;;;;7894:76;7983:14;;-1:-1:-1;;;;;7983:14:3;;;:34;7979:146;;8033:33;;-1:-1:-1;;;;;;8033:33:3;-1:-1:-1;;;;;8033:33:3;;;;;8085:29;;2438:50:192;;;8085:29:3;;2426:2:192;2411:18;8085:29:3;;;;;;;7979:146;7758:373;7709:422::o;9071:205::-;9129:30;;3147:66;9186:27;9171:42;9071:205;-1:-1:-1;;9071:205:3:o;14:127:192:-;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:983;235:6;243;296:2;284:9;275:7;271:23;267:32;264:52;;;312:1;309;302:12;264:52;339:16;;-1:-1:-1;;;;;404:14:192;;;401:34;;;431:1;428;421:12;401:34;469:6;458:9;454:22;444:32;;514:7;507:4;503:2;499:13;495:27;485:55;;536:1;533;526:12;485:55;565:2;559:9;587:2;583;580:10;577:36;;;593:18;;:::i;:::-;668:2;662:9;636:2;722:13;;-1:-1:-1;;718:22:192;;;742:2;714:31;710:40;698:53;;;766:18;;;786:22;;;763:46;760:72;;;812:18;;:::i;:::-;852:10;848:2;841:22;887:2;879:6;872:18;929:7;922:4;917:2;913;909:11;905:22;902:35;899:55;;;950:1;947;940:12;899:55;1003:2;996:4;992:2;988:13;981:4;973:6;969:17;963:43;1050:1;1043:4;1038:2;1030:6;1026:15;1022:26;1015:37;1071:6;1061:16;;;;;;;1117:4;1106:9;1102:20;1096:27;1086:37;;146:983;;;;;:::o;1134:212::-;1176:3;1214:5;1208:12;1258:6;1251:4;1244:5;1240:16;1235:3;1229:36;1320:1;1284:16;;1309:13;;;-1:-1:-1;1284:16:192;;1134:212;-1:-1:-1;1134:212:192:o;1351:526::-;1689:33;1684:3;1677:46;1659:3;1745:66;1771:39;1806:2;1801:3;1797:12;1789:6;1771:39;:::i;:::-;1763:6;1745:66;:::i;:::-;1820:21;;;-1:-1:-1;;1868:2:192;1857:14;;1351:526;-1:-1:-1;;1351:526:192:o;1882:225::-;1949:9;;;1970:11;;;1967:134;;;2023:10;2018:3;2014:20;2011:1;2004:31;2058:4;2055:1;2048:15;2086:4;2083:1;2076:15;2294:200;147:9400:127;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "147:9400:127:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;563:103;;614:52;563:103;;;;;160:25:192;;;148:2;133:18;563:103:127;;;;;;;;1869:137;;;;;;:::i;:::-;;:::i;:::-;;;-1:-1:-1;;;;;545:32:192;;;527:51;;515:2;500:18;1869:137:127;381:203:192;2930:148:127;;;;;;:::i;:::-;;:::i;:::-;;438:91;;483:46;438:91;;5392:164;;;;;;:::i;:::-;;:::i;1554:125::-;;;:::i;:::-;;;;;;;:::i;3112:351::-;;;;;;:::i;:::-;;:::i;4630:728::-;;;;;;:::i;:::-;;:::i;700:105::-;;752:53;700:105;;2522:341;;;;;;:::i;:::-;;:::i;:::-;;;;3593:14:192;;3586:22;3568:41;;3652:14;;3645:22;3640:2;3625:18;;3618:50;3541:18;2522:341:127;3406:268:192;3497:1099:127;;;;;;:::i;:::-;;:::i;5590:144::-;;;;;;:::i;:::-;;:::i;2040:140::-;;;;;;:::i;:::-;;:::i;:::-;;;5111:14:192;;5104:22;5086:41;;5074:2;5059:18;2040:140:127;4946:187:192;839:111:127;;894:56;839:111;;2214:274;;;;;;:::i;:::-;;:::i;:::-;;;;5372:13:192;;-1:-1:-1;;;;;5368:39:192;5350:58;;5468:4;5456:17;;;5450:24;5476:10;5446:41;5424:20;;;5417:71;5546:17;;;5540:24;5533:32;5526:40;5504:20;;;5497:70;5338:2;5323:18;2214:274:127;5138:435:192;1713:122:127;;;:::i;5768:567::-;;;;;;:::i;:::-;;:::i;311:93::-;;357:47;311:93;;1422:98;;;:::i;1869:137::-;1931:7;1957:42;1993:5;1957:16;:14;:16::i;:::-;:32;;;:35;:42::i;:::-;1950:49;1869:137;-1:-1:-1;;1869:137:127:o;2930:148::-;4158:30:3;4191:26;:24;:26::i;:::-;4302:15;;4158:59;;-1:-1:-1;4302:15:3;-1:-1:-1;;;4302:15:3;;;4301:16;;-1:-1:-1;;;;;4348:14:3;4279:19;4724:16;;:34;;;;;4744:14;4724:34;4704:54;;4768:17;4788:11;-1:-1:-1;;;;;4788:16:3;4803:1;4788:16;:50;;;;-1:-1:-1;4816:4:3;4808:25;:30;4788:50;4768:70;;4854:12;4853:13;:30;;;;;4871:12;4870:13;4853:30;4849:91;;;4906:23;;-1:-1:-1;;;4906:23:3;;;;;;;;;;;4849:91;4949:18;;-1:-1:-1;;4949:18:3;4966:1;4949:18;;;4977:67;;;;5011:22;;-1:-1:-1;;;;5011:22:3;-1:-1:-1;;;5011:22:3;;;4977:67;3008:25:127::1;3022:10;;3008:13;:25::i;:::-;3048:23;3060:10;;3048:23;;;;;;;:::i;:::-;;;;;;;;5068:14:3::0;5064:101;;;5098:23;;-1:-1:-1;;;;5098:23:3;;;5140:14;;-1:-1:-1;6536:50:192;;5140:14:3;;6524:2:192;6509:18;5140:14:3;;;;;;;5064:101;4092:1079;;;;;2930:148:127;;:::o;5392:164::-;614:52;1078:16;:14;:16::i;:::-;:22;-1:-1:-1;;;;;1078:22:127;1055:55;1111:4;966:10:5;1055:75:127;;-1:-1:-1;;;;;;1055:75:127;;;;;;;;;;6771:25:192;;;;-1:-1:-1;;;;;6832:32:192;6812:18;;;6805:60;6744:18;;1055:75:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1050:125;;1153:11;;-1:-1:-1;;;1153:11:127;;;;;;;;;;;1050:125;5514:35:::1;;;::::0;;::::1;::::0;::::1;5533:15:::0;5514:35:::1;:::i;:::-;:18;:35::i;:::-;5392:164:::0;;:::o;1554:125::-;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1641:16:127;:14;:16::i;:::-;1634:38;;;;;;;;1641:31;;;1634:38;-1:-1:-1;;;;;1634:38:127;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1634:38:127;;;;;;;;;;-1:-1:-1;;;1634:38:127;;;;;;;;;;-1:-1:-1;;;1634:38:127;;;;;;;;-1:-1:-1;;;1634:38:127;;;;;;;;-1:-1:-1;;;1634:38:127;;;;;;;;;1554:125;-1:-1:-1;1554:125:127:o;3112:351::-;-1:-1:-1;;;;;3169:20:127;;3165:69;;3212:11;;-1:-1:-1;;;3212:11:127;;;;;;;;;;;3165:69;3243:23;3269:16;:14;:16::i;:::-;3307:7;;3243:42;;-1:-1:-1;;;;;;3307:7:127;3299:30;3295:91;;3352:23;;-1:-1:-1;;;3352:23:127;;;;;;;;;;;3295:91;3395:30;;-1:-1:-1;;;;;;3395:30:127;-1:-1:-1;;;;;3395:30:127;;;;;;;3440:16;;;;-1:-1:-1;;3440:16:127;3155:308;3112:351;:::o;4630:728::-;3395:21:6;:19;:21::i;:::-;483:46:127::1;1078:16;:14;:16::i;:::-;:22:::0;-1:-1:-1;;;;;1078:22:127::1;1055:55;1111:4:::0;966:10:5;1055:75:127::1;::::0;-1:-1:-1;;;;;;1055:75:127::1;::::0;;;;;;::::1;::::0;::::1;6771:25:192::0;;;;-1:-1:-1;;;;;6832:32:192;6812:18;;;6805:60;6744:18;;1055:75:127::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1050:125;;1153:11;;-1:-1:-1::0;;;1153:11:127::1;;;;;;;;;;;1050:125;4792:23:::2;4818:16;:14;:16::i;:::-;-1:-1:-1::0;;;;;4877:16:127;::::2;4844:30;4877:16:::0;;;:9:::2;::::0;::::2;:16;::::0;;;;4908:20:::2;::::0;::::2;::::0;4792:42;;-1:-1:-1;4877:16:127;4908:20:::2;;4907:21;::::0;:53:::2;;-1:-1:-1::0;4932:16:127;;-1:-1:-1;;;;;4932:16:127::2;:28:::0;::::2;;4907:53;:87;;;-1:-1:-1::0;4964:17:127;;:30:::2;::::0;;::::2;-1:-1:-1::0;;;4964:17:127;;::::2;;:30;;4907:87;4903:140;;;5017:15;;-1:-1:-1::0;;;5017:15:127::2;;;;;;;;;;;4903:140;5052:20;::::0;;::::2;:28:::0;;-1:-1:-1;;5052:28:127::2;::::0;;5148:7;;5189:16;;5219:22;;;;5122:16;;::::2;::::0;-1:-1:-1;;;;;5148:7:127;;::::2;::::0;:20:::2;::::0;5182:5;;-1:-1:-1;;;;;5189:16:127;;::::2;::::0;5207:34:::2;::::0;-1:-1:-1;;;5219:22:127;::::2;;;5207:9:::0;:34:::2;:::i;:::-;5255:21;::::0;::::2;::::0;5243:33:::2;::::0;-1:-1:-1;;;5255:21:127;::::2;;;5243:9:::0;:33:::2;:::i;:::-;5148:138;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;::::0;::::2;;;;;;;;;;;;::::0;::::2;;;;;-1:-1:-1::0;;5323:16:127;;5301:50:::2;::::0;::::2;::::0;::::2;::::0;-1:-1:-1;;;;;;5323:16:127;;::::2;::::0;-1:-1:-1;;;;;;5301:50:127;::::2;::::0;::::2;::::0;5323:16:::2;::::0;5301:50:::2;4782:576;;;3426:1:6::1;3437:20:::0;1949:1;-1:-1:-1;;;;;;;;;;;4113:23:6;3860:283;3437:20;4630:728:127;;;:::o;2522:341::-;2599:12;2613:17;2642:23;2668:16;:14;:16::i;:::-;2642:42;-1:-1:-1;2699:33:127;:17;;;2726:5;2699:26;:33::i;:::-;2694:86;;2756:5;2763;2748:21;;;;;;;2694:86;-1:-1:-1;;;;;2821:16:127;;;;;;:9;;;:16;;;;;;;;;2796:60;;;;;;;2839:16;;;2796:60;-1:-1:-1;;;;;2796:60:127;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2796:60:127;;;;;;;;;;-1:-1:-1;;;2796:60:127;;;;;;;;;;-1:-1:-1;;;2796:60:127;;;;;;;;-1:-1:-1;;;2796:60:127;;;;;;;;-1:-1:-1;;;2796:60:127;;;;;;;;;;;;2811:8;;2796:14;:60::i;:::-;2789:67;;;;;2522:341;;;;;;:::o;3497:1099::-;3395:21:6;:19;:21::i;:::-;357:47:127::1;1078:16;:14;:16::i;:::-;:22:::0;-1:-1:-1;;;;;1078:22:127::1;1055:55;1111:4:::0;966:10:5;1055:75:127::1;::::0;-1:-1:-1;;;;;;1055:75:127::1;::::0;;;;;;::::1;::::0;::::1;6771:25:192::0;;;;-1:-1:-1;;;;;6832:32:192;6812:18;;;6805:60;6744:18;;1055:75:127::1;;;;;;;;;;;;;;;;;::::0;::::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1050:125;;1153:11;;-1:-1:-1::0;;;1153:11:127::1;;;;;;;;;;;1050:125;3609:23:::2;3635:16;:14;:16::i;:::-;3661:56;::::0;;::::2;::::0;::::2;::::0;;3701:16:::2;::::0;::::2;3661:56:::0;-1:-1:-1;;;;;3661:56:127;;::::2;::::0;;;;;;::::2;;::::0;::::2;::::0;;;;;-1:-1:-1;;;;;3661:56:127;;::::2;::::0;;;;;;;-1:-1:-1;;;3661:56:127;::::2;::::0;;::::2;::::0;;;;::::2;-1:-1:-1::0;;;3661:56:127;::::2;::::0;::::2;::::0;;;;-1:-1:-1;;;3661:56:127;::::2;::::0;::::2;::::0;;;;;;-1:-1:-1;;;3661:56:127;;::::2;;::::0;;;;3609:42;;-1:-1:-1;3661:37:127::2;::::0;3760:49:::2;::::0;:15:::2;:49;:::i;:::-;3727:83;;3820:22;3870:15;:30;;;3852:48;;:15;:48;;;;:::i;:::-;3933:7:::0;;3820:81;;-1:-1:-1;;;;;;3933:7:127::2;4002:17;::::0;::::2;4089:9;::::0;::::2;3911:19;4108:442;4128:18:::0;;::::2;4108:442;;;4167:22;4192:7;;4200:1;4192:10;;;;;;;:::i;:::-;;;::::0;;;::::2;::::0;-1:-1:-1;4221:39:127::2;::::0;-1:-1:-1;4247:12:127::2;;::::0;::::2;4192:10:::0;4247:12:::2;:::i;:::-;4221:16:::0;;:25:::2;:39::i;:::-;4216:116;;4304:12;;::::0;::::2;:6:::0;:12:::2;:::i;:::-;4287:30;::::0;-1:-1:-1;;;4287:30:127;;-1:-1:-1;;;;;545:32:192;;;4287:30:127::2;::::0;::::2;527:51:192::0;500:18;;4287:30:127::2;;;;;;;;4216:116;4349:71;4363:15:::0;4380::::2;::::0;;;::::2;::::0;::::2;;:::i;:::-;4397:8:::0;:22:::2;4406:12;;::::0;::::2;:6:::0;:12:::2;:::i;:::-;-1:-1:-1::0;;;;;4397:22:127::2;-1:-1:-1::0;;;;;4397:22:127::2;;;;;;;;;;;;4349:13;:71::i;:::-;4345:195;;;-1:-1:-1::0;;;;;4440:19:127;::::2;;4460:12;;::::0;::::2;:6:::0;:12:::2;:::i;:::-;4474:15;::::0;;;::::2;::::0;::::2;;:::i;:::-;4491:16;4509:15;4440:85;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;::::0;::::2;;;;;;;;;;;;::::0;::::2;;;;;;;;;4345:195;-1:-1:-1::0;4148:3:127::2;;4108:442;;;;4564:25;4581:7;;4564:25;;;;;;;:::i;:::-;;;;;;;;3599:997;;;;;;;3426:1:6::1;3437:20:::0;1949:1;-1:-1:-1;;;;;;;;;;;4113:23:6;3860:283;5590:144:127;752:53;1078:16;:14;:16::i;:::-;:22;-1:-1:-1;;;;;1078:22:127;1055:55;1111:4;966:10:5;1055:75:127;;-1:-1:-1;;;;;;1055:75:127;;;;;;;;;;6771:25:192;;;;-1:-1:-1;;;;;6832:32:192;6812:18;;;6805:60;6744:18;;1055:75:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1050:125;;1153:11;;-1:-1:-1;;;1153:11:127;;;;;;;;;;;1050:125;5700:27:::1;5720:6;;5700:27;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;::::0;::::1;::::0;;;;-1:-1:-1;5700:19:127::1;::::0;-1:-1:-1;;;5700:27:127:i:1;2040:140::-:0;2102:4;2125:48;2167:5;2125:16;:14;:16::i;:::-;:32;;;:41;:48::i;2214:274::-;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;2328:16:127;:14;:16::i;:::-;2302:42;-1:-1:-1;2359:33:127;:17;;;2386:5;2359:26;:33::i;:::-;2354:95;;2415:23;;-1:-1:-1;;;2415:23:127;;-1:-1:-1;;;;;545:32:192;;2415:23:127;;;527:51:192;500:18;;2415:23:127;381:203:192;2354:95:127;-1:-1:-1;;;;;2465:16:127;;;;;;;:9;;;;:16;;;;;;;;;2458:23;;;;;;;;;-1:-1:-1;;;;;2458:23:127;;;;;-1:-1:-1;;;2458:23:127;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;2458:23:127;2214:274::o;1713:122::-;1761:7;1787:41;:16;:14;:16::i;:::-;:32;;:39;:41::i;:::-;1780:48;;1713:122;:::o;5768:567::-;894:56;1078:16;:14;:16::i;:::-;:22;-1:-1:-1;;;;;1078:22:127;1055:55;1111:4;966:10:5;1055:75:127;;-1:-1:-1;;;;;;1055:75:127;;;;;;;;;;6771:25:192;;;;-1:-1:-1;;;;;6832:32:192;6812:18;;;6805:60;6744:18;;1055:75:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1050:125;;1153:11;;-1:-1:-1;;;1153:11:127;;;;;;;;;;;1050:125;5884:23:::1;5910:16;:14;:16::i;:::-;5884:42:::0;-1:-1:-1;5978:17:127::1;::::0;::::1;6059:9;::::0;::::1;5936:39;6078:206;6098:17:::0;;::::1;6078:206;;;6141:24;6155:6;;6162:1;6155:9;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;6141:6:::0;;:13:::1;:24::i;:::-;6136:98;;6209:6;;6216:1;6209:9;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;6136:98::-;6254:8;:19;6263:6;;6270:1;6263:9;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1::0;;;;;6254:19:127::1;::::0;;::::1;::::0;::::1;::::0;;;;;;-1:-1:-1;6254:19:127;;;6247:26;;;;;;::::1;::::0;;-1:-1:-1;;6247:26:127::1;::::0;;6117:3:::1;6078:206;;;;6298:30;6321:6;;6298:30;;;;;;;:::i;:::-;;;;;;;;5874:461;;;5768:567:::0;;;:::o;1422:98::-;1460:12;1491:16;:14;:16::i;:::-;:22;-1:-1:-1;;;;;1491:22:127;;1422:98;-1:-1:-1;1422:98:127:o;9365:180::-;9464:18;;9365:180::o;10987:156:72:-;11061:7;11111:22;11115:3;11127:5;11111:3;:22::i;:::-;11103:31;10987:156;-1:-1:-1;;;10987:156:72:o;9071:205:3:-;9129:30;;3147:66;9186:27;8819:122;6368:341:127;6929:20:3;:18;:20::i;:::-;6454:24:127::1;:22;:24::i;:::-;6489:37;::::0;6568:51:::1;::::0;;::::1;6579:10:::0;6568:51:::1;:::i;:::-;6488:131;;;;6629:35;6648:15;6629:18;:35::i;:::-;6674:28;6694:7;6674:19;:28::i;:::-;6444:265;;6368:341:::0;;:::o;8424:556::-;8500:23;8526:16;:14;:16::i;:::-;8569:27;;8500:42;;-1:-1:-1;;;;;;8569:32:127;;;:75;;-1:-1:-1;8605:34:127;;;;-1:-1:-1;;;;;8605:39:127;;8569:75;:130;;;-1:-1:-1;8664:30:127;;;;-1:-1:-1;;;;;8664:35:127;;8569:130;:176;;;-1:-1:-1;8703:37:127;;;;-1:-1:-1;;;;;8703:42:127;;8569:176;:199;;;-1:-1:-1;8749:14:127;;;;:19;;;8569:199;:246;;;-1:-1:-1;8788:22:127;;;;:27;;;8569:246;:276;;;-1:-1:-1;8819:21:127;;;;:26;;;8569:276;8552:347;;;8877:11;;-1:-1:-1;;;8877:11:127;;;;;;;;;;;8552:347;8908:25;;:16;;;:25;;-1:-1:-1;;;;;8908:25:127;;;-1:-1:-1;;;;;;8908:25:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;8908:25:127;-1:-1:-1;;;;8908:25:127;;;-1:-1:-1;;;8908:25:127;-1:-1:-1;;;;8908:25:127;;;;-1:-1:-1;;;8908:25:127;;;;;-1:-1:-1;;;;;;;;;8908:25:127;;;-1:-1:-1;;;8908:25:127;-1:-1:-1;;8908:25:127;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8948;;;;;8927:6;;8948:25;:::i;:::-;;;;;;;;8490:490;8424:556;:::o;3470:384:6:-;-1:-1:-1;;;;;;;;;;;3670:9:6;;-1:-1:-1;;3670:20:6;3666:88;;3713:30;;-1:-1:-1;;;3713:30:6;;;;;;;;;;;3666:88;1991:1;3828:19;;3470:384::o;3860:283::-;1949:1;-1:-1:-1;;;;;;;;;;;4113:23:6;3860:283::o;10284:165:72:-;-1:-1:-1;;;;;10417:23:72;;10364:4;5006:21;;;:14;;;:21;;;;;;:26;;10387:55;4910:129;7406:1012:127;7625:15;;7555:12;;;;-1:-1:-1;;;;;7625:15:127;7654:17;;;7650:67;;7695:4;7701;7687:19;;;;;;;7650:67;7726:25;7765:12;7754:8;:23;:75;;7806:23;7821:8;7806:12;:23;:::i;:::-;7754:75;;;7780:23;7791:12;7780:8;:23;:::i;:::-;7726:103;;7839:28;7870:53;7882:17;7901:7;7910:12;7870:11;:53::i;:::-;7957:27;;7839:84;;-1:-1:-1;;;;;;7937:47:127;;;;:104;;;8011:6;:30;;;-1:-1:-1;;;;;7988:53:127;:20;:53;7937:104;7933:156;;;8065:5;8072;8057:21;;;;;;;;;7933:156;8102:19;;;;;;8098:69;;;8145:4;8151;8137:19;;;;;;;;;8098:69;8213:6;:34;;;-1:-1:-1;;;;;8193:54:127;:17;:54;:134;;;;8290:6;:37;;;-1:-1:-1;;;;;8267:60:127;:20;:60;8193:134;8176:206;;;8360:4;8366;8352:19;;;;;;;;;8176:206;8399:4;8405:5;8391:20;;;;;;;7406:1012;;;;;;;:::o;6715:685::-;6875:16;;6851:4;;-1:-1:-1;;;6875:16:127;;;;:21;;;;:76;;-1:-1:-1;6917:16:127;;6900:14;;;;6936:15;;6900:33;;-1:-1:-1;;;6917:16:127;;;;;;6900:33;:::i;:::-;:51;;;6875:76;:100;;;;-1:-1:-1;6956:19:127;;;;;;6955:20;6875:100;6871:198;;;7041:16;;7024:14;;;;7007:15;;7024:33;;-1:-1:-1;;;7041:16:127;;;;;;7024:33;:::i;:::-;6998:60;;-1:-1:-1;;;6998:60:127;;;;;13180:25:192;;;;13253:10;13241:23;13221:18;;;13214:51;13153:18;;6998:60:127;13007:264:192;6871:198:127;7079:12;7093:17;7114:40;7129:8;-1:-1:-1;;;;;7114:40:127;7139:6;7147;7114:14;:40::i;:::-;7078:76;;;;7169:7;7164:68;;7199:22;;-1:-1:-1;;;7199:22:127;;-1:-1:-1;;;;;13440:32:192;;7199:22:127;;;13422:51:192;13395:18;;7199:22:127;13276:203:192;7164:68:127;-1:-1:-1;;;;;7241:26:127;;-1:-1:-1;;;7303:15:127;7277:42;;;;;;7241:26;7329:19;;:34;;-1:-1:-1;;7329:34:127;;;;;;;;;;;;-1:-1:-1;;6715:685:127;;;;;:::o;8986:373::-;9058:39;9100:16;:14;:16::i;:::-;:32;;9058:74;;9147:9;9142:168;9166:6;:13;9162:1;:17;9142:168;;;9205:21;9216:6;9223:1;9216:9;;;;;;;;:::i;:::-;;;;;;;9205:6;:10;;:21;;;;:::i;:::-;9200:100;;9275:6;9282:1;9275:9;;;;;;;;:::i;:::-;;;;;;;9253:32;;-1:-1:-1;;;9253:32:127;;;;;;;-1:-1:-1;;;;;545:32:192;;;;527:51;;515:2;500:18;;381:203;9200:100:127;9181:3;;9142:168;;;;9324:28;9345:6;9324:28;;;;;;:::i;10530:115:72:-;10593:7;10619:19;10627:3;5202:18;;5120:107;9650:156;9723:4;9746:53;9754:3;-1:-1:-1;;;;;9774:23:72;;9746:7;:53::i;5569:118::-;5636:7;5662:3;:11;;5674:5;5662:18;;;;;;;;:::i;:::-;;;;;;;;;5655:25;;5569:118;;;;:::o;7082:141:3:-;7149:17;:15;:17::i;:::-;7144:73;;7189:17;;-1:-1:-1;;;7189:17:3;;;;;;;;;;;7144:73;7082:141::o;2684:111:6:-;6929:20:3;:18;:20::i;:::-;2754:34:6::1;:32;:34::i;7242:3683:67:-:0;7324:14;7375:12;7389:11;7404:12;7411:1;7414;7404:6;:12::i;:::-;7374:42;;;;7498:4;7506:1;7498:9;7494:365;;7833:11;7827:3;:17;;;;;:::i;:::-;;7820:24;;;;;;7494:365;7984:4;7969:11;:19;7965:142;;8008:84;5312:5;8028:16;;5311:36;940:4:58;5306:42:67;8008:11;:84::i;:::-;8359:17;8510:11;8507:1;8504;8497:25;8902:12;8932:15;;;8917:31;;9067:22;;;;;9800:1;9781;:15;;9780:21;;10033;;;10029:25;;10018:36;10103:21;;;10099:25;;10088:36;10175:21;;;10171:25;;10160:36;10246:21;;;10242:25;;10231:36;10319:21;;;10315:25;;10304:36;10393:21;;;10389:25;;;10378:36;9309:12;;;;9305:23;;;9330:1;9301:31;8622:18;;;8612:29;;;9416:11;;;;8665:19;;;;9160:14;;;;9409:18;;;;10868:13;;-1:-1:-1;;7242:3683:67;;;;;:::o;9332:150:72:-;9402:4;9425:50;9430:3;-1:-1:-1;;;;;9450:23:72;;9425:4;:50::i;2910:1368::-;2976:4;3105:21;;;:14;;;:21;;;;;;3141:13;;3137:1135;;3508:18;3529:12;3540:1;3529:8;:12;:::i;:::-;3575:18;;3508:33;;-1:-1:-1;3555:17:72;;3575:22;;3596:1;;3575:22;:::i;:::-;3555:42;;3630:9;3616:10;:23;3612:378;;3659:17;3679:3;:11;;3691:9;3679:22;;;;;;;;:::i;:::-;;;;;;;;;3659:42;;3826:9;3800:3;:11;;3812:10;3800:23;;;;;;;;:::i;:::-;;;;;;;;;;;;:35;;;;3939:25;;;:14;;;:25;;;;;:36;;;3612:378;4068:17;;:3;;:17;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;4171:3;:14;;:21;4186:5;4171:21;;;;;;;;;;;4164:28;;;4214:4;4207:11;;;;;;;3137:1135;4256:5;4249:12;;;;;3137:1135;2982:1296;2910:1368;;;;:::o;8485:120:3:-;8535:4;8558:26;:24;:26::i;:::-;:40;-1:-1:-1;;;8558:40:3;;;;;;-1:-1:-1;8485:120:3:o;2801:183:6:-;6929:20:3;:18;:20::i;1027:550:67:-;1088:12;;-1:-1:-1;;1471:1:67;1468;1461:20;1501:9;;;;1549:11;;;1535:12;;;;1531:30;;;;;1027:550;-1:-1:-1;;1027:550:67:o;1776:194:58:-;1881:10;1875:4;1868:24;1918:4;1912;1905:18;1949:4;1943;1936:18;2336:406:72;2399:4;5006:21;;;:14;;;:21;;;;;;2415:321;;-1:-1:-1;2457:23:72;;;;;;;;:11;:23;;;;;;;;;;;;;2639:18;;2615:21;;;:14;;;:21;;;;;;:42;;;;2671:11;;2415:321;-1:-1:-1;2720:5:72;2713:12;;196:180:192;255:6;308:2;296:9;287:7;283:23;279:32;276:52;;;324:1;321;314:12;276:52;-1:-1:-1;347:23:192;;196:180;-1:-1:-1;196:180:192:o;589:591::-;659:6;667;720:2;708:9;699:7;695:23;691:32;688:52;;;736:1;733;726:12;688:52;776:9;763:23;-1:-1:-1;;;;;846:2:192;838:6;835:14;832:34;;;862:1;859;852:12;832:34;900:6;889:9;885:22;875:32;;945:7;938:4;934:2;930:13;926:27;916:55;;967:1;964;957:12;916:55;1007:2;994:16;1033:2;1025:6;1022:14;1019:34;;;1049:1;1046;1039:12;1019:34;1094:7;1089:2;1080:6;1076:2;1072:15;1068:24;1065:37;1062:57;;;1115:1;1112;1105:12;1062:57;1146:2;1138:11;;;;;1168:6;;-1:-1:-1;589:591:192;;-1:-1:-1;;;;589:591:192:o;1185:202::-;1279:6;1332:3;1320:9;1311:7;1307:23;1303:33;1300:53;;;1349:1;1346;1339:12;1300:53;-1:-1:-1;1372:9:192;1185:202;-1:-1:-1;1185:202:192:o;1392:882::-;1550:4;1592:3;1581:9;1577:19;1569:27;;1632:1;1628;1623:3;1619:11;1615:19;1680:2;1671:6;1665:13;1661:22;1650:9;1643:41;1752:2;1744:4;1736:6;1732:17;1726:24;1722:33;1715:4;1704:9;1700:20;1693:63;;1803:4;1795:6;1791:17;1785:24;-1:-1:-1;;;;;1902:2:192;1888:12;1884:21;1877:4;1866:9;1862:20;1855:51;1974:2;1966:4;1958:6;1954:17;1948:24;1944:33;1937:4;1926:9;1922:20;1915:63;;;2027:4;2019:6;2015:17;2009:24;2052:10;2120:2;2104:14;2100:23;2093:4;2082:9;2078:20;2071:53;2192:2;2184:4;2176:6;2172:17;2166:24;2162:33;2155:4;2144:9;2140:20;2133:63;2264:2;2256:4;2248:6;2244:17;2238:24;2234:33;2227:4;2216:9;2212:20;2205:63;;;1392:882;;;;:::o;2279:173::-;2347:20;;-1:-1:-1;;;;;2396:31:192;;2386:42;;2376:70;;2442:1;2439;2432:12;2376:70;2279:173;;;:::o;2457:186::-;2516:6;2569:2;2557:9;2548:7;2544:23;2540:32;2537:52;;;2585:1;2582;2575:12;2537:52;2608:29;2627:9;2608:29;:::i;2648:163::-;2715:20;;2775:10;2764:22;;2754:33;;2744:61;;2801:1;2798;2791:12;2816:326;2892:6;2900;2908;2961:2;2949:9;2940:7;2936:23;2932:32;2929:52;;;2977:1;2974;2967:12;2929:52;3000:29;3019:9;3000:29;:::i;:::-;2990:39;;3076:2;3065:9;3061:18;3048:32;3038:42;;3099:37;3132:2;3121:9;3117:18;3099:37;:::i;:::-;3089:47;;2816:326;;;;;:::o;3147:254::-;3215:6;3223;3276:2;3264:9;3255:7;3251:23;3247:32;3244:52;;;3292:1;3289;3282:12;3244:52;3328:9;3315:23;3305:33;;3357:38;3391:2;3380:9;3376:18;3357:38;:::i;:::-;3347:48;;3147:254;;;;;:::o;3679:642::-;3792:6;3800;3853:2;3841:9;3832:7;3828:23;3824:32;3821:52;;;3869:1;3866;3859:12;3821:52;3909:9;3896:23;-1:-1:-1;;;;;3979:2:192;3971:6;3968:14;3965:34;;;3995:1;3992;3985:12;3965:34;4033:6;4022:9;4018:22;4008:32;;4078:7;4071:4;4067:2;4063:13;4059:27;4049:55;;4100:1;4097;4090:12;4049:55;4140:2;4127:16;4166:2;4158:6;4155:14;4152:34;;;4182:1;4179;4172:12;4152:34;4235:7;4230:2;4220:6;4217:1;4213:14;4209:2;4205:23;4201:32;4198:45;4195:65;;;4256:1;4253;4246:12;4326:615;4412:6;4420;4473:2;4461:9;4452:7;4448:23;4444:32;4441:52;;;4489:1;4486;4479:12;4441:52;4529:9;4516:23;-1:-1:-1;;;;;4599:2:192;4591:6;4588:14;4585:34;;;4615:1;4612;4605:12;4585:34;4653:6;4642:9;4638:22;4628:32;;4698:7;4691:4;4687:2;4683:13;4679:27;4669:55;;4720:1;4717;4710:12;4669:55;4760:2;4747:16;4786:2;4778:6;4775:14;4772:34;;;4802:1;4799;4792:12;4772:34;4855:7;4850:2;4840:6;4837:1;4833:14;4829:2;4825:23;4821:32;4818:45;4815:65;;;4876:1;4873;4866:12;5990:388;6147:2;6136:9;6129:21;6186:6;6181:2;6170:9;6166:18;6159:34;6243:6;6235;6230:2;6219:9;6215:18;6202:48;6299:1;6270:22;;;6294:2;6266:31;;;6259:42;;;;6362:2;6341:15;;;-1:-1:-1;;6337:29:192;6322:45;6318:54;;5990:388;-1:-1:-1;5990:388:192:o;6876:277::-;6943:6;6996:2;6984:9;6975:7;6971:23;6967:32;6964:52;;;7012:1;7009;7002:12;6964:52;7044:9;7038:16;7097:5;7090:13;7083:21;7076:5;7073:32;7063:60;;7119:1;7116;7109:12;7158:127;7219:10;7214:3;7210:20;7207:1;7200:31;7250:4;7247:1;7240:15;7274:4;7271:1;7264:15;7290:275;7361:2;7355:9;7426:2;7407:13;;-1:-1:-1;;7403:27:192;7391:40;;-1:-1:-1;;;;;7446:34:192;;7482:22;;;7443:62;7440:88;;;7508:18;;:::i;:::-;7544:2;7537:22;7290:275;;-1:-1:-1;7290:275:192:o;7570:173::-;7638:20;;-1:-1:-1;;;;;7687:31:192;;7677:42;;7667:70;;7733:1;7730;7723:12;7748:171;7815:20;;-1:-1:-1;;;;;7864:30:192;;7854:41;;7844:69;;7909:1;7906;7899:12;7924:852;7985:5;8033:4;8021:9;8016:3;8012:19;8008:30;8005:50;;;8051:1;8048;8041:12;8005:50;8084:2;8078:9;8126:4;8118:6;8114:17;8197:6;8185:10;8182:22;-1:-1:-1;;;;;8149:10:192;8146:34;8143:62;8140:88;;;8208:18;;:::i;:::-;8244:2;8237:22;8277:6;-1:-1:-1;8277:6:192;8307:29;8326:9;8307:29;:::i;:::-;8299:6;8292:45;8370:38;8404:2;8393:9;8389:18;8370:38;:::i;:::-;8365:2;8357:6;8353:15;8346:63;8442:37;8475:2;8464:9;8460:18;8442:37;:::i;:::-;8437:2;8429:6;8425:15;8418:62;8513:37;8546:2;8535:9;8531:18;8513:37;:::i;:::-;8508:2;8500:6;8496:15;8489:62;8585:38;8618:3;8607:9;8603:19;8585:38;:::i;:::-;8579:3;8571:6;8567:16;8560:64;8658:38;8691:3;8680:9;8676:19;8658:38;:::i;:::-;8652:3;8644:6;8640:16;8633:64;8731:38;8764:3;8753:9;8749:19;8731:38;:::i;:::-;8725:3;8717:6;8713:16;8706:64;;7924:852;;;;:::o;8781:243::-;8873:6;8926:3;8914:9;8905:7;8901:23;8897:33;8894:53;;;8943:1;8940;8933:12;8894:53;8966:52;9010:7;8999:9;8966:52;:::i;9029:127::-;9090:10;9085:3;9081:20;9078:1;9071:31;9121:4;9118:1;9111:15;9145:4;9142:1;9135:15;9161:175;9229:10;9272;;;9260;;;9256:27;;9295:12;;;9292:38;;;9310:18;;:::i;9341:486::-;-1:-1:-1;;;;;9586:32:192;;;;9568:51;;-1:-1:-1;;;;;9655:32:192;;;;9650:2;9635:18;;9628:60;9707:10;9753:15;;;9748:2;9733:18;;9726:43;9805:15;9800:2;9785:18;;9778:43;9555:3;9540:19;;9341:486::o;9832:128::-;9899:9;;;9920:11;;;9917:37;;;9934:18;;:::i;9965:127::-;10026:10;10021:3;10017:20;10014:1;10007:31;10057:4;10054:1;10047:15;10081:4;10078:1;10071:15;10097:186;10156:6;10209:2;10197:9;10188:7;10184:23;10180:32;10177:52;;;10225:1;10222;10215:12;10177:52;10248:29;10267:9;10248:29;:::i;10288:805::-;10521:2;10573:21;;;10546:18;;;10629:22;;;10492:4;;10670:2;10688:18;;;10729:6;10492:4;10763:304;10777:6;10774:1;10771:13;10763:304;;;-1:-1:-1;;;;;10842:26:192;10861:6;10842:26;:::i;:::-;10838:52;10826:65;;-1:-1:-1;;;;;10929:35:192;10948:15;;;10929:35;:::i;:::-;10925:61;10911:12;;;10904:83;11007:12;;;;11042:15;;;;10799:1;10792:9;10763:304;;;-1:-1:-1;11084:3:192;;10288:805;-1:-1:-1;;;;;;;10288:805:192:o;11098:636::-;11279:2;11331:21;;;11304:18;;;11387:22;;;11250:4;;11466:6;11440:2;11425:18;;11250:4;11500:208;11514:6;11511:1;11508:13;11500:208;;;-1:-1:-1;;;;;11579:26:192;11598:6;11579:26;:::i;:::-;11575:52;11563:65;;11683:15;;;;11648:12;;;;11536:1;11529:9;11500:208;;;-1:-1:-1;11725:3:192;11098:636;-1:-1:-1;;;;;;11098:636:192:o;11739:1086::-;11865:6;11873;11926:3;11914:9;11905:7;11901:23;11897:33;11894:53;;;11943:1;11940;11933:12;11894:53;11966:52;12010:7;11999:9;11966:52;:::i;:::-;11956:62;;12069:3;12058:9;12054:19;12041:33;-1:-1:-1;;;;;12134:2:192;12126:6;12123:14;12120:34;;;12150:1;12147;12140:12;12120:34;12188:6;12177:9;12173:22;12163:32;;12233:7;12226:4;12222:2;12218:13;12214:27;12204:55;;12255:1;12252;12245:12;12204:55;12291:2;12278:16;12313:4;12336:2;12332;12329:10;12326:36;;;12342:18;;:::i;:::-;12388:2;12385:1;12381:10;12371:20;;12411:28;12435:2;12431;12427:11;12411:28;:::i;:::-;12473:15;;;12543:11;;;12539:20;;;12504:12;;;;12571:19;;;12568:39;;;12603:1;12600;12593:12;12568:39;12627:11;;;;12647:148;12663:6;12658:3;12655:15;12647:148;;;12729:23;12748:3;12729:23;:::i;:::-;12717:36;;12680:12;;;;12773;;;;12647:148;;;12814:5;12804:15;;;;;;;;11739:1086;;;;;:::o;12830:172::-;12897:10;12927;;;12939;;;12923:27;;12962:11;;;12959:37;;;12976:18;;:::i;13484:658::-;13655:2;13707:21;;;13777:13;;13680:18;;;13799:22;;;13626:4;;13655:2;13878:15;;;;13852:2;13837:18;;;13626:4;13921:195;13935:6;13932:1;13929:13;13921:195;;;14000:13;;-1:-1:-1;;;;;13996:39:192;13984:52;;14091:15;;;;14056:12;;;;14032:1;13950:9;13921:195;;;-1:-1:-1;14133:3:192;;13484:658;-1:-1:-1;;;;;;13484:658:192:o;14147:127::-;14208:10;14203:3;14199:20;14196:1;14189:31;14239:4;14236:1;14229:15;14263:4;14260:1;14253:15;14279:127;14340:10;14335:3;14331:20;14328:1;14321:31;14371:4;14368:1;14361:15;14395:4;14392:1;14385:15", "linkReferences": {}, "immutableReferences": {"68129": [{"start": 4417, "length": 32}]}}, "methodIdentifiers": {"ACCEPT_REPORT_ROLE()": "56679cf4", "ADD_SUPPORTED_ASSETS_ROLE()": "75a3eea4", "REMOVE_SUPPORTED_ASSETS_ROLE()": "9f79f613", "SET_SECURITY_PARAMS_ROLE()": "074cd353", "SUBMIT_REPORTS_ROLE()": "d4ad5f38", "acceptReport(address,uint256,uint32)": "6c038d03", "addSupportedAssets(address[])": "8fe4d996", "getReport(address)": "a3bdae3e", "initialize(bytes)": "439fab91", "isSupportedAsset(address)": "9be918e6", "removeSupportedAssets(address[])": "c663be59", "securityParams()": "6094fd57", "setSecurityParams((uint224,uint224,uint64,uint64,uint32,uint32,uint32))": "58f3d554", "setVault(address)": "6817031b", "submitReports((address,uint224)[])": "8f88cbfb", "supportedAssetAt(uint256)": "42c74b19", "supportedAssets()": "a80ce55c", "validatePrice(uint256,address)": "816c8850", "vault()": "fbfa77cf"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name_\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"version_\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"}],\"name\":\"AlreadySupportedAsset\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Forbidden\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"priceD18\",\"type\":\"uint256\"}],\"name\":\"InvalidPrice\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidReport\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"minTimestamp\",\"type\":\"uint256\"}],\"name\":\"TooEarly\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"}],\"name\":\"UnsupportedAsset\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZeroValue\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"initParams\",\"type\":\"bytes\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint224\",\"name\":\"priceD18\",\"type\":\"uint224\"},{\"indexed\":true,\"internalType\":\"uint32\",\"name\":\"timestamp\",\"type\":\"uint32\"}],\"name\":\"ReportAccepted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"uint224\",\"name\":\"priceD18\",\"type\":\"uint224\"}],\"indexed\":false,\"internalType\":\"struct IOracle.Report[]\",\"name\":\"reports\",\"type\":\"tuple[]\"}],\"name\":\"ReportsSubmitted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"components\":[{\"internalType\":\"uint224\",\"name\":\"maxAbsoluteDeviation\",\"type\":\"uint224\"},{\"internalType\":\"uint224\",\"name\":\"suspiciousAbsoluteDeviation\",\"type\":\"uint224\"},{\"internalType\":\"uint64\",\"name\":\"maxRelativeDeviationD18\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"suspiciousRelativeDeviationD18\",\"type\":\"uint64\"},{\"internalType\":\"uint32\",\"name\":\"timeout\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"depositInterval\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"redeemInterval\",\"type\":\"uint32\"}],\"indexed\":false,\"internalType\":\"struct IOracle.SecurityParams\",\"name\":\"securityParams\",\"type\":\"tuple\"}],\"name\":\"SecurityParamsSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"SetVault\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"assets\",\"type\":\"address[]\"}],\"name\":\"SupportedAssetsAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"assets\",\"type\":\"address[]\"}],\"name\":\"SupportedAssetsRemoved\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"ACCEPT_REPORT_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"ADD_SUPPORTED_ASSETS_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"REMOVE_SUPPORTED_ASSETS_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SET_SECURITY_PARAMS_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SUBMIT_REPORTS_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"priceD18\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"timestamp\",\"type\":\"uint32\"}],\"name\":\"acceptReport\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"assets\",\"type\":\"address[]\"}],\"name\":\"addSupportedAssets\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"}],\"name\":\"getReport\",\"outputs\":[{\"components\":[{\"internalType\":\"uint224\",\"name\":\"priceD18\",\"type\":\"uint224\"},{\"internalType\":\"uint32\",\"name\":\"timestamp\",\"type\":\"uint32\"},{\"internalType\":\"bool\",\"name\":\"isSuspicious\",\"type\":\"bool\"}],\"internalType\":\"struct IOracle.DetailedReport\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"initParams\",\"type\":\"bytes\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"}],\"name\":\"isSupportedAsset\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"assets\",\"type\":\"address[]\"}],\"name\":\"removeSupportedAssets\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"securityParams\",\"outputs\":[{\"components\":[{\"internalType\":\"uint224\",\"name\":\"maxAbsoluteDeviation\",\"type\":\"uint224\"},{\"internalType\":\"uint224\",\"name\":\"suspiciousAbsoluteDeviation\",\"type\":\"uint224\"},{\"internalType\":\"uint64\",\"name\":\"maxRelativeDeviationD18\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"suspiciousRelativeDeviationD18\",\"type\":\"uint64\"},{\"internalType\":\"uint32\",\"name\":\"timeout\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"depositInterval\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"redeemInterval\",\"type\":\"uint32\"}],\"internalType\":\"struct IOracle.SecurityParams\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint224\",\"name\":\"maxAbsoluteDeviation\",\"type\":\"uint224\"},{\"internalType\":\"uint224\",\"name\":\"suspiciousAbsoluteDeviation\",\"type\":\"uint224\"},{\"internalType\":\"uint64\",\"name\":\"maxRelativeDeviationD18\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"suspiciousRelativeDeviationD18\",\"type\":\"uint64\"},{\"internalType\":\"uint32\",\"name\":\"timeout\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"depositInterval\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"redeemInterval\",\"type\":\"uint32\"}],\"internalType\":\"struct IOracle.SecurityParams\",\"name\":\"securityParams_\",\"type\":\"tuple\"}],\"name\":\"setSecurityParams\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault_\",\"type\":\"address\"}],\"name\":\"setVault\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"uint224\",\"name\":\"priceD18\",\"type\":\"uint224\"}],\"internalType\":\"struct IOracle.Report[]\",\"name\":\"reports\",\"type\":\"tuple[]\"}],\"name\":\"submitReports\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"supportedAssetAt\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"supportedAssets\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"priceD18\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"}],\"name\":\"validatePrice\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"isValid\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"isSuspicious\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"vault\",\"outputs\":[{\"internalType\":\"contract IShareModule\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"AlreadySupportedAsset(address)\":[{\"params\":{\"asset\":\"The address of the already supported asset.\"}}],\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"InvalidPrice(uint256)\":[{\"params\":{\"priceD18\":\"The submitted price in 18-decimal fixed-point format.\"}}],\"InvalidReport()\":[{\"details\":\"This includes mismatches in price, timestamp, or incorrect `isSuspicios` flag.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}],\"TooEarly(uint256,uint256)\":[{\"params\":{\"minTimestamp\":\"The earliest acceptable timestamp based on timeout configuration.\",\"timestamp\":\"The submitted report timestamp.\"}}],\"UnsupportedAsset(address)\":[{\"params\":{\"asset\":\"The address of the unsupported asset.\"}}]},\"events\":{\"Initialized(bytes)\":{\"params\":{\"initParams\":\"The initialization parameters.\"}},\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"}},\"kind\":\"dev\",\"methods\":{\"acceptReport(address,uint256,uint32)\":{\"details\":\"Callable only by account with `ACCEPT_REPORT_ROLE`\",\"params\":{\"asset\":\"Address of the asset\",\"priceD18\":\"Timestamp that must match existing suspicious report\",\"timestamp\":\"Timestamp that must match existing suspicious report\"}},\"addSupportedAssets(address[])\":{\"params\":{\"assets\":\"Array of asset addresses to add\"}},\"getReport(address)\":{\"params\":{\"asset\":\"Address of the asset\"}},\"initialize(bytes)\":{\"params\":{\"initParams\":\"The initialization parameters.\"}},\"isSupportedAsset(address)\":{\"params\":{\"asset\":\"Address of the asset\"}},\"removeSupportedAssets(address[])\":{\"params\":{\"assets\":\"Array of asset addresses to remove\"}},\"setSecurityParams((uint224,uint224,uint64,uint64,uint32,uint32,uint32))\":{\"params\":{\"securityParams_\":\"New security settings\"}},\"setVault(address)\":{\"params\":{\"vault_\":\"Address of the vault module\"}},\"submitReports((address,uint224)[])\":{\"details\":\"Processes pending deposit and redemption requests across DepositQueue and RedeemQueue contracts.      The core processing logic is determined by the ShareModule and Queue contracts.      Only callable by accounts with the `SUBMIT_REPORTS_ROLE`.Note: Submitted prices MUST reflect protocol and performance fee deductions, ensuring accurate share issuance.\",\"params\":{\"reports\":\"An array of price reports, each specifying the target asset and its latest price (in 18 decimals).\"}},\"supportedAssetAt(uint256)\":{\"params\":{\"index\":\"Index in the supported asset set\"}},\"validatePrice(uint256,address)\":{\"details\":\"Evaluates both absolute and relative deviation limits to determine whether the price is valid or suspicious.\",\"params\":{\"asset\":\"Address of the asset being evaluated.\",\"priceD18\":\"Price to validate, in 18-decimal fixed-point format.\"},\"returns\":{\"isSuspicious\":\"True if the price exceeds the suspicious deviation threshold.\",\"isValid\":\"True if the price is within maximum allowed deviation.\"}}},\"version\":1},\"userdoc\":{\"errors\":{\"AlreadySupportedAsset(address)\":[{\"notice\":\"Thrown when attempting to register an asset that is already supported.\"}],\"Forbidden()\":[{\"notice\":\"Thrown when the caller lacks the necessary permission to perform the operation.\"}],\"InvalidPrice(uint256)\":[{\"notice\":\"Thrown when the submitted price violates oracle security rules.\"}],\"InvalidReport()\":[{\"notice\":\"Thrown when a suspicious report fails validation due to unexpected data.\"}],\"TooEarly(uint256,uint256)\":[{\"notice\":\"Thrown when a report is submitted before the required timeout period,         and the previous report was not marked as suspicious.\"}],\"UnsupportedAsset(address)\":[{\"notice\":\"Thrown when an asset is not supported by the oracle.\"}],\"ZeroValue()\":[{\"notice\":\"Thrown when a function receives a zero value where a non-zero value is required.\"}]},\"events\":{\"Initialized(bytes)\":{\"notice\":\"Emitted once the entity has been initialized.\"},\"ReportAccepted(address,uint224,uint32)\":{\"notice\":\"Emitted when a suspicious report is accepted\"},\"ReportsSubmitted((address,uint224)[])\":{\"notice\":\"Emitted when price reports are submitted\"},\"SecurityParamsSet((uint224,uint224,uint64,uint64,uint32,uint32,uint32))\":{\"notice\":\"Emitted when new security parameters are set\"},\"SetVault(address)\":{\"notice\":\"Emitted when vault is set\"},\"SupportedAssetsAdded(address[])\":{\"notice\":\"Emitted when new assets are added\"},\"SupportedAssetsRemoved(address[])\":{\"notice\":\"Emitted when supported assets are removed\"}},\"kind\":\"user\",\"methods\":{\"ACCEPT_REPORT_ROLE()\":{\"notice\":\"Role required to accept suspicious reports\"},\"ADD_SUPPORTED_ASSETS_ROLE()\":{\"notice\":\"Role required to add new supported assets\"},\"REMOVE_SUPPORTED_ASSETS_ROLE()\":{\"notice\":\"Role required to remove supported assets\"},\"SET_SECURITY_PARAMS_ROLE()\":{\"notice\":\"Role required to update security parameters\"},\"SUBMIT_REPORTS_ROLE()\":{\"notice\":\"Role required to submit reports\"},\"acceptReport(address,uint256,uint32)\":{\"notice\":\"Accepts a previously suspicious report\"},\"addSupportedAssets(address[])\":{\"notice\":\"Adds multiple new assets to the supported set\"},\"getReport(address)\":{\"notice\":\"Returns the most recent detailed report for an asset\"},\"initialize(bytes)\":{\"notice\":\"Initializes the factory-created entity with arbitrary initialization data.\"},\"isSupportedAsset(address)\":{\"notice\":\"Checks whether an asset is supported\"},\"removeSupportedAssets(address[])\":{\"notice\":\"Removes assets from the supported set\"},\"securityParams()\":{\"notice\":\"Returns current security parameters\"},\"setSecurityParams((uint224,uint224,uint64,uint64,uint32,uint32,uint32))\":{\"notice\":\"Updates oracle security parameters\"},\"setVault(address)\":{\"notice\":\"Sets the associated vault\"},\"submitReports((address,uint224)[])\":{\"notice\":\"Submits price reports for supported assets.\"},\"supportedAssetAt(uint256)\":{\"notice\":\"Returns the supported asset at a specific index\"},\"supportedAssets()\":{\"notice\":\"Returns total count of supported assets\"},\"validatePrice(uint256,address)\":{\"notice\":\"Validates the given price for a specific asset based on oracle security parameters.\"},\"vault()\":{\"notice\":\"Returns the connected vault module\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/oracles/Oracle.sol\":\"Oracle\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/contracts/access/AccessControlUpgradeable.sol\":{\"keccak256\":\"0x0d4d7d19a052a2ef9d17b28450133631188b895e1755747fa8ad0280aadfb534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://838551e10cc07e570b70aee1078e490658d3a0ab8f4adfd4f3f2a565200753da\",\"dweb:/ipfs/QmceT7R3Pqzywyxr3MSEA7VXU4axZAPS6BLy92iV97rGdG\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol\":{\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6\",\"dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/access/extensions/AccessControlEnumerableUpgradeable.sol\":{\"keccak256\":\"0xaec38804089a16494f7d45ebbbeab78fe05e5dc4bc2eae1d2af458fe999fb43c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://04796408e2c4a1051afb23319e9505edb595a3d9c663ff5d6fcfb8e185ab1d93\",\"dweb:/ipfs/Qma8ZTi4kGyqg45SyNibbJSqMF7broiQnJPHhEGLcZyF9U\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol\":{\"keccak256\":\"0x361126a17677994081cd9cb69c3f50cffff6e920d25cb7e428acdb1ae41d1866\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://19ae787a7dd001269cd60a394b1a5261b78925a0fc3a6f927beb2986a9aa56cf\",\"dweb:/ipfs/QmYLfXiuKmcRgTDBEDXMMjXU8t6JxsspUmjxYzqWS55oEv\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/introspection/ERC165Upgradeable.sol\":{\"keccak256\":\"0xc8ed8d2056934b7675b695dec032f2920c2f5c6cf33a17ca85650940675323ab\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3c8ccc75d1cd792d192aa09e54dd49ea35fe85baa9fcd17486f29227d9f29b89\",\"dweb:/ipfs/QmbboSbFUEiM9tdEgBwuTRb7bykFoJXZ7dsSr1PSREJXMr\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0\",\"dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/extensions/IAccessControlEnumerable.sol\":{\"keccak256\":\"0xca774fbe0568762efdc1a7cba31f09549c7fa96dbe97410f4843fa2f0bc000a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0187ffdbf3d61b6d86cba4fcd9826e53d876987d620533ee84c681bdaf0f3ba3\",\"dweb:/ipfs/QmVJDqdJv6uzHY7ifncfv2QJep8XTzS3bGb4s5Exhuv86m\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol\":{\"keccak256\":\"0xb25a4f11fa80c702bf5cd85adec90e6f6f507f32f4a8e6f5dbc31e8c10029486\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6917f8a323e7811f041aecd4d9fd6e92455a6fba38a797ac6f6e208c7912b79d\",\"dweb:/ipfs/QmShuYv55wYHGi4EFkDB8QfF7ZCHoKk2efyz3AWY1ExSq7\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol\":{\"keccak256\":\"0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049\",\"dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol\":{\"keccak256\":\"0x8decfa54cec979c824b044b8128cd91d713f72c71fd7dfa54974624d8c949898\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://271f914261a19d87117a777e0924ada545c16191ef9b00cc40b0134fc14ebc70\",\"dweb:/ipfs/QmdvVNWHGHQrGGPonZJs5NuzTevTjZRM2zayKrDJf7WBA2\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/Proxy.sol\":{\"keccak256\":\"0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac\",\"dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol\":{\"keccak256\":\"0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa\",\"dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol\":{\"keccak256\":\"0x46f86003755f50eff00a7c5aaf493ae62e024142b8aec4493a313851d3c14872\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://462c770cb667cc75ab22df5a29c50873b24b267274edf87ac5cfe0112bf4c3f7\",\"dweb:/ipfs/QmTfKk32AkQ2yyYZCYJ44V69EM5t9ryBFC6bRF7FVMHXvj\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol\":{\"keccak256\":\"0x92579f452fe663595a898cbac85d80bb3868a6c9f034f19ba7fbebdfa3b65a4d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d07a888cd4a26c7077a30dd99e9ac30fbe2752bfb37e58c65f06a3b192079ef3\",\"dweb:/ipfs/QmZCQY4FC4ynBv9dha4BBWM1vYxTUJPBdFBS4HEFe6XzVB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol\":{\"keccak256\":\"0xb5afb8e8eebc4d1c6404df2f5e1e6d2c3d24fd01e5dfc855314951ecfaae462d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://78586466c424f076c6a2a551d848cfbe3f7c49e723830807598484a1047b3b34\",\"dweb:/ipfs/Qmb717ovcFxm7qgNKEShiV6M9SPR3v1qnNpAGH84D6w29p\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0xaaa1d17c1129b127a4a401db2fbd72960e2671474be3d08cae71ccdc42f7624c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cb2f27cd3952aa667e198fba0d9b7bcec52fbb12c16f013c25fe6fb52b29cc0e\",\"dweb:/ipfs/QmeuohBFoeyDPZA9JNCTEDz3VBfBD4EABWuWXVhHAuEpKR\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol\":{\"keccak256\":\"0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd\",\"dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol\":{\"keccak256\":\"0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd\",\"dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Errors.sol\":{\"keccak256\":\"0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf\",\"dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol\":{\"keccak256\":\"0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2\",\"dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/Hashes.sol\":{\"keccak256\":\"0x8cbd338f083224b4b6f0ff42cbda641a0a6c31ffcdca197452b97fe4d0918269\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f517dec5ba0c6491395acbf7f1d621f4e89e8f218bf5303c867b1c5ad70c6b11\",\"dweb:/ipfs/QmWmXHRLEw8W6ckth7NyYTU88YfvuS7xSsfae5ksL8qNUe\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/MerkleProof.sol\":{\"keccak256\":\"0x36a0c409c437a753cac9b92b75f93b0fbe92803bf2c8ff1517e54b247f166134\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f91ba472de411aa557cdbf6560c40750d87bd11c9060bc04d2ba7119af9d5a6\",\"dweb:/ipfs/QmQjtYo2i7dDvzCEzZ67bDoNSG4RrwMoxPWuqFmX5Xzpuw\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621\",\"dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol\":{\"keccak256\":\"0x93ce66a307d66cb14e95dbc8f20641cedf48b2d060bf3bda431d62a4493e6e8d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://10d4122ee13fd8dcd2f468a86031665345d2dfdda76993d1f51201bdeb746f9d\",\"dweb:/ipfs/QmWxbbYEgyDCjwT4ZtNwujJwB4vdnR2qEKwL2Zrz9JoSDU\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableMap.sol\":{\"keccak256\":\"0x5d0c69ef32eae686acc22acfb1a54df9c91356e69adec593aa69eba83ae8f6f4\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f7a9fca2cb3d802c4f8a2d0fda84ccc7f880264885801c7df08373501dd22282\",\"dweb:/ipfs/QmdtM2Loz9NzNTuQ3RXNQCPBjoPUJw35Ukpt1MSM9XH3cD\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol\":{\"keccak256\":\"0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231\",\"dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y\"]},\"src/interfaces/factories/IFactory.sol\":{\"keccak256\":\"0x838a308272620b2ae6fdf1f5b425f8ba1439ed24a7baad50599d3fb34eb0bfa7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7a801db63324ec50da072b69c67ca98eb89d026f01a4aa7e8f60d757bc82f86d\",\"dweb:/ipfs/QmaJ6WHAe8vfwX3A8yHyLW2aJKqkbQCKuthRg1QU2f3TuY\"]},\"src/interfaces/factories/IFactoryEntity.sol\":{\"keccak256\":\"0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812\",\"dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx\"]},\"src/interfaces/hooks/IHook.sol\":{\"keccak256\":\"0xf414604a4cbf1feb04f976a806eb2825fa7dcce6815e209fc7aa50c6fe845bb3\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://a727a899804756179d294bfb0975487e8c6ca11f68aa741604d400ec6dfbf241\",\"dweb:/ipfs/QmRqsAigFe71aHAG5SgyKQKVpnHoiVyiP6VmnkxFK9KRz9\"]},\"src/interfaces/hooks/IRedeemHook.sol\":{\"keccak256\":\"0xd3df4e8013847ea38f603529d1062e6fccfc757ea62339bfbdd72719d2f7fbb1\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cddd2d10f020fa96f03b728dfcf8c7874942a19d871414010437ca3cdaef2974\",\"dweb:/ipfs/QmUZwodA14vQg3ntpzF6YxVQ2JCDmjXgQKrzE6HzJ19Mq8\"]},\"src/interfaces/managers/IFeeManager.sol\":{\"keccak256\":\"0x37d3c06ed6b23e367216c2dfc14f99f3fd6d686c984eca124406c2002e4f1516\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://5b3b1b974856f76c6fe2347895ab05e973c84c963fdf8f5483c37e2e4d434cad\",\"dweb:/ipfs/QmVeLCrCWgeQ3aS4V38y9FszFvZdSkbPHKKCMWKKM3V4qw\"]},\"src/interfaces/managers/IRiskManager.sol\":{\"keccak256\":\"0xb0dda9606a25e8187fb20cff7eb99dfecde18fbea30bae52a2124fd22afda97a\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://fee1bae75e2154b21e5ea2382ff21cd3e2ff2ea3a5e67828cfe4b563ee86e14e\",\"dweb:/ipfs/QmUtFozDVfCzehRMYxhJwZAbLJ43afzzVQDA89JD1HG1YH\"]},\"src/interfaces/managers/IShareManager.sol\":{\"keccak256\":\"0xb75fcb890e9a3e706c0d87781700965aa24faa8461967ee62b140d45f4c399e8\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ae50d1dda35eb9f6fa035fc39c2424f2e2eb2d41e6b0ffed296719818b1d9502\",\"dweb:/ipfs/Qme5jPDZG3WTLYtT2s8VFs3MKJVXgL9T5AdAzoXuuqM79z\"]},\"src/interfaces/modules/IACLModule.sol\":{\"keccak256\":\"0xf6ce9b4f4af5475d152845d6afa6f57b2d376c3a6d3eec2b5ac9650901a78243\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://f851cef38b6188b20be98c95047c8bd9d8651d37421a54e1d02b60f9f372c5ed\",\"dweb:/ipfs/QmaymGDd2RoKweuASXCiV3rPuhwvLXUvb6wBgi8xXoiYT8\"]},\"src/interfaces/modules/IBaseModule.sol\":{\"keccak256\":\"0x9c5c95ceb39f4c39f81f2afc7ddb55389a21315e76047a9cf66521db4dea68c3\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ccfb0453b14b105cb5990978a49c1e08f812563a046e17c354f4f1c648f95fe7\",\"dweb:/ipfs/QmYMAKaVdRq3NoTMfqadxgMMmUq6ejzTw2Lpsa3e9a3eyj\"]},\"src/interfaces/modules/IShareModule.sol\":{\"keccak256\":\"0x65ef35a38441080ad4e0c77c7668d6717fe9699df679ed4d2c775f47d1dce57a\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://e28e0e9e2dd2097df748fa2015ce4582f489615040b5323094880a618c54edac\",\"dweb:/ipfs/QmXNvxJDQ6edM3ExmmiTEmS5UreWYTDBgaAygU2kM6mgC6\"]},\"src/interfaces/modules/ISubvaultModule.sol\":{\"keccak256\":\"0x8db6f1a3bd2506dd0db82f4138f0436602ed37487717095a804dee81e44f5e0e\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://2547472f59c19612916f3f54e6fa69644ec31f14c669aa66dea3c006bc71bccc\",\"dweb:/ipfs/QmZ9VRCsofVntdB7siM3pfypUpgJnmaJAC1n8HviQb3K3U\"]},\"src/interfaces/modules/IVaultModule.sol\":{\"keccak256\":\"0x7696792b852bc9b435e6388a19f4284854259df2f50b45e2b833b6f9dd1b1648\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4364151c043fc5e56bc87c2cdbffc169b80cd745618a4b0da14e271752de6637\",\"dweb:/ipfs/Qmdfx3L8goy8eWFGimtsh58Se1vuLsukUw2mXo5chCbTy2\"]},\"src/interfaces/modules/IVerifierModule.sol\":{\"keccak256\":\"0x8b8ad9ea43b139afccc77ceb0fdcc34615c7401cc13e99c723895302049065a5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://0d578b1a55a180c652ee24e1b35f6d70d65bc067770dd791db44d0bcfa7a5d38\",\"dweb:/ipfs/QmexSPCfixS9spdjCydR4FUaoN4J188sfLkRmJaia14yk6\"]},\"src/interfaces/oracles/IOracle.sol\":{\"keccak256\":\"0xcbf84560c1fb9f7d3445cbbca4f5f3406606496f30867222d8e3deabad741296\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c90107edcd30e29044a5a8b05e71e728b93a485e7e7ef1a5b4ff695430e33a5e\",\"dweb:/ipfs/QmfHGNxtRQWeq7JENynxZNfhfTBytwbRwyekUyuHWrUyjK\"]},\"src/interfaces/permissions/ICustomVerifier.sol\":{\"keccak256\":\"0xdb343366ed86f63f8a94ff478c5c8e1d127888e4993c27fcb3bb3b51ae0cb8c7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://12407e89b20982a00961ccc03258e353962607747f214940d18b0989c11547c9\",\"dweb:/ipfs/Qmay19iWpwiV8LuvWccPjsazVU5Kkhme9QykFfLKfBmvSr\"]},\"src/interfaces/permissions/IMellowACL.sol\":{\"keccak256\":\"0xcc86b0ce4e4a53c1c4fa16ad79f9de0e2a1f1a77a1f256c0856120a9d6ea36df\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://6942df2ffadacae5563e01ec60697f16116362ba84a856a346b36bc7b9dd1ec2\",\"dweb:/ipfs/QmNYhqZGpS98UP89CS86CjoG4ZWbMHzTHRtd7bZzQqn3UY\"]},\"src/interfaces/permissions/IVerifier.sol\":{\"keccak256\":\"0x4b42731d76efaffcb017577bd2a478d00a3058c69220d6ea8e26f30f74476be7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ff2a6f09a946dd8a470dfe495b2a64d24b849c6d071dc859589140fb1b983362\",\"dweb:/ipfs/Qmb6UoqkmaC8tJMqkMfjcWR7pn1WCwPwuKKvUbLBQbPcJS\"]},\"src/interfaces/queues/IDepositQueue.sol\":{\"keccak256\":\"0x4ef38cb59d419010f453e62fe101efec25bec85a2ce1a24048098034060db47b\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://20e51dd02001309f23f46abea409edf77a39522bf21eeffb858ac888e4200e72\",\"dweb:/ipfs/QmWm9gfMuXz8nwuHb3he7abxTRgQw35x2viwMVuiNYAY5b\"]},\"src/interfaces/queues/IQueue.sol\":{\"keccak256\":\"0xd12e34631b204821c77eaa5b2e104902d740e3530cd716f7fc991af01a73b9ab\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://1f2a4d664244be639da1a5025ebccc8c1127e087a81ce6d0fc76af5134fec311\",\"dweb:/ipfs/QmS1GJwxGCV89myCAzLBAbmSrY6YL31G3nGj542dDM4QmT\"]},\"src/interfaces/queues/IRedeemQueue.sol\":{\"keccak256\":\"0xf8f95b95de259c8ccf29976f34d97a0ef31c9827f41ebcd5e8de54c10e646076\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://45ae0d496e865d846bf0c980593c1d16c038abf2b9359906224aa94cccf1c73f\",\"dweb:/ipfs/QmYTDXSTpUqhzJDXSKjoSKxwwQYee7wSTtf617wDsnnCC5\"]},\"src/libraries/FenwickTreeLibrary.sol\":{\"keccak256\":\"0xfd159ed27b96f288c4220febac4bfe286e4545b8ca7f523ae6e63d123341f8a3\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://58f0cc202896722786140571b111bf5e589730358a24b2940a450c4c75d864fe\",\"dweb:/ipfs/QmeRSjvcYURUSpvFe4r4VRwwpHcb1NmqiEUDEJ5rBxAxuf\"]},\"src/libraries/SlotLibrary.sol\":{\"keccak256\":\"0x35dcf16feec83c772add30250b0987ce29ccfbf308d04159701ebf9243444eeb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://97658d0c93ecc8174169e643d5a24c515b55905a2fcaaa6d6ec0188a3dd71a10\",\"dweb:/ipfs/QmS1z7ac8RbMvz15gVQ9YA1cCT5bs6KMHCiDhgcG8Pq8oK\"]},\"src/oracles/Oracle.sol\":{\"keccak256\":\"0xf2bca76efcb407bffe2568016b268a08ff606dbeaebea6c20d7dfe24abe095cd\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://a4e20d3e0fdf2799ba780e04f68b72ddb5e61670d2172df1996cf06d6e508fa7\",\"dweb:/ipfs/QmdVcWTdDeupfmCBGcRXJLYXsmqcSu5wwXR55EjKT2i6uP\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "uint256", "name": "version_", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "type": "error", "name": "AlreadySupportedAsset"}, {"inputs": [], "type": "error", "name": "Forbidden"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [{"internalType": "uint256", "name": "priceD18", "type": "uint256"}], "type": "error", "name": "InvalidPrice"}, {"inputs": [], "type": "error", "name": "InvalidReport"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [], "type": "error", "name": "ReentrancyGuardReentrantCall"}, {"inputs": [{"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "minTimestamp", "type": "uint256"}], "type": "error", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "type": "error", "name": "UnsupportedAsset"}, {"inputs": [], "type": "error", "name": "ZeroValue"}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "initParams", "type": "bytes", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address", "indexed": true}, {"internalType": "uint224", "name": "priceD18", "type": "uint224", "indexed": true}, {"internalType": "uint32", "name": "timestamp", "type": "uint32", "indexed": true}], "type": "event", "name": "ReportAccepted", "anonymous": false}, {"inputs": [{"internalType": "struct IOracle.Report[]", "name": "reports", "type": "tuple[]", "components": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint224", "name": "priceD18", "type": "uint224"}], "indexed": false}], "type": "event", "name": "ReportsSubmitted", "anonymous": false}, {"inputs": [{"internalType": "struct IOracle.SecurityParams", "name": "securityParams", "type": "tuple", "components": [{"internalType": "uint224", "name": "maxAbsoluteDeviation", "type": "uint224"}, {"internalType": "uint224", "name": "suspiciousAbsoluteDeviation", "type": "uint224"}, {"internalType": "uint64", "name": "maxRelativeDeviationD18", "type": "uint64"}, {"internalType": "uint64", "name": "suspiciousRelativeDeviationD18", "type": "uint64"}, {"internalType": "uint32", "name": "timeout", "type": "uint32"}, {"internalType": "uint32", "name": "depositInterval", "type": "uint32"}, {"internalType": "uint32", "name": "redeemInterval", "type": "uint32"}], "indexed": false}], "type": "event", "name": "SecurityParamsSet", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address", "indexed": true}], "type": "event", "name": "<PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "assets", "type": "address[]", "indexed": false}], "type": "event", "name": "SupportedAssetsAdded", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "assets", "type": "address[]", "indexed": false}], "type": "event", "name": "SupportedAssetsRemoved", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "ACCEPT_REPORT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "ADD_SUPPORTED_ASSETS_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "REMOVE_SUPPORTED_ASSETS_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SET_SECURITY_PARAMS_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SUBMIT_REPORTS_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "priceD18", "type": "uint256"}, {"internalType": "uint32", "name": "timestamp", "type": "uint32"}], "stateMutability": "nonpayable", "type": "function", "name": "acceptReport"}, {"inputs": [{"internalType": "address[]", "name": "assets", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "addSupportedAssets"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getReport", "outputs": [{"internalType": "struct IOracle.DetailedReport", "name": "", "type": "tuple", "components": [{"internalType": "uint224", "name": "priceD18", "type": "uint224"}, {"internalType": "uint32", "name": "timestamp", "type": "uint32"}, {"internalType": "bool", "name": "isSuspicious", "type": "bool"}]}]}, {"inputs": [{"internalType": "bytes", "name": "initParams", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isSupportedAsset", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address[]", "name": "assets", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "removeSupportedAssets"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "securityParams", "outputs": [{"internalType": "struct IOracle.SecurityParams", "name": "", "type": "tuple", "components": [{"internalType": "uint224", "name": "maxAbsoluteDeviation", "type": "uint224"}, {"internalType": "uint224", "name": "suspiciousAbsoluteDeviation", "type": "uint224"}, {"internalType": "uint64", "name": "maxRelativeDeviationD18", "type": "uint64"}, {"internalType": "uint64", "name": "suspiciousRelativeDeviationD18", "type": "uint64"}, {"internalType": "uint32", "name": "timeout", "type": "uint32"}, {"internalType": "uint32", "name": "depositInterval", "type": "uint32"}, {"internalType": "uint32", "name": "redeemInterval", "type": "uint32"}]}]}, {"inputs": [{"internalType": "struct IOracle.SecurityParams", "name": "securityParams_", "type": "tuple", "components": [{"internalType": "uint224", "name": "maxAbsoluteDeviation", "type": "uint224"}, {"internalType": "uint224", "name": "suspiciousAbsoluteDeviation", "type": "uint224"}, {"internalType": "uint64", "name": "maxRelativeDeviationD18", "type": "uint64"}, {"internalType": "uint64", "name": "suspiciousRelativeDeviationD18", "type": "uint64"}, {"internalType": "uint32", "name": "timeout", "type": "uint32"}, {"internalType": "uint32", "name": "depositInterval", "type": "uint32"}, {"internalType": "uint32", "name": "redeemInterval", "type": "uint32"}]}], "stateMutability": "nonpayable", "type": "function", "name": "setSecurityParams"}, {"inputs": [{"internalType": "address", "name": "vault_", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "<PERSON><PERSON><PERSON>"}, {"inputs": [{"internalType": "struct IOracle.Report[]", "name": "reports", "type": "tuple[]", "components": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint224", "name": "priceD18", "type": "uint224"}]}], "stateMutability": "nonpayable", "type": "function", "name": "submitReports"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "supportedAssetAt", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "supportedAssets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "priceD18", "type": "uint256"}, {"internalType": "address", "name": "asset", "type": "address"}], "stateMutability": "view", "type": "function", "name": "validatePrice", "outputs": [{"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bool"}, {"internalType": "bool", "name": "isSuspicious", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "vault", "outputs": [{"internalType": "contract IShareModule", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {"acceptReport(address,uint256,uint32)": {"details": "Callable only by account with `ACCEPT_REPORT_ROLE`", "params": {"asset": "Address of the asset", "priceD18": "Timestamp that must match existing suspicious report", "timestamp": "Timestamp that must match existing suspicious report"}}, "addSupportedAssets(address[])": {"params": {"assets": "Array of asset addresses to add"}}, "getReport(address)": {"params": {"asset": "Address of the asset"}}, "initialize(bytes)": {"params": {"initParams": "The initialization parameters."}}, "isSupportedAsset(address)": {"params": {"asset": "Address of the asset"}}, "removeSupportedAssets(address[])": {"params": {"assets": "Array of asset addresses to remove"}}, "setSecurityParams((uint224,uint224,uint64,uint64,uint32,uint32,uint32))": {"params": {"securityParams_": "New security settings"}}, "setVault(address)": {"params": {"vault_": "Address of the vault module"}}, "submitReports((address,uint224)[])": {"details": "Processes pending deposit and redemption requests across DepositQueue and RedeemQueue contracts.      The core processing logic is determined by the ShareModule and Queue contracts.      Only callable by accounts with the `SUBMIT_REPORTS_ROLE`.Note: Submitted prices MUST reflect protocol and performance fee deductions, ensuring accurate share issuance.", "params": {"reports": "An array of price reports, each specifying the target asset and its latest price (in 18 decimals)."}}, "supportedAssetAt(uint256)": {"params": {"index": "Index in the supported asset set"}}, "validatePrice(uint256,address)": {"details": "Evaluates both absolute and relative deviation limits to determine whether the price is valid or suspicious.", "params": {"asset": "Address of the asset being evaluated.", "priceD18": "Price to validate, in 18-decimal fixed-point format."}, "returns": {"isSuspicious": "True if the price exceeds the suspicious deviation threshold.", "isValid": "True if the price is within maximum allowed deviation."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"ACCEPT_REPORT_ROLE()": {"notice": "Role required to accept suspicious reports"}, "ADD_SUPPORTED_ASSETS_ROLE()": {"notice": "Role required to add new supported assets"}, "REMOVE_SUPPORTED_ASSETS_ROLE()": {"notice": "Role required to remove supported assets"}, "SET_SECURITY_PARAMS_ROLE()": {"notice": "Role required to update security parameters"}, "SUBMIT_REPORTS_ROLE()": {"notice": "Role required to submit reports"}, "acceptReport(address,uint256,uint32)": {"notice": "Accepts a previously suspicious report"}, "addSupportedAssets(address[])": {"notice": "Adds multiple new assets to the supported set"}, "getReport(address)": {"notice": "Returns the most recent detailed report for an asset"}, "initialize(bytes)": {"notice": "Initializes the factory-created entity with arbitrary initialization data."}, "isSupportedAsset(address)": {"notice": "Checks whether an asset is supported"}, "removeSupportedAssets(address[])": {"notice": "Removes assets from the supported set"}, "securityParams()": {"notice": "Returns current security parameters"}, "setSecurityParams((uint224,uint224,uint64,uint64,uint32,uint32,uint32))": {"notice": "Updates oracle security parameters"}, "setVault(address)": {"notice": "Sets the associated vault"}, "submitReports((address,uint224)[])": {"notice": "Submits price reports for supported assets."}, "supportedAssetAt(uint256)": {"notice": "Returns the supported asset at a specific index"}, "supportedAssets()": {"notice": "Returns total count of supported assets"}, "validatePrice(uint256,address)": {"notice": "Validates the given price for a specific asset based on oracle security parameters."}, "vault()": {"notice": "Returns the connected vault module"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/oracles/Oracle.sol": "Oracle"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/contracts/access/AccessControlUpgradeable.sol": {"keccak256": "0x0d4d7d19a052a2ef9d17b28450133631188b895e1755747fa8ad0280aadfb534", "urls": ["bzz-raw://838551e10cc07e570b70aee1078e490658d3a0ab8f4adfd4f3f2a565200753da", "dweb:/ipfs/QmceT7R3Pqzywyxr3MSEA7VXU4axZAPS6BLy92iV97rGdG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"keccak256": "0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a", "urls": ["bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6", "dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/access/extensions/AccessControlEnumerableUpgradeable.sol": {"keccak256": "0xaec38804089a16494f7d45ebbbeab78fe05e5dc4bc2eae1d2af458fe999fb43c", "urls": ["bzz-raw://04796408e2c4a1051afb23319e9505edb595a3d9c663ff5d6fcfb8e185ab1d93", "dweb:/ipfs/Qma8ZTi4kGyqg45SyNibbJSqMF7broiQnJPHhEGLcZyF9U"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05", "urls": ["bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08", "dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol": {"keccak256": "0x361126a17677994081cd9cb69c3f50cffff6e920d25cb7e428acdb1ae41d1866", "urls": ["bzz-raw://19ae787a7dd001269cd60a394b1a5261b78925a0fc3a6f927beb2986a9aa56cf", "dweb:/ipfs/QmYLfXiuKmcRgTDBEDXMMjXU8t6JxsspUmjxYzqWS55oEv"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/introspection/ERC165Upgradeable.sol": {"keccak256": "0xc8ed8d2056934b7675b695dec032f2920c2f5c6cf33a17ca85650940675323ab", "urls": ["bzz-raw://3c8ccc75d1cd792d192aa09e54dd49ea35fe85baa9fcd17486f29227d9f29b89", "dweb:/ipfs/QmbboSbFUEiM9tdEgBwuTRb7bykFoJXZ7dsSr1PSREJXMr"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530", "urls": ["bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0", "dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/extensions/IAccessControlEnumerable.sol": {"keccak256": "0xca774fbe0568762efdc1a7cba31f09549c7fa96dbe97410f4843fa2f0bc000a3", "urls": ["bzz-raw://0187ffdbf3d61b6d86cba4fcd9826e53d876987d620533ee84c681bdaf0f3ba3", "dweb:/ipfs/QmVJDqdJv6uzHY7ifncfv2QJep8XTzS3bGb4s5Exhuv86m"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol": {"keccak256": "0xb25a4f11fa80c702bf5cd85adec90e6f6f507f32f4a8e6f5dbc31e8c10029486", "urls": ["bzz-raw://6917f8a323e7811f041aecd4d9fd6e92455a6fba38a797ac6f6e208c7912b79d", "dweb:/ipfs/QmShuYv55wYHGi4EFkDB8QfF7ZCHoKk2efyz3AWY1ExSq7"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol": {"keccak256": "0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e", "urls": ["bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049", "dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol": {"keccak256": "0x8decfa54cec979c824b044b8128cd91d713f72c71fd7dfa54974624d8c949898", "urls": ["bzz-raw://271f914261a19d87117a777e0924ada545c16191ef9b00cc40b0134fc14ebc70", "dweb:/ipfs/QmdvVNWHGHQrGGPonZJs5NuzTevTjZRM2zayKrDJf7WBA2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/Proxy.sol": {"keccak256": "0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd", "urls": ["bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac", "dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol": {"keccak256": "0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c", "urls": ["bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa", "dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol": {"keccak256": "0x46f86003755f50eff00a7c5aaf493ae62e024142b8aec4493a313851d3c14872", "urls": ["bzz-raw://462c770cb667cc75ab22df5a29c50873b24b267274edf87ac5cfe0112bf4c3f7", "dweb:/ipfs/QmTfKk32AkQ2yyYZCYJ44V69EM5t9ryBFC6bRF7FVMHXvj"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol": {"keccak256": "0x92579f452fe663595a898cbac85d80bb3868a6c9f034f19ba7fbebdfa3b65a4d", "urls": ["bzz-raw://d07a888cd4a26c7077a30dd99e9ac30fbe2752bfb37e58c65f06a3b192079ef3", "dweb:/ipfs/QmZCQY4FC4ynBv9dha4BBWM1vYxTUJPBdFBS4HEFe6XzVB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol": {"keccak256": "0xb5afb8e8eebc4d1c6404df2f5e1e6d2c3d24fd01e5dfc855314951ecfaae462d", "urls": ["bzz-raw://78586466c424f076c6a2a551d848cfbe3f7c49e723830807598484a1047b3b34", "dweb:/ipfs/Qmb717ovcFxm7qgNKEShiV6M9SPR3v1qnNpAGH84D6w29p"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0xaaa1d17c1129b127a4a401db2fbd72960e2671474be3d08cae71ccdc42f7624c", "urls": ["bzz-raw://cb2f27cd3952aa667e198fba0d9b7bcec52fbb12c16f013c25fe6fb52b29cc0e", "dweb:/ipfs/QmeuohBFoeyDPZA9JNCTEDz3VBfBD4EABWuWXVhHAuEpKR"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol": {"keccak256": "0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6", "urls": ["bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd", "dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol": {"keccak256": "0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58", "urls": ["bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd", "dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Errors.sol": {"keccak256": "0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123", "urls": ["bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf", "dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol": {"keccak256": "0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26", "urls": ["bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2", "dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/Hashes.sol": {"keccak256": "0x8cbd338f083224b4b6f0ff42cbda641a0a6c31ffcdca197452b97fe4d0918269", "urls": ["bzz-raw://f517dec5ba0c6491395acbf7f1d621f4e89e8f218bf5303c867b1c5ad70c6b11", "dweb:/ipfs/QmWmXHRLEw8W6ckth7NyYTU88YfvuS7xSsfae5ksL8qNUe"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/MerkleProof.sol": {"keccak256": "0x36a0c409c437a753cac9b92b75f93b0fbe92803bf2c8ff1517e54b247f166134", "urls": ["bzz-raw://0f91ba472de411aa557cdbf6560c40750d87bd11c9060bc04d2ba7119af9d5a6", "dweb:/ipfs/QmQjtYo2i7dDvzCEzZ67bDoNSG4RrwMoxPWuqFmX5Xzpuw"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8", "urls": ["bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621", "dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol": {"keccak256": "0x93ce66a307d66cb14e95dbc8f20641cedf48b2d060bf3bda431d62a4493e6e8d", "urls": ["bzz-raw://10d4122ee13fd8dcd2f468a86031665345d2dfdda76993d1f51201bdeb746f9d", "dweb:/ipfs/QmWxbbYEgyDCjwT4ZtNwujJwB4vdnR2qEKwL2Zrz9JoSDU"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableMap.sol": {"keccak256": "0x5d0c69ef32eae686acc22acfb1a54df9c91356e69adec593aa69eba83ae8f6f4", "urls": ["bzz-raw://f7a9fca2cb3d802c4f8a2d0fda84ccc7f880264885801c7df08373501dd22282", "dweb:/ipfs/QmdtM2Loz9NzNTuQ3RXNQCPBjoPUJw35Ukpt1MSM9XH3cD"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol": {"keccak256": "0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06", "urls": ["bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231", "dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y"], "license": "MIT"}, "src/interfaces/factories/IFactory.sol": {"keccak256": "0x838a308272620b2ae6fdf1f5b425f8ba1439ed24a7baad50599d3fb34eb0bfa7", "urls": ["bzz-raw://7a801db63324ec50da072b69c67ca98eb89d026f01a4aa7e8f60d757bc82f86d", "dweb:/ipfs/QmaJ6WHAe8vfwX3A8yHyLW2aJKqkbQCKuthRg1QU2f3TuY"], "license": "BUSL-1.1"}, "src/interfaces/factories/IFactoryEntity.sol": {"keccak256": "0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5", "urls": ["bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812", "dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx"], "license": "BUSL-1.1"}, "src/interfaces/hooks/IHook.sol": {"keccak256": "0xf414604a4cbf1feb04f976a806eb2825fa7dcce6815e209fc7aa50c6fe845bb3", "urls": ["bzz-raw://a727a899804756179d294bfb0975487e8c6ca11f68aa741604d400ec6dfbf241", "dweb:/ipfs/QmRqsAigFe71aHAG5SgyKQKVpnHoiVyiP6VmnkxFK9KRz9"], "license": "BUSL-1.1"}, "src/interfaces/hooks/IRedeemHook.sol": {"keccak256": "0xd3df4e8013847ea38f603529d1062e6fccfc757ea62339bfbdd72719d2f7fbb1", "urls": ["bzz-raw://cddd2d10f020fa96f03b728dfcf8c7874942a19d871414010437ca3cdaef2974", "dweb:/ipfs/QmUZwodA14vQg3ntpzF6YxVQ2JCDmjXgQKrzE6HzJ19Mq8"], "license": "BUSL-1.1"}, "src/interfaces/managers/IFeeManager.sol": {"keccak256": "0x37d3c06ed6b23e367216c2dfc14f99f3fd6d686c984eca124406c2002e4f1516", "urls": ["bzz-raw://5b3b1b974856f76c6fe2347895ab05e973c84c963fdf8f5483c37e2e4d434cad", "dweb:/ipfs/QmVeLCrCWgeQ3aS4V38y9FszFvZdSkbPHKKCMWKKM3V4qw"], "license": "BUSL-1.1"}, "src/interfaces/managers/IRiskManager.sol": {"keccak256": "0xb0dda9606a25e8187fb20cff7eb99dfecde18fbea30bae52a2124fd22afda97a", "urls": ["bzz-raw://fee1bae75e2154b21e5ea2382ff21cd3e2ff2ea3a5e67828cfe4b563ee86e14e", "dweb:/ipfs/QmUtFozDVfCzehRMYxhJwZAbLJ43afzzVQDA89JD1HG1YH"], "license": "BUSL-1.1"}, "src/interfaces/managers/IShareManager.sol": {"keccak256": "0xb75fcb890e9a3e706c0d87781700965aa24faa8461967ee62b140d45f4c399e8", "urls": ["bzz-raw://ae50d1dda35eb9f6fa035fc39c2424f2e2eb2d41e6b0ffed296719818b1d9502", "dweb:/ipfs/Qme5jPDZG3WTLYtT2s8VFs3MKJVXgL9T5AdAzoXuuqM79z"], "license": "BUSL-1.1"}, "src/interfaces/modules/IACLModule.sol": {"keccak256": "0xf6ce9b4f4af5475d152845d6afa6f57b2d376c3a6d3eec2b5ac9650901a78243", "urls": ["bzz-raw://f851cef38b6188b20be98c95047c8bd9d8651d37421a54e1d02b60f9f372c5ed", "dweb:/ipfs/QmaymGDd2RoKweuASXCiV3rPuhwvLXUvb6wBgi8xXoiYT8"], "license": "BUSL-1.1"}, "src/interfaces/modules/IBaseModule.sol": {"keccak256": "0x9c5c95ceb39f4c39f81f2afc7ddb55389a21315e76047a9cf66521db4dea68c3", "urls": ["bzz-raw://ccfb0453b14b105cb5990978a49c1e08f812563a046e17c354f4f1c648f95fe7", "dweb:/ipfs/QmYMAKaVdRq3NoTMfqadxgMMmUq6ejzTw2Lpsa3e9a3eyj"], "license": "BUSL-1.1"}, "src/interfaces/modules/IShareModule.sol": {"keccak256": "0x65ef35a38441080ad4e0c77c7668d6717fe9699df679ed4d2c775f47d1dce57a", "urls": ["bzz-raw://e28e0e9e2dd2097df748fa2015ce4582f489615040b5323094880a618c54edac", "dweb:/ipfs/QmXNvxJDQ6edM3ExmmiTEmS5UreWYTDBgaAygU2kM6mgC6"], "license": "BUSL-1.1"}, "src/interfaces/modules/ISubvaultModule.sol": {"keccak256": "0x8db6f1a3bd2506dd0db82f4138f0436602ed37487717095a804dee81e44f5e0e", "urls": ["bzz-raw://2547472f59c19612916f3f54e6fa69644ec31f14c669aa66dea3c006bc71bccc", "dweb:/ipfs/QmZ9VRCsofVntdB7siM3pfypUpgJnmaJAC1n8HviQb3K3U"], "license": "BUSL-1.1"}, "src/interfaces/modules/IVaultModule.sol": {"keccak256": "0x7696792b852bc9b435e6388a19f4284854259df2f50b45e2b833b6f9dd1b1648", "urls": ["bzz-raw://4364151c043fc5e56bc87c2cdbffc169b80cd745618a4b0da14e271752de6637", "dweb:/ipfs/Qmdfx3L8goy8eWFGimtsh58Se1vuLsukUw2mXo5chCbTy2"], "license": "BUSL-1.1"}, "src/interfaces/modules/IVerifierModule.sol": {"keccak256": "0x8b8ad9ea43b139afccc77ceb0fdcc34615c7401cc13e99c723895302049065a5", "urls": ["bzz-raw://0d578b1a55a180c652ee24e1b35f6d70d65bc067770dd791db44d0bcfa7a5d38", "dweb:/ipfs/QmexSPCfixS9spdjCydR4FUaoN4J188sfLkRmJaia14yk6"], "license": "BUSL-1.1"}, "src/interfaces/oracles/IOracle.sol": {"keccak256": "0xcbf84560c1fb9f7d3445cbbca4f5f3406606496f30867222d8e3deabad741296", "urls": ["bzz-raw://c90107edcd30e29044a5a8b05e71e728b93a485e7e7ef1a5b4ff695430e33a5e", "dweb:/ipfs/QmfHGNxtRQWeq7JENynxZNfhfTBytwbRwyekUyuHWrUyjK"], "license": "BUSL-1.1"}, "src/interfaces/permissions/ICustomVerifier.sol": {"keccak256": "0xdb343366ed86f63f8a94ff478c5c8e1d127888e4993c27fcb3bb3b51ae0cb8c7", "urls": ["bzz-raw://12407e89b20982a00961ccc03258e353962607747f214940d18b0989c11547c9", "dweb:/ipfs/Qmay19iWpwiV8LuvWccPjsazVU5Kkhme9QykFfLKfBmvSr"], "license": "BUSL-1.1"}, "src/interfaces/permissions/IMellowACL.sol": {"keccak256": "0xcc86b0ce4e4a53c1c4fa16ad79f9de0e2a1f1a77a1f256c0856120a9d6ea36df", "urls": ["bzz-raw://6942df2ffadacae5563e01ec60697f16116362ba84a856a346b36bc7b9dd1ec2", "dweb:/ipfs/QmNYhqZGpS98UP89CS86CjoG4ZWbMHzTHRtd7bZzQqn3UY"], "license": "BUSL-1.1"}, "src/interfaces/permissions/IVerifier.sol": {"keccak256": "0x4b42731d76efaffcb017577bd2a478d00a3058c69220d6ea8e26f30f74476be7", "urls": ["bzz-raw://ff2a6f09a946dd8a470dfe495b2a64d24b849c6d071dc859589140fb1b983362", "dweb:/ipfs/Qmb6UoqkmaC8tJMqkMfjcWR7pn1WCwPwuKKvUbLBQbPcJS"], "license": "BUSL-1.1"}, "src/interfaces/queues/IDepositQueue.sol": {"keccak256": "0x4ef38cb59d419010f453e62fe101efec25bec85a2ce1a24048098034060db47b", "urls": ["bzz-raw://20e51dd02001309f23f46abea409edf77a39522bf21eeffb858ac888e4200e72", "dweb:/ipfs/QmWm9gfMuXz8nwuHb3he7abxTRgQw35x2viwMVuiNYAY5b"], "license": "BUSL-1.1"}, "src/interfaces/queues/IQueue.sol": {"keccak256": "0xd12e34631b204821c77eaa5b2e104902d740e3530cd716f7fc991af01a73b9ab", "urls": ["bzz-raw://1f2a4d664244be639da1a5025ebccc8c1127e087a81ce6d0fc76af5134fec311", "dweb:/ipfs/QmS1GJwxGCV89myCAzLBAbmSrY6YL31G3nGj542dDM4QmT"], "license": "BUSL-1.1"}, "src/interfaces/queues/IRedeemQueue.sol": {"keccak256": "0xf8f95b95de259c8ccf29976f34d97a0ef31c9827f41ebcd5e8de54c10e646076", "urls": ["bzz-raw://45ae0d496e865d846bf0c980593c1d16c038abf2b9359906224aa94cccf1c73f", "dweb:/ipfs/QmYTDXSTpUqhzJDXSKjoSKxwwQYee7wSTtf617wDsnnCC5"], "license": "BUSL-1.1"}, "src/libraries/FenwickTreeLibrary.sol": {"keccak256": "0xfd159ed27b96f288c4220febac4bfe286e4545b8ca7f523ae6e63d123341f8a3", "urls": ["bzz-raw://58f0cc202896722786140571b111bf5e589730358a24b2940a450c4c75d864fe", "dweb:/ipfs/QmeRSjvcYURUSpvFe4r4VRwwpHcb1NmqiEUDEJ5rBxAxuf"], "license": "BUSL-1.1"}, "src/libraries/SlotLibrary.sol": {"keccak256": "0x35dcf16feec83c772add30250b0987ce29ccfbf308d04159701ebf9243444eeb", "urls": ["bzz-raw://97658d0c93ecc8174169e643d5a24c515b55905a2fcaaa6d6ec0188a3dd71a10", "dweb:/ipfs/QmS1z7ac8RbMvz15gVQ9YA1cCT5bs6KMHCiDhgcG8Pq8oK"], "license": "BUSL-1.1"}, "src/oracles/Oracle.sol": {"keccak256": "0xf2bca76efcb407bffe2568016b268a08ff606dbeaebea6c20d7dfe24abe095cd", "urls": ["bzz-raw://a4e20d3e0fdf2799ba780e04f68b72ddb5e61670d2172df1996cf06d6e508fa7", "dweb:/ipfs/QmdVcWTdDeupfmCBGcRXJLYXsmqcSu5wwXR55EjKT2i6uP"], "license": "BUSL-1.1"}}, "version": 1}, "id": 127}