{"abi": [{"type": "function", "name": "IS_TEST", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "excludeArtifacts", "inputs": [], "outputs": [{"name": "excludedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeContracts", "inputs": [], "outputs": [{"name": "excludedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeSelectors", "inputs": [], "outputs": [{"name": "excludedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "excludeSenders", "inputs": [], "outputs": [{"name": "excludedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "failed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "setUp", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "targetArtifactSelectors", "inputs": [], "outputs": [{"name": "targetedArtifactSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzArtifactSelector[]", "components": [{"name": "artifact", "type": "string", "internalType": "string"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifacts", "inputs": [], "outputs": [{"name": "targetedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetContracts", "inputs": [], "outputs": [{"name": "targetedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetInterfaces", "inputs": [], "outputs": [{"name": "targetedInterfaces_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzInterface[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "artifacts", "type": "string[]", "internalType": "string[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSelectors", "inputs": [], "outputs": [{"name": "targetedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSenders", "inputs": [], "outputs": [{"name": "targetedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "testAnalyzeWstETHContract", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testCorrectStETHWrappingFlow", "inputs": [], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "testDirectETHTransferToWstETHFails", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLidoDepositHookETHPathFails", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testLidoDepositHookWETHPathFails", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testRootCauseAnalysis", "inputs": [], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "testStETHPathWorksCorrectly", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testVulnerabilityImpact", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testWstETHHasNoReceiveFunction", "inputs": [], "outputs": [], "stateMutability": "view"}, {"type": "event", "name": "log", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_address", "inputs": [{"name": "", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_bytes", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_bytes32", "inputs": [{"name": "", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_int", "inputs": [{"name": "", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_address", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes32", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_string", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_named_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_string", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_uint", "inputs": [{"name": "", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "logs", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "error", "name": "FailedCall", "inputs": []}, {"type": "error", "name": "InsufficientBalance", "inputs": [{"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}], "bytecode": {"object": "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", "sourceMap": "5006:7182:36:-:0;;;3126:44:2;;;3166:4;-1:-1:-1;;3126:44:2;;;;;;;;1016:26:12;;;;;;;;;;;5006:7182:36;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "5006:7182:36:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;7093:379;;;:::i;:::-;;5223:679;;;:::i;8230:495::-;;;:::i;7642:431::-;;;:::i;2907:134:5:-;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;11435:751:36;;;:::i;3823:151:5:-;;;:::i;:::-;;;;;;;:::i;10543:747:36:-;;;:::i;3684:133:5:-;;;:::i;3385:141::-;;;:::i;6077:365:36:-;;;:::i;9813:571::-;;;:::i;8887:746::-;;;:::i;3193:186:5:-;;;:::i;:::-;;;;;;;:::i;3047:140::-;;;:::i;:::-;;;;;;;:::i;3532:146::-;;;:::i;:::-;;;;;;;:::i;2754:147::-;;;:::i;2459:141::-;;;:::i;1243:204:1:-;;;:::i;:::-;;;6401:14:37;;6394:22;6376:41;;6364:2;6349:18;1243:204:1;6236:187:37;6593:347:36;;;:::i;2606:142:5:-;;;:::i;1016:26:12:-;;;;;;;;;7093:379:36;7154:56;;;;;;;;;;;;;;;;;;:11;:56::i;:::-;-1:-1:-1;;;;;;;;;;;309:37:0;;-1:-1:-1;;;;;7319:15:36;;:17;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;7346:4:36;;7368:8;;7346:41;;-1:-1:-1;;;7346:41:36;;-1:-1:-1;;;;;7346:4:36;;;;-1:-1:-1;7346:13:36;;-1:-1:-1;7346:41:36;;7368:8;;;;7379:7;;7346:41;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;7398:67;;;;;;;;;;;;;;;;;;:11;:67::i;:::-;7093:379::o;5223:679::-;5271:23;;;;;;;;;;;;;;-1:-1:-1;;;5271:23:36;;;:8;:23::i;:::-;5257:11;:37;;-1:-1:-1;;;;;;5257:37:36;-1:-1:-1;;;;;5257:37:36;;;;;;;;;;5350:15;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;5338:9:36;:27;;-1:-1:-1;;;;;;5338:27:36;-1:-1:-1;;;;;5338:27:36;;;;;;;;;5388:34;;;;;:::i;:::-;-1:-1:-1;;;;;6897:32:37;;;6879:51;;6867:2;6852:18;5388:34:36;;;;;;;;;;;;;;;;;;;;;;;5375:10;;:47;;;;;-1:-1:-1;;;;;5375:47:36;;;;;-1:-1:-1;;;;;5375:47:36;;;;;;5443:14;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;5432:8:36;:25;;-1:-1:-1;;;;;;5432:25:36;-1:-1:-1;;;;;5432:25:36;;;;;;;;;5561:10;;5533:71;;5432:25;5561:10;;;;;;;-1:-1:-1;;5533:71:36;;;:::i;:::-;-1:-1:-1;;;;;7199:15:37;;;7181:34;;7251:15;;;7246:2;7231:18;;7224:43;7303:15;;;7298:2;7283:18;;7276:43;7131:2;7116:18;5533:71:36;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;5526:4:36;:78;;-1:-1:-1;;;;;;5526:78:36;-1:-1:-1;;;;;5526:78:36;;;;;;5674:11;;5666:31;;-1:-1:-1;;;5666:31:36;;:7;;;;:31;;5674:11;;;5687:9;;5666:31;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;5723:4:36;;5707:33;;-1:-1:-1;;;5707:33:36;;:7;;-1:-1:-1;5707:7:36;;-1:-1:-1;5707:33:36;;-1:-1:-1;;;;;5723:4:36;;5730:9;;5707:33;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;5808:8:36;;5830:4;;5808:38;;-1:-1:-1;;;5808:38:36;;-1:-1:-1;;;;;5808:8:36;;;;-1:-1:-1;5808:13:36;;-1:-1:-1;5808:38:36;;5830:4;;;;5837:8;;5808:38;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;5856:9:36;;5879:4;;5856:39;;-1:-1:-1;;;5856:39:36;;-1:-1:-1;;;;;5856:9:36;;;;-1:-1:-1;5856:14:36;;-1:-1:-1;5856:39:36;;5879:4;;;;5886:8;;5856:39;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5223:679::o;8230:495::-;8292:58;;;;;;;;;;;;;;;;;;:11;:58::i;:::-;8426:20;8449:10;;;;;;;;;-1:-1:-1;;;;;8449:10:36;-1:-1:-1;;;;;8449:16:36;;:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;8510:9;;8426:41;;-1:-1:-1;;;;;;8486:34:36;;;8510:9;;8486:34;8478:70;;;;-1:-1:-1;;;8478:70:36;;8440:2:37;8478:70:36;;;8422:21:37;8479:2;8459:18;;;8452:30;8518:25;8498:18;;;8491:53;8561:18;;8478:70:36;;;;;;;;;8558:63;;;;;;;;;;;;;;;;;;:11;:63::i;:::-;8631:87;;;;;;;;;;;;;;;;;;:11;:87::i;:::-;8282:443;8230:495::o;7642:431::-;7706:55;;;;;;;;;;;;;;;;;;:11;:55::i;:::-;7805:17;7833:10;;;;;;;;;-1:-1:-1;;;;;7833:10:36;-1:-1:-1;;;;;7825:24:36;;;;;;;;;;;;;;;;;;;;;;;;7805:44;;7881:1;7867:4;:11;:15;7859:53;;;;-1:-1:-1;;;7859:53:36;;8792:2:37;7859:53:36;;;8774:21:37;8831:2;8811:18;;;8804:30;8870:27;8850:18;;;8843:55;8915:18;;7859:53:36;8590:349:37;7859:53:36;7923:54;;;;;;;;;;;;;;;;;;7965:4;:11;7923;:54::i;:::-;7987:79;;;;;;;;;;;;;;;;;;:11;:79::i;2907:134:5:-;2954:33;3018:16;2999:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2999:35:5;;;;;;;;;;;;;;;;;;;;;;;2907:134;:::o;11435:751:36:-;11490:50;;;;;;;;;;;;;;;;;;:11;:50::i;:::-;11550:39;;;;;;;;;;;;;;;;;;:11;:39::i;:::-;11599:96;;;;;;;;;;;;;;;;;;:11;:96::i;:::-;11705:71;;;;;;;;;;;;;;;;;;:11;:71::i;:::-;11786:73;;;;;;;;;;;;;;;;;;:11;:73::i;:::-;11869:80;;;;;;;;;;;;;;;;;;:11;:80::i;:::-;11959:72;;;;;;;;;;;;;;;;;;:11;:72::i;:::-;12041:71;;;;;;;;;;;;;;;;;;:11;:71::i;:::-;12122:57;;;;;;;;;;;;;;;;;;:11;:57::i;3823:151:5:-;3872:42;3948:19;3926:41;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3926:41:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3823:151;:::o;10543:747:36:-;10599:54;;;;;;;;;;;;;;;;;;:11;:54::i;:::-;10727:4;;10710:23;;-1:-1:-1;;;10710:23:36;;-1:-1:-1;;;;;10727:4:36;;;10710:23;;;6879:51:37;10710:8:36;;;;6852:18:37;;10710:23:36;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;10743:9:36;;10769:10;;10743:47;;-1:-1:-1;;;10743:47:36;;-1:-1:-1;;;;;10743:9:36;;;;-1:-1:-1;10743:17:36;;-1:-1:-1;10743:47:36;;:9;10769:10;;;;;;;10782:7;;10743:47;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;10832:10:36;;10861:4;;10832:35;;-1:-1:-1;;;10832:35:36;;-1:-1:-1;;;;;10861:4:36;;;10832:35;;;6879:51:37;10801:28:36;;10832:10;;;;;;;:20;;6852:18:37;;10832:35:36;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;10946:4;;10968:9;;10946:42;;-1:-1:-1;;;10946:42:36;;10801:66;;-1:-1:-1;;;;;;10946:4:36;;;;:13;;:42;;10968:9;;10980:7;;10946:42;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;11028:10:36;;11057:4;;11028:35;;-1:-1:-1;;;11028:35:36;;-1:-1:-1;;;;;11057:4:36;;;11028:35;;;6879:51:37;10999:26:36;;-1:-1:-1;11028:10:36;;;;;;-1:-1:-1;11028:20:36;;6852:18:37;;11028:35:36;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;10999:64;;11074:50;11083:18;11103:20;11074:8;:50::i;:::-;11134:69;;;;;;;;;;;;;;;;;;:11;:69::i;:::-;11213:70;;;;;;;;;;;;;;;;;;:11;:70::i;:::-;10589:701;;10543:747::o;3684:133:5:-;3730:33;3794:16;3775:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3775:35:5;;;;;;;;;;;;;;;;;;;;;;3684:133;:::o;3385:141::-;3433:35;3501:18;3480:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3480:39:5;;;;;;;;;;;;;;;;;;;;;;3385:141;:::o;6077:365:36:-;6140:60;;;;;;;;;;;;;;;;;;:11;:60::i;:::-;-1:-1:-1;;;;;;;;;;;309:37:0;;-1:-1:-1;;;;;6270:15:36;;:17;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;6331:10:36;;6297:56;;-1:-1:-1;6331:10:36;;;-1:-1:-1;;;;;6331:10:36;;-1:-1:-1;6345:7:36;6297:17;:56::i;:::-;6364:71;;;;;;;;;;;;;;;;;;:11;:71::i;9813:571::-;9867:64;;;;;;;;;;;;;;;;;;:11;:64::i;:::-;10053:10;;10045:44;;10028:12;;10053:10;;;-1:-1:-1;;;;;10053:10:36;;10077:7;;10028:12;10045:44;10028:12;10045:44;10077:7;10053:10;10045:44;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;10027:62;;;10176:7;10175:8;10167:66;;;;-1:-1:-1;;;10167:66:36;;10212:2:37;10167:66:36;;;10194:21:37;10251:2;10231:18;;;10224:30;10290:34;10270:18;;;10263:62;-1:-1:-1;;;10341:18:37;;;10334:43;10394:19;;10167:66:36;10010:409:37;10167:66:36;10243:65;;;;;;;;;;;;;;;;;;:11;:65::i;:::-;10318:59;;;;;;;;;;;;;;;;;;:11;:59::i;8887:746::-;8939:60;;;;;;;;;;;;;;;;;;:11;:60::i;:::-;9043:4;;9066:56;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;9043:4:36;;;9035:21;;9066:56;;9035:21;9066:11;:56::i;:::-;-1:-1:-1;;;;;;;;;;;309:37:0;;-1:-1:-1;;;;;9201:15:36;;:17;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;9228:4:36;;:43;;-1:-1:-1;;;9228:43:36;;-1:-1:-1;;;;;9228:4:36;;;;-1:-1:-1;9228:13:36;;-1:-1:-1;9228:43:36;;775:42:35;;9263:7:36;;9228:43;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;309:37:0;;-1:-1:-1;;;;;9282:15:36;;:17;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;9309:4:36;;9331:8;;9309:41;;-1:-1:-1;;;9309:41:36;;-1:-1:-1;;;;;9309:4:36;;;;-1:-1:-1;9309:13:36;;-1:-1:-1;9309:41:36;;9331:8;;;;9342:7;;9309:41;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;9443:4:36;;9426:47;;-1:-1:-1;;;;;;9443:4:36;9435:21;;-1:-1:-1;9458:14:36;9426:8;:47::i;:::-;9483:66;;;;;;;;;;;;;;;;;;:11;:66::i;:::-;9559:67;;;;;;;;;;;;;;;;;;:11;:67::i;3193:186:5:-;3249:56;3346:26;3317:55;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3317:55:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3047:140;3095:34;3162:18;3141:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3532:146;3580:40;3653:18;3632:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2754:147;2803:40;2876:18;2855:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2459:141;2508:34;2575:18;2554:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1243:204:1;1302:7;;1282:4;;1302:7;;1298:143;;;-1:-1:-1;1332:7:1;;;;;1243:204::o;1298:143::-;1377:39;;-1:-1:-1;;;1377:39:1;;1428:1;;1377:7;;;;:39;;-1:-1:-1;;;;;;;;;;;219:28:1;-1:-1:-1;;;1398:17:1;1377:39;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:53;;1370:60;;1243:204;:::o;6593:347:36:-;6653:55;;;;;;;;;;;;;;;;;;:11;:55::i;:::-;-1:-1:-1;;;;;;;;;;;309:37:0;;-1:-1:-1;;;;;6786:15:36;;:17;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;6813:4:36;;:43;;-1:-1:-1;;;6813:43:36;;-1:-1:-1;;;;;6813:4:36;;;;-1:-1:-1;6813:13:36;;-1:-1:-1;6813:43:36;;775:42:35;;6848:7:36;;6813:43;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6867:66;;;;;;;;;;;;;;;;;;:11;:66::i;2606:142:5:-;2655:35;2723:18;2702:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2702:39:5;;;;;;;;;;;;;;;;;;;;;;2606:142;:::o;6191:121:14:-;6246:59;6301:2;6262:42;;;;;;;;:::i;:::-;;;;-1:-1:-1;;6262:42:14;;;;;;;;;;;;;;-1:-1:-1;;;;;6262:42:14;-1:-1:-1;;;6262:42:14;;;6246:15;:59::i;20454:125:3:-;20518:12;20552:20;20567:4;20552:14;:20::i;:::-;-1:-1:-1;20542:30:3;20454:125;-1:-1:-1;;20454:125:3:o;7139:145:14:-;7206:71;7269:2;7273;7222:54;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;7222:54:14;;;;;;;;;;;;;;-1:-1:-1;;;;;7222:54:14;-1:-1:-1;;;7222:54:14;;;7206:15;:71::i;13112:110:1:-;13191:24;;-1:-1:-1;;;13191:24:1;;;;;11587:25:37;;;11628:18;;;11621:34;;;13191:11:1;;;;11560:18:37;;13191:24:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;13112:110;;:::o;1290:365:28:-;1399:6;1375:21;:30;1371:125;;;1428:57;;-1:-1:-1;;;1428:57:28;;1455:21;1428:57;;;11587:25:37;11628:18;;;11621:34;;;11560:18;;1428:57:28;11413:248:37;1371:125:28;1507:12;1521:23;1548:9;-1:-1:-1;;;;;1548:14:28;1570:6;1548:33;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1506:75;;;;1596:7;1591:58;;1619:19;1627:10;1619:7;:19::i;2270:110:1:-;2349:24;;-1:-1:-1;;;2349:24:1;;;;;11587:25:37;;;11628:18;;;11621:34;;;2349:11:1;;;;11560:18:37;;2349:24:1;11413:248:37;851:129:14;922:51;965:7;934:29;922:51::i;20173:242:3:-;20243:12;20257:18;20335:4;20318:22;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;20318:22:3;;;;;;;20308:33;;20318:22;20308:33;;;;-1:-1:-1;;;;;;20359:19:3;;;;;12120:25:37;;;20308:33:3;-1:-1:-1;20359:7:3;;;;12093:18:37;;20359:19:3;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;20388:20;;-1:-1:-1;;;20388:20:3;;20352:26;;-1:-1:-1;20388:8:3;;;;:20;;20352:26;;20403:4;;20388:20;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;20173:242;;;:::o;5559:487:28:-;5690:17;;:21;5686:354;;5887:10;5881:17;5943:15;5930:10;5926:2;5922:19;5915:44;5686:354;6010:19;;-1:-1:-1;;;6010:19:28;;;;;;;;;;;180:463:14;265:22;131:42;265:40;;594:1;571;541:7;535:14;510:2;501:7;497:16;461:14;434:5;402:211;381:246;367:270;180:463;:::o;-1:-1:-1:-;;;;;;;;:::o;:::-;;;;;;;;:::o;:::-;;;;;;;;:::o;:::-;;;;;;;;:::o;14:658:37:-;185:2;237:21;;;307:13;;210:18;;;329:22;;;156:4;;185:2;408:15;;;;382:2;367:18;;;156:4;451:195;465:6;462:1;459:13;451:195;;;530:13;;-1:-1:-1;;;;;526:39:37;514:52;;621:15;;;;586:12;;;;562:1;480:9;451:195;;;-1:-1:-1;663:3:37;;14:658;-1:-1:-1;;;;;;14:658:37:o;677:289::-;719:3;757:5;751:12;784:6;779:3;772:19;840:6;833:4;826:5;822:16;815:4;810:3;806:14;800:47;892:1;885:4;876:6;871:3;867:16;863:27;856:38;955:4;948:2;944:7;939:2;931:6;927:15;923:29;918:3;914:39;910:50;903:57;;;677:289;;;;:::o;971:1714::-;1204:2;1256:21;;;1326:13;;1229:18;;;1348:22;;;1175:4;;1204:2;1389;;1407:18;;;;1444:1;1487:14;;;1472:30;;1468:39;;1530:15;;;1175:4;1573:1083;1587:6;1584:1;1581:13;1573:1083;;;-1:-1:-1;;1652:22:37;;;1648:36;1636:49;;1708:13;;1795:9;;-1:-1:-1;;;;;1791:35:37;1776:51;;1866:11;;1860:18;1898:15;;;1891:27;;;1979:19;;1748:15;;;2011:24;;;2192:21;;;;2058:2;2140:17;;;2128:30;;2124:39;;;2082:15;;;;2237:1;2251:296;2267:8;2262:3;2259:17;2251:296;;;2373:2;2369:7;2360:6;2352;2348:19;2344:33;2337:5;2330:48;2405:42;2440:6;2429:8;2423:15;2405:42;:::i;:::-;2476:17;;;;2395:52;-1:-1:-1;2519:14:37;;;;2295:1;2286:11;2251:296;;;-1:-1:-1;;;2634:12:37;;;;2570:6;-1:-1:-1;;2599:15:37;;;;1609:1;1602:9;1573:1083;;;-1:-1:-1;2673:6:37;;971:1714;-1:-1:-1;;;;;;;;;971:1714:37:o;2690:465::-;2742:3;2780:5;2774:12;2807:6;2802:3;2795:19;2833:4;2862;2857:3;2853:14;2846:21;;2901:4;2894:5;2890:16;2924:1;2934:196;2948:6;2945:1;2942:13;2934:196;;;3013:13;;-1:-1:-1;;;;;;3009:40:37;2997:53;;3070:12;;;;3105:15;;;;2970:1;2963:9;2934:196;;;-1:-1:-1;3146:3:37;;2690:465;-1:-1:-1;;;;;2690:465:37:o;3160:1185::-;3378:4;3407:2;3447;3436:9;3432:18;3477:2;3466:9;3459:21;3500:6;3535;3529:13;3566:6;3558;3551:22;3592:2;3582:12;;3625:2;3614:9;3610:18;3603:25;;3687:2;3677:6;3674:1;3670:14;3659:9;3655:30;3651:39;3725:2;3717:6;3713:15;3746:1;3756:560;3770:6;3767:1;3764:13;3756:560;;;3835:22;;;-1:-1:-1;;3831:36:37;3819:49;;3891:13;;3937:9;;3959:18;;;4004:48;4036:15;;;3937:9;4004:48;:::i;:::-;4093:11;;;4087:18;4142:19;;;4125:15;;;4118:44;4087:18;3990:62;-1:-1:-1;4185:51:37;3990:62;4087:18;4185:51;:::i;:::-;4294:12;;;;4175:61;-1:-1:-1;;;4259:15:37;;;;3792:1;3785:9;3756:560;;;-1:-1:-1;4333:6:37;;3160:1185;-1:-1:-1;;;;;;;;3160:1185:37:o;4350:803::-;4512:4;4541:2;4581;4570:9;4566:18;4611:2;4600:9;4593:21;4634:6;4669;4663:13;4700:6;4692;4685:22;4738:2;4727:9;4723:18;4716:25;;4800:2;4790:6;4787:1;4783:14;4772:9;4768:30;4764:39;4750:53;;4838:2;4830:6;4826:15;4859:1;4869:255;4883:6;4880:1;4877:13;4869:255;;;4976:2;4972:7;4960:9;4952:6;4948:22;4944:36;4939:3;4932:49;5004:40;5037:6;5028;5022:13;5004:40;:::i;:::-;4994:50;-1:-1:-1;5102:12:37;;;;5067:15;;;;4905:1;4898:9;4869:255;;;-1:-1:-1;5141:6:37;;4350:803;-1:-1:-1;;;;;;;4350:803:37:o;5158:1073::-;5360:4;5389:2;5429;5418:9;5414:18;5459:2;5448:9;5441:21;5482:6;5517;5511:13;5548:6;5540;5533:22;5574:2;5564:12;;5607:2;5596:9;5592:18;5585:25;;5669:2;5659:6;5656:1;5652:14;5641:9;5637:30;5633:39;5707:2;5699:6;5695:15;5728:1;5738:464;5752:6;5749:1;5746:13;5738:464;;;5817:22;;;-1:-1:-1;;5813:36:37;5801:49;;5873:13;;5918:9;;-1:-1:-1;;;;;5914:35:37;5899:51;;5989:11;;5983:18;6021:15;;;6014:27;;;6064:58;6106:15;;;5983:18;6064:58;:::i;:::-;6180:12;;;;6054:68;-1:-1:-1;;6145:15:37;;;;5774:1;5767:9;5738:464;;6428:300;-1:-1:-1;;;;;6646:32:37;;;;6628:51;;6710:2;6695:18;;6688:34;6616:2;6601:18;;6428:300::o;7943:290::-;8013:6;8066:2;8054:9;8045:7;8041:23;8037:32;8034:52;;;8082:1;8079;8072:12;8034:52;8108:16;;-1:-1:-1;;;;;8153:31:37;;8143:42;;8133:70;;8199:1;8196;8189:12;8133:70;8222:5;7943:290;-1:-1:-1;;;7943:290:37:o;8944:380::-;9023:1;9019:12;;;;9066;;;9087:61;;9141:4;9133:6;9129:17;9119:27;;9087:61;9194:2;9186:6;9183:14;9163:18;9160:38;9157:161;;9240:10;9235:3;9231:20;9228:1;9221:31;9275:4;9272:1;9265:15;9303:4;9300:1;9293:15;9157:161;;8944:380;;;:::o;9329:277::-;9396:6;9449:2;9437:9;9428:7;9424:23;9420:32;9417:52;;;9465:1;9462;9455:12;9417:52;9497:9;9491:16;9550:5;9543:13;9536:21;9529:5;9526:32;9516:60;;9572:1;9569;9562:12;9611:184;9681:6;9734:2;9722:9;9713:7;9709:23;9705:32;9702:52;;;9750:1;9747;9740:12;9702:52;-1:-1:-1;9773:16:37;;9611:184;-1:-1:-1;9611:184:37:o;10892:220::-;11041:2;11030:9;11023:21;11004:4;11061:45;11102:2;11091:9;11087:18;11079:6;11061:45;:::i;11117:291::-;11294:2;11283:9;11276:21;11257:4;11314:45;11355:2;11344:9;11340:18;11332:6;11314:45;:::i;:::-;11306:53;;11395:6;11390:2;11379:9;11375:18;11368:34;11117:291;;;;;:::o;11666:303::-;11797:3;11835:6;11829:13;11881:6;11874:4;11866:6;11862:17;11857:3;11851:37;11943:1;11907:16;;11932:13;;;-1:-1:-1;11907:16:37;11666:303;-1:-1:-1;11666:303:37:o;12156:317::-;-1:-1:-1;;;;;12333:32:37;;12315:51;;12402:2;12397;12382:18;;12375:30;;;-1:-1:-1;;12422:45:37;;12448:18;;12440:6;12422:45;:::i;:::-;12414:53;12156:317;-1:-1:-1;;;;12156:317:37:o", "linkReferences": {}}, "methodIdentifiers": {"IS_TEST()": "fa7626d4", "excludeArtifacts()": "b5508aa9", "excludeContracts()": "e20c9f71", "excludeSelectors()": "b0464fdc", "excludeSenders()": "1ed7831c", "failed()": "ba414fa6", "setUp()": "0a9254e4", "targetArtifactSelectors()": "66d9a9a0", "targetArtifacts()": "85226c81", "targetContracts()": "3f7286f4", "targetInterfaces()": "2ade3880", "targetSelectors()": "916a17c6", "targetSenders()": "3e5e3c23", "testAnalyzeWstETHContract()": "493c0969", "testCorrectStETHWrappingFlow()": "13c1d836", "testDirectETHTransferToWstETHFails()": "441baa0e", "testLidoDepositHookETHPathFails()": "d347ae3a", "testLidoDepositHookWETHPathFails()": "007cdc69", "testRootCauseAnalysis()": "267eb6ce", "testStETHPathWorksCorrectly()": "2d162157", "testVulnerabilityImpact()": "4ba481e9", "testWstETHHasNoReceiveFunction()": "1e11568c"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"FailedCall\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"InsufficientBalance\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"log_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"log_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"log_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"name\":\"log_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"val\",\"type\":\"address\"}],\"name\":\"log_named_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"val\",\"type\":\"bytes\"}],\"name\":\"log_named_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"val\",\"type\":\"bytes32\"}],\"name\":\"log_named_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"}],\"name\":\"log_named_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"val\",\"type\":\"string\"}],\"name\":\"log_named_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"log_named_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"log_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"logs\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"IS_TEST\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"excludedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"excludedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"failed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"setUp\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifactSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"artifact\",\"type\":\"string\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzArtifactSelector[]\",\"name\":\"targetedArtifactSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"targetedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetInterfaces\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"string[]\",\"name\":\"artifacts\",\"type\":\"string[]\"}],\"internalType\":\"struct StdInvariant.FuzzInterface[]\",\"name\":\"targetedInterfaces_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"targetedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testAnalyzeWstETHContract\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testCorrectStETHWrappingFlow\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testDirectETHTransferToWstETHFails\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLidoDepositHookETHPathFails\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testLidoDepositHookWETHPathFails\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testRootCauseAnalysis\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testStETHPathWorksCorrectly\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testVulnerabilityImpact\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testWstETHHasNoReceiveFunction\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"This POC demonstrates the logical flaw in the contract design\",\"errors\":{\"FailedCall()\":[{\"details\":\"A call to an address target failed. The target may have reverted.\"}],\"InsufficientBalance(uint256,uint256)\":[{\"details\":\"The ETH balance of the account is not enough to perform the operation.\"}]},\"kind\":\"dev\",\"methods\":{\"testAnalyzeWstETHContract()\":{\"details\":\"Analyze the actual wstETH contract to prove it lacks a receive function\"},\"testCorrectStETHWrappingFlow()\":{\"details\":\"Show how the contract should work with stETH instead of ETH\"},\"testDirectETHTransferToWstETHFails()\":{\"details\":\"This demonstrates the core vulnerability - wstETH doesn't accept direct ETH\"},\"testLidoDepositHookETHPathFails()\":{\"details\":\"This tests the actual vulnerability in the hook contract\"},\"testLidoDepositHookWETHPathFails()\":{\"details\":\"This tests the WETH withdrawal -> ETH send to wstETH path\"},\"testRootCauseAnalysis()\":{\"details\":\"Confirm the exact technical reason for the vulnerability\"},\"testStETHPathWorksCorrectly()\":{\"details\":\"Show that the stETH path works correctly as intended\"},\"testVulnerabilityImpact()\":{\"details\":\"Demonstrate that the vulnerability breaks liveness for ETH/WETH deposits\"},\"testWstETHHasNoReceiveFunction()\":{\"details\":\"Check the contract bytecode to confirm no receive function exists\"}},\"title\":\"LidoDepositHookVulnerabilityPOC\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"testAnalyzeWstETHContract()\":{\"notice\":\"Test 7: Demonstrate the missing receive function in wstETH\"},\"testCorrectStETHWrappingFlow()\":{\"notice\":\"Test 5: Demonstrate correct stETH wrapping flow\"},\"testDirectETHTransferToWstETHFails()\":{\"notice\":\"Test 1: Direct ETH transfer to wstETH should fail\"},\"testLidoDepositHookETHPathFails()\":{\"notice\":\"Test 2: LidoDepositHook ETH path should fail\"},\"testLidoDepositHookWETHPathFails()\":{\"notice\":\"Test 3: LidoDepositHook WETH path should fail\"},\"testRootCauseAnalysis()\":{\"notice\":\"Test 9: Verify the root cause analysis\"},\"testStETHPathWorksCorrectly()\":{\"notice\":\"Test 8: Demonstrate successful stETH path (control test)\"},\"testVulnerabilityImpact()\":{\"notice\":\"Test 6: Verify the vulnerability impact\"},\"testWstETHHasNoReceiveFunction()\":{\"notice\":\"Test 4: Verify wstETH contract has no receive function\"}},\"notice\":\"Proof of Concept demonstrating the vulnerability in LidoDepositHook\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/poc/LidoDepositHookVulnerabilityPOC.t.sol\":\"LidoDepositHookVulnerabilityPOC\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/Base.sol\":{\"keccak256\":\"0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224\",\"dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe\",\"dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0x7dd11ac4db798fa196e00a0cfa288ce984acac881f1f34de03fe3187deed58de\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2c15fb03d5545e2d8d3948661596410edec5ec278a73988914b14a05d0d54557\",\"dweb:/ipfs/QmWGjkLgp4wSrTrFxF8cmhghaXQp5VyXYQvWE1DUTPL4rG\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0xc28b654cfbcafb8f7688c8dc69134921bb4841c82ddc2e5f27bcf5d8eb2177cf\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3f1875b52fb32efefdc467d9566bf93d72dad036547ce6955e4f7a318eea9d6f\",\"dweb:/ipfs/QmbgbSX8JajLzTyWcPqrYAcsQcHNuGTFTKdQ3HNTvyRgdj\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x4a5853bb9688c4e1c65626036cddd8ea2af84ef1046b704ddc3782d84a892410\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://09c1024e6aac9657312fcfcf5c3dee62c22edf9f74a1065e6924c4ec710ecafa\",\"dweb:/ipfs/QmRNURoTUkd6DiZnf8yY8o3stJpVQBifopdSpQgP9RhvRr\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e\",\"dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x51d822417db259b59eec2dfb7859285cef983a26b5d4fba20e1addabccc8734e\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://0e3b5abe0173ee6e479e4248eb4ee3a4374f7e08fb2848d93f5c2cf5e95214f1\",\"dweb:/ipfs/QmbUBD4NLFCuR6WHVx5unXH2uWHEhtADLcFrUwyev8iLrD\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/interfaces/IERC165.sol\":{\"keccak256\":\"0x414b2861b1acbf816ccb7346d3f16cf6c1e002e9e5e40d2f1f26fa5ddc2ea600\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://698352fb240868ea8f1d1fe389993035eeab930f10d06934f80ccfb2b6ccbfbc\",\"dweb:/ipfs/QmT6WLHAgXxFhh12kWym895oTzXid1326iZiwT3pyfggoT\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/interfaces/IERC20.sol\":{\"keccak256\":\"0x4cab887298790f908c27de107e4e2907ca5413aee482ef776f8d2f353c5ef947\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bb715e0c4a2bdbe432bb624501506041f06e878e0b72675aebba30ad2c2b72e7\",\"dweb:/ipfs/QmWhhLSvkxS2NrukJJHqFY8gDVE5r9rD4PfHvR24pwdKv9\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/interfaces/IERC721.sol\":{\"keccak256\":\"0xf069262a264fdb69f8f37a10d2df7374649e9ba73f8414c9c8a3b51184625f15\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://924a41ca82a68ffbd503a5faf2f76d13e9938f10501a71094716f12bb64b4b7f\",\"dweb:/ipfs/QmdWnqfKc5ZGhmxNPTde4zrFchnv9Yk9MpCMb2rdhXE5gm\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/mocks/MockERC20.sol\":{\"keccak256\":\"0xadbdfc6639edec00ba94bb1133a0fd8de31ccafe45c2ef5df9b3ca61b60b559b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc766f01bccf669abac8cc7b2096cc1352a66471ca3772ae61b1801dedc4ed4f\",\"dweb:/ipfs/QmNigaPj1LYkP7Z5xr4ijkUxr2K66fDGeoifG3WoM9ruyw\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/mocks/MockERC721.sol\":{\"keccak256\":\"0xbfbb755bb379f58b97699f7cda0e9a2b9113daaa072e41b280a5ce6a3bff44c9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5ae0c31d29db14d6a0e9b62d33d550feb307f48a6f45068901a013321f337f87\",\"dweb:/ipfs/QmX25DVXW6XZDvRm6SNBGCWrZQ6oNhdq5CmgtVocvsUdwv\"]},\"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0x9b6b3e7803bc5f2f8cd7ad57db8ac1def61a9930a5a3107df4882e028a9605d7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://da62d6be1f5c6edf577f0cb45666a8aa9c2086a4bac87d95d65f02e2f4c36a4b\",\"dweb:/ipfs/QmNkpvBpoCMvX8JwAFNSc5XxJ2q5BXJpL5L1txb4QkqVFF\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0xde7e9fd9aee8d4f40772f96bb3b58836cbc6dfc0227014a061947f8821ea9724\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://11fea9f8bc98949ac6709f0c1699db7430d2948137aa94d5a9e95a91f61a710a\",\"dweb:/ipfs/QmQdfRXxQjwP6yn3DVo1GHPpriKNcFghSPi94Z1oKEFUNS\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xce41876e78d1badc0512229b4d14e4daf83bc1003d7f83978d18e0e56f965b9c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a2608291cb038b388d80b79a06b6118a42f7894ff67b7da10ec0dbbf5b2973ba\",\"dweb:/ipfs/QmWohqcBLbcxmA4eGPhZDXe5RYMMEEpFq22nfkaUMvTfw1\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://df6f0c459663c9858b6cba2cda1d14a7d05a985bed6d2de72bd8e78c25ee79db\",\"dweb:/ipfs/QmeTTxZ7qVk9rjEv2R4CpCwdf8UMCcRqDNMvzNxHc3Fnn9\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508\",\"dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0xaaa1d17c1129b127a4a401db2fbd72960e2671474be3d08cae71ccdc42f7624c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cb2f27cd3952aa667e198fba0d9b7bcec52fbb12c16f013c25fe6fb52b29cc0e\",\"dweb:/ipfs/QmeuohBFoeyDPZA9JNCTEDz3VBfBD4EABWuWXVhHAuEpKR\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Errors.sol\":{\"keccak256\":\"0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf\",\"dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621\",\"dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL\"]},\"src/hooks/LidoDepositHook.sol\":{\"keccak256\":\"0x9018b1143ced609c9e4823eb1ba286f6035ae8af374cd2c78f18ca9903b55f76\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://3364334643db5d1c9c6ec604ef1c43cd3218cb8785bffc0197f0db9d34234e78\",\"dweb:/ipfs/QmWi9ADGArVWvf632vDEVhWqfEopxFZ16DaYKSyFrJb947\"]},\"src/interfaces/external/tokens/IWETH.sol\":{\"keccak256\":\"0x0b7dcdc2713f5f75105a7ed5d6c35341d1d102bdda563717a98db9c329426514\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c87103da00019d8555a04c3325a99eaf2e5e7a321319f23bd4eac92ddd70d18d\",\"dweb:/ipfs/QmSpFTyRndVTZDPVfPpHrWfHQXamc4p2M1dde4J5uAsKZG\"]},\"src/interfaces/external/tokens/IWSTETH.sol\":{\"keccak256\":\"0xd836767c06d507064f4dbfcad077ff827237bed25e0a5a0f11bc8acebe7ec2c6\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://1f9eedf7e5d048fd6cf78275916d9f65b3e4bcb18fc040e69dcf8e9d6c5624c1\",\"dweb:/ipfs/QmRmJGtdyN9n32YkhSLcxthk8hZUWYr1xfB6yHimG45Mzu\"]},\"src/interfaces/hooks/IHook.sol\":{\"keccak256\":\"0xf414604a4cbf1feb04f976a806eb2825fa7dcce6815e209fc7aa50c6fe845bb3\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://a727a899804756179d294bfb0975487e8c6ca11f68aa741604d400ec6dfbf241\",\"dweb:/ipfs/QmRqsAigFe71aHAG5SgyKQKVpnHoiVyiP6VmnkxFK9KRz9\"]},\"src/libraries/TransferLibrary.sol\":{\"keccak256\":\"0x3f2885a3bc24b3ab880a408f94511475136a8449a49c8c3fb5587de555a6c47e\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://6626b7d9956a63be7ce24825fc22c1f7f45503fa9b2e2ccdf5160b1e026eaa02\",\"dweb:/ipfs/QmX5VrYDquCHKSZMWEw6YNQM2inTiQzLNKM3sTevLgPxzM\"]},\"test/poc/LidoDepositHookVulnerabilityPOC.t.sol\":{\"keccak256\":\"0x9ca94e118245b4bf8035514b77ee6152f7077891a285c2e3bc33cef1d0367130\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://f16ecf36ab78c0dc8b31cfe503ba93b5447e2bae3941ace047a39a353726bd15\",\"dweb:/ipfs/QmWn9HKnDPdu23xxi8Dbm2EfoUHd5RZGJ2G5zuezYWqY8d\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "FailedCall"}, {"inputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "InsufficientBalance"}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address", "indexed": false}], "type": "event", "name": "log_address", "anonymous": false}, {"inputs": [{"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "log_bytes", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_bytes32", "anonymous": false}, {"inputs": [{"internalType": "int256", "name": "", "type": "int256", "indexed": false}], "type": "event", "name": "log_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address", "name": "val", "type": "address", "indexed": false}], "type": "event", "name": "log_named_address", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes", "name": "val", "type": "bytes", "indexed": false}], "type": "event", "name": "log_named_bytes", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes32", "name": "val", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_named_bytes32", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}], "type": "event", "name": "log_named_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "string", "name": "val", "type": "string", "indexed": false}], "type": "event", "name": "log_named_string", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log_string", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256", "indexed": false}], "type": "event", "name": "log_uint", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "logs", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_TEST", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeArtifacts", "outputs": [{"internalType": "string[]", "name": "excludedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeContracts", "outputs": [{"internalType": "address[]", "name": "excludedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "excludedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSenders", "outputs": [{"internalType": "address[]", "name": "excludedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "failed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "setUp"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifactSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzArtifactSelector[]", "name": "targetedArtifactSelectors_", "type": "tuple[]", "components": [{"internalType": "string", "name": "artifact", "type": "string"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifacts", "outputs": [{"internalType": "string[]", "name": "targetedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetContracts", "outputs": [{"internalType": "address[]", "name": "targetedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetInterfaces", "outputs": [{"internalType": "struct StdInvariant.FuzzInterface[]", "name": "targetedInterfaces_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "string[]", "name": "artifacts", "type": "string[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "targetedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSenders", "outputs": [{"internalType": "address[]", "name": "targetedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testAnalyzeWstETHContract"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "testCorrectStETHWrappingFlow"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testDirectETHTransferToWstETHFails"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLidoDepositHookETHPathFails"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testLidoDepositHookWETHPathFails"}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "testRootCauseAnalysis"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testStETHPathWorksCorrectly"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testVulnerabilityImpact"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "testWstETHHasNoReceiveFunction"}], "devdoc": {"kind": "dev", "methods": {"testAnalyzeWstETHContract()": {"details": "Analyze the actual wstETH contract to prove it lacks a receive function"}, "testCorrectStETHWrappingFlow()": {"details": "Show how the contract should work with stETH instead of ETH"}, "testDirectETHTransferToWstETHFails()": {"details": "This demonstrates the core vulnerability - wstETH doesn't accept direct ETH"}, "testLidoDepositHookETHPathFails()": {"details": "This tests the actual vulnerability in the hook contract"}, "testLidoDepositHookWETHPathFails()": {"details": "This tests the WETH withdrawal -> ETH send to wstETH path"}, "testRootCauseAnalysis()": {"details": "Confirm the exact technical reason for the vulnerability"}, "testStETHPathWorksCorrectly()": {"details": "Show that the stETH path works correctly as intended"}, "testVulnerabilityImpact()": {"details": "Demonstrate that the vulnerability breaks liveness for ETH/WETH deposits"}, "testWstETHHasNoReceiveFunction()": {"details": "Check the contract bytecode to confirm no receive function exists"}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"testAnalyzeWstETHContract()": {"notice": "Test 7: Demonstrate the missing receive function in wstETH"}, "testCorrectStETHWrappingFlow()": {"notice": "Test 5: Demonstrate correct stETH wrapping flow"}, "testDirectETHTransferToWstETHFails()": {"notice": "Test 1: Direct ETH transfer to wstETH should fail"}, "testLidoDepositHookETHPathFails()": {"notice": "Test 2: LidoDepositHook ETH path should fail"}, "testLidoDepositHookWETHPathFails()": {"notice": "Test 3: LidoDepositHook WETH path should fail"}, "testRootCauseAnalysis()": {"notice": "Test 9: Verify the root cause analysis"}, "testStETHPathWorksCorrectly()": {"notice": "Test 8: Demonstrate successful stETH path (control test)"}, "testVulnerabilityImpact()": {"notice": "Test 6: Verify the vulnerability impact"}, "testWstETHHasNoReceiveFunction()": {"notice": "Test 4: Verify wstETH contract has no receive function"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/poc/LidoDepositHookVulnerabilityPOC.t.sol": "LidoDepositHookVulnerabilityPOC"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/Base.sol": {"keccak256": "0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c", "urls": ["bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224", "dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdAssertions.sol": {"keccak256": "0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270", "urls": ["bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe", "dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdChains.sol": {"keccak256": "0x7dd11ac4db798fa196e00a0cfa288ce984acac881f1f34de03fe3187deed58de", "urls": ["bzz-raw://2c15fb03d5545e2d8d3948661596410edec5ec278a73988914b14a05d0d54557", "dweb:/ipfs/QmWGjkLgp4wSrTrFxF8cmhghaXQp5VyXYQvWE1DUTPL4rG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdCheats.sol": {"keccak256": "0xc28b654cfbcafb8f7688c8dc69134921bb4841c82ddc2e5f27bcf5d8eb2177cf", "urls": ["bzz-raw://3f1875b52fb32efefdc467d9566bf93d72dad036547ce6955e4f7a318eea9d6f", "dweb:/ipfs/QmbgbSX8JajLzTyWcPqrYAcsQcHNuGTFTKdQ3HNTvyRgdj"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/StdUtils.sol": {"keccak256": "0x4a5853bb9688c4e1c65626036cddd8ea2af84ef1046b704ddc3782d84a892410", "urls": ["bzz-raw://09c1024e6aac9657312fcfcf5c3dee62c22edf9f74a1065e6924c4ec710ecafa", "dweb:/ipfs/QmRNURoTUkd6DiZnf8yY8o3stJpVQBifopdSpQgP9RhvRr"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/Test.sol": {"keccak256": "0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe", "urls": ["bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e", "dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/Vm.sol": {"keccak256": "0x51d822417db259b59eec2dfb7859285cef983a26b5d4fba20e1addabccc8734e", "urls": ["bzz-raw://0e3b5abe0173ee6e479e4248eb4ee3a4374f7e08fb2848d93f5c2cf5e95214f1", "dweb:/ipfs/QmbUBD4NLFCuR6WHVx5unXH2uWHEhtADLcFrUwyev8iLrD"], "license": "MIT OR Apache-2.0"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/interfaces/IERC165.sol": {"keccak256": "0x414b2861b1acbf816ccb7346d3f16cf6c1e002e9e5e40d2f1f26fa5ddc2ea600", "urls": ["bzz-raw://698352fb240868ea8f1d1fe389993035eeab930f10d06934f80ccfb2b6ccbfbc", "dweb:/ipfs/QmT6WLHAgXxFhh12kWym895oTzXid1326iZiwT3pyfggoT"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/interfaces/IERC20.sol": {"keccak256": "0x4cab887298790f908c27de107e4e2907ca5413aee482ef776f8d2f353c5ef947", "urls": ["bzz-raw://bb715e0c4a2bdbe432bb624501506041f06e878e0b72675aebba30ad2c2b72e7", "dweb:/ipfs/QmWhhLSvkxS2NrukJJHqFY8gDVE5r9rD4PfHvR24pwdKv9"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/interfaces/IERC721.sol": {"keccak256": "0xf069262a264fdb69f8f37a10d2df7374649e9ba73f8414c9c8a3b51184625f15", "urls": ["bzz-raw://924a41ca82a68ffbd503a5faf2f76d13e9938f10501a71094716f12bb64b4b7f", "dweb:/ipfs/QmdWnqfKc5ZGhmxNPTde4zrFchnv9Yk9MpCMb2rdhXE5gm"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/mocks/MockERC20.sol": {"keccak256": "0xadbdfc6639edec00ba94bb1133a0fd8de31ccafe45c2ef5df9b3ca61b60b559b", "urls": ["bzz-raw://bc766f01bccf669abac8cc7b2096cc1352a66471ca3772ae61b1801dedc4ed4f", "dweb:/ipfs/QmNigaPj1LYkP7Z5xr4ijkUxr2K66fDGeoifG3WoM9ruyw"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/mocks/MockERC721.sol": {"keccak256": "0xbfbb755bb379f58b97699f7cda0e9a2b9113daaa072e41b280a5ce6a3bff44c9", "urls": ["bzz-raw://5ae0c31d29db14d6a0e9b62d33d550feb307f48a6f45068901a013321f337f87", "dweb:/ipfs/QmX25DVXW6XZDvRm6SNBGCWrZQ6oNhdq5CmgtVocvsUdwv"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol": {"keccak256": "0x9b6b3e7803bc5f2f8cd7ad57db8ac1def61a9930a5a3107df4882e028a9605d7", "urls": ["bzz-raw://da62d6be1f5c6edf577f0cb45666a8aa9c2086a4bac87d95d65f02e2f4c36a4b", "dweb:/ipfs/QmNkpvBpoCMvX8JwAFNSc5XxJ2q5BXJpL5L1txb4QkqVFF"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol": {"keccak256": "0xde7e9fd9aee8d4f40772f96bb3b58836cbc6dfc0227014a061947f8821ea9724", "urls": ["bzz-raw://11fea9f8bc98949ac6709f0c1699db7430d2948137aa94d5a9e95a91f61a710a", "dweb:/ipfs/QmQdfRXxQjwP6yn3DVo1GHPpriKNcFghSPi94Z1oKEFUNS"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xce41876e78d1badc0512229b4d14e4daf83bc1003d7f83978d18e0e56f965b9c", "urls": ["bzz-raw://a2608291cb038b388d80b79a06b6118a42f7894ff67b7da10ec0dbbf5b2973ba", "dweb:/ipfs/QmWohqcBLbcxmA4eGPhZDXe5RYMMEEpFq22nfkaUMvTfw1"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7", "urls": ["bzz-raw://df6f0c459663c9858b6cba2cda1d14a7d05a985bed6d2de72bd8e78c25ee79db", "dweb:/ipfs/QmeTTxZ7qVk9rjEv2R4CpCwdf8UMCcRqDNMvzNxHc3Fnn9"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5", "urls": ["bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508", "dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0xaaa1d17c1129b127a4a401db2fbd72960e2671474be3d08cae71ccdc42f7624c", "urls": ["bzz-raw://cb2f27cd3952aa667e198fba0d9b7bcec52fbb12c16f013c25fe6fb52b29cc0e", "dweb:/ipfs/QmeuohBFoeyDPZA9JNCTEDz3VBfBD4EABWuWXVhHAuEpKR"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Errors.sol": {"keccak256": "0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123", "urls": ["bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf", "dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8", "urls": ["bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621", "dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL"], "license": "MIT"}, "src/hooks/LidoDepositHook.sol": {"keccak256": "0x9018b1143ced609c9e4823eb1ba286f6035ae8af374cd2c78f18ca9903b55f76", "urls": ["bzz-raw://3364334643db5d1c9c6ec604ef1c43cd3218cb8785bffc0197f0db9d34234e78", "dweb:/ipfs/QmWi9ADGArVWvf632vDEVhWqfEopxFZ16DaYKSyFrJb947"], "license": "BUSL-1.1"}, "src/interfaces/external/tokens/IWETH.sol": {"keccak256": "0x0b7dcdc2713f5f75105a7ed5d6c35341d1d102bdda563717a98db9c329426514", "urls": ["bzz-raw://c87103da00019d8555a04c3325a99eaf2e5e7a321319f23bd4eac92ddd70d18d", "dweb:/ipfs/QmSpFTyRndVTZDPVfPpHrWfHQXamc4p2M1dde4J5uAsKZG"], "license": "BUSL-1.1"}, "src/interfaces/external/tokens/IWSTETH.sol": {"keccak256": "0xd836767c06d507064f4dbfcad077ff827237bed25e0a5a0f11bc8acebe7ec2c6", "urls": ["bzz-raw://1f9eedf7e5d048fd6cf78275916d9f65b3e4bcb18fc040e69dcf8e9d6c5624c1", "dweb:/ipfs/QmRmJGtdyN9n32YkhSLcxthk8hZUWYr1xfB6yHimG45Mzu"], "license": "BUSL-1.1"}, "src/interfaces/hooks/IHook.sol": {"keccak256": "0xf414604a4cbf1feb04f976a806eb2825fa7dcce6815e209fc7aa50c6fe845bb3", "urls": ["bzz-raw://a727a899804756179d294bfb0975487e8c6ca11f68aa741604d400ec6dfbf241", "dweb:/ipfs/QmRqsAigFe71aHAG5SgyKQKVpnHoiVyiP6VmnkxFK9KRz9"], "license": "BUSL-1.1"}, "src/libraries/TransferLibrary.sol": {"keccak256": "0x3f2885a3bc24b3ab880a408f94511475136a8449a49c8c3fb5587de555a6c47e", "urls": ["bzz-raw://6626b7d9956a63be7ce24825fc22c1f7f45503fa9b2e2ccdf5160b1e026eaa02", "dweb:/ipfs/QmX5VrYDquCHKSZMWEw6YNQM2inTiQzLNKM3sTevLgPxzM"], "license": "BUSL-1.1"}, "test/poc/LidoDepositHookVulnerabilityPOC.t.sol": {"keccak256": "0x9ca94e118245b4bf8035514b77ee6152f7077891a285c2e3bc33cef1d0367130", "urls": ["bzz-raw://f16ecf36ab78c0dc8b31cfe503ba93b5447e2bae3941ace047a39a353726bd15", "dweb:/ipfs/QmWn9HKnDPdu23xxi8Dbm2EfoUHd5RZGJ2G5zuezYWqY8d"], "license": "BUSL-1.1"}}, "version": 1}, "id": 36}