{"abi": [{"type": "function", "name": "CALCULATION_INTERVAL_SECONDS", "inputs": [], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "GENESIS_REWARDS_TIMESTAMP", "inputs": [], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "MAX_FUTURE_LENGTH", "inputs": [], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "MAX_RETROACTIVE_LENGTH", "inputs": [], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "MAX_REWARDS_DURATION", "inputs": [], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "activationDelay", "inputs": [], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "calculateEarnerLeafHash", "inputs": [{"name": "leaf", "type": "tuple", "internalType": "struct IRewardsCoordinator.EarnerTreeMerkleLeaf", "components": [{"name": "earner", "type": "address", "internalType": "address"}, {"name": "earnerTokenRoot", "type": "bytes32", "internalType": "bytes32"}]}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "calculateTokenLeafHash", "inputs": [{"name": "leaf", "type": "tuple", "internalType": "struct IRewardsCoordinator.TokenTreeMerkleLeaf", "components": [{"name": "token", "type": "address", "internalType": "contract IERC20"}, {"name": "cumulativeEarnings", "type": "uint256", "internalType": "uint256"}]}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "checkClaim", "inputs": [{"name": "claim", "type": "tuple", "internalType": "struct IRewardsCoordinator.RewardsMerkleClaim", "components": [{"name": "rootIndex", "type": "uint32", "internalType": "uint32"}, {"name": "earnerIndex", "type": "uint32", "internalType": "uint32"}, {"name": "earnerTreeProof", "type": "bytes", "internalType": "bytes"}, {"name": "earner<PERSON>eaf", "type": "tuple", "internalType": "struct IRewardsCoordinator.EarnerTreeMerkleLeaf", "components": [{"name": "earner", "type": "address", "internalType": "address"}, {"name": "earnerTokenRoot", "type": "bytes32", "internalType": "bytes32"}]}, {"name": "tokenIndices", "type": "uint32[]", "internalType": "uint32[]"}, {"name": "tokenTreeProofs", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "tokenLeaves", "type": "tuple[]", "internalType": "struct IRewardsCoordinator.TokenTreeMerkleLeaf[]", "components": [{"name": "token", "type": "address", "internalType": "contract IERC20"}, {"name": "cumulativeEarnings", "type": "uint256", "internalType": "uint256"}]}]}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "claimerFor", "inputs": [{"name": "earner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "cumulativeClaimed", "inputs": [{"name": "claimer", "type": "address", "internalType": "address"}, {"name": "token", "type": "address", "internalType": "contract IERC20"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "currRewardsCalculationEndTimestamp", "inputs": [], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "defaultOperatorSplitBips", "inputs": [], "outputs": [{"name": "", "type": "uint16", "internalType": "uint16"}], "stateMutability": "view"}, {"type": "function", "name": "disableRoot", "inputs": [{"name": "rootIndex", "type": "uint32", "internalType": "uint32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getCurrentClaimableDistributionRoot", "inputs": [], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IRewardsCoordinator.DistributionRoot", "components": [{"name": "root", "type": "bytes32", "internalType": "bytes32"}, {"name": "rewardsCalculationEndTimestamp", "type": "uint32", "internalType": "uint32"}, {"name": "activatedAt", "type": "uint32", "internalType": "uint32"}, {"name": "disabled", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "getCurrentDistributionRoot", "inputs": [], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IRewardsCoordinator.DistributionRoot", "components": [{"name": "root", "type": "bytes32", "internalType": "bytes32"}, {"name": "rewardsCalculationEndTimestamp", "type": "uint32", "internalType": "uint32"}, {"name": "activatedAt", "type": "uint32", "internalType": "uint32"}, {"name": "disabled", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "getDistributionRootAtIndex", "inputs": [{"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IRewardsCoordinator.DistributionRoot", "components": [{"name": "root", "type": "bytes32", "internalType": "bytes32"}, {"name": "rewardsCalculationEndTimestamp", "type": "uint32", "internalType": "uint32"}, {"name": "activatedAt", "type": "uint32", "internalType": "uint32"}, {"name": "disabled", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "getDistributionRootsLength", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getOperatorAVSSplit", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "avs", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint16", "internalType": "uint16"}], "stateMutability": "view"}, {"type": "function", "name": "getOperatorPISplit", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint16", "internalType": "uint16"}], "stateMutability": "view"}, {"type": "function", "name": "getRootIndexFromHash", "inputs": [{"name": "rootHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "processClaim", "inputs": [{"name": "claim", "type": "tuple", "internalType": "struct IRewardsCoordinator.RewardsMerkleClaim", "components": [{"name": "rootIndex", "type": "uint32", "internalType": "uint32"}, {"name": "earnerIndex", "type": "uint32", "internalType": "uint32"}, {"name": "earnerTreeProof", "type": "bytes", "internalType": "bytes"}, {"name": "earner<PERSON>eaf", "type": "tuple", "internalType": "struct IRewardsCoordinator.EarnerTreeMerkleLeaf", "components": [{"name": "earner", "type": "address", "internalType": "address"}, {"name": "earnerTokenRoot", "type": "bytes32", "internalType": "bytes32"}]}, {"name": "tokenIndices", "type": "uint32[]", "internalType": "uint32[]"}, {"name": "tokenTreeProofs", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "tokenLeaves", "type": "tuple[]", "internalType": "struct IRewardsCoordinator.TokenTreeMerkleLeaf[]", "components": [{"name": "token", "type": "address", "internalType": "contract IERC20"}, {"name": "cumulativeEarnings", "type": "uint256", "internalType": "uint256"}]}]}, {"name": "recipient", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "processClaims", "inputs": [{"name": "claims", "type": "tuple[]", "internalType": "struct IRewardsCoordinator.RewardsMerkleClaim[]", "components": [{"name": "rootIndex", "type": "uint32", "internalType": "uint32"}, {"name": "earnerIndex", "type": "uint32", "internalType": "uint32"}, {"name": "earnerTreeProof", "type": "bytes", "internalType": "bytes"}, {"name": "earner<PERSON>eaf", "type": "tuple", "internalType": "struct IRewardsCoordinator.EarnerTreeMerkleLeaf", "components": [{"name": "earner", "type": "address", "internalType": "address"}, {"name": "earnerTokenRoot", "type": "bytes32", "internalType": "bytes32"}]}, {"name": "tokenIndices", "type": "uint32[]", "internalType": "uint32[]"}, {"name": "tokenTreeProofs", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "tokenLeaves", "type": "tuple[]", "internalType": "struct IRewardsCoordinator.TokenTreeMerkleLeaf[]", "components": [{"name": "token", "type": "address", "internalType": "contract IERC20"}, {"name": "cumulativeEarnings", "type": "uint256", "internalType": "uint256"}]}]}, {"name": "recipient", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "rewardsUpdater", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "setActivation<PERSON>elay", "inputs": [{"name": "_activationDelay", "type": "uint32", "internalType": "uint32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setClaimerFor", "inputs": [{"name": "claimer", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setDefaultOperatorSplit", "inputs": [{"name": "split", "type": "uint16", "internalType": "uint16"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setOperatorAVSSplit", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "avs", "type": "address", "internalType": "address"}, {"name": "split", "type": "uint16", "internalType": "uint16"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setOperatorPISplit", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "split", "type": "uint16", "internalType": "uint16"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setRewardsForAllSubmitter", "inputs": [{"name": "_submitter", "type": "address", "internalType": "address"}, {"name": "_newValue", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setRewardsUpdater", "inputs": [{"name": "_rewardsUpdater", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "submitRoot", "inputs": [{"name": "root", "type": "bytes32", "internalType": "bytes32"}, {"name": "rewardsCalculationEndTimestamp", "type": "uint32", "internalType": "uint32"}], "outputs": [], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"CALCULATION_INTERVAL_SECONDS()": "9d45c281", "GENESIS_REWARDS_TIMESTAMP()": "131433b4", "MAX_FUTURE_LENGTH()": "04a0c502", "MAX_RETROACTIVE_LENGTH()": "37838ed0", "MAX_REWARDS_DURATION()": "bf21a8aa", "activationDelay()": "3a8c0786", "calculateEarnerLeafHash((address,bytes32))": "149bc872", "calculateTokenLeafHash((address,uint256))": "f8cd8448", "checkClaim((uint32,uint32,bytes,(address,bytes32),uint32[],bytes[],(address,uint256)[]))": "5e9d8348", "claimerFor(address)": "2b9f64a4", "cumulativeClaimed(address,address)": "865c6953", "currRewardsCalculationEndTimestamp()": "4d18cc35", "defaultOperatorSplitBips()": "63f6a798", "disableRoot(uint32)": "f96abf2e", "getCurrentClaimableDistributionRoot()": "0e9a53cf", "getCurrentDistributionRoot()": "9be3d4e4", "getDistributionRootAtIndex(uint256)": "de02e503", "getDistributionRootsLength()": "7b8f8b05", "getOperatorAVSSplit(address,address)": "e063f81f", "getOperatorPISplit(address)": "4b943960", "getRootIndexFromHash(bytes32)": "e810ce21", "processClaim((uint32,uint32,bytes,(address,bytes32),uint32[],bytes[],(address,uint256)[]),address)": "3ccc861d", "processClaims((uint32,uint32,bytes,(address,bytes32),uint32[],bytes[],(address,uint256)[])[],address)": "4596021c", "rewardsUpdater()": "fbf1e2c1", "setActivationDelay(uint32)": "58baaa3e", "setClaimerFor(address)": "a0169ddd", "setDefaultOperatorSplit(uint16)": "a50a1d9c", "setOperatorAVSSplit(address,address,uint16)": "dcbb03b3", "setOperatorPISplit(address,uint16)": "b3dbb0e0", "setRewardsForAllSubmitter(address,bool)": "0eb38345", "setRewardsUpdater(address)": "863cb9a9", "submitRoot(bytes32,uint32)": "3efe1db6"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"CALCULATION_INTERVAL_SECONDS\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"GENESIS_REWARDS_TIMESTAMP\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MAX_FUTURE_LENGTH\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MAX_RETROACTIVE_LENGTH\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MAX_REWARDS_DURATION\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"activationDelay\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"earner\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"earnerTokenRoot\",\"type\":\"bytes32\"}],\"internalType\":\"struct IRewardsCoordinator.EarnerTreeMerkleLeaf\",\"name\":\"leaf\",\"type\":\"tuple\"}],\"name\":\"calculateEarnerLeafHash\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"contract IERC20\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"cumulativeEarnings\",\"type\":\"uint256\"}],\"internalType\":\"struct IRewardsCoordinator.TokenTreeMerkleLeaf\",\"name\":\"leaf\",\"type\":\"tuple\"}],\"name\":\"calculateTokenLeafHash\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint32\",\"name\":\"rootIndex\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"earnerIndex\",\"type\":\"uint32\"},{\"internalType\":\"bytes\",\"name\":\"earnerTreeProof\",\"type\":\"bytes\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"earner\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"earnerTokenRoot\",\"type\":\"bytes32\"}],\"internalType\":\"struct IRewardsCoordinator.EarnerTreeMerkleLeaf\",\"name\":\"earnerLeaf\",\"type\":\"tuple\"},{\"internalType\":\"uint32[]\",\"name\":\"tokenIndices\",\"type\":\"uint32[]\"},{\"internalType\":\"bytes[]\",\"name\":\"tokenTreeProofs\",\"type\":\"bytes[]\"},{\"components\":[{\"internalType\":\"contract IERC20\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"cumulativeEarnings\",\"type\":\"uint256\"}],\"internalType\":\"struct IRewardsCoordinator.TokenTreeMerkleLeaf[]\",\"name\":\"tokenLeaves\",\"type\":\"tuple[]\"}],\"internalType\":\"struct IRewardsCoordinator.RewardsMerkleClaim\",\"name\":\"claim\",\"type\":\"tuple\"}],\"name\":\"checkClaim\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"earner\",\"type\":\"address\"}],\"name\":\"claimerFor\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"claimer\",\"type\":\"address\"},{\"internalType\":\"contract IERC20\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"cumulativeClaimed\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"currRewardsCalculationEndTimestamp\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"defaultOperatorSplitBips\",\"outputs\":[{\"internalType\":\"uint16\",\"name\":\"\",\"type\":\"uint16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"rootIndex\",\"type\":\"uint32\"}],\"name\":\"disableRoot\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getCurrentClaimableDistributionRoot\",\"outputs\":[{\"components\":[{\"internalType\":\"bytes32\",\"name\":\"root\",\"type\":\"bytes32\"},{\"internalType\":\"uint32\",\"name\":\"rewardsCalculationEndTimestamp\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"activatedAt\",\"type\":\"uint32\"},{\"internalType\":\"bool\",\"name\":\"disabled\",\"type\":\"bool\"}],\"internalType\":\"struct IRewardsCoordinator.DistributionRoot\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getCurrentDistributionRoot\",\"outputs\":[{\"components\":[{\"internalType\":\"bytes32\",\"name\":\"root\",\"type\":\"bytes32\"},{\"internalType\":\"uint32\",\"name\":\"rewardsCalculationEndTimestamp\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"activatedAt\",\"type\":\"uint32\"},{\"internalType\":\"bool\",\"name\":\"disabled\",\"type\":\"bool\"}],\"internalType\":\"struct IRewardsCoordinator.DistributionRoot\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"getDistributionRootAtIndex\",\"outputs\":[{\"components\":[{\"internalType\":\"bytes32\",\"name\":\"root\",\"type\":\"bytes32\"},{\"internalType\":\"uint32\",\"name\":\"rewardsCalculationEndTimestamp\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"activatedAt\",\"type\":\"uint32\"},{\"internalType\":\"bool\",\"name\":\"disabled\",\"type\":\"bool\"}],\"internalType\":\"struct IRewardsCoordinator.DistributionRoot\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getDistributionRootsLength\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"}],\"name\":\"getOperatorAVSSplit\",\"outputs\":[{\"internalType\":\"uint16\",\"name\":\"\",\"type\":\"uint16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"getOperatorPISplit\",\"outputs\":[{\"internalType\":\"uint16\",\"name\":\"\",\"type\":\"uint16\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"rootHash\",\"type\":\"bytes32\"}],\"name\":\"getRootIndexFromHash\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint32\",\"name\":\"rootIndex\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"earnerIndex\",\"type\":\"uint32\"},{\"internalType\":\"bytes\",\"name\":\"earnerTreeProof\",\"type\":\"bytes\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"earner\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"earnerTokenRoot\",\"type\":\"bytes32\"}],\"internalType\":\"struct IRewardsCoordinator.EarnerTreeMerkleLeaf\",\"name\":\"earnerLeaf\",\"type\":\"tuple\"},{\"internalType\":\"uint32[]\",\"name\":\"tokenIndices\",\"type\":\"uint32[]\"},{\"internalType\":\"bytes[]\",\"name\":\"tokenTreeProofs\",\"type\":\"bytes[]\"},{\"components\":[{\"internalType\":\"contract IERC20\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"cumulativeEarnings\",\"type\":\"uint256\"}],\"internalType\":\"struct IRewardsCoordinator.TokenTreeMerkleLeaf[]\",\"name\":\"tokenLeaves\",\"type\":\"tuple[]\"}],\"internalType\":\"struct IRewardsCoordinator.RewardsMerkleClaim\",\"name\":\"claim\",\"type\":\"tuple\"},{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"}],\"name\":\"processClaim\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint32\",\"name\":\"rootIndex\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"earnerIndex\",\"type\":\"uint32\"},{\"internalType\":\"bytes\",\"name\":\"earnerTreeProof\",\"type\":\"bytes\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"earner\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"earnerTokenRoot\",\"type\":\"bytes32\"}],\"internalType\":\"struct IRewardsCoordinator.EarnerTreeMerkleLeaf\",\"name\":\"earnerLeaf\",\"type\":\"tuple\"},{\"internalType\":\"uint32[]\",\"name\":\"tokenIndices\",\"type\":\"uint32[]\"},{\"internalType\":\"bytes[]\",\"name\":\"tokenTreeProofs\",\"type\":\"bytes[]\"},{\"components\":[{\"internalType\":\"contract IERC20\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"cumulativeEarnings\",\"type\":\"uint256\"}],\"internalType\":\"struct IRewardsCoordinator.TokenTreeMerkleLeaf[]\",\"name\":\"tokenLeaves\",\"type\":\"tuple[]\"}],\"internalType\":\"struct IRewardsCoordinator.RewardsMerkleClaim[]\",\"name\":\"claims\",\"type\":\"tuple[]\"},{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"}],\"name\":\"processClaims\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rewardsUpdater\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_activationDelay\",\"type\":\"uint32\"}],\"name\":\"setActivationDelay\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"claimer\",\"type\":\"address\"}],\"name\":\"setClaimerFor\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"split\",\"type\":\"uint16\"}],\"name\":\"setDefaultOperatorSplit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"avs\",\"type\":\"address\"},{\"internalType\":\"uint16\",\"name\":\"split\",\"type\":\"uint16\"}],\"name\":\"setOperatorAVSSplit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"uint16\",\"name\":\"split\",\"type\":\"uint16\"}],\"name\":\"setOperatorPISplit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_submitter\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"_newValue\",\"type\":\"bool\"}],\"name\":\"setRewardsForAllSubmitter\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_rewardsUpdater\",\"type\":\"address\"}],\"name\":\"setRewardsUpdater\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"root\",\"type\":\"bytes32\"},{\"internalType\":\"uint32\",\"name\":\"rewardsCalculationEndTimestamp\",\"type\":\"uint32\"}],\"name\":\"submitRoot\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/external/eigen-layer/IRewardsCoordinator.sol\":\"IRewardsCoordinator\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://df6f0c459663c9858b6cba2cda1d14a7d05a985bed6d2de72bd8e78c25ee79db\",\"dweb:/ipfs/QmeTTxZ7qVk9rjEv2R4CpCwdf8UMCcRqDNMvzNxHc3Fnn9\"]},\"src/interfaces/external/eigen-layer/IAllocationManager.sol\":{\"keccak256\":\"0xae3e733a2af24b269f9e861eb091a70123dfb662073296af1e0404d3ef38077c\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b35c72a2bdebe1d6a1515a1215e90bb354414de570e3723bd1f764204fe51e86\",\"dweb:/ipfs/QmWibt9unFu2MYSMXmkb8rAtCEpxUxio5Rn2fM43D4cKth\"]},\"src/interfaces/external/eigen-layer/IDelegationManager.sol\":{\"keccak256\":\"0x84e8357193d083bd63c3af2ae3ee1583d1ba734a09d24a897fa6c7785a28b700\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://e4ffb431c1e5283743009f98027dcdb06a42b32789d7251fb21f6a223e3565aa\",\"dweb:/ipfs/Qme9V6VZFkTDuB3aM3XhkntjYJ6Fv3Ud7QuHgorY4iDGXL\"]},\"src/interfaces/external/eigen-layer/IRewardsCoordinator.sol\":{\"keccak256\":\"0x8802e05f49d4d1b1c7b283ec96e7808416747d0f3900f730240d3520288e03c9\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ee0009552f9dad177606b11476d169590024a902294b2355d3da5f9a9398e4eb\",\"dweb:/ipfs/QmUV9wM8VzVRppaeqGPQTNh7BjZszzdxafyDXY7rd9RjCQ\"]},\"src/interfaces/external/eigen-layer/ISignatureUtils.sol\":{\"keccak256\":\"0xf0a4f7b91b079e85ad7b27132a751955d09694358cb6494b70a6d4217f248f03\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://0773df5dae86559e56fbced497b20cbe8def225f3f2dadf43cf7728c10cd6bc2\",\"dweb:/ipfs/QmS7YDqmyttHZ8sbX1YB21Z2rk7wsS9hneDTkAcSPbED6E\"]},\"src/interfaces/external/eigen-layer/IStrategy.sol\":{\"keccak256\":\"0x938f287999a7edf7f3a67f6c2aa3205268cf6e5ebc8dbc00302d2886a558b098\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c7e22f4a1ed1cf9fc2f255d98d5af1a85077cfb77bdfaa351348f4bb28bca872\",\"dweb:/ipfs/QmeCA5afkHVoW5FVhDmaVNUwV4tN6YzwKyHyC9dJpffRof\"]},\"src/interfaces/external/eigen-layer/IStrategyManager.sol\":{\"keccak256\":\"0xf8557b7be6db605615100f77fb6a423350afd25709c78badc077063b16db80b9\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://5220c4e8c2d2d5b50f3ea0e88d8d91660be87340ffb4621f3180a7ed8fc977eb\",\"dweb:/ipfs/QmPdgaqa7DhzmMya6wB31K1Voky7iSBF2SBEVR6VPahEuA\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "CALCULATION_INTERVAL_SECONDS", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "GENESIS_REWARDS_TIMESTAMP", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "MAX_FUTURE_LENGTH", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "MAX_RETROACTIVE_LENGTH", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "MAX_REWARDS_DURATION", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "activationDelay", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}]}, {"inputs": [{"internalType": "struct IRewardsCoordinator.EarnerTreeMerkleLeaf", "name": "leaf", "type": "tuple", "components": [{"internalType": "address", "name": "earner", "type": "address"}, {"internalType": "bytes32", "name": "earnerTokenRoot", "type": "bytes32"}]}], "stateMutability": "pure", "type": "function", "name": "calculateEarnerLeafHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "struct IRewardsCoordinator.TokenTreeMerkleLeaf", "name": "leaf", "type": "tuple", "components": [{"internalType": "contract IERC20", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "cumulativeEarnings", "type": "uint256"}]}], "stateMutability": "pure", "type": "function", "name": "calculateTokenLeafHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "struct IRewardsCoordinator.RewardsMerkleClaim", "name": "claim", "type": "tuple", "components": [{"internalType": "uint32", "name": "rootIndex", "type": "uint32"}, {"internalType": "uint32", "name": "earnerIndex", "type": "uint32"}, {"internalType": "bytes", "name": "earnerTreeProof", "type": "bytes"}, {"internalType": "struct IRewardsCoordinator.EarnerTreeMerkleLeaf", "name": "earner<PERSON>eaf", "type": "tuple", "components": [{"internalType": "address", "name": "earner", "type": "address"}, {"internalType": "bytes32", "name": "earnerTokenRoot", "type": "bytes32"}]}, {"internalType": "uint32[]", "name": "tokenIndices", "type": "uint32[]"}, {"internalType": "bytes[]", "name": "tokenTreeProofs", "type": "bytes[]"}, {"internalType": "struct IRewardsCoordinator.TokenTreeMerkleLeaf[]", "name": "tokenLeaves", "type": "tuple[]", "components": [{"internalType": "contract IERC20", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "cumulativeEarnings", "type": "uint256"}]}]}], "stateMutability": "view", "type": "function", "name": "checkClaim", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "earner", "type": "address"}], "stateMutability": "view", "type": "function", "name": "claimerFor", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "claimer", "type": "address"}, {"internalType": "contract IERC20", "name": "token", "type": "address"}], "stateMutability": "view", "type": "function", "name": "cumulativeClaimed", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "currRewardsCalculationEndTimestamp", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "defaultOperatorSplitBips", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}]}, {"inputs": [{"internalType": "uint32", "name": "rootIndex", "type": "uint32"}], "stateMutability": "nonpayable", "type": "function", "name": "disableRoot"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getCurrentClaimableDistributionRoot", "outputs": [{"internalType": "struct IRewardsCoordinator.DistributionRoot", "name": "", "type": "tuple", "components": [{"internalType": "bytes32", "name": "root", "type": "bytes32"}, {"internalType": "uint32", "name": "rewardsCalculationEndTimestamp", "type": "uint32"}, {"internalType": "uint32", "name": "activatedAt", "type": "uint32"}, {"internalType": "bool", "name": "disabled", "type": "bool"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getCurrentDistributionRoot", "outputs": [{"internalType": "struct IRewardsCoordinator.DistributionRoot", "name": "", "type": "tuple", "components": [{"internalType": "bytes32", "name": "root", "type": "bytes32"}, {"internalType": "uint32", "name": "rewardsCalculationEndTimestamp", "type": "uint32"}, {"internalType": "uint32", "name": "activatedAt", "type": "uint32"}, {"internalType": "bool", "name": "disabled", "type": "bool"}]}]}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getDistributionRootAtIndex", "outputs": [{"internalType": "struct IRewardsCoordinator.DistributionRoot", "name": "", "type": "tuple", "components": [{"internalType": "bytes32", "name": "root", "type": "bytes32"}, {"internalType": "uint32", "name": "rewardsCalculationEndTimestamp", "type": "uint32"}, {"internalType": "uint32", "name": "activatedAt", "type": "uint32"}, {"internalType": "bool", "name": "disabled", "type": "bool"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getDistributionRootsLength", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "avs", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getOperatorAVSSplit", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getOperatorPISplit", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}]}, {"inputs": [{"internalType": "bytes32", "name": "rootHash", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "getRootIndexFromHash", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}]}, {"inputs": [{"internalType": "struct IRewardsCoordinator.RewardsMerkleClaim", "name": "claim", "type": "tuple", "components": [{"internalType": "uint32", "name": "rootIndex", "type": "uint32"}, {"internalType": "uint32", "name": "earnerIndex", "type": "uint32"}, {"internalType": "bytes", "name": "earnerTreeProof", "type": "bytes"}, {"internalType": "struct IRewardsCoordinator.EarnerTreeMerkleLeaf", "name": "earner<PERSON>eaf", "type": "tuple", "components": [{"internalType": "address", "name": "earner", "type": "address"}, {"internalType": "bytes32", "name": "earnerTokenRoot", "type": "bytes32"}]}, {"internalType": "uint32[]", "name": "tokenIndices", "type": "uint32[]"}, {"internalType": "bytes[]", "name": "tokenTreeProofs", "type": "bytes[]"}, {"internalType": "struct IRewardsCoordinator.TokenTreeMerkleLeaf[]", "name": "tokenLeaves", "type": "tuple[]", "components": [{"internalType": "contract IERC20", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "cumulativeEarnings", "type": "uint256"}]}]}, {"internalType": "address", "name": "recipient", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "processClaim"}, {"inputs": [{"internalType": "struct IRewardsCoordinator.RewardsMerkleClaim[]", "name": "claims", "type": "tuple[]", "components": [{"internalType": "uint32", "name": "rootIndex", "type": "uint32"}, {"internalType": "uint32", "name": "earnerIndex", "type": "uint32"}, {"internalType": "bytes", "name": "earnerTreeProof", "type": "bytes"}, {"internalType": "struct IRewardsCoordinator.EarnerTreeMerkleLeaf", "name": "earner<PERSON>eaf", "type": "tuple", "components": [{"internalType": "address", "name": "earner", "type": "address"}, {"internalType": "bytes32", "name": "earnerTokenRoot", "type": "bytes32"}]}, {"internalType": "uint32[]", "name": "tokenIndices", "type": "uint32[]"}, {"internalType": "bytes[]", "name": "tokenTreeProofs", "type": "bytes[]"}, {"internalType": "struct IRewardsCoordinator.TokenTreeMerkleLeaf[]", "name": "tokenLeaves", "type": "tuple[]", "components": [{"internalType": "contract IERC20", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "cumulativeEarnings", "type": "uint256"}]}]}, {"internalType": "address", "name": "recipient", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "processClaims"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "rewardsUpdater", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "uint32", "name": "_activationDelay", "type": "uint32"}], "stateMutability": "nonpayable", "type": "function", "name": "setActivation<PERSON>elay"}, {"inputs": [{"internalType": "address", "name": "claimer", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setClaimerFor"}, {"inputs": [{"internalType": "uint16", "name": "split", "type": "uint16"}], "stateMutability": "nonpayable", "type": "function", "name": "setDefaultOperatorSplit"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "avs", "type": "address"}, {"internalType": "uint16", "name": "split", "type": "uint16"}], "stateMutability": "nonpayable", "type": "function", "name": "setOperatorAVSSplit"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "uint16", "name": "split", "type": "uint16"}], "stateMutability": "nonpayable", "type": "function", "name": "setOperatorPISplit"}, {"inputs": [{"internalType": "address", "name": "_submitter", "type": "address"}, {"internalType": "bool", "name": "_newValue", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "setRewardsForAllSubmitter"}, {"inputs": [{"internalType": "address", "name": "_rewardsUpdater", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setRewardsUpdater"}, {"inputs": [{"internalType": "bytes32", "name": "root", "type": "bytes32"}, {"internalType": "uint32", "name": "rewardsCalculationEndTimestamp", "type": "uint32"}], "stateMutability": "nonpayable", "type": "function", "name": "submitRoot"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/external/eigen-layer/IRewardsCoordinator.sol": "IRewardsCoordinator"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7", "urls": ["bzz-raw://df6f0c459663c9858b6cba2cda1d14a7d05a985bed6d2de72bd8e78c25ee79db", "dweb:/ipfs/QmeTTxZ7qVk9rjEv2R4CpCwdf8UMCcRqDNMvzNxHc3Fnn9"], "license": "MIT"}, "src/interfaces/external/eigen-layer/IAllocationManager.sol": {"keccak256": "0xae3e733a2af24b269f9e861eb091a70123dfb662073296af1e0404d3ef38077c", "urls": ["bzz-raw://b35c72a2bdebe1d6a1515a1215e90bb354414de570e3723bd1f764204fe51e86", "dweb:/ipfs/QmWibt9unFu2MYSMXmkb8rAtCEpxUxio5Rn2fM43D4cKth"], "license": "BUSL-1.1"}, "src/interfaces/external/eigen-layer/IDelegationManager.sol": {"keccak256": "0x84e8357193d083bd63c3af2ae3ee1583d1ba734a09d24a897fa6c7785a28b700", "urls": ["bzz-raw://e4ffb431c1e5283743009f98027dcdb06a42b32789d7251fb21f6a223e3565aa", "dweb:/ipfs/Qme9V6VZFkTDuB3aM3XhkntjYJ6Fv3Ud7QuHgorY4iDGXL"], "license": "BUSL-1.1"}, "src/interfaces/external/eigen-layer/IRewardsCoordinator.sol": {"keccak256": "0x8802e05f49d4d1b1c7b283ec96e7808416747d0f3900f730240d3520288e03c9", "urls": ["bzz-raw://ee0009552f9dad177606b11476d169590024a902294b2355d3da5f9a9398e4eb", "dweb:/ipfs/QmUV9wM8VzVRppaeqGPQTNh7BjZszzdxafyDXY7rd9RjCQ"], "license": "BUSL-1.1"}, "src/interfaces/external/eigen-layer/ISignatureUtils.sol": {"keccak256": "0xf0a4f7b91b079e85ad7b27132a751955d09694358cb6494b70a6d4217f248f03", "urls": ["bzz-raw://0773df5dae86559e56fbced497b20cbe8def225f3f2dadf43cf7728c10cd6bc2", "dweb:/ipfs/QmS7YDqmyttHZ8sbX1YB21Z2rk7wsS9hneDTkAcSPbED6E"], "license": "BUSL-1.1"}, "src/interfaces/external/eigen-layer/IStrategy.sol": {"keccak256": "0x938f287999a7edf7f3a67f6c2aa3205268cf6e5ebc8dbc00302d2886a558b098", "urls": ["bzz-raw://c7e22f4a1ed1cf9fc2f255d98d5af1a85077cfb77bdfaa351348f4bb28bca872", "dweb:/ipfs/QmeCA5afkHVoW5FVhDmaVNUwV4tN6YzwKyHyC9dJpffRof"], "license": "BUSL-1.1"}, "src/interfaces/external/eigen-layer/IStrategyManager.sol": {"keccak256": "0xf8557b7be6db605615100f77fb6a423350afd25709c78badc077063b16db80b9", "urls": ["bzz-raw://5220c4e8c2d2d5b50f3ea0e88d8d91660be87340ffb4621f3180a7ed8fc977eb", "dweb:/ipfs/QmPdgaqa7DhzmMya6wB31K1Voky7iSBF2SBEVR6VPahEuA"], "license": "BUSL-1.1"}}, "version": 1}, "id": 79}