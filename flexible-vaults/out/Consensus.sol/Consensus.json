{"abi": [{"type": "constructor", "inputs": [{"name": "name_", "type": "string", "internalType": "string"}, {"name": "version_", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "addSigner", "inputs": [{"name": "signer", "type": "address", "internalType": "address"}, {"name": "threshold_", "type": "uint256", "internalType": "uint256"}, {"name": "signatureType", "type": "uint8", "internalType": "enum IConsensus.SignatureType"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "checkSignatures", "inputs": [{"name": "orderHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "signatures", "type": "tuple[]", "internalType": "struct IConsensus.Signature[]", "components": [{"name": "signer", "type": "address", "internalType": "address"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "removeSigner", "inputs": [{"name": "signer", "type": "address", "internalType": "address"}, {"name": "threshold_", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "requireValidSignatures", "inputs": [{"name": "orderHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "signatures", "type": "tuple[]", "internalType": "struct IConsensus.Signature[]", "components": [{"name": "signer", "type": "address", "internalType": "address"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}]}], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "threshold_", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "signerAt", "inputs": [{"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "signers", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "threshold", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "initParams", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SignerAdded", "inputs": [{"name": "signer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "signatureType", "type": "uint8", "indexed": false, "internalType": "enum IConsensus.SignatureType"}], "anonymous": false}, {"type": "event", "name": "SignerRemoved", "inputs": [{"name": "signer", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "ThresholdSet", "inputs": [{"name": "threshold", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "ECDSAInvalidSignature", "inputs": []}, {"type": "error", "name": "ECDSAInvalidSignatureLength", "inputs": [{"name": "length", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ECDSAInvalidSignatureS", "inputs": [{"name": "s", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "InvalidSignatures", "inputs": [{"name": "orderHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "signatures", "type": "tuple[]", "internalType": "struct IConsensus.Signature[]", "components": [{"name": "signer", "type": "address", "internalType": "address"}, {"name": "signature", "type": "bytes", "internalType": "bytes"}]}]}, {"type": "error", "name": "InvalidThreshold", "inputs": [{"name": "threshold", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "SignerAlreadyExists", "inputs": [{"name": "signer", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "SignerNotFound", "inputs": [{"name": "signer", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ZeroAddress", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "154:4357:129:-:0;;;328:173;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;413:49;;;;;;;;;;;;-1:-1:-1;;;413:49:129;;;;;;446:5;453:8;413:19;:49::i;:::-;389:73;;472:22;:20;:22::i;:::-;328:173;;154:4357;;656:343:113;761:7;986:4;970:22;;969:23;941:1;908:12;922:4;928:7;856:80;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;846:91;;;;;;838:100;;:104;;;;:::i;:::-;810:146;;;;;;2258:25:192;;2246:2;2231:18;;2112:177;810:146:113;;;;;;;;;;;;;787:179;;;;;;:205;780:212;;656:343;;;;;:::o;7709:422:3:-;7824:30;7857:26;:24;:26::i;:::-;7898:15;;;;-1:-1:-1;7898:15:3;;;;;7894:76;;;7936:23;;-1:-1:-1;;;7936:23:3;;;;;;;;;;;7894:76;7983:14;;-1:-1:-1;;;;;7983:14:3;;;:34;7979:146;;8033:33;;-1:-1:-1;;;;;;8033:33:3;-1:-1:-1;;;;;8033:33:3;;;;;8085:29;;2438:50:192;;;8085:29:3;;2426:2:192;2411:18;8085:29:3;;;;;;;7979:146;7758:373;7709:422::o;9071:205::-;9129:30;;3147:66;9186:27;9171:42;9071:205;-1:-1:-1;;9071:205:3:o;14:127:192:-;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:983;235:6;243;296:2;284:9;275:7;271:23;267:32;264:52;;;312:1;309;302:12;264:52;339:16;;-1:-1:-1;;;;;404:14:192;;;401:34;;;431:1;428;421:12;401:34;469:6;458:9;454:22;444:32;;514:7;507:4;503:2;499:13;495:27;485:55;;536:1;533;526:12;485:55;565:2;559:9;587:2;583;580:10;577:36;;;593:18;;:::i;:::-;668:2;662:9;636:2;722:13;;-1:-1:-1;;718:22:192;;;742:2;714:31;710:40;698:53;;;766:18;;;786:22;;;763:46;760:72;;;812:18;;:::i;:::-;852:10;848:2;841:22;887:2;879:6;872:18;929:7;922:4;917:2;913;909:11;905:22;902:35;899:55;;;950:1;947;940:12;899:55;1003:2;996:4;992:2;988:13;981:4;973:6;969:17;963:43;1050:1;1043:4;1038:2;1030:6;1026:15;1022:26;1015:37;1071:6;1061:16;;;;;;;1117:4;1106:9;1102:20;1096:27;1086:37;;146:983;;;;;:::o;1134:212::-;1176:3;1214:5;1208:12;1258:6;1251:4;1244:5;1240:16;1235:3;1229:36;1320:1;1284:16;;1309:13;;;-1:-1:-1;1284:16:192;;1134:212;-1:-1:-1;1134:212:192:o;1351:526::-;1689:33;1684:3;1677:46;1659:3;1745:66;1771:39;1806:2;1801:3;1797:12;1789:6;1771:39;:::i;:::-;1763:6;1745:66;:::i;:::-;1820:21;;;-1:-1:-1;;1868:2:192;1857:14;;1351:526;-1:-1:-1;;1351:526:192:o;1882:225::-;1949:9;;;1970:11;;;1967:134;;;2023:10;2018:3;2014:20;2011:1;2004:31;2058:4;2055:1;2048:15;2086:4;2083:1;2076:15;2294:200;154:4357:129;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "154:4357:129:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3642:307;;;;;;:::i;:::-;;:::i;:::-;;2159:106;4407:21;2229:29;;;2159:106;;;616:25:192;;;604:2;589:18;2159:106:129;;;;;;;;2822:154;;;;;;:::i;:::-;;:::i;2302:111::-;;;:::i;3155:101:1:-;;;:::i;2622:133:129:-;;;;;;:::i;:::-;;:::i;:::-;;;1665:14:192;;1658:22;1640:41;;1628:2;1613:18;2622:133:129;1500:187:192;2441:144:1;1313:22;2570:8;2441:144;;-1:-1:-1;;;;;2570:8:1;;;1838:51:192;;1826:2;1811:18;2441:144:1;1692:203:192;3013:103:129;;;;;;:::i;:::-;;:::i;3153:452::-;;;;;;:::i;:::-;;:::i;561:1293::-;;;;;;:::i;:::-;;:::i;3405:215:1:-;;;;;;:::i;:::-;;:::i;2450:135:129:-;;;;;;:::i;:::-;;:::i;:::-;;;;-1:-1:-1;;;;;3484:32:192;;;3466:51;;3548:2;3533:18;;3526:34;;;;3439:18;2450:135:129;3292:274:192;1891:231:129;;;;;;:::i;:::-;;:::i;3642:307::-;2334:13:1;:11;:13::i;:::-;4407:21:129;3792:24:::1;4407:21:::0;3809:6;3792:16:::1;:24::i;:::-;3787:85;;3839:22;::::0;-1:-1:-1;;;3839:22:129;;-1:-1:-1;;;;;1856:32:192;;3839:22:129::1;::::0;::::1;1838:51:192::0;1811:18;;3839:22:129::1;;;;;;;;3787:85;3881:25;3895:10;3881:13;:25::i;:::-;3921:21;::::0;-1:-1:-1;;;;;3921:21:129;::::1;::::0;::::1;::::0;;;::::1;3719:230;3642:307:::0;;:::o;2822:154::-;4158:30:3;4191:26;:24;:26::i;:::-;4302:15;;4158:59;;-1:-1:-1;4302:15:3;-1:-1:-1;;;4302:15:3;;;4301:16;;4348:14;;4279:19;4724:16;;:34;;;;;4744:14;4724:34;4704:54;;4768:17;4788:11;:16;;4803:1;4788:16;:50;;;;-1:-1:-1;4816:4:3;4808:25;:30;4788:50;4768:70;;4854:12;4853:13;:30;;;;;4871:12;4870:13;4853:30;4849:91;;;4906:23;;-1:-1:-1;;;4906:23:3;;;;;;;;;;;4849:91;4949:18;;-1:-1:-1;;4949:18:3;4966:1;4949:18;;;4977:67;;;;5011:22;;-1:-1:-1;;;;5011:22:3;-1:-1:-1;;;5011:22:3;;;4977:67;2894:43:129::1;2909:27;::::0;;::::1;2920:4:::0;2909:27:::1;:::i;:::-;2894:14;:43::i;:::-;2952:17;2964:4;;2952:17;;;;;;;:::i;:::-;;;;;;;;5068:14:3::0;5064:101;;;5098:23;;-1:-1:-1;;;;5098:23:3;;;5140:14;;-1:-1:-1;4504:50:192;;5140:14:3;;4492:2:192;4477:18;5140:14:3;;;;;;;5064:101;4092:1079;;;;;2822:154:129;;:::o;2302:111::-;2344:7;2370:36;4407:21;2370:34;:36::i;:::-;2363:43;;2302:111;:::o;3155:101:1:-;2334:13;:11;:13::i;:::-;3219:30:::1;3246:1;3219:18;:30::i;:::-;3155:101::o:0;2622:133:129:-;2680:4;2703:45;4407:21;2740:7;2703:36;:45::i;:::-;2696:52;2622:133;-1:-1:-1;;2622:133:129:o;3013:103::-;2334:13:1;:11;:13::i;:::-;3084:25:129::1;3098:10;3084:13;:25::i;:::-;3013:103:::0;:::o;3153:452::-;2334:13:1;:11;:13::i;:::-;4407:21:129;-1:-1:-1;;;;;3328:20:129;::::1;3324:71;;3371:13;;-1:-1:-1::0;;;3371:13:129::1;;;;;;;;;;;3324:71;3409:45;3423:6;3439:13;3431:22;;;;;;;;:::i;:::-;3409:1:::0;;:45;:13:::1;:45::i;:::-;3404:111;;3477:27;::::0;-1:-1:-1;;;3477:27:129;;-1:-1:-1;;;;;1856:32:192;;3477:27:129::1;::::0;::::1;1838:51:192::0;1811:18;;3477:27:129::1;1692:203:192::0;3404:111:129::1;3524:25;3538:10;3524:13;:25::i;:::-;3576:6;-1:-1:-1::0;;;;;3564:34:129::1;;3584:13;3564:34;;;;;;:::i;:::-;;;;;;;;3256:349;3153:452:::0;;;:::o;561:1293::-;659:4;4407:21;737:22;;;:57;;-1:-1:-1;783:11:129;;;;763:31;;737:57;733:100;;;817:5;810:12;;;;;733:100;847:9;842:985;862:21;;;842:985;;;904:14;921:10;;932:1;921:13;;;;;;;:::i;:::-;;;;;;;;;;;;:::i;:::-;:20;;;;;;;:::i;:::-;904:37;-1:-1:-1;956:11:129;;999:24;:1;904:37;999:16;:24::i;:::-;955:68;;;;1042:6;1037:58;;1075:5;1068:12;;;;;;;;;1037:58;1108:27;1152:18;1138:33;;;;;;;;:::i;:::-;1108:63;-1:-1:-1;1206:20:129;1189:13;:37;;;;;;;;:::i;:::-;;1185:632;;1246:23;1272:49;1286:9;1297:10;;1308:1;1297:13;;;;;;;:::i;:::-;;;;;;;;;;;;:::i;:::-;:23;;;;;;;:::i;:::-;1272:49;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1272:13:129;;-1:-1:-1;;;1272:49:129:i;:::-;1246:75;-1:-1:-1;;;;;;1343:29:129;;;;:58;;;1395:6;-1:-1:-1;;;;;1376:25:129;:15;-1:-1:-1;;;;;1376:25:129;;;1343:58;1339:117;;;1432:5;1425:12;;;;;;;;;;;1339:117;1228:242;1185:632;;;1497:21;1480:13;:38;;;;;;;;:::i;:::-;;1476:341;;1538:17;1567:6;-1:-1:-1;;;;;1558:33:129;;1592:9;1603:10;;1614:1;1603:13;;;;;;;:::i;:::-;;;;;;;;;;;;:::i;:::-;:23;;;;;;;:::i;:::-;1558:69;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1538:89;-1:-1:-1;;;;;;;1649:48:129;;-1:-1:-1;;;1649:48:129;1645:107;;1728:5;1721:12;;;;;;;;;;;1476:341;1797:5;1790:12;;;;;;;;;;1476:341;-1:-1:-1;;885:3:129;;;;;-1:-1:-1;842:985:129;;-1:-1:-1;842:985:129;;;1843:4;1836:11;;;561:1293;;;;;;:::o;3405:215:1:-;2334:13;:11;:13::i;:::-;-1:-1:-1;;;;;3489:22:1;::::1;3485:91;;3534:31;::::0;-1:-1:-1;;;3534:31:1;;3562:1:::1;3534:31;::::0;::::1;1838:51:192::0;1811:18;;3534:31:1::1;1692:203:192::0;3485:91:1::1;3585:28;3604:8;3585:18;:28::i;2450:135:129:-:0;2506:7;;2541:37;4407:21;2572:5;2541:30;:37::i;:::-;2534:44;;;;2450:135;;;:::o;1891:231::-;2004:38;2020:9;2031:10;;2004:15;:38::i;:::-;1999:117;;2083:9;2094:10;;2065:40;;-1:-1:-1;;;2065:40:129;;;;;;;;;;:::i;1999:117::-;1891:231;;;:::o;2658:162:1:-;966:10:5;2717:7:1;1313:22;2570:8;-1:-1:-1;;;;;2570:8:1;;2441:144;2717:7;-1:-1:-1;;;;;2717:23:1;;2713:101;;2763:40;;-1:-1:-1;;;2763:40:1;;966:10:5;2763:40:1;;;1838:51:192;1811:18;;2763:40:1;1692:203:192;18246:157:71;18323:4;18346:50;18353:3;-1:-1:-1;;;;;18373:21:71;;18346:6;:50::i;3982:314:129:-;4407:21;4105:15;;;:50;;-1:-1:-1;4137:18:129;:1;:16;:18::i;:::-;4124:10;:31;4105:50;4101:116;;;4178:28;;-1:-1:-1;;;4178:28:129;;;;;616:25:192;;;589:18;;4178:28:129;470:177:192;4101:116:129;4226:11;;;:24;;;4265;;4240:10;;4265:24;;;;;4033:263;3982:314;:::o;9071:205:3:-;9129:30;;3147:66;9186:27;8819:122;1847:127:1;6929:20:3;:18;:20::i;:::-;1929:38:1::1;1954:12;1929:24;:38::i;19134:120:71:-:0;19203:7;19229:18;19236:3;19229:6;:18::i;3774:248:1:-;1313:22;3923:8;;-1:-1:-1;;;;;;3941:19:1;;-1:-1:-1;;;;;3941:19:1;;;;;;;;3975:40;;3923:8;;;;;3975:40;;3847:24;;3975:40;3837:185;;3774:248;:::o;18885:166:71:-;18969:4;18992:52;19001:3;-1:-1:-1;;;;;19021:21:71;;18992:8;:52::i;17905:182::-;17994:4;18017:63;18021:3;-1:-1:-1;;;;;18041:21:71;;18073:5;18017:3;:63::i;:::-;18010:70;17905:182;-1:-1:-1;;;;17905:182:71:o;19974:247::-;20056:11;;;;20124:50;20131:3;-1:-1:-1;;;;;20151:21:71;;20124:6;:50::i;:::-;20094:80;;-1:-1:-1;20094:80:71;-1:-1:-1;;;19974:247:71;;;;;;:::o;3714:255:62:-;3792:7;3812:17;3831:18;3851:16;3871:27;3882:4;3888:9;3871:10;:27::i;:::-;3811:87;;;;;;3908:28;3920:5;3927:8;3908:11;:28::i;:::-;-1:-1:-1;3953:9:62;;3714:255;-1:-1:-1;;;;3714:255:62:o;19591:242:71:-;19671:11;;;;19740:21;19743:3;19755:5;19740:2;:21::i;3298:164::-;3378:4;3401:16;;;:11;;;:16;;;;;3394:23;;;3434:21;3401:3;3413;3434:16;:21::i;7082:141:3:-;7149:17;:15;:17::i;:::-;7144:73;;7189:17;;-1:-1:-1;;;7189:17:3;;;;;;;;;;;1980:235:1;6929:20:3;:18;:20::i;4315:123:71:-;4387:7;4413:18;:3;:16;:18::i;4085:140::-;4172:4;4195:23;:3;4214;4195:18;:23::i;2956:174::-;3048:4;3064:16;;;:11;;;:16;;;;;:24;;;3105:18;3064:3;3076;3105:13;:18::i;5139:305::-;5224:11;5276:16;;;:11;;;:16;;;;;;5224:11;;5276:16;5302:136;;5347:18;5356:3;5361;5347:8;:18::i;:::-;5339:39;-1:-1:-1;5375:1:71;;-1:-1:-1;5339:39:71;;-1:-1:-1;5339:39:71;5302:136;5417:4;;-1:-1:-1;5423:3:71;-1:-1:-1;5409:18:71;;2129:778:62;2232:17;2251:16;2269:14;2299:9;:16;2319:2;2299:22;2295:606;;2604:4;2589:20;;2583:27;2653:4;2638:20;;2632:27;2710:4;2695:20;;2689:27;2337:9;2681:36;2751:25;2762:4;2681:36;2583:27;2632;2751:10;:25::i;:::-;2744:32;;;;;;;;;;;2295:606;-1:-1:-1;;2872:16:62;;2823:1;;-1:-1:-1;2827:35:62;;2295:606;2129:778;;;;;:::o;7280:532::-;7375:20;7366:5;:29;;;;;;;;:::i;:::-;;7362:444;;7280:532;;:::o;7362:444::-;7471:29;7462:5;:38;;;;;;;;:::i;:::-;;7458:348;;7523:23;;-1:-1:-1;;;7523:23:62;;;;;;;;;;;7458:348;7576:35;7567:5;:44;;;;;;;;:::i;:::-;;7563:243;;7634:46;;-1:-1:-1;;;7634:46:62;;;;;616:25:192;;;589:18;;7634:46:62;470:177:192;7563:243:62;7710:30;7701:5;:39;;;;;;;;:::i;:::-;;7697:109;;7763:32;;-1:-1:-1;;;7763:32:62;;;;;616:25:192;;;589:18;;7763:32:62;470:177:192;7697:109:62;7280:532;;:::o;4791:207:71:-;4874:11;;;4928:19;:3;4941:5;4928:12;:19::i;:::-;4972:18;;;;:11;;;;;:18;;;;;;;;;4791:207;-1:-1:-1;;;;4791:207:71:o;6867:129:72:-;6940:4;6963:26;6971:3;6983:5;6963:7;:26::i;8485:120:3:-;8535:4;8558:26;:24;:26::i;:::-;:40;-1:-1:-1;;;8558:40:3;;;;;;-1:-1:-1;8485:120:3:o;7693:115:72:-;7756:7;7782:19;7790:3;5202:18;;5120:107;7474:138;7554:4;5006:21;;;:14;;;:21;;;;;;:26;;7577:28;4910:129;6576:123;6646:4;6669:23;6674:3;6686:5;6669:4;:23::i;5203:1551:62:-;5329:17;;;6283:66;6270:79;;6266:164;;;-1:-1:-1;6381:1:62;;-1:-1:-1;6385:30:62;;-1:-1:-1;6417:1:62;6365:54;;6266:164;6541:24;;;6524:14;6541:24;;;;;;;;;8869:25:192;;;8942:4;8930:17;;8910:18;;;8903:45;;;;8964:18;;;8957:34;;;9007:18;;;9000:34;;;6541:24:62;;8841:19:192;;6541:24:62;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;6541:24:62;;-1:-1:-1;;6541:24:62;;;-1:-1:-1;;;;;;;6579:20:62;;6575:113;;-1:-1:-1;6631:1:62;;-1:-1:-1;6635:29:62;;-1:-1:-1;6631:1:62;;-1:-1:-1;6615:62:62;;6575:113;6706:6;-1:-1:-1;6714:20:62;;-1:-1:-1;6714:20:62;;-1:-1:-1;5203:1551:62;;;;;;;;;:::o;8150:129:72:-;8224:7;8250:22;8254:3;8266:5;8250:3;:22::i;2910:1368::-;2976:4;3105:21;;;:14;;;:21;;;;;;3141:13;;3137:1135;;3508:18;3529:12;3540:1;3529:8;:12;:::i;:::-;3575:18;;3508:33;;-1:-1:-1;3555:17:72;;3575:22;;3596:1;;3575:22;:::i;:::-;3555:42;;3630:9;3616:10;:23;3612:378;;3659:17;3679:3;:11;;3691:9;3679:22;;;;;;;;:::i;:::-;;;;;;;;;3659:42;;3826:9;3800:3;:11;;3812:10;3800:23;;;;;;;;:::i;:::-;;;;;;;;;;;;:35;;;;3939:25;;;:14;;;:25;;;;;:36;;;3612:378;4068:17;;:3;;:17;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;4171:3;:14;;:21;4186:5;4171:21;;;;;;;;;;;4164:28;;;4214:4;4207:11;;;;;;;3137:1135;4256:5;4249:12;;;;;2336:406;2399:4;5006:21;;;:14;;;:21;;;;;;2415:321;;-1:-1:-1;2457:23:72;;;;;;;;:11;:23;;;;;;;;;;;;;2639:18;;2615:21;;;:14;;;:21;;;;;;:42;;;;2671:11;;2415:321;-1:-1:-1;2720:5:72;2713:12;;5569:118;5636:7;5662:3;:11;;5674:5;5662:18;;;;;;;;:::i;:::-;;;;;;;;;5655:25;;5569:118;;;;:::o;14:131:192:-;-1:-1:-1;;;;;89:31:192;;79:42;;69:70;;135:1;132;125:12;150:315;218:6;226;279:2;267:9;258:7;254:23;250:32;247:52;;;295:1;292;285:12;247:52;334:9;321:23;353:31;378:5;353:31;:::i;:::-;403:5;455:2;440:18;;;;427:32;;-1:-1:-1;;;150:315:192:o;652:591::-;722:6;730;783:2;771:9;762:7;758:23;754:32;751:52;;;799:1;796;789:12;751:52;839:9;826:23;868:18;909:2;901:6;898:14;895:34;;;925:1;922;915:12;895:34;963:6;952:9;948:22;938:32;;1008:7;1001:4;997:2;993:13;989:27;979:55;;1030:1;1027;1020:12;979:55;1070:2;1057:16;1096:2;1088:6;1085:14;1082:34;;;1112:1;1109;1102:12;1082:34;1157:7;1152:2;1143:6;1139:2;1135:15;1131:24;1128:37;1125:57;;;1178:1;1175;1168:12;1125:57;1209:2;1201:11;;;;;1231:6;;-1:-1:-1;652:591:192;;-1:-1:-1;;;;652:591:192:o;1248:247::-;1307:6;1360:2;1348:9;1339:7;1335:23;1331:32;1328:52;;;1376:1;1373;1366:12;1328:52;1415:9;1402:23;1434:31;1459:5;1434:31;:::i;1900:180::-;1959:6;2012:2;2000:9;1991:7;1987:23;1983:32;1980:52;;;2028:1;2025;2018:12;1980:52;-1:-1:-1;2051:23:192;;1900:180;-1:-1:-1;1900:180:192:o;2085:484::-;2181:6;2189;2197;2250:2;2238:9;2229:7;2225:23;2221:32;2218:52;;;2266:1;2263;2256:12;2218:52;2305:9;2292:23;2324:31;2349:5;2324:31;:::i;:::-;2374:5;-1:-1:-1;2426:2:192;2411:18;;2398:32;;-1:-1:-1;2482:2:192;2467:18;;2454:32;2517:1;2505:14;;2495:42;;2533:1;2530;2523:12;2495:42;2556:7;2546:17;;;2085:484;;;;;:::o;2574:713::-;2699:6;2707;2715;2768:2;2756:9;2747:7;2743:23;2739:32;2736:52;;;2784:1;2781;2774:12;2736:52;2820:9;2807:23;2797:33;;2881:2;2870:9;2866:18;2853:32;2904:18;2945:2;2937:6;2934:14;2931:34;;;2961:1;2958;2951:12;2931:34;2999:6;2988:9;2984:22;2974:32;;3044:7;3037:4;3033:2;3029:13;3025:27;3015:55;;3066:1;3063;3056:12;3015:55;3106:2;3093:16;3132:2;3124:6;3121:14;3118:34;;;3148:1;3145;3138:12;3118:34;3201:7;3196:2;3186:6;3183:1;3179:14;3175:2;3171:23;3167:32;3164:45;3161:65;;;3222:1;3219;3212:12;3161:65;3253:2;3249;3245:11;3235:21;;3275:6;3265:16;;;;;2574:713;;;;;:::o;3831:266::-;3919:6;3914:3;3907:19;3971:6;3964:5;3957:4;3952:3;3948:14;3935:43;-1:-1:-1;4023:1:192;3998:16;;;4016:4;3994:27;;;3987:38;;;;4079:2;4058:15;;;-1:-1:-1;;4054:29:192;4045:39;;;4041:50;;3831:266::o;4102:244::-;4259:2;4248:9;4241:21;4222:4;4279:61;4336:2;4325:9;4321:18;4313:6;4305;4279:61;:::i;4565:127::-;4626:10;4621:3;4617:20;4614:1;4607:31;4657:4;4654:1;4647:15;4681:4;4678:1;4671:15;4697:347;4848:2;4833:18;;4881:1;4870:13;;4860:144;;4926:10;4921:3;4917:20;4914:1;4907:31;4961:4;4958:1;4951:15;4989:4;4986:1;4979:15;4860:144;5013:25;;;4697:347;:::o;5049:127::-;5110:10;5105:3;5101:20;5098:1;5091:31;5141:4;5138:1;5131:15;5165:4;5162:1;5155:15;5181:327;5277:4;5335:11;5322:25;5429:2;5425:7;5414:8;5398:14;5394:29;5390:43;5370:18;5366:68;5356:96;;5448:1;5445;5438:12;5356:96;5469:33;;;;;5181:327;-1:-1:-1;;5181:327:192:o;5513:521::-;5590:4;5596:6;5656:11;5643:25;5750:2;5746:7;5735:8;5719:14;5715:29;5711:43;5691:18;5687:68;5677:96;;5769:1;5766;5759:12;5677:96;5796:33;;5848:20;;;-1:-1:-1;5891:18:192;5880:30;;5877:50;;;5923:1;5920;5913:12;5877:50;5956:4;5944:17;;-1:-1:-1;5987:14:192;5983:27;;;5973:38;;5970:58;;;6024:1;6021;6014:12;6039:315;6224:6;6213:9;6206:25;6267:2;6262;6251:9;6247:18;6240:30;6187:4;6287:61;6344:2;6333:9;6329:18;6321:6;6313;6287:61;:::i;:::-;6279:69;6039:315;-1:-1:-1;;;;;6039:315:192:o;6359:290::-;6428:6;6481:2;6469:9;6460:7;6456:23;6452:32;6449:52;;;6497:1;6494;6487:12;6449:52;6523:16;;-1:-1:-1;;;;;;6568:32:192;;6558:43;;6548:71;;6615:1;6612;6605:12;6654:1801;6892:4;6921:2;6961;6950:9;6946:18;6991:6;6980:9;6973:25;7017:2;7055;7050;7039:9;7035:18;7028:30;7078:6;7108;7100;7093:22;7146:2;7135:9;7131:18;7124:25;;7208:2;7198:6;7195:1;7191:14;7180:9;7176:30;7172:39;7158:53;;7234:6;7258:1;7268:1158;7282:6;7279:1;7276:13;7268:1158;;;7347:22;;;-1:-1:-1;;7343:36:192;7331:49;;7419:20;;7494:14;7490:27;;;-1:-1:-1;;7486:41:192;7462:66;;7452:94;;7542:1;7539;7532:12;7452:94;7572:31;;7631:19;;7663:33;7631:19;7663:33;:::i;:::-;-1:-1:-1;;;;;7724:33:192;7709:49;;7812:14;;;7799:28;7884:14;7880:26;;;-1:-1:-1;;7876:40:192;7850:67;;7840:95;;7931:1;7928;7921:12;7840:95;7963:32;8071:16;;;;8022:21;8114:18;8103:30;;8100:50;;;8146:1;8143;8136:12;8100:50;8199:6;8183:14;8179:27;8170:7;8166:41;8163:61;;;8220:1;8217;8210:12;8163:61;8261:2;8256;8248:6;8244:15;8237:27;8287:59;8342:2;8334:6;8330:15;8322:6;8313:7;8287:59;:::i;:::-;8277:69;-1:-1:-1;;;8404:12:192;;;;8369:15;;;;7304:1;7297:9;7268:1158;;;-1:-1:-1;8443:6:192;;6654:1801;-1:-1:-1;;;;;;;;;6654:1801:192:o;9045:225::-;9112:9;;;9133:11;;;9130:134;;;9186:10;9181:3;9177:20;9174:1;9167:31;9221:4;9218:1;9211:15;9249:4;9246:1;9239:15;9275:127;9336:10;9331:3;9327:20;9324:1;9317:31;9367:4;9364:1;9357:15;9391:4;9388:1;9381:15", "linkReferences": {}, "immutableReferences": {"69233": [{"start": 230, "length": 32}, {"start": 568, "length": 32}, {"start": 1043, "length": 32}, {"start": 1109, "length": 32}, {"start": 1182, "length": 32}, {"start": 1405, "length": 32}, {"start": 2123, "length": 32}, {"start": 2333, "length": 32}]}}, "methodIdentifiers": {"addSigner(address,uint256,uint8)": "9aa1a31a", "checkSignatures(bytes32,(address,bytes)[])": "b31078d3", "initialize(bytes)": "439fab91", "isSigner(address)": "7df73e27", "owner()": "8da5cb5b", "removeSigner(address,uint256)": "3bad5426", "renounceOwnership()": "715018a6", "requireValidSignatures(bytes32,(address,bytes)[])": "faa4da34", "setThreshold(uint256)": "960bfe04", "signerAt(uint256)": "fa3e6da4", "signers()": "46f0975a", "threshold()": "42cde4e8", "transferOwnership(address)": "f2fde38b"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name_\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"version_\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"ECDSAInvalidSignature\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"length\",\"type\":\"uint256\"}],\"name\":\"ECDSAInvalidSignatureLength\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"name\":\"ECDSAInvalidSignatureS\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"orderHash\",\"type\":\"bytes32\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"}],\"internalType\":\"struct IConsensus.Signature[]\",\"name\":\"signatures\",\"type\":\"tuple[]\"}],\"name\":\"InvalidSignatures\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"threshold\",\"type\":\"uint256\"}],\"name\":\"InvalidThreshold\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"}],\"name\":\"SignerAlreadyExists\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"}],\"name\":\"SignerNotFound\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZeroAddress\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"initParams\",\"type\":\"bytes\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"enum IConsensus.SignatureType\",\"name\":\"signatureType\",\"type\":\"uint8\"}],\"name\":\"SignerAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"}],\"name\":\"SignerRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"threshold\",\"type\":\"uint256\"}],\"name\":\"ThresholdSet\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"threshold_\",\"type\":\"uint256\"},{\"internalType\":\"enum IConsensus.SignatureType\",\"name\":\"signatureType\",\"type\":\"uint8\"}],\"name\":\"addSigner\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"orderHash\",\"type\":\"bytes32\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"}],\"internalType\":\"struct IConsensus.Signature[]\",\"name\":\"signatures\",\"type\":\"tuple[]\"}],\"name\":\"checkSignatures\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"isSigner\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"threshold_\",\"type\":\"uint256\"}],\"name\":\"removeSigner\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"orderHash\",\"type\":\"bytes32\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"}],\"internalType\":\"struct IConsensus.Signature[]\",\"name\":\"signatures\",\"type\":\"tuple[]\"}],\"name\":\"requireValidSignatures\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"threshold_\",\"type\":\"uint256\"}],\"name\":\"setThreshold\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"signerAt\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"signers\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"threshold\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"ECDSAInvalidSignature()\":[{\"details\":\"The signature derives the `address(0)`.\"}],\"ECDSAInvalidSignatureLength(uint256)\":[{\"details\":\"The signature has an invalid length.\"}],\"ECDSAInvalidSignatureS(bytes32)\":[{\"details\":\"The signature has an S value that is in the upper half order.\"}],\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}]},\"events\":{\"Initialized(bytes)\":{\"params\":{\"initParams\":\"The initialization parameters.\"}},\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"}},\"kind\":\"dev\",\"methods\":{\"addSigner(address,uint256,uint8)\":{\"params\":{\"signatureType\":\"Signature type used by this signer\",\"signer\":\"Signer address to add\",\"threshold_\":\"New threshold to set after adding\"}},\"checkSignatures(bytes32,(address,bytes)[])\":{\"params\":{\"orderHash\":\"The message hash that was signed\",\"signatures\":\"List of (signer, signature) entries\"}},\"initialize(bytes)\":{\"params\":{\"initParams\":\"The initialization parameters.\"}},\"isSigner(address)\":{\"params\":{\"account\":\"Address to check\"}},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"removeSigner(address,uint256)\":{\"params\":{\"signer\":\"Signer address to remove\",\"threshold_\":\"New threshold to set after removal\"}},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"requireValidSignatures(bytes32,(address,bytes)[])\":{\"params\":{\"orderHash\":\"The message hash that was signed\",\"signatures\":\"List of (signer, signature) entries\"}},\"setThreshold(uint256)\":{\"params\":{\"threshold\":\"New threshold (must be > 0 and <= signer count)\"}},\"signerAt(uint256)\":{\"params\":{\"index\":\"Index into the signer list\"}},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"errors\":{\"InvalidSignatures(bytes32,(address,bytes)[])\":[{\"notice\":\"Thrown when provided signatures are invalid or below the required threshold\"}],\"InvalidThreshold(uint256)\":[{\"notice\":\"Thrown when attempting to set an invalid threshold (zero or above signer count)\"}],\"SignerAlreadyExists(address)\":[{\"notice\":\"Thrown when trying to add a signer that already exists\"}],\"SignerNotFound(address)\":[{\"notice\":\"Thrown when trying to remove a signer that isn't in the signer set\"}],\"ZeroAddress()\":[{\"notice\":\"Thrown when a provided address is the zero address\"}]},\"events\":{\"Initialized(bytes)\":{\"notice\":\"Emitted once the entity has been initialized.\"},\"SignerAdded(address,uint8)\":{\"notice\":\"Emitted when a signer is added\"},\"SignerRemoved(address)\":{\"notice\":\"Emitted when a signer is removed\"},\"ThresholdSet(uint256)\":{\"notice\":\"Emitted when the threshold is changed\"}},\"kind\":\"user\",\"methods\":{\"addSigner(address,uint256,uint8)\":{\"notice\":\"Adds a new signer and updates the threshold\"},\"checkSignatures(bytes32,(address,bytes)[])\":{\"notice\":\"Returns true if the given signatures are valid and meet the current threshold\"},\"initialize(bytes)\":{\"notice\":\"Initializes the factory-created entity with arbitrary initialization data.\"},\"isSigner(address)\":{\"notice\":\"Checks if the given address is a registered signer\"},\"removeSigner(address,uint256)\":{\"notice\":\"Removes a signer and updates the threshold\"},\"requireValidSignatures(bytes32,(address,bytes)[])\":{\"notice\":\"Verifies the given signatures or reverts if invalid\"},\"setThreshold(uint256)\":{\"notice\":\"Updates the threshold required to approve an operation\"},\"signerAt(uint256)\":{\"notice\":\"Returns signer address and signature type at a given index\"},\"signers()\":{\"notice\":\"Returns the number of registered signers\"},\"threshold()\":{\"notice\":\"Returns the current threshold of required valid signatures\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/permissions/Consensus.sol\":\"Consensus\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol\":{\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6\",\"dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol\":{\"keccak256\":\"0x3d4cd07258d675b6dfa3d9dfd623a77b38fb9935f4a5381ff4bc969948720976\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://30abb60a4d98ad9a33de4fa5e9072342e12df3a79e9c71b5f59ce049ae8cd535\",\"dweb:/ipfs/QmT6NXJDjsf8H14nPYQsFELDB8q5ZTmG9cjdX2bEQJW7ST\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol\":{\"keccak256\":\"0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd\",\"dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol\":{\"keccak256\":\"0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd\",\"dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol\":{\"keccak256\":\"0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2\",\"dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol\":{\"keccak256\":\"0x69f54c02b7d81d505910ec198c11ed4c6a728418a868b906b4a0cf29946fda84\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8e25e4bdb7ae1f21d23bfee996e22736fc0ab44cfabedac82a757b1edc5623b9\",\"dweb:/ipfs/QmQdWQvB6JCP9ZMbzi8EvQ1PTETqkcTWrbcVurS7DKpa5n\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableMap.sol\":{\"keccak256\":\"0x5d0c69ef32eae686acc22acfb1a54df9c91356e69adec593aa69eba83ae8f6f4\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f7a9fca2cb3d802c4f8a2d0fda84ccc7f880264885801c7df08373501dd22282\",\"dweb:/ipfs/QmdtM2Loz9NzNTuQ3RXNQCPBjoPUJw35Ukpt1MSM9XH3cD\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol\":{\"keccak256\":\"0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231\",\"dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y\"]},\"src/interfaces/factories/IFactoryEntity.sol\":{\"keccak256\":\"0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812\",\"dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx\"]},\"src/interfaces/permissions/IConsensus.sol\":{\"keccak256\":\"0x4a454dfa9c1a3852c45341d9eb076867f5b5c043cbb5b79f9f8c3d7c00fcc9d6\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://f4993f2afd615e45e9836e581b42518363be7c1a2dd7f842473b18cb7339a3c2\",\"dweb:/ipfs/QmXZSRP7k8dP49gD1ZQsgAh3MRRLUWcRdhtHi1JrkCz4Ka\"]},\"src/libraries/SlotLibrary.sol\":{\"keccak256\":\"0x35dcf16feec83c772add30250b0987ce29ccfbf308d04159701ebf9243444eeb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://97658d0c93ecc8174169e643d5a24c515b55905a2fcaaa6d6ec0188a3dd71a10\",\"dweb:/ipfs/QmS1z7ac8RbMvz15gVQ9YA1cCT5bs6KMHCiDhgcG8Pq8oK\"]},\"src/permissions/Consensus.sol\":{\"keccak256\":\"0x956321b52c91c7a2fbf492a5f0b9ffca888cf9f650cf316d57d8cf8206d80a1a\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cbd2728243caaf7b814a1176f0cbe51b2d75739bfacb5023894015b0ace60550\",\"dweb:/ipfs/QmPg826Asz7MK2psrDM9guXT71m3dTe6iDTszNSmPeS2Xe\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "uint256", "name": "version_", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "ECDSAInvalidSignature"}, {"inputs": [{"internalType": "uint256", "name": "length", "type": "uint256"}], "type": "error", "name": "ECDSAInvalidSignatureLength"}, {"inputs": [{"internalType": "bytes32", "name": "s", "type": "bytes32"}], "type": "error", "name": "ECDSAInvalidSignatureS"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [{"internalType": "bytes32", "name": "orderHash", "type": "bytes32"}, {"internalType": "struct IConsensus.Signature[]", "name": "signatures", "type": "tuple[]", "components": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}]}], "type": "error", "name": "InvalidSignatures"}, {"inputs": [{"internalType": "uint256", "name": "threshold", "type": "uint256"}], "type": "error", "name": "InvalidThreshold"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}], "type": "error", "name": "SignerAlreadyExists"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}], "type": "error", "name": "SignerNotFound"}, {"inputs": [], "type": "error", "name": "ZeroAddress"}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "initParams", "type": "bytes", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address", "indexed": true}, {"internalType": "enum IConsensus.SignatureType", "name": "signatureType", "type": "uint8", "indexed": false}], "type": "event", "name": "SignerAdded", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address", "indexed": true}], "type": "event", "name": "SignerRemoved", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "threshold", "type": "uint256", "indexed": true}], "type": "event", "name": "ThresholdSet", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "uint256", "name": "threshold_", "type": "uint256"}, {"internalType": "enum IConsensus.SignatureType", "name": "signatureType", "type": "uint8"}], "stateMutability": "nonpayable", "type": "function", "name": "addSigner"}, {"inputs": [{"internalType": "bytes32", "name": "orderHash", "type": "bytes32"}, {"internalType": "struct IConsensus.Signature[]", "name": "signatures", "type": "tuple[]", "components": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}]}], "stateMutability": "view", "type": "function", "name": "checkSignatures", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "uint256", "name": "threshold_", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "removeSigner"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [{"internalType": "bytes32", "name": "orderHash", "type": "bytes32"}, {"internalType": "struct IConsensus.Signature[]", "name": "signatures", "type": "tuple[]", "components": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}]}], "stateMutability": "view", "type": "function", "name": "requireValidSignatures"}, {"inputs": [{"internalType": "uint256", "name": "threshold_", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "signerAt", "outputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "signers", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "threshold", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}], "devdoc": {"kind": "dev", "methods": {"addSigner(address,uint256,uint8)": {"params": {"signatureType": "Signature type used by this signer", "signer": "Signer address to add", "threshold_": "New threshold to set after adding"}}, "checkSignatures(bytes32,(address,bytes)[])": {"params": {"orderHash": "The message hash that was signed", "signatures": "List of (signer, signature) entries"}}, "initialize(bytes)": {"params": {"initParams": "The initialization parameters."}}, "isSigner(address)": {"params": {"account": "Address to check"}}, "owner()": {"details": "Returns the address of the current owner."}, "removeSigner(address,uint256)": {"params": {"signer": "Signer address to remove", "threshold_": "New threshold to set after removal"}}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "requireValidSignatures(bytes32,(address,bytes)[])": {"params": {"orderHash": "The message hash that was signed", "signatures": "List of (signer, signature) entries"}}, "setThreshold(uint256)": {"params": {"threshold": "New threshold (must be > 0 and <= signer count)"}}, "signerAt(uint256)": {"params": {"index": "Index into the signer list"}}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"addSigner(address,uint256,uint8)": {"notice": "Adds a new signer and updates the threshold"}, "checkSignatures(bytes32,(address,bytes)[])": {"notice": "Returns true if the given signatures are valid and meet the current threshold"}, "initialize(bytes)": {"notice": "Initializes the factory-created entity with arbitrary initialization data."}, "isSigner(address)": {"notice": "Checks if the given address is a registered signer"}, "removeSigner(address,uint256)": {"notice": "Removes a signer and updates the threshold"}, "requireValidSignatures(bytes32,(address,bytes)[])": {"notice": "Verifies the given signatures or reverts if invalid"}, "setThreshold(uint256)": {"notice": "Updates the threshold required to approve an operation"}, "signerAt(uint256)": {"notice": "Returns signer address and signature type at a given index"}, "signers()": {"notice": "Returns the number of registered signers"}, "threshold()": {"notice": "Returns the current threshold of required valid signatures"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/permissions/Consensus.sol": "Consensus"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"keccak256": "0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a", "urls": ["bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6", "dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05", "urls": ["bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08", "dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1271.sol": {"keccak256": "0x3d4cd07258d675b6dfa3d9dfd623a77b38fb9935f4a5381ff4bc969948720976", "urls": ["bzz-raw://30abb60a4d98ad9a33de4fa5e9072342e12df3a79e9c71b5f59ce049ae8cd535", "dweb:/ipfs/QmT6NXJDjsf8H14nPYQsFELDB8q5ZTmG9cjdX2bEQJW7ST"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol": {"keccak256": "0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6", "urls": ["bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd", "dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol": {"keccak256": "0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58", "urls": ["bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd", "dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol": {"keccak256": "0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26", "urls": ["bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2", "dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol": {"keccak256": "0x69f54c02b7d81d505910ec198c11ed4c6a728418a868b906b4a0cf29946fda84", "urls": ["bzz-raw://8e25e4bdb7ae1f21d23bfee996e22736fc0ab44cfabedac82a757b1edc5623b9", "dweb:/ipfs/QmQdWQvB6JCP9ZMbzi8EvQ1PTETqkcTWrbcVurS7DKpa5n"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableMap.sol": {"keccak256": "0x5d0c69ef32eae686acc22acfb1a54df9c91356e69adec593aa69eba83ae8f6f4", "urls": ["bzz-raw://f7a9fca2cb3d802c4f8a2d0fda84ccc7f880264885801c7df08373501dd22282", "dweb:/ipfs/QmdtM2Loz9NzNTuQ3RXNQCPBjoPUJw35Ukpt1MSM9XH3cD"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol": {"keccak256": "0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06", "urls": ["bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231", "dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y"], "license": "MIT"}, "src/interfaces/factories/IFactoryEntity.sol": {"keccak256": "0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5", "urls": ["bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812", "dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx"], "license": "BUSL-1.1"}, "src/interfaces/permissions/IConsensus.sol": {"keccak256": "0x4a454dfa9c1a3852c45341d9eb076867f5b5c043cbb5b79f9f8c3d7c00fcc9d6", "urls": ["bzz-raw://f4993f2afd615e45e9836e581b42518363be7c1a2dd7f842473b18cb7339a3c2", "dweb:/ipfs/QmXZSRP7k8dP49gD1ZQsgAh3MRRLUWcRdhtHi1JrkCz4Ka"], "license": "BUSL-1.1"}, "src/libraries/SlotLibrary.sol": {"keccak256": "0x35dcf16feec83c772add30250b0987ce29ccfbf308d04159701ebf9243444eeb", "urls": ["bzz-raw://97658d0c93ecc8174169e643d5a24c515b55905a2fcaaa6d6ec0188a3dd71a10", "dweb:/ipfs/QmS1z7ac8RbMvz15gVQ9YA1cCT5bs6KMHCiDhgcG8Pq8oK"], "license": "BUSL-1.1"}, "src/permissions/Consensus.sol": {"keccak256": "0x956321b52c91c7a2fbf492a5f0b9ffca888cf9f650cf316d57d8cf8206d80a1a", "urls": ["bzz-raw://cbd2728243caaf7b814a1176f0cbe51b2d75739bfacb5023894015b0ace60550", "dweb:/ipfs/QmPg826Asz7MK2psrDM9guXT71m3dTe6iDTszNSmPeS2Xe"], "license": "BUSL-1.1"}}, "version": 1}, "id": 129}