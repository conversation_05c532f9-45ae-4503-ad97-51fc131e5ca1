{"abi": [{"type": "constructor", "inputs": [{"name": "name_", "type": "string", "internalType": "string"}, {"name": "version_", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "DEFAULT_ADMIN_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "getRoleAdmin", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "getRoleMember", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getRoleMemberCount", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getRoleMembers", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getStorageAt", "inputs": [{"name": "slot", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct StorageSlot.Bytes32Slot", "components": [{"name": "value", "type": "bytes32", "internalType": "bytes32"}]}], "stateMutability": "pure"}, {"type": "function", "name": "grantRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "hasRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "hasSupportedRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "initParams", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "onERC721Received", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bytes4", "internalType": "bytes4"}], "stateMutability": "pure"}, {"type": "function", "name": "renounceRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "callerConfirmation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "revokeRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportedRoleAt", "inputs": [{"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "supportedRoles", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "test", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "RoleAdded", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "RoleAdminChanged", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "previousAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "newAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "RoleGranted", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RoleRemoved", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "RoleRevoked", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AccessControlBadConfirmation", "inputs": []}, {"type": "error", "name": "AccessControlUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "neededRole", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "Forbidden", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "ZeroAddress", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "99:334:153:-:0;;;141:80;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;202:5;209:8;202:5;209:8;233:22:121;:20;:22::i;:::-;432:49:130;;;;;;;;;;;;-1:-1:-1;;;432:49:130;;;;;;465:5;472:8;432:19;:49::i;:::-;408:73;;491:22;:20;:22::i;:::-;347:173;;288:80:120;;141::153;;99:334;;7709:422:3;7824:30;7857:26;:24;:26::i;:::-;7898:15;;;;-1:-1:-1;7898:15:3;;;;;7894:76;;;7936:23;;-1:-1:-1;;;7936:23:3;;;;;;;;;;;7894:76;7983:14;;-1:-1:-1;;;;;7983:14:3;;;:34;7979:146;;8033:33;;-1:-1:-1;;;;;;8033:33:3;-1:-1:-1;;;;;8033:33:3;;;;;8085:29;;1278:50:192;;;8085:29:3;;1266:2:192;1251:18;8085:29:3;;;;;;;7979:146;7758:373;7709:422::o;656:343:113:-;761:7;986:4;970:22;;969:23;941:1;908:12;922:4;928:7;856:80;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;846:91;;;;;;838:100;;:104;;;;:::i;:::-;810:146;;;;;;2463:25:192;;2451:2;2436:18;;2317:177;810:146:113;;;;;;;;;;;;;787:179;;;;;;:205;780:212;;656:343;;;;;:::o;9071:205:3:-;9129:30;;3147:66;9186:27;9171:42;9071:205;-1:-1:-1;;9071:205:3:o;14:127:192:-;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:983;235:6;243;296:2;284:9;275:7;271:23;267:32;264:52;;;312:1;309;302:12;264:52;339:16;;-1:-1:-1;;;;;404:14:192;;;401:34;;;431:1;428;421:12;401:34;469:6;458:9;454:22;444:32;;514:7;507:4;503:2;499:13;495:27;485:55;;536:1;533;526:12;485:55;565:2;559:9;587:2;583;580:10;577:36;;;593:18;;:::i;:::-;668:2;662:9;636:2;722:13;;-1:-1:-1;;718:22:192;;;742:2;714:31;710:40;698:53;;;766:18;;;786:22;;;763:46;760:72;;;812:18;;:::i;:::-;852:10;848:2;841:22;887:2;879:6;872:18;929:7;922:4;917:2;913;909:11;905:22;902:35;899:55;;;950:1;947;940:12;899:55;1003:2;996:4;992:2;988:13;981:4;973:6;969:17;963:43;1050:1;1043:4;1038:2;1030:6;1026:15;1022:26;1015:37;1071:6;1061:16;;;;;;;1117:4;1106:9;1102:20;1096:27;1086:37;;146:983;;;;;:::o;1339:212::-;1381:3;1419:5;1413:12;1463:6;1456:4;1449:5;1445:16;1440:3;1434:36;1525:1;1489:16;;1514:13;;;-1:-1:-1;1489:16:192;;1339:212;-1:-1:-1;1339:212:192:o;1556:526::-;1894:33;1889:3;1882:46;1864:3;1950:66;1976:39;2011:2;2006:3;2002:12;1994:6;1976:39;:::i;:::-;1968:6;1950:66;:::i;:::-;2025:21;;;-1:-1:-1;;2073:2:192;2062:14;;1556:526;-1:-1:-1;;1556:526:192:o;2087:225::-;2154:9;;;2175:11;;;2172:134;;;2228:10;2223:3;2219:20;2216:1;2209:31;2263:4;2260:1;2253:15;2291:4;2288:1;2281:15;2317:177;99:334:153;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "99:334:153:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1695:212:2;;;;;;;;;;-1:-1:-1;1695:212:2;;;;;:::i;:::-;;:::i;:::-;;;470:14:192;;463:22;445:41;;433:2;418:18;1695:212:2;;;;;;;;512:165:121;;;;;;;;;;-1:-1:-1;512:165:121;;;;;:::i;:::-;-1:-1:-1;;;512:165:121;;;;;;;;;;;-1:-1:-1;;;;;;1906:33:192;;;1888:52;;1876:2;1861:18;512:165:121;1744:202:192;323:147:121;;;;;;;;;;-1:-1:-1;323:147:121;;;;;:::i;:::-;-1:-1:-1;;;;;;;;;;;;;424:39:121;;;;;;;;;;;;;323:147;;;;2360:13:192;;2342:32;;2330:2;2315:18;323:147:121;2136:244:192;4759:191:0;;;;;;;;;;-1:-1:-1;4759:191:0;;;;;:::i;:::-;;:::i;:::-;;;2531:25:192;;;2519:2;2504:18;4759:191:0;2385:177:192;5246:136:0;;;;;;;;;;-1:-1:-1;5246:136:0;;;;;:::i;:::-;;:::i;:::-;;6348:245;;;;;;;;;;-1:-1:-1;6348:245:0;;;;;:::i;:::-;;:::i;580:125:130:-;;;;;;;;;;;;;:::i;227:171:153:-;;;;;;;;;;-1:-1:-1;227:171:153;;;;;:::i;:::-;;:::i;919:142:130:-;;;;;;;;;;-1:-1:-1;919:142:130;;;;;:::i;:::-;;:::i;2492:233:2:-;;;;;;;;;;-1:-1:-1;2492:233:2;;;;;:::i;:::-;;:::i;:::-;;;-1:-1:-1;;;;;3900:32:192;;;3882:51;;3870:2;3855:18;2492:233:2;3736:203:192;3732:207:0;;;;;;;;;;-1:-1:-1;3732:207:0;;;;;:::i;:::-;;:::i;2317:49::-;;;;;;;;;;-1:-1:-1;2317:49:0;2362:4;2317:49;;742:140:130;;;;;;;;;;-1:-1:-1;742:140:130;;;;;:::i;:::-;;:::i;3658:227:2:-;;;;;;;;;;-1:-1:-1;3658:227:2;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;2893:222::-;;;;;;;;;;-1:-1:-1;2893:222:2;;;;;:::i;:::-;;:::i;5662:138:0:-;;;;;;;;;;-1:-1:-1;5662:138:0;;;;;:::i;:::-;;:::i;404:27:153:-;;;;;;;;;1695:212:2;1780:4;-1:-1:-1;;;;;;1803:57:2;;-1:-1:-1;;;1803:57:2;;:97;;;1864:36;1888:11;1864:23;:36::i;:::-;1796:104;1695:212;-1:-1:-1;;1695:212:2:o;4759:191:0:-;4824:7;4919:14;;;-1:-1:-1;;;;;;;;;;;4919:14:0;;;;;:24;;;;4759:191::o;5246:136::-;5320:18;5333:4;5320:12;:18::i;:::-;3191:16;3202:4;3191:10;:16::i;:::-;5350:25:::1;5361:4;5367:7;5350:10;:25::i;:::-;;5246:136:::0;;;:::o;6348:245::-;-1:-1:-1;;;;;6441:34:0;;966:10:5;6441:34:0;6437:102;;6498:30;;-1:-1:-1;;;6498:30:0;;;;;;;;;;;6437:102;6549:37;6561:4;6567:18;6549:11;:37::i;:::-;;6348:245;;:::o;580:125:130:-;629:7;655:43;1902:21;655:41;:43::i;:::-;648:50;;580:125;:::o;227:171:153:-;4158:30:3;4191:26;:24;:26::i;:::-;4302:15;;4158:59;;-1:-1:-1;4302:15:3;-1:-1:-1;;;4302:15:3;;;4301:16;;4348:14;;4279:19;4724:16;;:34;;;;;4744:14;4724:34;4704:54;;4768:17;4788:11;:16;;4803:1;4788:16;:50;;;;-1:-1:-1;4816:4:3;4808:25;:30;4788:50;4768:70;;4854:12;4853:13;:30;;;;;4871:12;4870:13;4853:30;4849:91;;;4906:23;;-1:-1:-1;;;4906:23:3;;;;;;;;;;;4849:91;4949:18;;-1:-1:-1;;4949:18:3;4966:1;4949:18;;;4977:67;;;;5011:22;;-1:-1:-1;;;;5011:22:3;-1:-1:-1;;;5011:22:3;;;4977:67;306:14:153::1;324:33;::::0;;::::1;335:10:::0;324:33:::1;:::i;:::-;305:52;;367:24;384:6;367:16;:24::i;:::-;295:103;5068:14:3::0;5064:101;;;5098:23;;-1:-1:-1;;;;5098:23:3;;;5140:14;;-1:-1:-1;5205:50:192;;5140:14:3;;5193:2:192;5178:18;5140:14:3;;;;;;;5064:101;4092:1079;;;;;227:171:153;;:::o;919:142:130:-;982:4;1005:49;1902:21;1049:4;1005:43;:49::i;2492:233:2:-;2573:7;2688:20;;;-1:-1:-1;;;;;;;;;;;2688:20:2;;;;;;;:30;;2712:5;2688:23;:30::i;:::-;2681:37;2492:233;-1:-1:-1;;;;2492:233:2:o;3732:207:0:-;3809:4;3901:14;;;-1:-1:-1;;;;;;;;;;;3901:14:0;;;;;;;;-1:-1:-1;;;;;3901:31:0;;;;;;;;;;;;;;;3732:207::o;742:140:130:-;805:7;831:44;1902:21;869:5;831:37;:44::i;3658:227:2:-;3753:40;3849:20;;;-1:-1:-1;;;;;;;;;;;3849:20:2;;;;;;;;3725:16;;1403:38;3849:29;;:27;:29::i;:::-;3842:36;3658:227;-1:-1:-1;;;3658:227:2:o;2893:222::-;2964:7;3079:20;;;-1:-1:-1;;;;;;;;;;;3079:20:2;;;;;;;:29;;:27;:29::i;5662:138:0:-;5737:18;5750:4;5737:12;:18::i;:::-;3191:16;3202:4;3191:10;:16::i;:::-;5767:26:::1;5779:4;5785:7;5767:11;:26::i;404:27:153:-:0;:::o;3443:202:0:-;3528:4;-1:-1:-1;;;;;;3551:47:0;;-1:-1:-1;;;3551:47:0;;:87;;-1:-1:-1;;;;;;;;;;1134:40:8;;;3602:36:0;1035:146:8;4148:103:0;4214:30;4225:4;966:10:5;4214::0;:30::i;:::-;4148:103;:::o;1094:319:130:-;1180:4;1200:31;1217:4;1223:7;1200:16;:31::i;:::-;1196:189;;;1251:44;1902:21;1290:4;1251:38;:44::i;:::-;1247:103;;;1320:15;;1330:4;;1320:15;;;;;1247:103;-1:-1:-1;1370:4:130;1363:11;;1196:189;-1:-1:-1;1401:5:130;1094:319;;;;:::o;1419:373::-;1506:4;1526:32;1544:4;1550:7;1526:17;:32::i;:::-;1522:242;;;1578:24;1597:4;1578:18;:24::i;:::-;1606:1;1578:29;1574:155;;1627:47;1902:21;1669:4;1627:41;:47::i;:::-;-1:-1:-1;1697:17:130;;1709:4;;1697:17;;;;;-1:-1:-1;1749:4:130;1742:11;;7693:115:72;7756:7;7782:19;7790:3;5202:18;;5120:107;9071:205:3;9129:30;;3147:66;9186:27;8819:122;401:203:120;6929:20:3;:18;:20::i;:::-;-1:-1:-1;;;;;483:20:120;::::1;479:71;;526:13;;-1:-1:-1::0;;;526:13:120::1;;;;;;;;;;;479:71;559:38;2362:4:0;590:6:120::0;559:10:::1;:38::i;:::-;;401:203:::0;:::o;7474:138:72:-;7554:4;5006:21;;;:14;;;:21;;;;;;:26;;7577:28;4910:129;10987:156;11061:7;11111:22;11115:3;11127:5;11111:3;:22::i;11683:273::-;11746:16;11774:22;11799:19;11807:3;11799:7;:19::i;4381:197:0:-;4469:22;4477:4;4483:7;4469;:22::i;:::-;4464:108;;4514:47;;-1:-1:-1;;;4514:47:0;;-1:-1:-1;;;;;5458:32:192;;4514:47:0;;;5440:51:192;5507:18;;;5500:34;;;5413:18;;4514:47:0;;;;;;;3987:348:2;4073:4;-1:-1:-1;;;;;;;;;;;4073:4:2;4193:31;4210:4;4216:7;4193:16;:31::i;:::-;4178:46;;4238:7;4234:71;;;4261:14;:20;;;;;;;;;;:33;;4286:7;4261:24;:33::i;:::-;;4321:7;3987:348;-1:-1:-1;;;;3987:348:2:o;6576:123:72:-;6646:4;6669:23;6674:3;6686:5;6669:4;:23::i;4438:353:2:-;4525:4;-1:-1:-1;;;;;;;;;;;4525:4:2;4645:32;4663:4;4669:7;4645:17;:32::i;:::-;4630:47;;4691:7;4687:74;;;4714:14;:20;;;;;;;;;;:36;;4742:7;4714:27;:36::i;6867:129:72:-;6940:4;6963:26;6971:3;6983:5;6963:7;:26::i;7082:141:3:-;7149:17;:15;:17::i;:::-;7144:73;;7189:17;;-1:-1:-1;;;7189:17:3;;;;;;;;;;;5569:118:72;5636:7;5662:3;:11;;5674:5;5662:18;;;;;;;;:::i;:::-;;;;;;;;;5655:25;;5569:118;;;;:::o;6227:109::-;6283:16;6318:3;:11;;6311:18;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6227:109;;;:::o;7270:387:0:-;7347:4;-1:-1:-1;;;;;;;;;;;7437:22:0;7445:4;7451:7;7437;:22::i;:::-;7432:219;;7475:8;:14;;;;;;;;;;;-1:-1:-1;;;;;7475:31:0;;;;;;;;;:38;;-1:-1:-1;;7475:38:0;7509:4;7475:38;;;7559:12;966:10:5;;887:96;7559:12:0;-1:-1:-1;;;;;7532:40:0;7550:7;-1:-1:-1;;;;;7532:40:0;7544:4;7532:40;;;;;;;;;;7593:4;7586:11;;;;;7432:219;7635:5;7628:12;;;;;9332:150:72;9402:4;9425:50;9430:3;-1:-1:-1;;;;;9450:23:72;;2336:406;2399:4;5006:21;;;:14;;;:21;;;;;;2415:321;;-1:-1:-1;2457:23:72;;;;;;;;:11;:23;;;;;;;;;;;;;2639:18;;2615:21;;;:14;;;:21;;;;;;:42;;;;2671:11;;2415:321;-1:-1:-1;2720:5:72;2713:12;;7894:388:0;7972:4;-1:-1:-1;;;;;;;;;;;8061:22:0;8069:4;8075:7;8061;:22::i;:::-;8057:219;;;8133:5;8099:14;;;;;;;;;;;-1:-1:-1;;;;;8099:31:0;;;;;;;;;;:39;;-1:-1:-1;;8099:39:0;;;8157:40;966:10:5;;8099:14:0;;8157:40;;8133:5;8157:40;8218:4;8211:11;;;;;9650:156:72;9723:4;9746:53;9754:3;-1:-1:-1;;;;;9774:23:72;;2910:1368;2976:4;3105:21;;;:14;;;:21;;;;;;3141:13;;3137:1135;;3508:18;3529:12;3540:1;3529:8;:12;:::i;:::-;3575:18;;3508:33;;-1:-1:-1;3555:17:72;;3575:22;;3596:1;;3575:22;:::i;:::-;3555:42;;3630:9;3616:10;:23;3612:378;;3659:17;3679:3;:11;;3691:9;3679:22;;;;;;;;:::i;:::-;;;;;;;;;3659:42;;3826:9;3800:3;:11;;3812:10;3800:23;;;;;;;;:::i;:::-;;;;;;;;;;;;:35;;;;3939:25;;;:14;;;:25;;;;;:36;;;3612:378;4068:17;;:3;;:17;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;4171:3;:14;;:21;4186:5;4171:21;;;;;;;;;;;4164:28;;;4214:4;4207:11;;;;;;;8485:120:3;8535:4;8558:26;:24;:26::i;:::-;:40;-1:-1:-1;;;8558:40:3;;;;;;-1:-1:-1;8485:120:3:o;14:286:192:-;72:6;125:2;113:9;104:7;100:23;96:32;93:52;;;141:1;138;131:12;93:52;167:23;;-1:-1:-1;;;;;;219:32:192;;209:43;;199:71;;266:1;263;256:12;497:131;-1:-1:-1;;;;;572:31:192;;562:42;;552:70;;618:1;615;608:12;633:347;684:8;694:6;748:3;741:4;733:6;729:17;725:27;715:55;;766:1;763;756:12;715:55;-1:-1:-1;789:20:192;;832:18;821:30;;818:50;;;864:1;861;854:12;818:50;901:4;893:6;889:17;877:29;;953:3;946:4;937:6;929;925:19;921:30;918:39;915:59;;;970:1;967;960:12;915:59;633:347;;;;;:::o;985:754::-;1082:6;1090;1098;1106;1114;1167:3;1155:9;1146:7;1142:23;1138:33;1135:53;;;1184:1;1181;1174:12;1135:53;1223:9;1210:23;1242:31;1267:5;1242:31;:::i;:::-;1292:5;-1:-1:-1;1349:2:192;1334:18;;1321:32;1362:33;1321:32;1362:33;:::i;:::-;1414:7;-1:-1:-1;1468:2:192;1453:18;;1440:32;;-1:-1:-1;1523:2:192;1508:18;;1495:32;1550:18;1539:30;;1536:50;;;1582:1;1579;1572:12;1536:50;1621:58;1671:7;1662:6;1651:9;1647:22;1621:58;:::i;:::-;985:754;;;;-1:-1:-1;985:754:192;;-1:-1:-1;1698:8:192;;1595:84;985:754;-1:-1:-1;;;985:754:192:o;1951:180::-;2010:6;2063:2;2051:9;2042:7;2038:23;2034:32;2031:52;;;2079:1;2076;2069:12;2031:52;-1:-1:-1;2102:23:192;;1951:180;-1:-1:-1;1951:180:192:o;2567:315::-;2635:6;2643;2696:2;2684:9;2675:7;2671:23;2667:32;2664:52;;;2712:1;2709;2702:12;2664:52;2748:9;2735:23;2725:33;;2808:2;2797:9;2793:18;2780:32;2821:31;2846:5;2821:31;:::i;:::-;2871:5;2861:15;;;2567:315;;;;;:::o;3069:409::-;3139:6;3147;3200:2;3188:9;3179:7;3175:23;3171:32;3168:52;;;3216:1;3213;3206:12;3168:52;3256:9;3243:23;3289:18;3281:6;3278:30;3275:50;;;3321:1;3318;3311:12;3275:50;3360:58;3410:7;3401:6;3390:9;3386:22;3360:58;:::i;:::-;3437:8;;3334:84;;-1:-1:-1;3069:409:192;-1:-1:-1;;;;3069:409:192:o;3483:248::-;3551:6;3559;3612:2;3600:9;3591:7;3587:23;3583:32;3580:52;;;3628:1;3625;3618:12;3580:52;-1:-1:-1;;3651:23:192;;;3721:2;3706:18;;;3693:32;;-1:-1:-1;3483:248:192:o;4129:658::-;4300:2;4352:21;;;4422:13;;4325:18;;;4444:22;;;4271:4;;4300:2;4523:15;;;;4497:2;4482:18;;;4271:4;4566:195;4580:6;4577:1;4574:13;4566:195;;;4645:13;;-1:-1:-1;;;;;4641:39:192;4629:52;;4736:15;;;;4701:12;;;;4677:1;4595:9;4566:195;;;-1:-1:-1;4778:3:192;;4129:658;-1:-1:-1;;;;;;4129:658:192:o;4792:255::-;4859:6;4912:2;4900:9;4891:7;4887:23;4883:32;4880:52;;;4928:1;4925;4918:12;4880:52;4967:9;4954:23;4986:31;5011:5;4986:31;:::i;5545:127::-;5606:10;5601:3;5597:20;5594:1;5587:31;5637:4;5634:1;5627:15;5661:4;5658:1;5651:15;5677:225;5744:9;;;5765:11;;;5762:134;;;5818:10;5813:3;5809:20;5806:1;5799:31;5853:4;5850:1;5843:15;5881:4;5878:1;5871:15;5907:127;5968:10;5963:3;5959:20;5956:1;5949:31;5999:4;5996:1;5989:15;6023:4;6020:1;6013:15", "linkReferences": {}, "immutableReferences": {"69670": [{"start": 1069, "length": 32}, {"start": 1382, "length": 32}, {"start": 1524, "length": 32}, {"start": 1761, "length": 32}, {"start": 1898, "length": 32}]}}, "methodIdentifiers": {"DEFAULT_ADMIN_ROLE()": "a217fddf", "getRoleAdmin(bytes32)": "248a9ca3", "getRoleMember(bytes32,uint256)": "9010d07c", "getRoleMemberCount(bytes32)": "ca15c873", "getRoleMembers(bytes32)": "a3246ad3", "getStorageAt(bytes32)": "1ca0027a", "grantRole(bytes32,address)": "2f2ff15d", "hasRole(bytes32,address)": "91d14854", "hasSupportedRole(bytes32)": "4a46b4cf", "initialize(bytes)": "439fab91", "onERC721Received(address,address,uint256,bytes)": "150b7a02", "renounceRole(bytes32,address)": "36568abe", "revokeRole(bytes32,address)": "d547741f", "supportedRoleAt(uint256)": "a2cb31e5", "supportedRoles()": "419a2053", "supportsInterface(bytes4)": "01ffc9a7", "test()": "f8a8fd6d"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name_\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"version_\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"AccessControlBadConfirmation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"neededRole\",\"type\":\"bytes32\"}],\"name\":\"AccessControlUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Forbidden\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZeroAddress\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"RoleAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"previousAdminRole\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"newAdminRole\",\"type\":\"bytes32\"}],\"name\":\"RoleAdminChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleGranted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"RoleRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleRevoked\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"DEFAULT_ADMIN_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"getRoleAdmin\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"getRoleMember\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"getRoleMemberCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"getRoleMembers\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"slot\",\"type\":\"bytes32\"}],\"name\":\"getStorageAt\",\"outputs\":[{\"components\":[{\"internalType\":\"bytes32\",\"name\":\"value\",\"type\":\"bytes32\"}],\"internalType\":\"struct StorageSlot.Bytes32Slot\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"grantRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"hasRole\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"hasSupportedRole\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"initParams\",\"type\":\"bytes\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"onERC721Received\",\"outputs\":[{\"internalType\":\"bytes4\",\"name\":\"\",\"type\":\"bytes4\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"callerConfirmation\",\"type\":\"address\"}],\"name\":\"renounceRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"revokeRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"supportedRoleAt\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"supportedRoles\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"test\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"stateMutability\":\"payable\",\"type\":\"receive\"}],\"devdoc\":{\"errors\":{\"AccessControlBadConfirmation()\":[{\"details\":\"The caller of a function is not the expected one. NOTE: Don't confuse with {AccessControlUnauthorizedAccount}.\"}],\"AccessControlUnauthorizedAccount(address,bytes32)\":[{\"details\":\"The `account` is missing a role.\"}],\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}]},\"events\":{\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"},\"RoleAdminChanged(bytes32,bytes32,bytes32)\":{\"details\":\"Emitted when `newAdminRole` is set as ``role``'s admin role, replacing `previousAdminRole` `DEFAULT_ADMIN_ROLE` is the starting admin for all roles, despite {RoleAdminChanged} not being emitted to signal this.\"},\"RoleGranted(bytes32,address,address)\":{\"details\":\"Emitted when `account` is granted `role`. `sender` is the account that originated the contract call. This account bears the admin role (for the granted role). Expected in cases where the role was granted using the internal {AccessControl-_grantRole}.\"},\"RoleRevoked(bytes32,address,address)\":{\"details\":\"Emitted when `account` is revoked `role`. `sender` is the account that originated the contract call:   - if using `revokeRole`, it is the admin role bearer   - if using `renounceRole`, it is the role bearer (i.e. `account`)\"}},\"kind\":\"dev\",\"methods\":{\"getRoleAdmin(bytes32)\":{\"details\":\"Returns the admin role that controls `role`. See {grantRole} and {revokeRole}. To change a role's admin, use {_setRoleAdmin}.\"},\"getRoleMember(bytes32,uint256)\":{\"details\":\"Returns one of the accounts that have `role`. `index` must be a value between 0 and {getRoleMemberCount}, non-inclusive. Role bearers are not sorted in any particular way, and their ordering may change at any point. WARNING: When using {getRoleMember} and {getRoleMemberCount}, make sure you perform all queries on the same block. See the following https://forum.openzeppelin.com/t/iterating-over-elements-on-enumerableset-in-openzeppelin-contracts/2296[forum post] for more information.\"},\"getRoleMemberCount(bytes32)\":{\"details\":\"Returns the number of accounts that have `role`. Can be used together with {getRoleMember} to enumerate all bearers of a role.\"},\"getRoleMembers(bytes32)\":{\"details\":\"Return all accounts that have `role` WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that this function has an unbounded cost, and using it as part of a state-changing function may render the function uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\"},\"getStorageAt(bytes32)\":{\"params\":{\"slot\":\"The keccak256-derived storage slot identifier\"},\"returns\":{\"_0\":\"A struct exposing the `.value` field stored at the given slot\"}},\"grantRole(bytes32,address)\":{\"details\":\"Grants `role` to `account`. If `account` had not been already granted `role`, emits a {RoleGranted} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleGranted} event.\"},\"hasRole(bytes32,address)\":{\"details\":\"Returns `true` if `account` has been granted `role`.\"},\"hasSupportedRole(bytes32)\":{\"params\":{\"role\":\"The bytes32 identifier of the role to check\"},\"returns\":{\"_0\":\"isActive True if the role has any members assigned\"}},\"onERC721Received(address,address,uint256,bytes)\":{\"details\":\"Whenever an {IERC721} `tokenId` token is transferred to this contract via {IERC721-safeTransferFrom} by `operator` from `from`, this function is called. It must return its Solidity selector to confirm the token transfer. If any other value is returned or the interface is not implemented by the recipient, the transfer will be reverted. The selector can be obtained in Solidity with `IERC721Receiver.onERC721Received.selector`.\"},\"renounceRole(bytes32,address)\":{\"details\":\"Revokes `role` from the calling account. Roles are often managed via {grantRole} and {revokeRole}: this function's purpose is to provide a mechanism for accounts to lose their privileges if they are compromised (such as when a trusted device is misplaced). If the calling account had been revoked `role`, emits a {RoleRevoked} event. Requirements: - the caller must be `callerConfirmation`. May emit a {RoleRevoked} event.\"},\"revokeRole(bytes32,address)\":{\"details\":\"Revokes `role` from `account`. If `account` had been granted `role`, emits a {RoleRevoked} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleRevoked} event.\"},\"supportedRoleAt(uint256)\":{\"params\":{\"index\":\"Index within the supported role set\"},\"returns\":{\"_0\":\"role The bytes32 identifier of the role\"}},\"supportsInterface(bytes4)\":{\"details\":\"See {IERC165-supportsInterface}.\"}},\"version\":1},\"userdoc\":{\"errors\":{\"Forbidden()\":[{\"notice\":\"Thrown when an unauthorized caller attempts a restricted operation\"}],\"ZeroAddress()\":[{\"notice\":\"Thrown when a zero address is provided\"}]},\"events\":{\"RoleAdded(bytes32)\":{\"notice\":\"Emitted when a new role is granted for the first time\"},\"RoleRemoved(bytes32)\":{\"notice\":\"Emitted when a role loses its last member\"}},\"kind\":\"user\",\"methods\":{\"getStorageAt(bytes32)\":{\"notice\":\"Returns a reference to a storage slot as a `StorageSlot.Bytes32Slot` struct\"},\"hasSupportedRole(bytes32)\":{\"notice\":\"Checks whether a given role is currently active (i.e., has at least one member)\"},\"supportedRoleAt(uint256)\":{\"notice\":\"Returns the role at the specified index in the set of active roles\"},\"supportedRoles()\":{\"notice\":\"Returns the total number of unique roles that are currently assigned\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/mocks/MockACLModule.sol\":\"MockACLModule\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/contracts/access/AccessControlUpgradeable.sol\":{\"keccak256\":\"0x0d4d7d19a052a2ef9d17b28450133631188b895e1755747fa8ad0280aadfb534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://838551e10cc07e570b70aee1078e490658d3a0ab8f4adfd4f3f2a565200753da\",\"dweb:/ipfs/QmceT7R3Pqzywyxr3MSEA7VXU4axZAPS6BLy92iV97rGdG\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/access/extensions/AccessControlEnumerableUpgradeable.sol\":{\"keccak256\":\"0xaec38804089a16494f7d45ebbbeab78fe05e5dc4bc2eae1d2af458fe999fb43c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://04796408e2c4a1051afb23319e9505edb595a3d9c663ff5d6fcfb8e185ab1d93\",\"dweb:/ipfs/Qma8ZTi4kGyqg45SyNibbJSqMF7broiQnJPHhEGLcZyF9U\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol\":{\"keccak256\":\"0x361126a17677994081cd9cb69c3f50cffff6e920d25cb7e428acdb1ae41d1866\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://19ae787a7dd001269cd60a394b1a5261b78925a0fc3a6f927beb2986a9aa56cf\",\"dweb:/ipfs/QmYLfXiuKmcRgTDBEDXMMjXU8t6JxsspUmjxYzqWS55oEv\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/introspection/ERC165Upgradeable.sol\":{\"keccak256\":\"0xc8ed8d2056934b7675b695dec032f2920c2f5c6cf33a17ca85650940675323ab\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3c8ccc75d1cd792d192aa09e54dd49ea35fe85baa9fcd17486f29227d9f29b89\",\"dweb:/ipfs/QmbboSbFUEiM9tdEgBwuTRb7bykFoJXZ7dsSr1PSREJXMr\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0\",\"dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/extensions/IAccessControlEnumerable.sol\":{\"keccak256\":\"0xca774fbe0568762efdc1a7cba31f09549c7fa96dbe97410f4843fa2f0bc000a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0187ffdbf3d61b6d86cba4fcd9826e53d876987d620533ee84c681bdaf0f3ba3\",\"dweb:/ipfs/QmVJDqdJv6uzHY7ifncfv2QJep8XTzS3bGb4s5Exhuv86m\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol\":{\"keccak256\":\"0xb5afb8e8eebc4d1c6404df2f5e1e6d2c3d24fd01e5dfc855314951ecfaae462d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://78586466c424f076c6a2a551d848cfbe3f7c49e723830807598484a1047b3b34\",\"dweb:/ipfs/Qmb717ovcFxm7qgNKEShiV6M9SPR3v1qnNpAGH84D6w29p\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol\":{\"keccak256\":\"0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd\",\"dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol\":{\"keccak256\":\"0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd\",\"dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol\":{\"keccak256\":\"0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2\",\"dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621\",\"dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol\":{\"keccak256\":\"0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231\",\"dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y\"]},\"src/interfaces/modules/IACLModule.sol\":{\"keccak256\":\"0xf6ce9b4f4af5475d152845d6afa6f57b2d376c3a6d3eec2b5ac9650901a78243\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://f851cef38b6188b20be98c95047c8bd9d8651d37421a54e1d02b60f9f372c5ed\",\"dweb:/ipfs/QmaymGDd2RoKweuASXCiV3rPuhwvLXUvb6wBgi8xXoiYT8\"]},\"src/interfaces/modules/IBaseModule.sol\":{\"keccak256\":\"0x9c5c95ceb39f4c39f81f2afc7ddb55389a21315e76047a9cf66521db4dea68c3\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ccfb0453b14b105cb5990978a49c1e08f812563a046e17c354f4f1c648f95fe7\",\"dweb:/ipfs/QmYMAKaVdRq3NoTMfqadxgMMmUq6ejzTw2Lpsa3e9a3eyj\"]},\"src/interfaces/permissions/IMellowACL.sol\":{\"keccak256\":\"0xcc86b0ce4e4a53c1c4fa16ad79f9de0e2a1f1a77a1f256c0856120a9d6ea36df\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://6942df2ffadacae5563e01ec60697f16116362ba84a856a346b36bc7b9dd1ec2\",\"dweb:/ipfs/QmNYhqZGpS98UP89CS86CjoG4ZWbMHzTHRtd7bZzQqn3UY\"]},\"src/libraries/SlotLibrary.sol\":{\"keccak256\":\"0x35dcf16feec83c772add30250b0987ce29ccfbf308d04159701ebf9243444eeb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://97658d0c93ecc8174169e643d5a24c515b55905a2fcaaa6d6ec0188a3dd71a10\",\"dweb:/ipfs/QmS1z7ac8RbMvz15gVQ9YA1cCT5bs6KMHCiDhgcG8Pq8oK\"]},\"src/modules/ACLModule.sol\":{\"keccak256\":\"0x00a3ab26a5fa470e3894933025f7f805a8aa228792547396c31af545550a2c5d\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://911b8f81d024abc81c4a2ca3bd6d6a6cfeda47645212fdee4850952e13bd865e\",\"dweb:/ipfs/QmRsrjHBForqbb892SMCoD9VPwSdbezmgtp7FwoSNnA5pz\"]},\"src/modules/BaseModule.sol\":{\"keccak256\":\"0x903f864d58e1f2a87374228359c5d896954748935bfc8e5c1e69f51c9f2c0811\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://f37ec172df06e262caf1a072f3964fec2c07bb59aed4608f42619ae32214ac4d\",\"dweb:/ipfs/QmTKWUcLjjs2ddcsKBciJdUuiEbeiq8ZiD7qGABoKHC2w6\"]},\"src/permissions/MellowACL.sol\":{\"keccak256\":\"0xeea276fd09714ef09b380fc9dfb8ae6639c9ff338b3e32cf3b824282a4a3a280\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c87b6a2c3e1c70772ceff8578785aa8e0daa76bccfb031ff2ecb72ccc7d2d0f2\",\"dweb:/ipfs/QmefXov36R68VpEaeafPJCWG89yMEWqe6UnKcWyX4vRsVH\"]},\"test/mocks/MockACLModule.sol\":{\"keccak256\":\"0x6334bb87a52310456aaa1c383cfd88ae041b064917cc27a6067483df16da2e55\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b1ee07721e9f0ef1faa63c1dc911d9bc37e000c9ce3b36da562c56f0957510f6\",\"dweb:/ipfs/QmaNXbjoYKBibb2XjgX1JUGGW2UHiG9Y5JBTMg2r38Y3JW\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "uint256", "name": "version_", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "AccessControlBadConfirmation"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "type": "error", "name": "AccessControlUnauthorizedAccount"}, {"inputs": [], "type": "error", "name": "Forbidden"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [], "type": "error", "name": "ReentrancyGuardReentrantCall"}, {"inputs": [], "type": "error", "name": "ZeroAddress"}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}], "type": "event", "name": "RoleAdded", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32", "indexed": true}, {"internalType": "bytes32", "name": "newAdminRole", "type": "bytes32", "indexed": true}], "type": "event", "name": "RoleAdminChanged", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": true}], "type": "event", "name": "RoleGranted", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}], "type": "event", "name": "RoleRemoved", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": true}], "type": "event", "name": "RoleRevoked", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getRoleMember", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "getRoleMemberCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "getRoleMembers", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}]}, {"inputs": [{"internalType": "bytes32", "name": "slot", "type": "bytes32"}], "stateMutability": "pure", "type": "function", "name": "getStorageAt", "outputs": [{"internalType": "struct StorageSlot.Bytes32Slot", "name": "", "type": "tuple", "components": [{"internalType": "bytes32", "name": "value", "type": "bytes32"}]}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "grantRole"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "hasSupportedRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "bytes", "name": "initParams", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "pure", "type": "function", "name": "onERC721Received", "outputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "renounceRole"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "revokeRole"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "supportedRoleAt", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "supportedRoles", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "stateMutability": "view", "type": "function", "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "test"}, {"inputs": [], "stateMutability": "payable", "type": "receive"}], "devdoc": {"kind": "dev", "methods": {"getRoleAdmin(bytes32)": {"details": "Returns the admin role that controls `role`. See {grantRole} and {revokeRole}. To change a role's admin, use {_setRoleAdmin}."}, "getRoleMember(bytes32,uint256)": {"details": "Returns one of the accounts that have `role`. `index` must be a value between 0 and {getRoleMemberCount}, non-inclusive. Role bearers are not sorted in any particular way, and their ordering may change at any point. WARNING: When using {getRoleMember} and {getRoleMemberCount}, make sure you perform all queries on the same block. See the following https://forum.openzeppelin.com/t/iterating-over-elements-on-enumerableset-in-openzeppelin-contracts/2296[forum post] for more information."}, "getRoleMemberCount(bytes32)": {"details": "Returns the number of accounts that have `role`. Can be used together with {getRoleMember} to enumerate all bearers of a role."}, "getRoleMembers(bytes32)": {"details": "Return all accounts that have `role` WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that this function has an unbounded cost, and using it as part of a state-changing function may render the function uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block."}, "getStorageAt(bytes32)": {"params": {"slot": "The keccak256-derived storage slot identifier"}, "returns": {"_0": "A struct exposing the `.value` field stored at the given slot"}}, "grantRole(bytes32,address)": {"details": "Grants `role` to `account`. If `account` had not been already granted `role`, emits a {RoleGranted} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleGranted} event."}, "hasRole(bytes32,address)": {"details": "Returns `true` if `account` has been granted `role`."}, "hasSupportedRole(bytes32)": {"params": {"role": "The bytes32 identifier of the role to check"}, "returns": {"_0": "isActive True if the role has any members assigned"}}, "onERC721Received(address,address,uint256,bytes)": {"details": "Whenever an {IERC721} `tokenId` token is transferred to this contract via {IERC721-safeTransferFrom} by `operator` from `from`, this function is called. It must return its Solidity selector to confirm the token transfer. If any other value is returned or the interface is not implemented by the recipient, the transfer will be reverted. The selector can be obtained in Solidity with `IERC721Receiver.onERC721Received.selector`."}, "renounceRole(bytes32,address)": {"details": "Revokes `role` from the calling account. Roles are often managed via {grantRole} and {revokeRole}: this function's purpose is to provide a mechanism for accounts to lose their privileges if they are compromised (such as when a trusted device is misplaced). If the calling account had been revoked `role`, emits a {RoleRevoked} event. Requirements: - the caller must be `callerConfirmation`. May emit a {RoleRevoked} event."}, "revokeRole(bytes32,address)": {"details": "Revokes `role` from `account`. If `account` had been granted `role`, emits a {RoleRevoked} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleRevoked} event."}, "supportedRoleAt(uint256)": {"params": {"index": "Index within the supported role set"}, "returns": {"_0": "role The bytes32 identifier of the role"}}, "supportsInterface(bytes4)": {"details": "See {IERC165-supportsInterface}."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"getStorageAt(bytes32)": {"notice": "Returns a reference to a storage slot as a `StorageSlot.Bytes32Slot` struct"}, "hasSupportedRole(bytes32)": {"notice": "Checks whether a given role is currently active (i.e., has at least one member)"}, "supportedRoleAt(uint256)": {"notice": "Returns the role at the specified index in the set of active roles"}, "supportedRoles()": {"notice": "Returns the total number of unique roles that are currently assigned"}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/mocks/MockACLModule.sol": "MockACLModule"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/contracts/access/AccessControlUpgradeable.sol": {"keccak256": "0x0d4d7d19a052a2ef9d17b28450133631188b895e1755747fa8ad0280aadfb534", "urls": ["bzz-raw://838551e10cc07e570b70aee1078e490658d3a0ab8f4adfd4f3f2a565200753da", "dweb:/ipfs/QmceT7R3Pqzywyxr3MSEA7VXU4axZAPS6BLy92iV97rGdG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/access/extensions/AccessControlEnumerableUpgradeable.sol": {"keccak256": "0xaec38804089a16494f7d45ebbbeab78fe05e5dc4bc2eae1d2af458fe999fb43c", "urls": ["bzz-raw://04796408e2c4a1051afb23319e9505edb595a3d9c663ff5d6fcfb8e185ab1d93", "dweb:/ipfs/Qma8ZTi4kGyqg45SyNibbJSqMF7broiQnJPHhEGLcZyF9U"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05", "urls": ["bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08", "dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol": {"keccak256": "0x361126a17677994081cd9cb69c3f50cffff6e920d25cb7e428acdb1ae41d1866", "urls": ["bzz-raw://19ae787a7dd001269cd60a394b1a5261b78925a0fc3a6f927beb2986a9aa56cf", "dweb:/ipfs/QmYLfXiuKmcRgTDBEDXMMjXU8t6JxsspUmjxYzqWS55oEv"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/introspection/ERC165Upgradeable.sol": {"keccak256": "0xc8ed8d2056934b7675b695dec032f2920c2f5c6cf33a17ca85650940675323ab", "urls": ["bzz-raw://3c8ccc75d1cd792d192aa09e54dd49ea35fe85baa9fcd17486f29227d9f29b89", "dweb:/ipfs/QmbboSbFUEiM9tdEgBwuTRb7bykFoJXZ7dsSr1PSREJXMr"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530", "urls": ["bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0", "dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/extensions/IAccessControlEnumerable.sol": {"keccak256": "0xca774fbe0568762efdc1a7cba31f09549c7fa96dbe97410f4843fa2f0bc000a3", "urls": ["bzz-raw://0187ffdbf3d61b6d86cba4fcd9826e53d876987d620533ee84c681bdaf0f3ba3", "dweb:/ipfs/QmVJDqdJv6uzHY7ifncfv2QJep8XTzS3bGb4s5Exhuv86m"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol": {"keccak256": "0xb5afb8e8eebc4d1c6404df2f5e1e6d2c3d24fd01e5dfc855314951ecfaae462d", "urls": ["bzz-raw://78586466c424f076c6a2a551d848cfbe3f7c49e723830807598484a1047b3b34", "dweb:/ipfs/Qmb717ovcFxm7qgNKEShiV6M9SPR3v1qnNpAGH84D6w29p"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol": {"keccak256": "0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6", "urls": ["bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd", "dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol": {"keccak256": "0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58", "urls": ["bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd", "dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol": {"keccak256": "0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26", "urls": ["bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2", "dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8", "urls": ["bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621", "dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol": {"keccak256": "0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06", "urls": ["bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231", "dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y"], "license": "MIT"}, "src/interfaces/modules/IACLModule.sol": {"keccak256": "0xf6ce9b4f4af5475d152845d6afa6f57b2d376c3a6d3eec2b5ac9650901a78243", "urls": ["bzz-raw://f851cef38b6188b20be98c95047c8bd9d8651d37421a54e1d02b60f9f372c5ed", "dweb:/ipfs/QmaymGDd2RoKweuASXCiV3rPuhwvLXUvb6wBgi8xXoiYT8"], "license": "BUSL-1.1"}, "src/interfaces/modules/IBaseModule.sol": {"keccak256": "0x9c5c95ceb39f4c39f81f2afc7ddb55389a21315e76047a9cf66521db4dea68c3", "urls": ["bzz-raw://ccfb0453b14b105cb5990978a49c1e08f812563a046e17c354f4f1c648f95fe7", "dweb:/ipfs/QmYMAKaVdRq3NoTMfqadxgMMmUq6ejzTw2Lpsa3e9a3eyj"], "license": "BUSL-1.1"}, "src/interfaces/permissions/IMellowACL.sol": {"keccak256": "0xcc86b0ce4e4a53c1c4fa16ad79f9de0e2a1f1a77a1f256c0856120a9d6ea36df", "urls": ["bzz-raw://6942df2ffadacae5563e01ec60697f16116362ba84a856a346b36bc7b9dd1ec2", "dweb:/ipfs/QmNYhqZGpS98UP89CS86CjoG4ZWbMHzTHRtd7bZzQqn3UY"], "license": "BUSL-1.1"}, "src/libraries/SlotLibrary.sol": {"keccak256": "0x35dcf16feec83c772add30250b0987ce29ccfbf308d04159701ebf9243444eeb", "urls": ["bzz-raw://97658d0c93ecc8174169e643d5a24c515b55905a2fcaaa6d6ec0188a3dd71a10", "dweb:/ipfs/QmS1z7ac8RbMvz15gVQ9YA1cCT5bs6KMHCiDhgcG8Pq8oK"], "license": "BUSL-1.1"}, "src/modules/ACLModule.sol": {"keccak256": "0x00a3ab26a5fa470e3894933025f7f805a8aa228792547396c31af545550a2c5d", "urls": ["bzz-raw://911b8f81d024abc81c4a2ca3bd6d6a6cfeda47645212fdee4850952e13bd865e", "dweb:/ipfs/QmRsrjHBForqbb892SMCoD9VPwSdbezmgtp7FwoSNnA5pz"], "license": "BUSL-1.1"}, "src/modules/BaseModule.sol": {"keccak256": "0x903f864d58e1f2a87374228359c5d896954748935bfc8e5c1e69f51c9f2c0811", "urls": ["bzz-raw://f37ec172df06e262caf1a072f3964fec2c07bb59aed4608f42619ae32214ac4d", "dweb:/ipfs/QmTKWUcLjjs2ddcsKBciJdUuiEbeiq8ZiD7qGABoKHC2w6"], "license": "BUSL-1.1"}, "src/permissions/MellowACL.sol": {"keccak256": "0xeea276fd09714ef09b380fc9dfb8ae6639c9ff338b3e32cf3b824282a4a3a280", "urls": ["bzz-raw://c87b6a2c3e1c70772ceff8578785aa8e0daa76bccfb031ff2ecb72ccc7d2d0f2", "dweb:/ipfs/QmefXov36R68VpEaeafPJCWG89yMEWqe6UnKcWyX4vRsVH"], "license": "BUSL-1.1"}, "test/mocks/MockACLModule.sol": {"keccak256": "0x6334bb87a52310456aaa1c383cfd88ae041b064917cc27a6067483df16da2e55", "urls": ["bzz-raw://b1ee07721e9f0ef1faa63c1dc911d9bc37e000c9ce3b36da562c56f0957510f6", "dweb:/ipfs/QmaNXbjoYKBibb2XjgX1JUGGW2UHiG9Y5JBTMg2r38Y3JW"], "license": "BUSL-1.1"}}, "version": 1}, "id": 153}