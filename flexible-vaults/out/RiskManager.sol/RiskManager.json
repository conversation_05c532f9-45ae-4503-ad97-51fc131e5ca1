{"abi": [{"type": "constructor", "inputs": [{"name": "name_", "type": "string", "internalType": "string"}, {"name": "version_", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "ALLOW_SUBVAULT_ASSETS_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "DISALLOW_SUBVAULT_ASSETS_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "MODIFY_PENDING_ASSETS_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "MODIFY_SUBVAULT_BALANCE_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "MODIFY_VAULT_BALANCE_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "SET_SUBVAULT_LIMIT_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "SET_VAULT_LIMIT_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "allowSubvaultAssets", "inputs": [{"name": "subvault", "type": "address", "internalType": "address"}, {"name": "assets", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "allowedAssetAt", "inputs": [{"name": "subvault", "type": "address", "internalType": "address"}, {"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "allowedAssets", "inputs": [{"name": "subvault", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "convertToShares", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}, {"name": "value", "type": "int256", "internalType": "int256"}], "outputs": [{"name": "shares", "type": "int256", "internalType": "int256"}], "stateMutability": "view"}, {"type": "function", "name": "disallowSubvaultAssets", "inputs": [{"name": "subvault", "type": "address", "internalType": "address"}, {"name": "assets", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "initialize", "inputs": [{"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isAllowedAsset", "inputs": [{"name": "subvault", "type": "address", "internalType": "address"}, {"name": "asset", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "maxDeposit", "inputs": [{"name": "subvault", "type": "address", "internalType": "address"}, {"name": "asset", "type": "address", "internalType": "address"}], "outputs": [{"name": "limit", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "modifyPendingAssets", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}, {"name": "change", "type": "int256", "internalType": "int256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "modifySubvaultBalance", "inputs": [{"name": "subvault", "type": "address", "internalType": "address"}, {"name": "asset", "type": "address", "internalType": "address"}, {"name": "change", "type": "int256", "internalType": "int256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "modifyVaultBalance", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}, {"name": "change", "type": "int256", "internalType": "int256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "pendingAssets", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "int256", "internalType": "int256"}], "stateMutability": "view"}, {"type": "function", "name": "pendingBalance", "inputs": [], "outputs": [{"name": "", "type": "int256", "internalType": "int256"}], "stateMutability": "view"}, {"type": "function", "name": "pendingShares", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "int256", "internalType": "int256"}], "stateMutability": "view"}, {"type": "function", "name": "requireValidSubvault", "inputs": [{"name": "vault_", "type": "address", "internalType": "address"}, {"name": "subvault", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "view"}, {"type": "function", "name": "setSubvaultLimit", "inputs": [{"name": "subvault", "type": "address", "internalType": "address"}, {"name": "limit", "type": "int256", "internalType": "int256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON>", "inputs": [{"name": "vault_", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setVaultLimit", "inputs": [{"name": "limit", "type": "int256", "internalType": "int256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "subvaultState", "inputs": [{"name": "subvault", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IRiskManager.State", "components": [{"name": "balance", "type": "int256", "internalType": "int256"}, {"name": "limit", "type": "int256", "internalType": "int256"}]}], "stateMutability": "view"}, {"type": "function", "name": "vault", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "vaultState", "inputs": [], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IRiskManager.State", "components": [{"name": "balance", "type": "int256", "internalType": "int256"}, {"name": "limit", "type": "int256", "internalType": "int256"}]}], "stateMutability": "view"}, {"type": "event", "name": "AllowSubvaultAssets", "inputs": [{"name": "subvault", "type": "address", "indexed": true, "internalType": "address"}, {"name": "assets", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "DisallowSubvaultAssets", "inputs": [{"name": "subvault", "type": "address", "indexed": true, "internalType": "address"}, {"name": "assets", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "initParams", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "ModifyPendingAssets", "inputs": [{"name": "asset", "type": "address", "indexed": true, "internalType": "address"}, {"name": "change", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "pendingAssetsAfter", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "pendingSharesAfter", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "ModifySubvaultBalance", "inputs": [{"name": "subvault", "type": "address", "indexed": true, "internalType": "address"}, {"name": "asset", "type": "address", "indexed": true, "internalType": "address"}, {"name": "change", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "newBalance", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "ModifyVaultBalance", "inputs": [{"name": "asset", "type": "address", "indexed": true, "internalType": "address"}, {"name": "shares", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "newBalance", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "SetSubvaultLimit", "inputs": [{"name": "subvault", "type": "address", "indexed": true, "internalType": "address"}, {"name": "limit", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "<PERSON><PERSON><PERSON>", "inputs": [{"name": "vault", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SetVaultLimit", "inputs": [{"name": "limit", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "error", "name": "AlreadyAllowedAsset", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "Forbidden", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "InvalidReport", "inputs": []}, {"type": "error", "name": "LimitExceeded", "inputs": [{"name": "newValue", "type": "int256", "internalType": "int256"}, {"name": "maxValue", "type": "int256", "internalType": "int256"}]}, {"type": "error", "name": "NotAllowedAsset", "inputs": [{"name": "asset", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "NotSubvault", "inputs": [{"name": "subvault", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ZeroValue", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "153:10134:117:-:0;;;1167:177;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1254:51;;;;;;;;;;;;-1:-1:-1;;;1254:51:117;;;;;;1289:5;1296:8;1254:19;:51::i;:::-;1228:77;;1315:22;:20;:22::i;:::-;1167:177;;153:10134;;656:343:113;761:7;986:4;970:22;;969:23;941:1;908:12;922:4;928:7;856:80;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;846:91;;;;;;838:100;;:104;;;;:::i;:::-;810:146;;;;;;2258:25:192;;2246:2;2231:18;;2112:177;810:146:113;;;;;;;;;;;;;787:179;;;;;;:205;780:212;;656:343;;;;;:::o;7709:422:3:-;7824:30;7857:26;:24;:26::i;:::-;7898:15;;;;-1:-1:-1;7898:15:3;;;;;7894:76;;;7936:23;;-1:-1:-1;;;7936:23:3;;;;;;;;;;;7894:76;7983:14;;-1:-1:-1;;;;;7983:14:3;;;:34;7979:146;;8033:33;;-1:-1:-1;;;;;;8033:33:3;-1:-1:-1;;;;;8033:33:3;;;;;8085:29;;2438:50:192;;;8085:29:3;;2426:2:192;2411:18;8085:29:3;;;;;;;7979:146;7758:373;7709:422::o;9071:205::-;9129:30;;3147:66;9186:27;9171:42;9071:205;-1:-1:-1;;9071:205:3:o;14:127:192:-;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:983;235:6;243;296:2;284:9;275:7;271:23;267:32;264:52;;;312:1;309;302:12;264:52;339:16;;-1:-1:-1;;;;;404:14:192;;;401:34;;;431:1;428;421:12;401:34;469:6;458:9;454:22;444:32;;514:7;507:4;503:2;499:13;495:27;485:55;;536:1;533;526:12;485:55;565:2;559:9;587:2;583;580:10;577:36;;;593:18;;:::i;:::-;668:2;662:9;636:2;722:13;;-1:-1:-1;;718:22:192;;;742:2;714:31;710:40;698:53;;;766:18;;;786:22;;;763:46;760:72;;;812:18;;:::i;:::-;852:10;848:2;841:22;887:2;879:6;872:18;929:7;922:4;917:2;913;909:11;905:22;902:35;899:55;;;950:1;947;940:12;899:55;1003:2;996:4;992:2;988:13;981:4;973:6;969:17;963:43;1050:1;1043:4;1038:2;1030:6;1026:15;1022:26;1015:37;1071:6;1061:16;;;;;;;1117:4;1106:9;1102:20;1096:27;1086:37;;146:983;;;;;:::o;1134:212::-;1176:3;1214:5;1208:12;1258:6;1251:4;1244:5;1240:16;1235:3;1229:36;1320:1;1284:16;;1309:13;;;-1:-1:-1;1284:16:192;;1134:212;-1:-1:-1;1134:212:192:o;1351:526::-;1689:33;1684:3;1677:46;1659:3;1745:66;1771:39;1806:2;1801:3;1797:12;1789:6;1771:39;:::i;:::-;1763:6;1745:66;:::i;:::-;1820:21;;;-1:-1:-1;;1868:2:192;1857:14;;1351:526;-1:-1:-1;;1351:526:192:o;1882:225::-;1949:9;;;1970:11;;;1967:134;;;2023:10;2018:3;2014:20;2011:1;2004:31;2058:4;2055:1;2048:15;2086:4;2083:1;2076:15;2294:200;153:10134:117;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "153:10134:117:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;743:113;;796:60;743:113;;;;;160:25:192;;;148:2;133:18;743:113:117;;;;;;;;7611:1015;;;;;;:::i;:::-;;:::i;:::-;;2470:113;;;:::i;:::-;;;;863:13:192;;845:32;;933:4;921:17;;;915:24;893:20;;;886:54;;;;818:18;2470:113:117;651:295:192;2948:133:117;;;;;;:::i;:::-;;:::i;3120:146::-;;;;;;:::i;:::-;;:::i;979:125::-;;1042:62;979:125;;5266:178;;;;;;:::i;:::-;;:::i;862:111::-;;914:59;862:111;;3493:166;;;;;;:::i;:::-;;:::i;:::-;;;-1:-1:-1;;;;;2463:32:192;;;2445:51;;2433:2;2418:18;3493:166:117;2299:203:192;2622:115:117;;;:::i;5450:338::-;;;;;;:::i;:::-;;:::i;491:113::-;;544:60;491:113;;4523:676;;;;;;:::i;:::-;;:::i;2100:194::-;;;;;;:::i;:::-;;:::i;8665:572::-;;;;;;:::i;:::-;;:::i;3698:169::-;;;;;;:::i;:::-;;:::i;:::-;;;3247:14:192;;3240:22;3222:41;;3210:2;3195:18;3698:169:117;3082:187:192;5827:317:117;;;;;;:::i;:::-;;:::i;610:127::-;;674:63;610:127;;378:107;;428:57;378:107;;3305:149;;;;;;:::i;:::-;;:::i;7397:175::-;;;;;;:::i;:::-;;:::i;271:101::-;;318:54;271:101;;3906:578;;;;;;:::i;:::-;;:::i;6183:564::-;;;;;;:::i;:::-;;:::i;2776:133::-;;;;;;:::i;:::-;;:::i;6786:572::-;;;;;;:::i;:::-;;:::i;2333:98::-;;;:::i;9276:781::-;;;;;;:::i;:::-;;:::i;7611:1015::-;796:60;966:10:5;1839:14:117;1895:7;:5;:7::i;:::-;1917:37;;-1:-1:-1;;;1917:37:117;;-1:-1:-1;;;;;2463:32:192;;;1917:37:117;;;2445:51:192;1878:24:117;;-1:-1:-1;1917:29:117;;;;;;2418:18:192;;1917:37:117;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1916:38;:83;;;;-1:-1:-1;1959:40:117;;-1:-1:-1;;;1959:40:117;;;;;5223:25:192;;;-1:-1:-1;;;;;5284:32:192;;;5264:18;;;5257:60;1959:26:117;;;;;5196:18:192;;1959:40:117;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1958:41;1916:83;1912:132;;;2022:11;;-1:-1:-1;;;2022:11:117;;;;;;;;;;;1912:132;7733:28:::1;7764:21;:19;:21::i;:::-;-1:-1:-1::0;;;;;7824:22:117;::::1;7795:26;7824:22:::0;;;:15:::1;::::0;::::1;:22;::::0;;;;;;;;7885:15:::1;::::0;::::1;:22:::0;;;;;;7733:52;;-1:-1:-1;7824:22:117;7885;7945:28:::1;7967:6:::0;7824:22;7945:28:::1;:::i;:::-;7917:56;;7983:25;8011:42;8027:5;8034:18;8011:15;:42::i;:::-;7983:70:::0;-1:-1:-1;8063:13:117::1;8079:40;8100:19:::0;7983:70;8079:40:::1;:::i;:::-;8063:56;;8129:24;8175:6;8156:1;:16;;;:25;;;;:::i;:::-;8129:52;;8204:1;8195:6;:10;:75;;;;-1:-1:-1::0;8252:18:117;;;;:12:::1;::::0;::::1;8209:20:::0;:40:::1;::::0;8232:17;;8209:40:::1;:::i;:::-;:61;8195:75;8191:188;;;8307:12;::::0;::::1;:20:::0;:40:::1;::::0;8330:17;;8307:40:::1;:::i;:::-;8349:18:::0;;;;8293:75:::1;::::0;-1:-1:-1;;;8293:75:117;;::::1;::::0;::::1;6056:25:192::0;;;;6097:18;;;6090:34;6029:18;;8293:75:117::1;;;;;;;;8191:188;-1:-1:-1::0;;;;;8388:22:117;::::1;;::::0;;;:15:::1;::::0;::::1;:22;::::0;;;;;;;:43;;;8441:15:::1;::::0;::::1;:22:::0;;;;;;:43;;;8494:16:::1;::::0;::::1;:36:::0;;;8545:74;;6331:25:192;;;6372:18;;;6365:34;;;6415:18;;;6408:34;;;8545:74:117::1;::::0;6319:2:192;6304:18;8545:74:117::1;;;;;;;7723:903;;;;;;;1829:232:::0;;7611:1015;;;:::o;2470:113::-;-1:-1:-1;;;;;;;;;;;;;;;;;2544:21:117;:19;:21::i;:::-;2537:39;;;;;;;;;2544:32;;;2537:39;;;;;;;;;;;;;2470:113;-1:-1:-1;2470:113:117:o;2948:133::-;3007:6;3032:21;:19;:21::i;:::-;-1:-1:-1;;;;;3032:42:117;;;;;;;:35;;;;;:42;;-1:-1:-1;3032:42:117;;;;;2948:133::o;3120:146::-;-1:-1:-1;;;;;;;;;;;;;;;;;3213:21:117;:19;:21::i;:::-;-1:-1:-1;;;;;3213:46:117;;;;;;;:36;;;;;:46;;;;;;;;;3206:53;;;;;;;;;;;;;;;;;;;-1:-1:-1;3206:53:117;3120:146::o;5266:178::-;4158:30:3;4191:26;:24;:26::i;:::-;4302:15;;4158:59;;-1:-1:-1;4302:15:3;-1:-1:-1;;;4302:15:3;;;4301:16;;4348:14;;4279:19;4724:16;;:34;;;;;4744:14;4724:34;4704:54;;4768:17;4788:11;:16;;4803:1;4788:16;:50;;;;-1:-1:-1;4816:4:3;4808:25;:30;4788:50;4768:70;;4854:12;4853:13;:30;;;;;4871:12;4870:13;4853:30;4849:91;;;4906:23;;-1:-1:-1;;;4906:23:3;;;;;;;;;;;4849:91;4949:18;;-1:-1:-1;;4949:18:3;4966:1;4949:18;;;4977:67;;;;5011:22;;-1:-1:-1;;;;5011:22:3;-1:-1:-1;;;5011:22:3;;;4977:67;5379:26:117::1;::::0;;::::1;5390:4:::0;5379:26:::1;:::i;:::-;5338:21;:19;:21::i;:::-;:38:::0;;:67;5420:17:::1;::::0;::::1;::::0;::::1;::::0;5432:4;;;;5420:17:::1;:::i;:::-;;;;;;;;5068:14:3::0;5064:101;;;5098:23;;-1:-1:-1;;;;5098:23:3;;;5140:14;;-1:-1:-1;6999:50:192;;5140:14:3;;6987:2:192;6972:18;5140:14:3;;;;;;;5064:101;4092:1079;;;;;5266:178:117;;:::o;3493:166::-;3571:7;3597:55;3646:5;3597:21;:19;:21::i;:::-;-1:-1:-1;;;;;3597:45:117;;;;;;:35;;;;;:45;;;;;;:48;:55::i;:::-;3590:62;;3493:166;;;;;:::o;2622:115::-;2669:6;2694:21;:19;:21::i;:::-;:36;;;2687:43;;2622:115;:::o;5450:338::-;-1:-1:-1;;;;;5507:20:117;;5503:69;;5550:11;;-1:-1:-1;;;5550:11:117;;;;;;;;;;;5503:69;5581:28;5612:21;:19;:21::i;:::-;5647:7;;5581:52;;-1:-1:-1;;;;;;5647:7:117;:21;5643:82;;5691:23;;-1:-1:-1;;;5691:23:117;;;;;;;;;;;5643:82;5734:16;;-1:-1:-1;;;;;;5734:16:117;-1:-1:-1;;;;;5734:16:117;;;;;;;5765;;;;-1:-1:-1;;5765:16:117;5493:295;5450:338;:::o;4523:676::-;4597:13;4622:28;4653:21;:19;:21::i;:::-;-1:-1:-1;;;;;4689:25:117;;;;;;:15;;;:25;;;;;4622:52;;-1:-1:-1;4689:41:117;;4724:5;4689:34;:41::i;:::-;4684:81;;4753:1;4746:8;;;;;4684:81;-1:-1:-1;;;;;4796:26:117;;4774:19;4796:26;;;:16;;;:26;;;;;4862:13;;4848:11;;;;4796:26;;4774:19;4848:27;;4862:13;4848:27;:::i;:::-;4832:43;;4899:1;4889:6;:11;4885:50;;4923:1;4916:8;;;;;;;4885:50;4996:7;;4983:30;;;-1:-1:-1;;;4983:30:117;;;;4944:36;;-1:-1:-1;;;;;4996:7:117;;4983:28;;:30;;;;;;;;;;;;;;4996:7;4983:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:47;;-1:-1:-1;;;4983:47:117;;-1:-1:-1;;;;;2463:32:192;;;4983:47:117;;;2445:51:192;4983:40:117;;;;;;;2418:18:192;;4983:47:117;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4944:86;;5044:6;:19;;;:43;;;-1:-1:-1;5067:15:117;;-1:-1:-1;;;;;5067:20:117;;5044:43;5040:82;;;5110:1;5103:8;;;;;;;;5040:82;5138:54;5158:6;5167:7;5176:6;:15;;;-1:-1:-1;;;;;5138:54:117;:11;:54::i;:::-;5131:61;4523:676;-1:-1:-1;;;;;;;4523:676:117:o;2100:194::-;2191:42;;-1:-1:-1;;;2191:42:117;;-1:-1:-1;;;;;2463:32:192;;;2191:42:117;;;2445:51:192;2191:32:117;;;;;2418:18:192;;2191:42:117;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2186:102;;2256:21;;-1:-1:-1;;;2256:21:117;;-1:-1:-1;;;;;2463:32:192;;2256:21:117;;;2445:51:192;2418:18;;2256:21:117;2299:203:192;2186:102:117;2100:194;;:::o;8665:572::-;914:59;966:10:5;1839:14:117;1895:7;:5;:7::i;:::-;1917:37;;-1:-1:-1;;;1917:37:117;;-1:-1:-1;;;;;2463:32:192;;;1917:37:117;;;2445:51:192;1878:24:117;;-1:-1:-1;1917:29:117;;;;;;2418:18:192;;1917:37:117;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1916:38;:83;;;;-1:-1:-1;1959:40:117;;-1:-1:-1;;;1959:40:117;;;;;5223:25:192;;;-1:-1:-1;;;;;5284:32:192;;;5264:18;;;5257:60;1959:26:117;;;;;5196:18:192;;1959:40:117;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1958:41;1916:83;1912:132;;;2022:11;;-1:-1:-1;;;2022:11:117;;;;;;;;;;;1912:132;8785:13:::1;8801:30;8817:5;8824:6;8801:15;:30::i;:::-;8785:46;;8841:28;8872:21;:19;:21::i;:::-;8841:52;;8903:17;8946:6;8923:1;:12;;:20;;;:29;;;;:::i;:::-;8903:49;;8975:1;8966:6;:10;:64;;;;-1:-1:-1::0;9012:18:117;;;;8993:16:::1;::::0;::::1;::::0;8980:29:::1;::::0;:10;:29:::1;:::i;:::-;:50;8966:64;8962:166;;;9080:16;::::0;::::1;::::0;9067:29:::1;::::0;:10;:29:::1;:::i;:::-;9098:18:::0;;;;9053:64:::1;::::0;-1:-1:-1;;;9053:64:117;;::::1;::::0;::::1;6056:25:192::0;;;;6097:18;;;6090:34;6029:18;;9053:64:117::1;5886:244:192::0;8962:166:117::1;9137:12;::::0;::::1;:33:::0;;;9185:45:::1;::::0;;6056:25:192;;;6112:2;6097:18;;6090:34;;;-1:-1:-1;;;;;9185:45:117;::::1;::::0;::::1;::::0;6029:18:192;9185:45:117::1;;;;;;;8775:462;;;1829:232:::0;;8665:572;;;:::o;3698:169::-;3776:4;3799:61;3854:5;3799:21;:19;:21::i;:::-;-1:-1:-1;;;;;3799:45:117;;;;;;:35;;;;;:45;;;;;;:54;:61::i;5827:317::-;428:57;1431:7;:5;:7::i;:::-;-1:-1:-1;;;;;1420:27:117;;1448:4;966:10:5;1420:47:117;;-1:-1:-1;;;;;;1420:47:117;;;;;;;;;;5223:25:192;;;;-1:-1:-1;;;;;5284:32:192;5264:18;;;5257:60;5196:18;;1420:47:117;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1415:97;;1490:11;;-1:-1:-1;;;1490:11:117;;;;;;;;;;;1415:97;5938:28:::1;5969:21;:19;:21::i;:::-;6021:7:::0;;5938:52;;-1:-1:-1;6000:39:117::1;::::0;-1:-1:-1;;;;;6021:7:117::1;6030:8:::0;6000:20:::1;:39::i;:::-;-1:-1:-1::0;;;;;6049:26:117;::::1;;::::0;;;:16:::1;::::0;::::1;:26;::::0;;;;;;:32:::1;;:40:::0;;;6104:33;::::1;::::0;::::1;::::0;6084:5;160:25:192;;148:2;133:18;;14:177;6104:33:117::1;;;;;;;;5928:216;5827:317:::0;;;:::o;3305:149::-;3367:7;3393:54;:21;:19;:21::i;:::-;-1:-1:-1;;;;;3393:45:117;;;;;;:35;;;;;:45;;;;;:52;:54::i;7397:175::-;318:54;1431:7;:5;:7::i;:::-;-1:-1:-1;;;;;1420:27:117;;1448:4;966:10:5;1420:47:117;;-1:-1:-1;;;;;;1420:47:117;;;;;;;;;;5223:25:192;;;;-1:-1:-1;;;;;5284:32:192;5264:18;;;5257:60;5196:18;;1420:47:117;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1415:97;;1490:11;;-1:-1:-1;;;1490:11:117;;;;;;;;;;;1415:97;7525:5:::1;7484:21;:19;:21::i;:::-;:38:::0;;:46;7545:20:::1;::::0;160:25:192;;;7545:20:117::1;::::0;148:2:192;133:18;7545:20:117::1;;;;;;;7397:175:::0;;:::o;3906:578::-;3981:13;4006:28;4037:21;:19;:21::i;:::-;4006:52;;4068:36;4120:1;:7;;;;;;;;;;-1:-1:-1;;;;;4120:7:117;-1:-1:-1;;;;;4107:28:117;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:47;;-1:-1:-1;;;4107:47:117;;-1:-1:-1;;;;;2463:32:192;;;4107:47:117;;;2445:51:192;4107:40:117;;;;;;;2418:18:192;;4107:47:117;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4068:86;;4168:6;:19;;;:43;;;-1:-1:-1;4191:15:117;;-1:-1:-1;;;;;4191:20:117;;4168:43;4164:96;;;4234:15;;-1:-1:-1;;;4234:15:117;;;;;;;;;;;4164:96;4281:1;4273:5;:9;4269:209;;;4313:54;4333:6;4334:5;4333:6;:::i;:::-;4342:15;;-1:-1:-1;;;;;4313:54:117;4359:7;4313:11;:54::i;:::-;4305:63;;;:::i;:::-;4298:70;;;;;;4269:209;4413:53;4433:5;4441:6;:15;;;-1:-1:-1;;;;;4413:53:117;4458:7;4413:11;:53::i;4269:209::-;3996:488;;3906:578;;;;:::o;6183:564::-;544:60;1431:7;:5;:7::i;:::-;-1:-1:-1;;;;;1420:27:117;;1448:4;966:10:5;1420:47:117;;-1:-1:-1;;;;;;1420:47:117;;;;;;;;;;5223:25:192;;;;-1:-1:-1;;;;;5284:32:192;5264:18;;;5257:60;5196:18;;1420:47:117;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1415:97;;1490:11;;-1:-1:-1;;;1490:11:117;;;;;;;;;;;1415:97;6333:28:::1;6364:21;:19;:21::i;:::-;6416:7:::0;;6333:52;;-1:-1:-1;6395:39:117::1;::::0;-1:-1:-1;;;;;6416:7:117::1;6425:8:::0;6395:20:::1;:39::i;:::-;-1:-1:-1::0;;;;;6487:25:117;::::1;6444:40;6487:25:::0;;;:15:::1;::::0;::::1;:25;::::0;;;;;6522:167:::1;6542:17:::0;;::::1;6522:167;;;6585:22;6597:6;;6604:1;6597:9;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;6585:7:::0;;:11:::1;:22::i;:::-;6580:99;;6654:6;;6661:1;6654:9;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;6634:30;::::0;-1:-1:-1;;;;;;6634:30:117;;-1:-1:-1;;;;;2463:32:192;;;6634:30:117::1;::::0;::::1;2445:51:192::0;2418:18;;6634:30:117::1;2299:203:192::0;6580:99:117::1;6561:3;;6522:167;;;;6723:8;-1:-1:-1::0;;;;;6703:37:117::1;;6733:6;;6703:37;;;;;;;:::i;:::-;;;;;;;;6323:424;;6183:564:::0;;;;:::o;2776:133::-;2835:6;2860:21;:19;:21::i;:::-;-1:-1:-1;;;;;2860:42:117;;;;;;;:35;;;;;:42;;-1:-1:-1;2860:42:117;;;;;2776:133::o;6786:572::-;674:63;1431:7;:5;:7::i;:::-;-1:-1:-1;;;;;1420:27:117;;1448:4;966:10:5;1420:47:117;;-1:-1:-1;;;;;;1420:47:117;;;;;;;;;;5223:25:192;;;;-1:-1:-1;;;;;5284:32:192;5264:18;;;5257:60;5196:18;;1420:47:117;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1415:97;;1490:11;;-1:-1:-1;;;1490:11:117;;;;;;;;;;;1415:97;6942:28:::1;6973:21;:19;:21::i;:::-;7025:7:::0;;6942:52;;-1:-1:-1;7004:39:117::1;::::0;-1:-1:-1;;;;;7025:7:117::1;7034:8:::0;7004:20:::1;:39::i;:::-;-1:-1:-1::0;;;;;7096:25:117;::::1;7053:40;7096:25:::0;;;:15:::1;::::0;::::1;:25;::::0;;;;;7131:166:::1;7151:17:::0;;::::1;7131:166;;;7194:25;7209:6;;7216:1;7209:9;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;7194:7:::0;;:14:::1;:25::i;:::-;7189:98;;7262:6;;7269:1;7262:9;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;7246:26;::::0;-1:-1:-1;;;7246:26:117;;-1:-1:-1;;;;;2463:32:192;;;7246:26:117::1;::::0;::::1;2445:51:192::0;2418:18;;7246:26:117::1;2299:203:192::0;7189:98:117::1;7170:3;;7131:166;;;;7334:8;-1:-1:-1::0;;;;;7311:40:117::1;;7344:6;;7311:40;;;;;;;:::i;2333:98::-:0;2371:7;2397:21;:19;:21::i;:::-;:27;-1:-1:-1;;;;;2397:27:117;;2333:98;-1:-1:-1;2333:98:117:o;9276:781::-;1042:62;966:10:5;1584:14:117;1640:7;:5;:7::i;:::-;1623:24;;1671:6;-1:-1:-1;;;;;1661:16:117;:6;-1:-1:-1;;;;;1661:16:117;;;:61;;;;-1:-1:-1;1682:40:117;;-1:-1:-1;;;1682:40:117;;;;;5223:25:192;;;-1:-1:-1;;;;;5284:32:192;;;5264:18;;;5257:60;1682:26:117;;;;;5196:18:192;;1682:40:117;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1681:41;1661:61;1657:110;;;1745:11;;-1:-1:-1;;;1745:11:117;;;;;;;;;;;1657:110;9440:28:::1;9471:21;:19;:21::i;:::-;9523:7:::0;;9440:52;;-1:-1:-1;9502:39:117::1;::::0;-1:-1:-1;;;;;9523:7:117::1;9532:8:::0;9502:20:::1;:39::i;:::-;-1:-1:-1::0;;;;;9556:25:117;::::1;;::::0;;;:15:::1;::::0;::::1;:25;::::0;;;;:41:::1;::::0;9591:5;9556:34:::1;:41::i;:::-;9551:102;;9620:22;::::0;-1:-1:-1;;;9620:22:117;;-1:-1:-1;;;;;2463:32:192;;9620:22:117::1;::::0;::::1;2445:51:192::0;2418:18;;9620:22:117::1;2299:203:192::0;9551:102:117::1;-1:-1:-1::0;;;;;9684:26:117;::::1;9662:19;9684:26:::0;;;:16:::1;::::0;::::1;:26;::::0;;;;;9736:30:::1;9752:5:::0;9759:6;9736:15:::1;:30::i;:::-;9720:46;;9776:17;9812:6;9796:5;:13;;;:22;;;;:::i;:::-;9776:42;;9841:1;9832:6;:10;:38;;;;;9859:5;:11;;;9846:10;:24;9832:38;9828:114;;;9919:11;::::0;::::1;::::0;9893:38:::1;::::0;-1:-1:-1;;;9893:38:117;;::::1;::::0;9907:10;;9893:38:::1;;6056:25:192::0;;;6112:2;6097:18;;6090:34;6044:2;6029:18;;5886:244;9828:114:117::1;9951:26:::0;;;9992:58:::1;::::0;;6056:25:192;;;6112:2;6097:18;;6090:34;;;-1:-1:-1;;;;;9992:58:117;;::::1;::::0;;;::::1;::::0;::::1;::::0;6029:18:192;9992:58:117::1;;;;;;;9430:627;;;;1574:210:::0;;9276:781;;;;:::o;10090:195::-;10199:23;;10090:195::o;9071:205:3:-;9129:30;;3147:66;9186:27;8819:122;10987:156:72;11061:7;11111:22;11115:3;11127:5;11111:3;:22::i;10284:165::-;-1:-1:-1;;;;;10417:23:72;;10364:4;5006:21;;;:14;;;:21;;;;;;:26;;10387:55;4910:129;7242:3683:67;7324:14;7375:12;7389:11;7404:12;7411:1;7414;7404:6;:12::i;:::-;7374:42;;;;7498:4;7506:1;7498:9;7494:365;;7833:11;7827:3;:17;;;;;:::i;:::-;;7820:24;;;;;;7494:365;7984:4;7969:11;:19;7965:142;;8008:84;5312:5;8028:16;;5311:36;940:4:58;5306:42:67;8008:11;:84::i;:::-;8359:17;8510:11;8507:1;8504;8497:25;8902:12;8932:15;;;8917:31;;9067:22;;;;;9800:1;9781;:15;;9780:21;;10033;;;10029:25;;10018:36;10103:21;;;10099:25;;10088:36;10175:21;;;10171:25;;10160:36;10246:21;;;10242:25;;10231:36;10319:21;;;10315:25;;10304:36;10393:21;;;10389:25;;;10378:36;9309:12;;;;9305:23;;;9330:1;9301:31;8622:18;;;8612:29;;;9416:11;;;;8665:19;;;;9160:14;;;;9409:18;;;;10868:13;;-1:-1:-1;;7242:3683:67;;;;;;:::o;10530:115:72:-;10593:7;10619:19;10627:3;5202:18;;5120:107;9332:150;9402:4;9425:50;9430:3;-1:-1:-1;;;;;9450:23:72;;9425:4;:50::i;9650:156::-;9723:4;9746:53;9754:3;-1:-1:-1;;;;;9774:23:72;;9746:7;:53::i;5569:118::-;5636:7;5662:3;:11;;5674:5;5662:18;;;;;;;;:::i;:::-;;;;;;;;;5655:25;;5569:118;;;;:::o;1027:550:67:-;1088:12;;-1:-1:-1;;1471:1:67;1468;1461:20;1501:9;;;;1549:11;;;1535:12;;;;1531:30;;;;;1027:550;-1:-1:-1;;1027:550:67:o;1776:194:58:-;1881:10;1875:4;1868:24;1918:4;1912;1905:18;1949:4;1943;1936:18;2336:406:72;2399:4;5006:21;;;:14;;;:21;;;;;;2415:321;;-1:-1:-1;2457:23:72;;;;;;;;:11;:23;;;;;;;;;;;;;2639:18;;2615:21;;;:14;;;:21;;;;;;:42;;;;2671:11;;2415:321;-1:-1:-1;2720:5:72;2713:12;;2910:1368;2976:4;3105:21;;;:14;;;:21;;;;;;3141:13;;3137:1135;;3508:18;3529:12;3540:1;3529:8;:12;:::i;:::-;3575:18;;3508:33;;-1:-1:-1;3555:17:72;;3575:22;;3596:1;;3575:22;:::i;:::-;3555:42;;3630:9;3616:10;:23;3612:378;;3659:17;3679:3;:11;;3691:9;3679:22;;;;;;;;:::i;:::-;;;;;;;;;3659:42;;3826:9;3800:3;:11;;3812:10;3800:23;;;;;;;;:::i;:::-;;;;;;;;;;;;:35;;;;3939:25;;;:14;;;:25;;;;;:36;;;3612:378;4068:17;;:3;;:17;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;4171:3;:14;;:21;4186:5;4171:21;;;;;;;;;;;4164:28;;;4214:4;4207:11;;;;;;;3137:1135;4256:5;4249:12;;;;;3137:1135;2982:1296;2910:1368;;;;:::o;196:131:192:-;-1:-1:-1;;;;;271:31:192;;261:42;;251:70;;317:1;314;307:12;251:70;196:131;:::o;332:314::-;399:6;407;460:2;448:9;439:7;435:23;431:32;428:52;;;476:1;473;466:12;428:52;515:9;502:23;534:31;559:5;534:31;:::i;:::-;584:5;636:2;621:18;;;;608:32;;-1:-1:-1;;;332:314:192:o;951:247::-;1010:6;1063:2;1051:9;1042:7;1038:23;1034:32;1031:52;;;1079:1;1076;1069:12;1031:52;1118:9;1105:23;1137:31;1162:5;1137:31;:::i;1383:591::-;1453:6;1461;1514:2;1502:9;1493:7;1489:23;1485:32;1482:52;;;1530:1;1527;1520:12;1482:52;1570:9;1557:23;1599:18;1640:2;1632:6;1629:14;1626:34;;;1656:1;1653;1646:12;1626:34;1694:6;1683:9;1679:22;1669:32;;1739:7;1732:4;1728:2;1724:13;1720:27;1710:55;;1761:1;1758;1751:12;1710:55;1801:2;1788:16;1827:2;1819:6;1816:14;1813:34;;;1843:1;1840;1833:12;1813:34;1888:7;1883:2;1874:6;1870:2;1866:15;1862:24;1859:37;1856:57;;;1909:1;1906;1899:12;1856:57;1940:2;1932:11;;;;;1962:6;;-1:-1:-1;1383:591:192;;-1:-1:-1;;;;1383:591:192:o;2507:388::-;2575:6;2583;2636:2;2624:9;2615:7;2611:23;2607:32;2604:52;;;2652:1;2649;2642:12;2604:52;2691:9;2678:23;2710:31;2735:5;2710:31;:::i;:::-;2760:5;-1:-1:-1;2817:2:192;2802:18;;2789:32;2830:33;2789:32;2830:33;:::i;:::-;2882:7;2872:17;;;2507:388;;;;;:::o;3274:179::-;3332:6;3385:2;3373:9;3364:7;3360:23;3356:32;3353:52;;;3401:1;3398;3391:12;3353:52;-1:-1:-1;3424:23:192;;3274:179;-1:-1:-1;3274:179:192:o;3458:750::-;3553:6;3561;3569;3622:2;3610:9;3601:7;3597:23;3593:32;3590:52;;;3638:1;3635;3628:12;3590:52;3677:9;3664:23;3696:31;3721:5;3696:31;:::i;:::-;3746:5;-1:-1:-1;3802:2:192;3787:18;;3774:32;3825:18;3855:14;;;3852:34;;;3882:1;3879;3872:12;3852:34;3920:6;3909:9;3905:22;3895:32;;3965:7;3958:4;3954:2;3950:13;3946:27;3936:55;;3987:1;3984;3977:12;3936:55;4027:2;4014:16;4053:2;4045:6;4042:14;4039:34;;;4069:1;4066;4059:12;4039:34;4122:7;4117:2;4107:6;4104:1;4100:14;4096:2;4092:23;4088:32;4085:45;4082:65;;;4143:1;4140;4133:12;4082:65;4174:2;4170;4166:11;4156:21;;4196:6;4186:16;;;;;3458:750;;;;;:::o;4213:455::-;4289:6;4297;4305;4358:2;4346:9;4337:7;4333:23;4329:32;4326:52;;;4374:1;4371;4364:12;4326:52;4413:9;4400:23;4432:31;4457:5;4432:31;:::i;:::-;4482:5;-1:-1:-1;4539:2:192;4524:18;;4511:32;4552:33;4511:32;4552:33;:::i;:::-;4213:455;;4604:7;;-1:-1:-1;;;4658:2:192;4643:18;;;;4630:32;;4213:455::o;4673:164::-;4749:13;;4798;;4791:21;4781:32;;4771:60;;4827:1;4824;4817:12;4771:60;4673:164;;;:::o;4842:202::-;4909:6;4962:2;4950:9;4941:7;4937:23;4933:32;4930:52;;;4978:1;4975;4968:12;4930:52;5001:37;5028:9;5001:37;:::i;5328:127::-;5389:10;5384:3;5380:20;5377:1;5370:31;5420:4;5417:1;5410:15;5444:4;5441:1;5434:15;5460:216;5524:9;;;5552:11;;;5499:3;5582:9;;5610:10;;5606:19;;5635:10;;5627:19;;5603:44;5600:70;;;5650:18;;:::i;5681:200::-;5747:9;;;5720:4;5775:9;;5803:10;;5815:12;;;5799:29;5838:12;;;5830:21;;5796:56;5793:82;;;5855:18;;:::i;6453:388::-;6610:2;6599:9;6592:21;6649:6;6644:2;6633:9;6629:18;6622:34;6706:6;6698;6693:2;6682:9;6678:18;6665:48;6762:1;6733:22;;;6757:2;6729:31;;;6722:42;;;;6825:2;6804:15;;;-1:-1:-1;;6800:29:192;6785:45;6781:54;;6453:388;-1:-1:-1;6453:388:192:o;7060:268::-;7147:6;7200:2;7188:9;7179:7;7175:23;7171:32;7168:52;;;7216:1;7213;7206:12;7168:52;7248:9;7242:16;7267:31;7292:5;7267:31;:::i;7333:897::-;7436:6;7489:2;7477:9;7468:7;7464:23;7460:32;7457:52;;;7505:1;7502;7495:12;7457:52;7538:2;7532:9;7580:2;7572:6;7568:15;7649:6;7637:10;7634:22;7613:18;7601:10;7598:34;7595:62;7592:185;;;7699:10;7694:3;7690:20;7687:1;7680:31;7734:4;7731:1;7724:15;7762:4;7759:1;7752:15;7592:185;7793:2;7786:22;7830:16;;-1:-1:-1;;;;;7875:31:192;;7865:42;;7855:70;;7921:1;7918;7911:12;7855:70;7934:21;;8000:2;7985:18;;7979:25;8048:10;8035:24;;8023:37;;8013:65;;8074:1;8071;8064:12;8013:65;8106:2;8094:15;;8087:32;8152:46;8194:2;8179:18;;8152:46;:::i;:::-;8147:2;8135:15;;8128:71;8139:6;7333:897;-1:-1:-1;;;7333:897:192:o;8235:136::-;8270:3;-1:-1:-1;;;8291:22:192;;8288:48;;8316:18;;:::i;:::-;-1:-1:-1;8356:1:192;8352:13;;8235:136::o;8376:127::-;8437:10;8432:3;8428:20;8425:1;8418:31;8468:4;8465:1;8458:15;8492:4;8489:1;8482:15;8508:705;8689:2;8741:21;;;8714:18;;;8797:22;;;8660:4;;8876:6;8850:2;8835:18;;8660:4;8910:277;8924:6;8921:1;8918:13;8910:277;;;8999:6;8986:20;9019:31;9044:5;9019:31;:::i;:::-;-1:-1:-1;;;;;9075:31:192;9063:44;;9162:15;;;;9127:12;;;;9103:1;8939:9;8910:277;;;-1:-1:-1;9204:3:192;8508:705;-1:-1:-1;;;;;;8508:705:192:o;9218:127::-;9279:10;9274:3;9270:20;9267:1;9260:31;9310:4;9307:1;9300:15;9334:4;9331:1;9324:15;9350:128;9417:9;;;9438:11;;;9435:37;;;9452:18;;:::i;9483:127::-;9544:10;9539:3;9535:20;9532:1;9525:31;9575:4;9572:1;9565:15;9599:4;9596:1;9589:15", "linkReferences": {}, "immutableReferences": {"63840": [{"start": 6255, "length": 32}]}}, "methodIdentifiers": {"ALLOW_SUBVAULT_ASSETS_ROLE()": "69a38f0f", "DISALLOW_SUBVAULT_ASSETS_ROLE()": "951287d4", "MODIFY_PENDING_ASSETS_ROLE()": "0123abd0", "MODIFY_SUBVAULT_BALANCE_ROLE()": "3e3b11c4", "MODIFY_VAULT_BALANCE_ROLE()": "4439f891", "SET_SUBVAULT_LIMIT_ROLE()": "a37773f7", "SET_VAULT_LIMIT_ROLE()": "ae76c044", "allowSubvaultAssets(address,address[])": "ca3c9a42", "allowedAssetAt(address,uint256)": "44739f60", "allowedAssets(address)": "a4c1cccb", "convertToShares(address,int256)": "bf78fbc0", "disallowSubvaultAssets(address,address[])": "f26a1ba2", "initialize(bytes)": "439fab91", "isAllowedAsset(address,address)": "939b89b7", "maxDeposit(address,address)": "76016870", "modifyPendingAssets(address,int256)": "0629aa66", "modifySubvaultBalance(address,address,int256)": "fea34d98", "modifyVaultBalance(address,int256)": "82dcf074", "pendingAssets(address)": "d287bba1", "pendingBalance()": "57b4d18e", "pendingShares(address)": "2d9d5336", "requireValidSubvault(address,address)": "7cb8bb32", "setSubvaultLimit(address,int256)": "9481241a", "setVault(address)": "6817031b", "setVaultLimit(int256)": "a9dc69bb", "subvaultState(address)": "36f1409f", "vault()": "fbfa77cf", "vaultState()": "2728f333"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name_\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"version_\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"}],\"name\":\"AlreadyAllowedAsset\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Forbidden\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidReport\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"newValue\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"maxValue\",\"type\":\"int256\"}],\"name\":\"LimitExceeded\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"}],\"name\":\"NotAllowedAsset\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subvault\",\"type\":\"address\"}],\"name\":\"NotSubvault\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZeroValue\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"subvault\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"assets\",\"type\":\"address[]\"}],\"name\":\"AllowSubvaultAssets\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"subvault\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"assets\",\"type\":\"address[]\"}],\"name\":\"DisallowSubvaultAssets\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"initParams\",\"type\":\"bytes\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"change\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"pendingAssetsAfter\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"pendingSharesAfter\",\"type\":\"int256\"}],\"name\":\"ModifyPendingAssets\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"subvault\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"change\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"newBalance\",\"type\":\"int256\"}],\"name\":\"ModifySubvaultBalance\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"shares\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"newBalance\",\"type\":\"int256\"}],\"name\":\"ModifyVaultBalance\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"subvault\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"limit\",\"type\":\"int256\"}],\"name\":\"SetSubvaultLimit\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"vault\",\"type\":\"address\"}],\"name\":\"SetVault\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"limit\",\"type\":\"int256\"}],\"name\":\"SetVaultLimit\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"ALLOW_SUBVAULT_ASSETS_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DISALLOW_SUBVAULT_ASSETS_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MODIFY_PENDING_ASSETS_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MODIFY_SUBVAULT_BALANCE_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MODIFY_VAULT_BALANCE_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SET_SUBVAULT_LIMIT_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SET_VAULT_LIMIT_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subvault\",\"type\":\"address\"},{\"internalType\":\"address[]\",\"name\":\"assets\",\"type\":\"address[]\"}],\"name\":\"allowSubvaultAssets\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subvault\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"allowedAssetAt\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subvault\",\"type\":\"address\"}],\"name\":\"allowedAssets\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"int256\",\"name\":\"value\",\"type\":\"int256\"}],\"name\":\"convertToShares\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"shares\",\"type\":\"int256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subvault\",\"type\":\"address\"},{\"internalType\":\"address[]\",\"name\":\"assets\",\"type\":\"address[]\"}],\"name\":\"disallowSubvaultAssets\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subvault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"}],\"name\":\"isAllowedAsset\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subvault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"}],\"name\":\"maxDeposit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"limit\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"int256\",\"name\":\"change\",\"type\":\"int256\"}],\"name\":\"modifyPendingAssets\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subvault\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"int256\",\"name\":\"change\",\"type\":\"int256\"}],\"name\":\"modifySubvaultBalance\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"int256\",\"name\":\"change\",\"type\":\"int256\"}],\"name\":\"modifyVaultBalance\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"}],\"name\":\"pendingAssets\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pendingBalance\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"}],\"name\":\"pendingShares\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault_\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"subvault\",\"type\":\"address\"}],\"name\":\"requireValidSubvault\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subvault\",\"type\":\"address\"},{\"internalType\":\"int256\",\"name\":\"limit\",\"type\":\"int256\"}],\"name\":\"setSubvaultLimit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"vault_\",\"type\":\"address\"}],\"name\":\"setVault\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int256\",\"name\":\"limit\",\"type\":\"int256\"}],\"name\":\"setVaultLimit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"subvault\",\"type\":\"address\"}],\"name\":\"subvaultState\",\"outputs\":[{\"components\":[{\"internalType\":\"int256\",\"name\":\"balance\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"limit\",\"type\":\"int256\"}],\"internalType\":\"struct IRiskManager.State\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"vault\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"vaultState\",\"outputs\":[{\"components\":[{\"internalType\":\"int256\",\"name\":\"balance\",\"type\":\"int256\"},{\"internalType\":\"int256\",\"name\":\"limit\",\"type\":\"int256\"}],\"internalType\":\"struct IRiskManager.State\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}]},\"events\":{\"Initialized(bytes)\":{\"params\":{\"initParams\":\"The initialization parameters.\"}},\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"}},\"kind\":\"dev\",\"methods\":{\"convertToShares(address,int256)\":{\"params\":{\"asset\":\"Asset being valued\",\"value\":\"Amount in asset units (can be positive or negative)\"},\"returns\":{\"shares\":\"Share amount\"}},\"initialize(bytes)\":{\"params\":{\"initParams\":\"The initialization parameters.\"}}},\"version\":1},\"userdoc\":{\"errors\":{\"AlreadyAllowedAsset(address)\":[{\"notice\":\"Thrown when attempting to allow an already allowed asset\"}],\"Forbidden()\":[{\"notice\":\"Thrown when the caller lacks appropriate permission\"}],\"InvalidReport()\":[{\"notice\":\"Thrown when a price report is flagged as suspicious, or has not been set yet.\"}],\"LimitExceeded(int256,int256)\":[{\"notice\":\"Thrown when a vault or subvault exceeds its configured limit\"}],\"NotAllowedAsset(address)\":[{\"notice\":\"Thrown when attempting to disallow or use a non-allowed asset\"}],\"NotSubvault(address)\":[{\"notice\":\"Thrown when a given address is not recognized as a valid subvault\"}],\"ZeroValue()\":[{\"notice\":\"Thrown when a zero address is passed as a parameter\"}]},\"events\":{\"AllowSubvaultAssets(address,address[])\":{\"notice\":\"Emitted when assets are newly allowed for a subvault\"},\"DisallowSubvaultAssets(address,address[])\":{\"notice\":\"Emitted when assets are disallowed from a subvault\"},\"Initialized(bytes)\":{\"notice\":\"Emitted once the entity has been initialized.\"},\"ModifyPendingAssets(address,int256,int256,int256)\":{\"notice\":\"Emitted when pending asset balances are updated\"},\"ModifySubvaultBalance(address,address,int256,int256)\":{\"notice\":\"Emitted when a subvault's balance is changed\"},\"ModifyVaultBalance(address,int256,int256)\":{\"notice\":\"Emitted when the vault balance is changed\"},\"SetSubvaultLimit(address,int256)\":{\"notice\":\"Emitted when a limit is set for a specific subvault\"},\"SetVault(address)\":{\"notice\":\"Emitted when the associated vault address is set\"},\"SetVaultLimit(int256)\":{\"notice\":\"Emitted when the vault limit is updated\"}},\"kind\":\"user\",\"methods\":{\"allowSubvaultAssets(address,address[])\":{\"notice\":\"Allows specific assets to be used in a subvault\"},\"allowedAssetAt(address,uint256)\":{\"notice\":\"Returns the allowed asset at a given index for a subvault\"},\"allowedAssets(address)\":{\"notice\":\"Returns number of assets allowed for a given subvault\"},\"convertToShares(address,int256)\":{\"notice\":\"Converts an asset amount into its equivalent share representation by the last oracle report\"},\"disallowSubvaultAssets(address,address[])\":{\"notice\":\"Disallows specific assets from being used in a subvault\"},\"initialize(bytes)\":{\"notice\":\"Initializes the factory-created entity with arbitrary initialization data.\"},\"isAllowedAsset(address,address)\":{\"notice\":\"Checks if an asset is allowed for the specified subvault\"},\"maxDeposit(address,address)\":{\"notice\":\"Returns the maximum amount that can be deposited into a subvault for a specific asset\"},\"modifyPendingAssets(address,int256)\":{\"notice\":\"Modifies the vault's pending balances by a signed delta (in asset terms)\"},\"modifySubvaultBalance(address,address,int256)\":{\"notice\":\"Modifies a subvault's internal balance by a signed delta (in asset terms)\"},\"modifyVaultBalance(address,int256)\":{\"notice\":\"Modifies the vault's internal balance by a signed delta (in asset terms)\"},\"pendingAssets(address)\":{\"notice\":\"Returns the pending asset value for a specific asset\"},\"pendingBalance()\":{\"notice\":\"Returns the pending share balance across all assets and deposit queues.\"},\"pendingShares(address)\":{\"notice\":\"Returns the pending shares equivalent of a specific asset converted by the last oracle report for the given asset.\"},\"requireValidSubvault(address,address)\":{\"notice\":\"Reverts if the given subvault is not valid for the vault\"},\"setSubvaultLimit(address,int256)\":{\"notice\":\"Sets the maximum allowable approximate (soft) balance for a specific subvault\"},\"setVault(address)\":{\"notice\":\"Sets the vault address this RiskManager is associated with\"},\"setVaultLimit(int256)\":{\"notice\":\"Sets the maximum allowable approximate (soft) balance for the entire vault in shares\"},\"subvaultState(address)\":{\"notice\":\"Returns the approximate balance and the limit of a specific subvault\"},\"vault()\":{\"notice\":\"Returns the address of the Vault\"},\"vaultState()\":{\"notice\":\"Returns the approximate share balance and the share limit limit of the vault.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/managers/RiskManager.sol\":\"RiskManager\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/contracts/access/AccessControlUpgradeable.sol\":{\"keccak256\":\"0x0d4d7d19a052a2ef9d17b28450133631188b895e1755747fa8ad0280aadfb534\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://838551e10cc07e570b70aee1078e490658d3a0ab8f4adfd4f3f2a565200753da\",\"dweb:/ipfs/QmceT7R3Pqzywyxr3MSEA7VXU4axZAPS6BLy92iV97rGdG\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol\":{\"keccak256\":\"0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6\",\"dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/access/extensions/AccessControlEnumerableUpgradeable.sol\":{\"keccak256\":\"0xaec38804089a16494f7d45ebbbeab78fe05e5dc4bc2eae1d2af458fe999fb43c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://04796408e2c4a1051afb23319e9505edb595a3d9c663ff5d6fcfb8e185ab1d93\",\"dweb:/ipfs/Qma8ZTi4kGyqg45SyNibbJSqMF7broiQnJPHhEGLcZyF9U\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol\":{\"keccak256\":\"0x361126a17677994081cd9cb69c3f50cffff6e920d25cb7e428acdb1ae41d1866\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://19ae787a7dd001269cd60a394b1a5261b78925a0fc3a6f927beb2986a9aa56cf\",\"dweb:/ipfs/QmYLfXiuKmcRgTDBEDXMMjXU8t6JxsspUmjxYzqWS55oEv\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/introspection/ERC165Upgradeable.sol\":{\"keccak256\":\"0xc8ed8d2056934b7675b695dec032f2920c2f5c6cf33a17ca85650940675323ab\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3c8ccc75d1cd792d192aa09e54dd49ea35fe85baa9fcd17486f29227d9f29b89\",\"dweb:/ipfs/QmbboSbFUEiM9tdEgBwuTRb7bykFoJXZ7dsSr1PSREJXMr\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0\",\"dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/extensions/IAccessControlEnumerable.sol\":{\"keccak256\":\"0xca774fbe0568762efdc1a7cba31f09549c7fa96dbe97410f4843fa2f0bc000a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0187ffdbf3d61b6d86cba4fcd9826e53d876987d620533ee84c681bdaf0f3ba3\",\"dweb:/ipfs/QmVJDqdJv6uzHY7ifncfv2QJep8XTzS3bGb4s5Exhuv86m\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol\":{\"keccak256\":\"0xb25a4f11fa80c702bf5cd85adec90e6f6f507f32f4a8e6f5dbc31e8c10029486\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6917f8a323e7811f041aecd4d9fd6e92455a6fba38a797ac6f6e208c7912b79d\",\"dweb:/ipfs/QmShuYv55wYHGi4EFkDB8QfF7ZCHoKk2efyz3AWY1ExSq7\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol\":{\"keccak256\":\"0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049\",\"dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol\":{\"keccak256\":\"0x8decfa54cec979c824b044b8128cd91d713f72c71fd7dfa54974624d8c949898\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://271f914261a19d87117a777e0924ada545c16191ef9b00cc40b0134fc14ebc70\",\"dweb:/ipfs/QmdvVNWHGHQrGGPonZJs5NuzTevTjZRM2zayKrDJf7WBA2\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/Proxy.sol\":{\"keccak256\":\"0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac\",\"dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol\":{\"keccak256\":\"0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa\",\"dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol\":{\"keccak256\":\"0x46f86003755f50eff00a7c5aaf493ae62e024142b8aec4493a313851d3c14872\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://462c770cb667cc75ab22df5a29c50873b24b267274edf87ac5cfe0112bf4c3f7\",\"dweb:/ipfs/QmTfKk32AkQ2yyYZCYJ44V69EM5t9ryBFC6bRF7FVMHXvj\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol\":{\"keccak256\":\"0x92579f452fe663595a898cbac85d80bb3868a6c9f034f19ba7fbebdfa3b65a4d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d07a888cd4a26c7077a30dd99e9ac30fbe2752bfb37e58c65f06a3b192079ef3\",\"dweb:/ipfs/QmZCQY4FC4ynBv9dha4BBWM1vYxTUJPBdFBS4HEFe6XzVB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol\":{\"keccak256\":\"0xb5afb8e8eebc4d1c6404df2f5e1e6d2c3d24fd01e5dfc855314951ecfaae462d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://78586466c424f076c6a2a551d848cfbe3f7c49e723830807598484a1047b3b34\",\"dweb:/ipfs/Qmb717ovcFxm7qgNKEShiV6M9SPR3v1qnNpAGH84D6w29p\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0xaaa1d17c1129b127a4a401db2fbd72960e2671474be3d08cae71ccdc42f7624c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cb2f27cd3952aa667e198fba0d9b7bcec52fbb12c16f013c25fe6fb52b29cc0e\",\"dweb:/ipfs/QmeuohBFoeyDPZA9JNCTEDz3VBfBD4EABWuWXVhHAuEpKR\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol\":{\"keccak256\":\"0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd\",\"dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol\":{\"keccak256\":\"0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd\",\"dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Errors.sol\":{\"keccak256\":\"0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf\",\"dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol\":{\"keccak256\":\"0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2\",\"dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/Hashes.sol\":{\"keccak256\":\"0x8cbd338f083224b4b6f0ff42cbda641a0a6c31ffcdca197452b97fe4d0918269\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f517dec5ba0c6491395acbf7f1d621f4e89e8f218bf5303c867b1c5ad70c6b11\",\"dweb:/ipfs/QmWmXHRLEw8W6ckth7NyYTU88YfvuS7xSsfae5ksL8qNUe\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/MerkleProof.sol\":{\"keccak256\":\"0x36a0c409c437a753cac9b92b75f93b0fbe92803bf2c8ff1517e54b247f166134\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f91ba472de411aa557cdbf6560c40750d87bd11c9060bc04d2ba7119af9d5a6\",\"dweb:/ipfs/QmQjtYo2i7dDvzCEzZ67bDoNSG4RrwMoxPWuqFmX5Xzpuw\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621\",\"dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol\":{\"keccak256\":\"0x93ce66a307d66cb14e95dbc8f20641cedf48b2d060bf3bda431d62a4493e6e8d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://10d4122ee13fd8dcd2f468a86031665345d2dfdda76993d1f51201bdeb746f9d\",\"dweb:/ipfs/QmWxbbYEgyDCjwT4ZtNwujJwB4vdnR2qEKwL2Zrz9JoSDU\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableMap.sol\":{\"keccak256\":\"0x5d0c69ef32eae686acc22acfb1a54df9c91356e69adec593aa69eba83ae8f6f4\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f7a9fca2cb3d802c4f8a2d0fda84ccc7f880264885801c7df08373501dd22282\",\"dweb:/ipfs/QmdtM2Loz9NzNTuQ3RXNQCPBjoPUJw35Ukpt1MSM9XH3cD\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol\":{\"keccak256\":\"0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231\",\"dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y\"]},\"src/interfaces/factories/IFactory.sol\":{\"keccak256\":\"0x838a308272620b2ae6fdf1f5b425f8ba1439ed24a7baad50599d3fb34eb0bfa7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://7a801db63324ec50da072b69c67ca98eb89d026f01a4aa7e8f60d757bc82f86d\",\"dweb:/ipfs/QmaJ6WHAe8vfwX3A8yHyLW2aJKqkbQCKuthRg1QU2f3TuY\"]},\"src/interfaces/factories/IFactoryEntity.sol\":{\"keccak256\":\"0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812\",\"dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx\"]},\"src/interfaces/hooks/IHook.sol\":{\"keccak256\":\"0xf414604a4cbf1feb04f976a806eb2825fa7dcce6815e209fc7aa50c6fe845bb3\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://a727a899804756179d294bfb0975487e8c6ca11f68aa741604d400ec6dfbf241\",\"dweb:/ipfs/QmRqsAigFe71aHAG5SgyKQKVpnHoiVyiP6VmnkxFK9KRz9\"]},\"src/interfaces/hooks/IRedeemHook.sol\":{\"keccak256\":\"0xd3df4e8013847ea38f603529d1062e6fccfc757ea62339bfbdd72719d2f7fbb1\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://cddd2d10f020fa96f03b728dfcf8c7874942a19d871414010437ca3cdaef2974\",\"dweb:/ipfs/QmUZwodA14vQg3ntpzF6YxVQ2JCDmjXgQKrzE6HzJ19Mq8\"]},\"src/interfaces/managers/IFeeManager.sol\":{\"keccak256\":\"0x37d3c06ed6b23e367216c2dfc14f99f3fd6d686c984eca124406c2002e4f1516\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://5b3b1b974856f76c6fe2347895ab05e973c84c963fdf8f5483c37e2e4d434cad\",\"dweb:/ipfs/QmVeLCrCWgeQ3aS4V38y9FszFvZdSkbPHKKCMWKKM3V4qw\"]},\"src/interfaces/managers/IRiskManager.sol\":{\"keccak256\":\"0xb0dda9606a25e8187fb20cff7eb99dfecde18fbea30bae52a2124fd22afda97a\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://fee1bae75e2154b21e5ea2382ff21cd3e2ff2ea3a5e67828cfe4b563ee86e14e\",\"dweb:/ipfs/QmUtFozDVfCzehRMYxhJwZAbLJ43afzzVQDA89JD1HG1YH\"]},\"src/interfaces/managers/IShareManager.sol\":{\"keccak256\":\"0xb75fcb890e9a3e706c0d87781700965aa24faa8461967ee62b140d45f4c399e8\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ae50d1dda35eb9f6fa035fc39c2424f2e2eb2d41e6b0ffed296719818b1d9502\",\"dweb:/ipfs/Qme5jPDZG3WTLYtT2s8VFs3MKJVXgL9T5AdAzoXuuqM79z\"]},\"src/interfaces/modules/IACLModule.sol\":{\"keccak256\":\"0xf6ce9b4f4af5475d152845d6afa6f57b2d376c3a6d3eec2b5ac9650901a78243\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://f851cef38b6188b20be98c95047c8bd9d8651d37421a54e1d02b60f9f372c5ed\",\"dweb:/ipfs/QmaymGDd2RoKweuASXCiV3rPuhwvLXUvb6wBgi8xXoiYT8\"]},\"src/interfaces/modules/IBaseModule.sol\":{\"keccak256\":\"0x9c5c95ceb39f4c39f81f2afc7ddb55389a21315e76047a9cf66521db4dea68c3\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ccfb0453b14b105cb5990978a49c1e08f812563a046e17c354f4f1c648f95fe7\",\"dweb:/ipfs/QmYMAKaVdRq3NoTMfqadxgMMmUq6ejzTw2Lpsa3e9a3eyj\"]},\"src/interfaces/modules/IShareModule.sol\":{\"keccak256\":\"0x65ef35a38441080ad4e0c77c7668d6717fe9699df679ed4d2c775f47d1dce57a\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://e28e0e9e2dd2097df748fa2015ce4582f489615040b5323094880a618c54edac\",\"dweb:/ipfs/QmXNvxJDQ6edM3ExmmiTEmS5UreWYTDBgaAygU2kM6mgC6\"]},\"src/interfaces/modules/ISubvaultModule.sol\":{\"keccak256\":\"0x8db6f1a3bd2506dd0db82f4138f0436602ed37487717095a804dee81e44f5e0e\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://2547472f59c19612916f3f54e6fa69644ec31f14c669aa66dea3c006bc71bccc\",\"dweb:/ipfs/QmZ9VRCsofVntdB7siM3pfypUpgJnmaJAC1n8HviQb3K3U\"]},\"src/interfaces/modules/IVaultModule.sol\":{\"keccak256\":\"0x7696792b852bc9b435e6388a19f4284854259df2f50b45e2b833b6f9dd1b1648\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://4364151c043fc5e56bc87c2cdbffc169b80cd745618a4b0da14e271752de6637\",\"dweb:/ipfs/Qmdfx3L8goy8eWFGimtsh58Se1vuLsukUw2mXo5chCbTy2\"]},\"src/interfaces/modules/IVerifierModule.sol\":{\"keccak256\":\"0x8b8ad9ea43b139afccc77ceb0fdcc34615c7401cc13e99c723895302049065a5\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://0d578b1a55a180c652ee24e1b35f6d70d65bc067770dd791db44d0bcfa7a5d38\",\"dweb:/ipfs/QmexSPCfixS9spdjCydR4FUaoN4J188sfLkRmJaia14yk6\"]},\"src/interfaces/oracles/IOracle.sol\":{\"keccak256\":\"0xcbf84560c1fb9f7d3445cbbca4f5f3406606496f30867222d8e3deabad741296\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c90107edcd30e29044a5a8b05e71e728b93a485e7e7ef1a5b4ff695430e33a5e\",\"dweb:/ipfs/QmfHGNxtRQWeq7JENynxZNfhfTBytwbRwyekUyuHWrUyjK\"]},\"src/interfaces/permissions/ICustomVerifier.sol\":{\"keccak256\":\"0xdb343366ed86f63f8a94ff478c5c8e1d127888e4993c27fcb3bb3b51ae0cb8c7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://12407e89b20982a00961ccc03258e353962607747f214940d18b0989c11547c9\",\"dweb:/ipfs/Qmay19iWpwiV8LuvWccPjsazVU5Kkhme9QykFfLKfBmvSr\"]},\"src/interfaces/permissions/IMellowACL.sol\":{\"keccak256\":\"0xcc86b0ce4e4a53c1c4fa16ad79f9de0e2a1f1a77a1f256c0856120a9d6ea36df\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://6942df2ffadacae5563e01ec60697f16116362ba84a856a346b36bc7b9dd1ec2\",\"dweb:/ipfs/QmNYhqZGpS98UP89CS86CjoG4ZWbMHzTHRtd7bZzQqn3UY\"]},\"src/interfaces/permissions/IVerifier.sol\":{\"keccak256\":\"0x4b42731d76efaffcb017577bd2a478d00a3058c69220d6ea8e26f30f74476be7\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://ff2a6f09a946dd8a470dfe495b2a64d24b849c6d071dc859589140fb1b983362\",\"dweb:/ipfs/Qmb6UoqkmaC8tJMqkMfjcWR7pn1WCwPwuKKvUbLBQbPcJS\"]},\"src/interfaces/queues/IDepositQueue.sol\":{\"keccak256\":\"0x4ef38cb59d419010f453e62fe101efec25bec85a2ce1a24048098034060db47b\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://20e51dd02001309f23f46abea409edf77a39522bf21eeffb858ac888e4200e72\",\"dweb:/ipfs/QmWm9gfMuXz8nwuHb3he7abxTRgQw35x2viwMVuiNYAY5b\"]},\"src/interfaces/queues/IQueue.sol\":{\"keccak256\":\"0xd12e34631b204821c77eaa5b2e104902d740e3530cd716f7fc991af01a73b9ab\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://1f2a4d664244be639da1a5025ebccc8c1127e087a81ce6d0fc76af5134fec311\",\"dweb:/ipfs/QmS1GJwxGCV89myCAzLBAbmSrY6YL31G3nGj542dDM4QmT\"]},\"src/interfaces/queues/IRedeemQueue.sol\":{\"keccak256\":\"0xf8f95b95de259c8ccf29976f34d97a0ef31c9827f41ebcd5e8de54c10e646076\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://45ae0d496e865d846bf0c980593c1d16c038abf2b9359906224aa94cccf1c73f\",\"dweb:/ipfs/QmYTDXSTpUqhzJDXSKjoSKxwwQYee7wSTtf617wDsnnCC5\"]},\"src/libraries/FenwickTreeLibrary.sol\":{\"keccak256\":\"0xfd159ed27b96f288c4220febac4bfe286e4545b8ca7f523ae6e63d123341f8a3\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://58f0cc202896722786140571b111bf5e589730358a24b2940a450c4c75d864fe\",\"dweb:/ipfs/QmeRSjvcYURUSpvFe4r4VRwwpHcb1NmqiEUDEJ5rBxAxuf\"]},\"src/libraries/SlotLibrary.sol\":{\"keccak256\":\"0x35dcf16feec83c772add30250b0987ce29ccfbf308d04159701ebf9243444eeb\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://97658d0c93ecc8174169e643d5a24c515b55905a2fcaaa6d6ec0188a3dd71a10\",\"dweb:/ipfs/QmS1z7ac8RbMvz15gVQ9YA1cCT5bs6KMHCiDhgcG8Pq8oK\"]},\"src/managers/RiskManager.sol\":{\"keccak256\":\"0xbb6103cb0865dd4cb99df1cfb050c8e83718b49f08f607c4707d11fcc2027f01\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://d0e102e9a5fc216aa277b5409a7de9f91540ea3bf728c43bbb9c7573dc4dd36a\",\"dweb:/ipfs/QmPRfqdNE4x992gTgrcQGx9haJKR8gM6GJ75WbxTXFhBDc\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "name_", "type": "string"}, {"internalType": "uint256", "name": "version_", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "type": "error", "name": "AlreadyAllowedAsset"}, {"inputs": [], "type": "error", "name": "Forbidden"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [], "type": "error", "name": "InvalidReport"}, {"inputs": [{"internalType": "int256", "name": "newValue", "type": "int256"}, {"internalType": "int256", "name": "maxValue", "type": "int256"}], "type": "error", "name": "LimitExceeded"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "type": "error", "name": "NotAllowedAsset"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [{"internalType": "address", "name": "subvault", "type": "address"}], "type": "error", "name": "NotSubvault"}, {"inputs": [], "type": "error", "name": "ZeroValue"}, {"inputs": [{"internalType": "address", "name": "subvault", "type": "address", "indexed": true}, {"internalType": "address[]", "name": "assets", "type": "address[]", "indexed": false}], "type": "event", "name": "AllowSubvaultAssets", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "subvault", "type": "address", "indexed": true}, {"internalType": "address[]", "name": "assets", "type": "address[]", "indexed": false}], "type": "event", "name": "DisallowSubvaultAssets", "anonymous": false}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "initParams", "type": "bytes", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address", "indexed": true}, {"internalType": "int256", "name": "change", "type": "int256", "indexed": false}, {"internalType": "int256", "name": "pendingAssetsAfter", "type": "int256", "indexed": false}, {"internalType": "int256", "name": "pendingSharesAfter", "type": "int256", "indexed": false}], "type": "event", "name": "ModifyPendingAssets", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "subvault", "type": "address", "indexed": true}, {"internalType": "address", "name": "asset", "type": "address", "indexed": true}, {"internalType": "int256", "name": "change", "type": "int256", "indexed": false}, {"internalType": "int256", "name": "newBalance", "type": "int256", "indexed": false}], "type": "event", "name": "ModifySubvaultBalance", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address", "indexed": true}, {"internalType": "int256", "name": "shares", "type": "int256", "indexed": false}, {"internalType": "int256", "name": "newBalance", "type": "int256", "indexed": false}], "type": "event", "name": "ModifyVaultBalance", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "subvault", "type": "address", "indexed": true}, {"internalType": "int256", "name": "limit", "type": "int256", "indexed": false}], "type": "event", "name": "SetSubvaultLimit", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "vault", "type": "address", "indexed": true}], "type": "event", "name": "<PERSON><PERSON><PERSON>", "anonymous": false}, {"inputs": [{"internalType": "int256", "name": "limit", "type": "int256", "indexed": false}], "type": "event", "name": "SetVaultLimit", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "ALLOW_SUBVAULT_ASSETS_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DISALLOW_SUBVAULT_ASSETS_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "MODIFY_PENDING_ASSETS_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "MODIFY_SUBVAULT_BALANCE_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "MODIFY_VAULT_BALANCE_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SET_SUBVAULT_LIMIT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SET_VAULT_LIMIT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "subvault", "type": "address"}, {"internalType": "address[]", "name": "assets", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "allowSubvaultAssets"}, {"inputs": [{"internalType": "address", "name": "subvault", "type": "address"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "allowedAssetAt", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "subvault", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowedAssets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "int256", "name": "value", "type": "int256"}], "stateMutability": "view", "type": "function", "name": "convertToShares", "outputs": [{"internalType": "int256", "name": "shares", "type": "int256"}]}, {"inputs": [{"internalType": "address", "name": "subvault", "type": "address"}, {"internalType": "address[]", "name": "assets", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "disallowSubvaultAssets"}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "subvault", "type": "address"}, {"internalType": "address", "name": "asset", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isAllowedAsset", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "subvault", "type": "address"}, {"internalType": "address", "name": "asset", "type": "address"}], "stateMutability": "view", "type": "function", "name": "maxDeposit", "outputs": [{"internalType": "uint256", "name": "limit", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "int256", "name": "change", "type": "int256"}], "stateMutability": "nonpayable", "type": "function", "name": "modifyPendingAssets"}, {"inputs": [{"internalType": "address", "name": "subvault", "type": "address"}, {"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "int256", "name": "change", "type": "int256"}], "stateMutability": "nonpayable", "type": "function", "name": "modifySubvaultBalance"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "int256", "name": "change", "type": "int256"}], "stateMutability": "nonpayable", "type": "function", "name": "modifyVaultBalance"}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "stateMutability": "view", "type": "function", "name": "pendingAssets", "outputs": [{"internalType": "int256", "name": "", "type": "int256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "pendingBalance", "outputs": [{"internalType": "int256", "name": "", "type": "int256"}]}, {"inputs": [{"internalType": "address", "name": "asset", "type": "address"}], "stateMutability": "view", "type": "function", "name": "pendingShares", "outputs": [{"internalType": "int256", "name": "", "type": "int256"}]}, {"inputs": [{"internalType": "address", "name": "vault_", "type": "address"}, {"internalType": "address", "name": "subvault", "type": "address"}], "stateMutability": "view", "type": "function", "name": "requireValidSubvault"}, {"inputs": [{"internalType": "address", "name": "subvault", "type": "address"}, {"internalType": "int256", "name": "limit", "type": "int256"}], "stateMutability": "nonpayable", "type": "function", "name": "setSubvaultLimit"}, {"inputs": [{"internalType": "address", "name": "vault_", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "<PERSON><PERSON><PERSON>"}, {"inputs": [{"internalType": "int256", "name": "limit", "type": "int256"}], "stateMutability": "nonpayable", "type": "function", "name": "setVaultLimit"}, {"inputs": [{"internalType": "address", "name": "subvault", "type": "address"}], "stateMutability": "view", "type": "function", "name": "subvaultState", "outputs": [{"internalType": "struct IRiskManager.State", "name": "", "type": "tuple", "components": [{"internalType": "int256", "name": "balance", "type": "int256"}, {"internalType": "int256", "name": "limit", "type": "int256"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "vault", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "vaultState", "outputs": [{"internalType": "struct IRiskManager.State", "name": "", "type": "tuple", "components": [{"internalType": "int256", "name": "balance", "type": "int256"}, {"internalType": "int256", "name": "limit", "type": "int256"}]}]}], "devdoc": {"kind": "dev", "methods": {"convertToShares(address,int256)": {"params": {"asset": "Asset being valued", "value": "Amount in asset units (can be positive or negative)"}, "returns": {"shares": "Share amount"}}, "initialize(bytes)": {"params": {"initParams": "The initialization parameters."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"allowSubvaultAssets(address,address[])": {"notice": "Allows specific assets to be used in a subvault"}, "allowedAssetAt(address,uint256)": {"notice": "Returns the allowed asset at a given index for a subvault"}, "allowedAssets(address)": {"notice": "Returns number of assets allowed for a given subvault"}, "convertToShares(address,int256)": {"notice": "Converts an asset amount into its equivalent share representation by the last oracle report"}, "disallowSubvaultAssets(address,address[])": {"notice": "Disallows specific assets from being used in a subvault"}, "initialize(bytes)": {"notice": "Initializes the factory-created entity with arbitrary initialization data."}, "isAllowedAsset(address,address)": {"notice": "Checks if an asset is allowed for the specified subvault"}, "maxDeposit(address,address)": {"notice": "Returns the maximum amount that can be deposited into a subvault for a specific asset"}, "modifyPendingAssets(address,int256)": {"notice": "Modifies the vault's pending balances by a signed delta (in asset terms)"}, "modifySubvaultBalance(address,address,int256)": {"notice": "Modifies a subvault's internal balance by a signed delta (in asset terms)"}, "modifyVaultBalance(address,int256)": {"notice": "Modifies the vault's internal balance by a signed delta (in asset terms)"}, "pendingAssets(address)": {"notice": "Returns the pending asset value for a specific asset"}, "pendingBalance()": {"notice": "Returns the pending share balance across all assets and deposit queues."}, "pendingShares(address)": {"notice": "Returns the pending shares equivalent of a specific asset converted by the last oracle report for the given asset."}, "requireValidSubvault(address,address)": {"notice": "Reverts if the given subvault is not valid for the vault"}, "setSubvaultLimit(address,int256)": {"notice": "Sets the maximum allowable approximate (soft) balance for a specific subvault"}, "setVault(address)": {"notice": "Sets the vault address this RiskManager is associated with"}, "setVaultLimit(int256)": {"notice": "Sets the maximum allowable approximate (soft) balance for the entire vault in shares"}, "subvaultState(address)": {"notice": "Returns the approximate balance and the limit of a specific subvault"}, "vault()": {"notice": "Returns the address of the Vault"}, "vaultState()": {"notice": "Returns the approximate share balance and the share limit limit of the vault."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/managers/RiskManager.sol": "RiskManager"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/contracts/access/AccessControlUpgradeable.sol": {"keccak256": "0x0d4d7d19a052a2ef9d17b28450133631188b895e1755747fa8ad0280aadfb534", "urls": ["bzz-raw://838551e10cc07e570b70aee1078e490658d3a0ab8f4adfd4f3f2a565200753da", "dweb:/ipfs/QmceT7R3Pqzywyxr3MSEA7VXU4axZAPS6BLy92iV97rGdG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol": {"keccak256": "0xc163fcf9bb10138631a9ba5564df1fa25db9adff73bd9ee868a8ae1858fe093a", "urls": ["bzz-raw://9706d43a0124053d9880f6e31a59f31bc0a6a3dc1acd66ce0a16e1111658c5f6", "dweb:/ipfs/QmUFmfowzkRwGtDu36cXV9SPTBHJ3n7dG9xQiK5B28jTf2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/access/extensions/AccessControlEnumerableUpgradeable.sol": {"keccak256": "0xaec38804089a16494f7d45ebbbeab78fe05e5dc4bc2eae1d2af458fe999fb43c", "urls": ["bzz-raw://04796408e2c4a1051afb23319e9505edb595a3d9c663ff5d6fcfb8e185ab1d93", "dweb:/ipfs/Qma8ZTi4kGyqg45SyNibbJSqMF7broiQnJPHhEGLcZyF9U"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05", "urls": ["bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08", "dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol": {"keccak256": "0x361126a17677994081cd9cb69c3f50cffff6e920d25cb7e428acdb1ae41d1866", "urls": ["bzz-raw://19ae787a7dd001269cd60a394b1a5261b78925a0fc3a6f927beb2986a9aa56cf", "dweb:/ipfs/QmYLfXiuKmcRgTDBEDXMMjXU8t6JxsspUmjxYzqWS55oEv"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/introspection/ERC165Upgradeable.sol": {"keccak256": "0xc8ed8d2056934b7675b695dec032f2920c2f5c6cf33a17ca85650940675323ab", "urls": ["bzz-raw://3c8ccc75d1cd792d192aa09e54dd49ea35fe85baa9fcd17486f29227d9f29b89", "dweb:/ipfs/QmbboSbFUEiM9tdEgBwuTRb7bykFoJXZ7dsSr1PSREJXMr"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530", "urls": ["bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0", "dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/access/extensions/IAccessControlEnumerable.sol": {"keccak256": "0xca774fbe0568762efdc1a7cba31f09549c7fa96dbe97410f4843fa2f0bc000a3", "urls": ["bzz-raw://0187ffdbf3d61b6d86cba4fcd9826e53d876987d620533ee84c681bdaf0f3ba3", "dweb:/ipfs/QmVJDqdJv6uzHY7ifncfv2QJep8XTzS3bGb4s5Exhuv86m"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol": {"keccak256": "0xb25a4f11fa80c702bf5cd85adec90e6f6f507f32f4a8e6f5dbc31e8c10029486", "urls": ["bzz-raw://6917f8a323e7811f041aecd4d9fd6e92455a6fba38a797ac6f6e208c7912b79d", "dweb:/ipfs/QmShuYv55wYHGi4EFkDB8QfF7ZCHoKk2efyz3AWY1ExSq7"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol": {"keccak256": "0xa3066ff86b94128a9d3956a63a0511fa1aae41bd455772ab587b32ff322acb2e", "urls": ["bzz-raw://bf7b192fd82acf6187970c80548f624b1b9c80425b62fa49e7fdb538a52de049", "dweb:/ipfs/QmWXG1YCde1tqDYTbNwjkZDWVgPEjzaQGSDqWkyKLzaNua"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol": {"keccak256": "0x8decfa54cec979c824b044b8128cd91d713f72c71fd7dfa54974624d8c949898", "urls": ["bzz-raw://271f914261a19d87117a777e0924ada545c16191ef9b00cc40b0134fc14ebc70", "dweb:/ipfs/QmdvVNWHGHQrGGPonZJs5NuzTevTjZRM2zayKrDJf7WBA2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/Proxy.sol": {"keccak256": "0xc3f2ec76a3de8ed7a7007c46166f5550c72c7709e3fc7e8bb3111a7191cdedbd", "urls": ["bzz-raw://e73efb4c2ca655882dc237c6b4f234a9bd36d97159d8fcaa837eb01171f726ac", "dweb:/ipfs/QmTNnnv7Gu5fs5G1ZMh7Fexp8N4XUs3XrNAngjcxgiss3e"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol": {"keccak256": "0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c", "urls": ["bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa", "dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/ProxyAdmin.sol": {"keccak256": "0x46f86003755f50eff00a7c5aaf493ae62e024142b8aec4493a313851d3c14872", "urls": ["bzz-raw://462c770cb667cc75ab22df5a29c50873b24b267274edf87ac5cfe0112bf4c3f7", "dweb:/ipfs/QmTfKk32AkQ2yyYZCYJ44V69EM5t9ryBFC6bRF7FVMHXvj"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/proxy/transparent/TransparentUpgradeableProxy.sol": {"keccak256": "0x92579f452fe663595a898cbac85d80bb3868a6c9f034f19ba7fbebdfa3b65a4d", "urls": ["bzz-raw://d07a888cd4a26c7077a30dd99e9ac30fbe2752bfb37e58c65f06a3b192079ef3", "dweb:/ipfs/QmZCQY4FC4ynBv9dha4BBWM1vYxTUJPBdFBS4HEFe6XzVB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC721/IERC721Receiver.sol": {"keccak256": "0xb5afb8e8eebc4d1c6404df2f5e1e6d2c3d24fd01e5dfc855314951ecfaae462d", "urls": ["bzz-raw://78586466c424f076c6a2a551d848cfbe3f7c49e723830807598484a1047b3b34", "dweb:/ipfs/Qmb717ovcFxm7qgNKEShiV6M9SPR3v1qnNpAGH84D6w29p"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0xaaa1d17c1129b127a4a401db2fbd72960e2671474be3d08cae71ccdc42f7624c", "urls": ["bzz-raw://cb2f27cd3952aa667e198fba0d9b7bcec52fbb12c16f013c25fe6fb52b29cc0e", "dweb:/ipfs/QmeuohBFoeyDPZA9JNCTEDz3VBfBD4EABWuWXVhHAuEpKR"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Arrays.sol": {"keccak256": "0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6", "urls": ["bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd", "dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Comparators.sol": {"keccak256": "0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58", "urls": ["bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd", "dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Errors.sol": {"keccak256": "0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123", "urls": ["bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf", "dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol": {"keccak256": "0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26", "urls": ["bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2", "dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/Hashes.sol": {"keccak256": "0x8cbd338f083224b4b6f0ff42cbda641a0a6c31ffcdca197452b97fe4d0918269", "urls": ["bzz-raw://f517dec5ba0c6491395acbf7f1d621f4e89e8f218bf5303c867b1c5ad70c6b11", "dweb:/ipfs/QmWmXHRLEw8W6ckth7NyYTU88YfvuS7xSsfae5ksL8qNUe"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/cryptography/MerkleProof.sol": {"keccak256": "0x36a0c409c437a753cac9b92b75f93b0fbe92803bf2c8ff1517e54b247f166134", "urls": ["bzz-raw://0f91ba472de411aa557cdbf6560c40750d87bd11c9060bc04d2ba7119af9d5a6", "dweb:/ipfs/QmQjtYo2i7dDvzCEzZ67bDoNSG4RrwMoxPWuqFmX5Xzpuw"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8", "urls": ["bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621", "dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol": {"keccak256": "0x93ce66a307d66cb14e95dbc8f20641cedf48b2d060bf3bda431d62a4493e6e8d", "urls": ["bzz-raw://10d4122ee13fd8dcd2f468a86031665345d2dfdda76993d1f51201bdeb746f9d", "dweb:/ipfs/QmWxbbYEgyDCjwT4ZtNwujJwB4vdnR2qEKwL2Zrz9JoSDU"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableMap.sol": {"keccak256": "0x5d0c69ef32eae686acc22acfb1a54df9c91356e69adec593aa69eba83ae8f6f4", "urls": ["bzz-raw://f7a9fca2cb3d802c4f8a2d0fda84ccc7f880264885801c7df08373501dd22282", "dweb:/ipfs/QmdtM2Loz9NzNTuQ3RXNQCPBjoPUJw35Ukpt1MSM9XH3cD"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol": {"keccak256": "0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06", "urls": ["bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231", "dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y"], "license": "MIT"}, "src/interfaces/factories/IFactory.sol": {"keccak256": "0x838a308272620b2ae6fdf1f5b425f8ba1439ed24a7baad50599d3fb34eb0bfa7", "urls": ["bzz-raw://7a801db63324ec50da072b69c67ca98eb89d026f01a4aa7e8f60d757bc82f86d", "dweb:/ipfs/QmaJ6WHAe8vfwX3A8yHyLW2aJKqkbQCKuthRg1QU2f3TuY"], "license": "BUSL-1.1"}, "src/interfaces/factories/IFactoryEntity.sol": {"keccak256": "0xa09fc818b5484f21f20f46535537d82a8df88379bdb4283887010f5dcf3b7bf5", "urls": ["bzz-raw://48202a8213b1c37f6f17f8b85eee5006655746f7cdca32d69616ae9cc1765812", "dweb:/ipfs/QmT1RNDTou59wmw7ByeKiGf6gr9kS3mZwom6its5pkeehx"], "license": "BUSL-1.1"}, "src/interfaces/hooks/IHook.sol": {"keccak256": "0xf414604a4cbf1feb04f976a806eb2825fa7dcce6815e209fc7aa50c6fe845bb3", "urls": ["bzz-raw://a727a899804756179d294bfb0975487e8c6ca11f68aa741604d400ec6dfbf241", "dweb:/ipfs/QmRqsAigFe71aHAG5SgyKQKVpnHoiVyiP6VmnkxFK9KRz9"], "license": "BUSL-1.1"}, "src/interfaces/hooks/IRedeemHook.sol": {"keccak256": "0xd3df4e8013847ea38f603529d1062e6fccfc757ea62339bfbdd72719d2f7fbb1", "urls": ["bzz-raw://cddd2d10f020fa96f03b728dfcf8c7874942a19d871414010437ca3cdaef2974", "dweb:/ipfs/QmUZwodA14vQg3ntpzF6YxVQ2JCDmjXgQKrzE6HzJ19Mq8"], "license": "BUSL-1.1"}, "src/interfaces/managers/IFeeManager.sol": {"keccak256": "0x37d3c06ed6b23e367216c2dfc14f99f3fd6d686c984eca124406c2002e4f1516", "urls": ["bzz-raw://5b3b1b974856f76c6fe2347895ab05e973c84c963fdf8f5483c37e2e4d434cad", "dweb:/ipfs/QmVeLCrCWgeQ3aS4V38y9FszFvZdSkbPHKKCMWKKM3V4qw"], "license": "BUSL-1.1"}, "src/interfaces/managers/IRiskManager.sol": {"keccak256": "0xb0dda9606a25e8187fb20cff7eb99dfecde18fbea30bae52a2124fd22afda97a", "urls": ["bzz-raw://fee1bae75e2154b21e5ea2382ff21cd3e2ff2ea3a5e67828cfe4b563ee86e14e", "dweb:/ipfs/QmUtFozDVfCzehRMYxhJwZAbLJ43afzzVQDA89JD1HG1YH"], "license": "BUSL-1.1"}, "src/interfaces/managers/IShareManager.sol": {"keccak256": "0xb75fcb890e9a3e706c0d87781700965aa24faa8461967ee62b140d45f4c399e8", "urls": ["bzz-raw://ae50d1dda35eb9f6fa035fc39c2424f2e2eb2d41e6b0ffed296719818b1d9502", "dweb:/ipfs/Qme5jPDZG3WTLYtT2s8VFs3MKJVXgL9T5AdAzoXuuqM79z"], "license": "BUSL-1.1"}, "src/interfaces/modules/IACLModule.sol": {"keccak256": "0xf6ce9b4f4af5475d152845d6afa6f57b2d376c3a6d3eec2b5ac9650901a78243", "urls": ["bzz-raw://f851cef38b6188b20be98c95047c8bd9d8651d37421a54e1d02b60f9f372c5ed", "dweb:/ipfs/QmaymGDd2RoKweuASXCiV3rPuhwvLXUvb6wBgi8xXoiYT8"], "license": "BUSL-1.1"}, "src/interfaces/modules/IBaseModule.sol": {"keccak256": "0x9c5c95ceb39f4c39f81f2afc7ddb55389a21315e76047a9cf66521db4dea68c3", "urls": ["bzz-raw://ccfb0453b14b105cb5990978a49c1e08f812563a046e17c354f4f1c648f95fe7", "dweb:/ipfs/QmYMAKaVdRq3NoTMfqadxgMMmUq6ejzTw2Lpsa3e9a3eyj"], "license": "BUSL-1.1"}, "src/interfaces/modules/IShareModule.sol": {"keccak256": "0x65ef35a38441080ad4e0c77c7668d6717fe9699df679ed4d2c775f47d1dce57a", "urls": ["bzz-raw://e28e0e9e2dd2097df748fa2015ce4582f489615040b5323094880a618c54edac", "dweb:/ipfs/QmXNvxJDQ6edM3ExmmiTEmS5UreWYTDBgaAygU2kM6mgC6"], "license": "BUSL-1.1"}, "src/interfaces/modules/ISubvaultModule.sol": {"keccak256": "0x8db6f1a3bd2506dd0db82f4138f0436602ed37487717095a804dee81e44f5e0e", "urls": ["bzz-raw://2547472f59c19612916f3f54e6fa69644ec31f14c669aa66dea3c006bc71bccc", "dweb:/ipfs/QmZ9VRCsofVntdB7siM3pfypUpgJnmaJAC1n8HviQb3K3U"], "license": "BUSL-1.1"}, "src/interfaces/modules/IVaultModule.sol": {"keccak256": "0x7696792b852bc9b435e6388a19f4284854259df2f50b45e2b833b6f9dd1b1648", "urls": ["bzz-raw://4364151c043fc5e56bc87c2cdbffc169b80cd745618a4b0da14e271752de6637", "dweb:/ipfs/Qmdfx3L8goy8eWFGimtsh58Se1vuLsukUw2mXo5chCbTy2"], "license": "BUSL-1.1"}, "src/interfaces/modules/IVerifierModule.sol": {"keccak256": "0x8b8ad9ea43b139afccc77ceb0fdcc34615c7401cc13e99c723895302049065a5", "urls": ["bzz-raw://0d578b1a55a180c652ee24e1b35f6d70d65bc067770dd791db44d0bcfa7a5d38", "dweb:/ipfs/QmexSPCfixS9spdjCydR4FUaoN4J188sfLkRmJaia14yk6"], "license": "BUSL-1.1"}, "src/interfaces/oracles/IOracle.sol": {"keccak256": "0xcbf84560c1fb9f7d3445cbbca4f5f3406606496f30867222d8e3deabad741296", "urls": ["bzz-raw://c90107edcd30e29044a5a8b05e71e728b93a485e7e7ef1a5b4ff695430e33a5e", "dweb:/ipfs/QmfHGNxtRQWeq7JENynxZNfhfTBytwbRwyekUyuHWrUyjK"], "license": "BUSL-1.1"}, "src/interfaces/permissions/ICustomVerifier.sol": {"keccak256": "0xdb343366ed86f63f8a94ff478c5c8e1d127888e4993c27fcb3bb3b51ae0cb8c7", "urls": ["bzz-raw://12407e89b20982a00961ccc03258e353962607747f214940d18b0989c11547c9", "dweb:/ipfs/Qmay19iWpwiV8LuvWccPjsazVU5Kkhme9QykFfLKfBmvSr"], "license": "BUSL-1.1"}, "src/interfaces/permissions/IMellowACL.sol": {"keccak256": "0xcc86b0ce4e4a53c1c4fa16ad79f9de0e2a1f1a77a1f256c0856120a9d6ea36df", "urls": ["bzz-raw://6942df2ffadacae5563e01ec60697f16116362ba84a856a346b36bc7b9dd1ec2", "dweb:/ipfs/QmNYhqZGpS98UP89CS86CjoG4ZWbMHzTHRtd7bZzQqn3UY"], "license": "BUSL-1.1"}, "src/interfaces/permissions/IVerifier.sol": {"keccak256": "0x4b42731d76efaffcb017577bd2a478d00a3058c69220d6ea8e26f30f74476be7", "urls": ["bzz-raw://ff2a6f09a946dd8a470dfe495b2a64d24b849c6d071dc859589140fb1b983362", "dweb:/ipfs/Qmb6UoqkmaC8tJMqkMfjcWR7pn1WCwPwuKKvUbLBQbPcJS"], "license": "BUSL-1.1"}, "src/interfaces/queues/IDepositQueue.sol": {"keccak256": "0x4ef38cb59d419010f453e62fe101efec25bec85a2ce1a24048098034060db47b", "urls": ["bzz-raw://20e51dd02001309f23f46abea409edf77a39522bf21eeffb858ac888e4200e72", "dweb:/ipfs/QmWm9gfMuXz8nwuHb3he7abxTRgQw35x2viwMVuiNYAY5b"], "license": "BUSL-1.1"}, "src/interfaces/queues/IQueue.sol": {"keccak256": "0xd12e34631b204821c77eaa5b2e104902d740e3530cd716f7fc991af01a73b9ab", "urls": ["bzz-raw://1f2a4d664244be639da1a5025ebccc8c1127e087a81ce6d0fc76af5134fec311", "dweb:/ipfs/QmS1GJwxGCV89myCAzLBAbmSrY6YL31G3nGj542dDM4QmT"], "license": "BUSL-1.1"}, "src/interfaces/queues/IRedeemQueue.sol": {"keccak256": "0xf8f95b95de259c8ccf29976f34d97a0ef31c9827f41ebcd5e8de54c10e646076", "urls": ["bzz-raw://45ae0d496e865d846bf0c980593c1d16c038abf2b9359906224aa94cccf1c73f", "dweb:/ipfs/QmYTDXSTpUqhzJDXSKjoSKxwwQYee7wSTtf617wDsnnCC5"], "license": "BUSL-1.1"}, "src/libraries/FenwickTreeLibrary.sol": {"keccak256": "0xfd159ed27b96f288c4220febac4bfe286e4545b8ca7f523ae6e63d123341f8a3", "urls": ["bzz-raw://58f0cc202896722786140571b111bf5e589730358a24b2940a450c4c75d864fe", "dweb:/ipfs/QmeRSjvcYURUSpvFe4r4VRwwpHcb1NmqiEUDEJ5rBxAxuf"], "license": "BUSL-1.1"}, "src/libraries/SlotLibrary.sol": {"keccak256": "0x35dcf16feec83c772add30250b0987ce29ccfbf308d04159701ebf9243444eeb", "urls": ["bzz-raw://97658d0c93ecc8174169e643d5a24c515b55905a2fcaaa6d6ec0188a3dd71a10", "dweb:/ipfs/QmS1z7ac8RbMvz15gVQ9YA1cCT5bs6KMHCiDhgcG8Pq8oK"], "license": "BUSL-1.1"}, "src/managers/RiskManager.sol": {"keccak256": "0xbb6103cb0865dd4cb99df1cfb050c8e83718b49f08f607c4707d11fcc2027f01", "urls": ["bzz-raw://d0e102e9a5fc216aa277b5409a7de9f91540ea3bf728c43bbb9c7573dc4dd36a", "dweb:/ipfs/QmPRfqdNE4x992gTgrcQGx9haJKR8gM6GJ75WbxTXFhBDc"], "license": "BUSL-1.1"}}, "version": 1}, "id": 117}