{"abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "burn", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "mint", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "take", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "ERC20InsufficientAllowance", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "allowance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InsufficientBalance", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InvalidApprover", "inputs": [{"name": "approver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidReceiver", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSender", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSpender", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "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", "sourceMap": "119:404:154:-:0;;;153:43;;;;;;;;;;1582:113:48;;;;;;;;;;;;;-1:-1:-1;;;1582:113:48;;;;;;;;;;;;;;;;-1:-1:-1;;;1582:113:48;;;1656:5;1648;:13;;;;;;:::i;:::-;-1:-1:-1;1671:7:48;:17;1681:7;1671;:17;:::i;:::-;;1582:113;;119:404:154;;14:127:192;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:380;225:1;221:12;;;;268;;;289:61;;343:4;335:6;331:17;321:27;;289:61;396:2;388:6;385:14;365:18;362:38;359:161;;442:10;437:3;433:20;430:1;423:31;477:4;474:1;467:15;505:4;502:1;495:15;359:161;;146:380;;;:::o;657:518::-;759:2;754:3;751:11;748:421;;;795:5;792:1;785:16;839:4;836:1;826:18;909:2;897:10;893:19;890:1;886:27;880:4;876:38;945:4;933:10;930:20;927:47;;;-1:-1:-1;968:4:192;927:47;1023:2;1018:3;1014:12;1011:1;1007:20;1001:4;997:31;987:41;;1078:81;1096:2;1089:5;1086:13;1078:81;;;1155:1;1141:16;;1122:1;1111:13;1078:81;;;1082:3;;748:421;657:518;;;:::o;1351:1345::-;1471:10;;-1:-1:-1;;;;;1493:30:192;;1490:56;;;1526:18;;:::i;:::-;1555:97;1645:6;1605:38;1637:4;1631:11;1605:38;:::i;:::-;1599:4;1555:97;:::i;:::-;1707:4;;1764:2;1753:14;;1781:1;1776:663;;;;2483:1;2500:6;2497:89;;;-1:-1:-1;2552:19:192;;;2546:26;2497:89;-1:-1:-1;;1308:1:192;1304:11;;;1300:24;1296:29;1286:40;1332:1;1328:11;;;1283:57;2599:81;;1746:944;;1776:663;604:1;597:14;;;641:4;628:18;;-1:-1:-1;;1812:20:192;;;1930:236;1944:7;1941:1;1938:14;1930:236;;;2033:19;;;2027:26;2012:42;;2125:27;;;;2093:1;2081:14;;;;1960:19;;1930:236;;;1934:3;2194:6;2185:7;2182:19;2179:201;;;2255:19;;;2249:26;-1:-1:-1;;2338:1:192;2334:14;;;2350:3;2330:24;2326:37;2322:42;2307:58;2292:74;;2179:201;;;2426:1;2417:6;2414:1;2410:14;2406:22;2400:4;2393:36;1746:944;;;;;1351:1345;;:::o;:::-;119:404:154;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "119:404:154:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1760:89:48;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;3979:186;;;;;;:::i;:::-;;:::i;:::-;;;1039:14:192;;1032:22;1014:41;;1002:2;987:18;3979:186:48;874:187:192;2830:97:48;2908:12;;2830:97;;;1212:25:192;;;1200:2;1185:18;2830:97:48;1066:177:192;4757:244:48;;;;;;:::i;:::-;;:::i;2688:82::-;;;2761:2;1723:36:192;;1711:2;1696:18;2688:82:48;1581:184:192;202:83:154;;;;;;:::i;:::-;;:::i;:::-;;380:103;;;;;;:::i;:::-;;:::i;2985:116:48:-;;;;;;:::i;:::-;-1:-1:-1;;;;;3076:18:48;3050:7;3076:18;;;;;;;;;;;;2985:116;1962:93;;;:::i;291:83:154:-;;;;;;:::i;:::-;;:::i;3296:178:48:-;;;;;;:::i;:::-;;:::i;3532:140::-;;;;;;:::i;:::-;-1:-1:-1;;;;;3638:18:48;;;3612:7;3638:18;;;:11;:18;;;;;;;;:27;;;;;;;;;;;;;3532:140;1760:89;1805:13;1837:5;1830:12;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1760:89;:::o;3979:186::-;4052:4;735:10:56;4106:31:48;735:10:56;4122:7:48;4131:5;4106:8;:31::i;:::-;4154:4;4147:11;;;3979:186;;;;;:::o;4757:244::-;4844:4;735:10:56;4900:37:48;4916:4;735:10:56;4931:5:48;4900:15;:37::i;:::-;4947:26;4957:4;4963:2;4967:5;4947:9;:26::i;:::-;-1:-1:-1;4990:4:48;;4757:244;-1:-1:-1;;;;4757:244:48:o;202:83:154:-;262:16;268:2;272:5;262;:16::i;:::-;202:83;;:::o;380:103::-;442:34;452:4;458:10;470:5;442:9;:34::i;1962:93:48:-;2009:13;2041:7;2034:14;;;;;:::i;291:83:154:-;351:16;357:2;361:5;351;:16::i;3296:178:48:-;3365:4;735:10:56;3419:27:48;735:10:56;3436:2:48;3440:5;3419:9;:27::i;8707:128::-;8791:37;8800:5;8807:7;8816:5;8823:4;8791:8;:37::i;:::-;8707:128;;;:::o;10396:476::-;-1:-1:-1;;;;;3638:18:48;;;10495:24;3638:18;;;:11;:18;;;;;;;;:27;;;;;;;;;;-1:-1:-1;;10561:36:48;;10557:309;;;10636:5;10617:16;:24;10613:130;;;10668:60;;-1:-1:-1;;;10668:60:48;;-1:-1:-1;;;;;2831:32:192;;10668:60:48;;;2813:51:192;2880:18;;;2873:34;;;2923:18;;;2916:34;;;2786:18;;10668:60:48;;;;;;;;10613:130;10784:57;10793:5;10800:7;10828:5;10809:16;:24;10835:5;10784:8;:57::i;:::-;10485:387;10396:476;;;:::o;5374:300::-;-1:-1:-1;;;;;5457:18:48;;5453:86;;5498:30;;-1:-1:-1;;;5498:30:48;;5525:1;5498:30;;;3107:51:192;3080:18;;5498:30:48;2961:203:192;5453:86:48;-1:-1:-1;;;;;5552:16:48;;5548:86;;5591:32;;-1:-1:-1;;;5591:32:48;;5620:1;5591:32;;;3107:51:192;3080:18;;5591:32:48;2961:203:192;5548:86:48;5643:24;5651:4;5657:2;5661:5;5643:7;:24::i;7439:208::-;-1:-1:-1;;;;;7509:21:48;;7505:91;;7553:32;;-1:-1:-1;;;7553:32:48;;7582:1;7553:32;;;3107:51:192;3080:18;;7553:32:48;2961:203:192;7505:91:48;7605:35;7621:1;7625:7;7634:5;7605:7;:35::i;7965:206::-;-1:-1:-1;;;;;8035:21:48;;8031:89;;8079:30;;-1:-1:-1;;;8079:30:48;;8106:1;8079:30;;;3107:51:192;3080:18;;8079:30:48;2961:203:192;8031:89:48;8129:35;8137:7;8154:1;8158:5;8129:7;:35::i;9682:432::-;-1:-1:-1;;;;;9794:19:48;;9790:89;;9836:32;;-1:-1:-1;;;9836:32:48;;9865:1;9836:32;;;3107:51:192;3080:18;;9836:32:48;2961:203:192;9790:89:48;-1:-1:-1;;;;;9892:21:48;;9888:90;;9936:31;;-1:-1:-1;;;9936:31:48;;9964:1;9936:31;;;3107:51:192;3080:18;;9936:31:48;2961:203:192;9888:90:48;-1:-1:-1;;;;;9987:18:48;;;;;;;:11;:18;;;;;;;;:27;;;;;;;;;:35;;;10032:76;;;;10082:7;-1:-1:-1;;;;;10066:31:48;10075:5;-1:-1:-1;;;;;10066:31:48;;10091:5;10066:31;;;;1212:25:192;;1200:2;1185:18;;1066:177;10066:31:48;;;;;;;;9682:432;;;;:::o;5989:1107::-;-1:-1:-1;;;;;6078:18:48;;6074:540;;6230:5;6214:12;;:21;;;;;;;:::i;:::-;;;;-1:-1:-1;6074:540:48;;-1:-1:-1;6074:540:48;;-1:-1:-1;;;;;6288:15:48;;6266:19;6288:15;;;;;;;;;;;6321:19;;;6317:115;;;6367:50;;-1:-1:-1;;;6367:50:48;;-1:-1:-1;;;;;2831:32:192;;6367:50:48;;;2813:51:192;2880:18;;;2873:34;;;2923:18;;;2916:34;;;2786:18;;6367:50:48;2611:345:192;6317:115:48;-1:-1:-1;;;;;6552:15:48;;:9;:15;;;;;;;;;;6570:19;;;;6552:37;;6074:540;-1:-1:-1;;;;;6628:16:48;;6624:425;;6791:12;:21;;;;;;;6624:425;;;-1:-1:-1;;;;;7002:13:48;;:9;:13;;;;;;;;;;:22;;;;;;6624:425;7079:2;-1:-1:-1;;;;;7064:25:48;7073:4;-1:-1:-1;;;;;7064:25:48;;7083:5;7064:25;;;;1212::192;;1200:2;1185:18;;1066:177;7064:25:48;;;;;;;;5989:1107;;;:::o;14:418:192:-;163:2;152:9;145:21;126:4;195:6;189:13;238:6;233:2;222:9;218:18;211:34;297:6;292:2;284:6;280:15;275:2;264:9;260:18;254:50;353:1;348:2;339:6;328:9;324:22;320:31;313:42;423:2;416;412:7;407:2;399:6;395:15;391:29;380:9;376:45;372:54;364:62;;;14:418;;;;:::o;437:173::-;505:20;;-1:-1:-1;;;;;554:31:192;;544:42;;534:70;;600:1;597;590:12;534:70;437:173;;;:::o;615:254::-;683:6;691;744:2;732:9;723:7;719:23;715:32;712:52;;;760:1;757;750:12;712:52;783:29;802:9;783:29;:::i;:::-;773:39;859:2;844:18;;;;831:32;;-1:-1:-1;;;615:254:192:o;1248:328::-;1325:6;1333;1341;1394:2;1382:9;1373:7;1369:23;1365:32;1362:52;;;1410:1;1407;1400:12;1362:52;1433:29;1452:9;1433:29;:::i;:::-;1423:39;;1481:38;1515:2;1504:9;1500:18;1481:38;:::i;:::-;1471:48;;1566:2;1555:9;1551:18;1538:32;1528:42;;1248:328;;;;;:::o;1770:186::-;1829:6;1882:2;1870:9;1861:7;1857:23;1853:32;1850:52;;;1898:1;1895;1888:12;1850:52;1921:29;1940:9;1921:29;:::i;:::-;1911:39;1770:186;-1:-1:-1;;;1770:186:192:o;1961:260::-;2029:6;2037;2090:2;2078:9;2069:7;2065:23;2061:32;2058:52;;;2106:1;2103;2096:12;2058:52;2129:29;2148:9;2129:29;:::i;:::-;2119:39;;2177:38;2211:2;2200:9;2196:18;2177:38;:::i;:::-;2167:48;;1961:260;;;;;:::o;2226:380::-;2305:1;2301:12;;;;2348;;;2369:61;;2423:4;2415:6;2411:17;2401:27;;2369:61;2476:2;2468:6;2465:14;2445:18;2442:38;2439:161;;2522:10;2517:3;2513:20;2510:1;2503:31;2557:4;2554:1;2547:15;2585:4;2582:1;2575:15;2439:161;;2226:380;;;:::o;3169:222::-;3234:9;;;3255:10;;;3252:133;;;3307:10;3302:3;3298:20;3295:1;3288:31;3342:4;3339:1;3332:15;3370:4;3367:1;3360:15", "linkReferences": {}}, "methodIdentifiers": {"allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "burn(address,uint256)": "9dc29fac", "decimals()": "313ce567", "mint(address,uint256)": "40c10f19", "name()": "06fdde03", "symbol()": "95d89b41", "take(address,uint256)": "52180208", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"allowance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientAllowance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"approver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidApprover\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidReceiver\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSpender\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"burn\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"mint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"take\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"ERC20InsufficientAllowance(address,uint256,uint256)\":[{\"details\":\"Indicates a failure with the `spender`\\u2019s `allowance`. Used in transfers.\",\"params\":{\"allowance\":\"Amount of tokens a `spender` is allowed to operate with.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC20InsufficientBalance(address,uint256,uint256)\":[{\"details\":\"Indicates an error related to the current `balance` of a `sender`. Used in transfers.\",\"params\":{\"balance\":\"Current balance for the interacting account.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidApprover(address)\":[{\"details\":\"Indicates a failure with the `approver` of a token to be approved. Used in approvals.\",\"params\":{\"approver\":\"Address initiating an approval operation.\"}}],\"ERC20InvalidReceiver(address)\":[{\"details\":\"Indicates a failure with the token `receiver`. Used in transfers.\",\"params\":{\"receiver\":\"Address to which tokens are being transferred.\"}}],\"ERC20InvalidSender(address)\":[{\"details\":\"Indicates a failure with the token `sender`. Used in transfers.\",\"params\":{\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidSpender(address)\":[{\"details\":\"Indicates a failure with the `spender` to be approved. Used in approvals.\",\"params\":{\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}]},\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"}},\"kind\":\"dev\",\"methods\":{\"allowance(address,address)\":{\"details\":\"See {IERC20-allowance}.\"},\"approve(address,uint256)\":{\"details\":\"See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address.\"},\"balanceOf(address)\":{\"details\":\"See {IERC20-balanceOf}.\"},\"decimals()\":{\"details\":\"Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between Ether and Wei. This is the default value returned by this function, unless it's overridden. NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}.\"},\"name()\":{\"details\":\"Returns the name of the token.\"},\"symbol()\":{\"details\":\"Returns the symbol of the token, usually a shorter version of the name.\"},\"totalSupply()\":{\"details\":\"See {IERC20-totalSupply}.\"},\"transfer(address,uint256)\":{\"details\":\"See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"See {IERC20-transferFrom}. Skips emitting an {Approval} event indicating an allowance update. This is not required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve]. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/mocks/MockERC20.sol\":\"MockERC20\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x880da465c203cec76b10d72dbd87c80f387df4102274f23eea1f9c9b0918792b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://399594cd8bb0143bc9e55e0f1d071d0d8c850a394fb7a319d50edd55d9ed822b\",\"dweb:/ipfs/QmbPZzgtT6LEm9CMqWfagQFwETbV1ztpECBB1DtQHrKiRz\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x41f6b3b9e030561e7896dbef372b499cc8d418a80c3884a4d65a68f2fdc7493a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://80b0992a11b2fd1f75ced2971696d07bbd1d19ce6761dd50d8b6d48aa435f42a\",\"dweb:/ipfs/QmZDe5xd2gXHjVEjv9t8C1KQ68K5T8qFwdinwQgmP3rF3x\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://df6f0c459663c9858b6cba2cda1d14a7d05a985bed6d2de72bd8e78c25ee79db\",\"dweb:/ipfs/QmeTTxZ7qVk9rjEv2R4CpCwdf8UMCcRqDNMvzNxHc3Fnn9\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0x70f2f713b13b7ce4610bcd0ac9fec0f3cc43693b043abcb8dc40a42a726eb330\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c13d13304ac79a83ab1c30168967d19e2203342ebbd6a9bbce4db7550522dcbf\",\"dweb:/ipfs/QmeN5jKMN2vw5bhacr6tkg78afbTTZUeaacNHqjWt4Ew1r\"]},\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"test/mocks/MockERC20.sol\":{\"keccak256\":\"0xabfdbd8413b69ae05b4d55138e7ee90c0d8706386265eaa84496340f381b8500\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://a638bad1dba281d001763f9ac04a9fd789c923325da7829703d4355aa6b15875\",\"dweb:/ipfs/QmP2EBdnowJPCYVggrBGUZ2kuwoziEDLpcS7U9nmFN6xwc\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientAllowance"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientBalance"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "type": "error", "name": "ERC20InvalidApprover"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "type": "error", "name": "ERC20InvalidReceiver"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "type": "error", "name": "ERC20InvalidSender"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "type": "error", "name": "ERC20InvalidSpender"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "spender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "burn"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "mint"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "take"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"allowance(address,address)": {"details": "See {IERC20-allowance}."}, "approve(address,uint256)": {"details": "See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address."}, "balanceOf(address)": {"details": "See {IERC20-balanceOf}."}, "decimals()": {"details": "Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between <PERSON><PERSON> and <PERSON>. This is the default value returned by this function, unless it's overridden. NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}."}, "name()": {"details": "Returns the name of the token."}, "symbol()": {"details": "Returns the symbol of the token, usually a shorter version of the name."}, "totalSupply()": {"details": "See {IERC20-totalSupply}."}, "transfer(address,uint256)": {"details": "See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`."}, "transferFrom(address,address,uint256)": {"details": "See {IERC20-transferFrom}. Skips emitting an {Approval} event indicating an allowance update. This is not required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve]. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/mocks/MockERC20.sol": "MockERC20"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x880da465c203cec76b10d72dbd87c80f387df4102274f23eea1f9c9b0918792b", "urls": ["bzz-raw://399594cd8bb0143bc9e55e0f1d071d0d8c850a394fb7a319d50edd55d9ed822b", "dweb:/ipfs/QmbPZzgtT6LEm9CMqWfagQFwETbV1ztpECBB1DtQHrKiRz"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x41f6b3b9e030561e7896dbef372b499cc8d418a80c3884a4d65a68f2fdc7493a", "urls": ["bzz-raw://80b0992a11b2fd1f75ced2971696d07bbd1d19ce6761dd50d8b6d48aa435f42a", "dweb:/ipfs/QmZDe5xd2gXHjVEjv9t8C1KQ68K5T8qFwdinwQgmP3rF3x"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7", "urls": ["bzz-raw://df6f0c459663c9858b6cba2cda1d14a7d05a985bed6d2de72bd8e78c25ee79db", "dweb:/ipfs/QmeTTxZ7qVk9rjEv2R4CpCwdf8UMCcRqDNMvzNxHc3Fnn9"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0x70f2f713b13b7ce4610bcd0ac9fec0f3cc43693b043abcb8dc40a42a726eb330", "urls": ["bzz-raw://c13d13304ac79a83ab1c30168967d19e2203342ebbd6a9bbce4db7550522dcbf", "dweb:/ipfs/QmeN5jKMN2vw5bhacr6tkg78afbTTZUeaacNHqjWt4Ew1r"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "test/mocks/MockERC20.sol": {"keccak256": "0xabfdbd8413b69ae05b4d55138e7ee90c0d8706386265eaa84496340f381b8500", "urls": ["bzz-raw://a638bad1dba281d001763f9ac04a9fd789c923325da7829703d4355aa6b15875", "dweb:/ipfs/QmP2EBdnowJPCYVggrBGUZ2kuwoziEDLpcS7U9nmFN6xwc"], "license": "BUSL-1.1"}}, "version": 1}, "id": 154}