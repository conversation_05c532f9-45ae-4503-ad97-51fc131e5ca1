{"abi": [{"type": "function", "name": "DELEGATION_APPROVAL_TYPEHASH", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "allocationManager", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IAllocationManager"}], "stateMutability": "view"}, {"type": "function", "name": "beaconChainETHStrategy", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IStrategy"}], "stateMutability": "view"}, {"type": "function", "name": "calculateDelegationApprovalDigestHash", "inputs": [{"name": "staker", "type": "address", "internalType": "address"}, {"name": "operator", "type": "address", "internalType": "address"}, {"name": "_delegationApprover", "type": "address", "internalType": "address"}, {"name": "approver<PERSON><PERSON><PERSON>", "type": "bytes32", "internalType": "bytes32"}, {"name": "expiry", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "calculateWithdrawalRoot", "inputs": [{"name": "withdrawal", "type": "tuple", "internalType": "struct IDelegationManager.Withdrawal", "components": [{"name": "staker", "type": "address", "internalType": "address"}, {"name": "delegatedTo", "type": "address", "internalType": "address"}, {"name": "withdrawer", "type": "address", "internalType": "address"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "startBlock", "type": "uint32", "internalType": "uint32"}, {"name": "strategies", "type": "address[]", "internalType": "contract IStrategy[]"}, {"name": "shares", "type": "uint256[]", "internalType": "uint256[]"}]}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "pure"}, {"type": "function", "name": "completeQueuedWithdrawal", "inputs": [{"name": "withdrawal", "type": "tuple", "internalType": "struct IDelegationManager.Withdrawal", "components": [{"name": "staker", "type": "address", "internalType": "address"}, {"name": "delegatedTo", "type": "address", "internalType": "address"}, {"name": "withdrawer", "type": "address", "internalType": "address"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "startBlock", "type": "uint32", "internalType": "uint32"}, {"name": "strategies", "type": "address[]", "internalType": "contract IStrategy[]"}, {"name": "shares", "type": "uint256[]", "internalType": "uint256[]"}]}, {"name": "tokens", "type": "address[]", "internalType": "contract IERC20[]"}, {"name": "receiveAsTokens", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "completeQueuedWithdrawals", "inputs": [{"name": "withdrawals", "type": "tuple[]", "internalType": "struct IDelegationManager.Withdrawal[]", "components": [{"name": "staker", "type": "address", "internalType": "address"}, {"name": "delegatedTo", "type": "address", "internalType": "address"}, {"name": "withdrawer", "type": "address", "internalType": "address"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "startBlock", "type": "uint32", "internalType": "uint32"}, {"name": "strategies", "type": "address[]", "internalType": "contract IStrategy[]"}, {"name": "shares", "type": "uint256[]", "internalType": "uint256[]"}]}, {"name": "tokens", "type": "address[][]", "internalType": "contract IERC20[][]"}, {"name": "receiveAsTokens", "type": "bool[]", "internalType": "bool[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "convertToDepositShares", "inputs": [{"name": "staker", "type": "address", "internalType": "address"}, {"name": "strategies", "type": "address[]", "internalType": "contract IStrategy[]"}, {"name": "withdrawableShares", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [{"name": "", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "cumulativeWithdrawalsQueued", "inputs": [{"name": "staker", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "delegate<PERSON>o", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "approverSignatureAndExpiry", "type": "tuple", "internalType": "struct ISignatureUtils.SignatureWithExpiry", "components": [{"name": "signature", "type": "bytes", "internalType": "bytes"}, {"name": "expiry", "type": "uint256", "internalType": "uint256"}]}, {"name": "approver<PERSON><PERSON><PERSON>", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "delegatedTo", "inputs": [{"name": "staker", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "delegationApprover", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "delegationApproverSaltIsSpent", "inputs": [{"name": "_delegationApprover", "type": "address", "internalType": "address"}, {"name": "salt", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "depositScalingFactor", "inputs": [{"name": "staker", "type": "address", "internalType": "address"}, {"name": "strategy", "type": "address", "internalType": "contract IStrategy"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "domainSeparator", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "getDepositedShares", "inputs": [{"name": "staker", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address[]", "internalType": "contract IStrategy[]"}, {"name": "", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "getOperatorShares", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "strategies", "type": "address[]", "internalType": "contract IStrategy[]"}], "outputs": [{"name": "", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "getOperatorsShares", "inputs": [{"name": "operators", "type": "address[]", "internalType": "address[]"}, {"name": "strategies", "type": "address[]", "internalType": "contract IStrategy[]"}], "outputs": [{"name": "", "type": "uint256[][]", "internalType": "uint256[][]"}], "stateMutability": "view"}, {"type": "function", "name": "getQueuedWithdrawal", "inputs": [{"name": "withdrawalRoot", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "withdrawal", "type": "tuple", "internalType": "struct IDelegationManager.Withdrawal", "components": [{"name": "staker", "type": "address", "internalType": "address"}, {"name": "delegatedTo", "type": "address", "internalType": "address"}, {"name": "withdrawer", "type": "address", "internalType": "address"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "startBlock", "type": "uint32", "internalType": "uint32"}, {"name": "strategies", "type": "address[]", "internalType": "contract IStrategy[]"}, {"name": "shares", "type": "uint256[]", "internalType": "uint256[]"}]}, {"name": "shares", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "getQueuedWithdrawalRoots", "inputs": [{"name": "staker", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bytes32[]", "internalType": "bytes32[]"}], "stateMutability": "view"}, {"type": "function", "name": "getQueuedWithdrawals", "inputs": [{"name": "staker", "type": "address", "internalType": "address"}], "outputs": [{"name": "withdrawals", "type": "tuple[]", "internalType": "struct IDelegationManager.Withdrawal[]", "components": [{"name": "staker", "type": "address", "internalType": "address"}, {"name": "delegatedTo", "type": "address", "internalType": "address"}, {"name": "withdrawer", "type": "address", "internalType": "address"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "startBlock", "type": "uint32", "internalType": "uint32"}, {"name": "strategies", "type": "address[]", "internalType": "contract IStrategy[]"}, {"name": "shares", "type": "uint256[]", "internalType": "uint256[]"}]}, {"name": "shares", "type": "uint256[][]", "internalType": "uint256[][]"}], "stateMutability": "view"}, {"type": "function", "name": "getSlashableSharesInQueue", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "strategy", "type": "address", "internalType": "contract IStrategy"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getWithdrawableShares", "inputs": [{"name": "staker", "type": "address", "internalType": "address"}, {"name": "strategies", "type": "address[]", "internalType": "contract IStrategy[]"}], "outputs": [{"name": "withdrawableShares", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "depositShares", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "isDelegated", "inputs": [{"name": "staker", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isOperator", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "minWithdrawalDelayBlocks", "inputs": [], "outputs": [{"name": "", "type": "uint32", "internalType": "uint32"}], "stateMutability": "view"}, {"type": "function", "name": "queueWithdrawals", "inputs": [{"name": "params", "type": "tuple[]", "internalType": "struct IDelegationManager.QueuedWithdrawalParams[]", "components": [{"name": "strategies", "type": "address[]", "internalType": "contract IStrategy[]"}, {"name": "depositShares", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "__deprecated_withdrawer", "type": "address", "internalType": "address"}]}], "outputs": [{"name": "", "type": "bytes32[]", "internalType": "bytes32[]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "queuedWithdrawals", "inputs": [{"name": "withdrawalRoot", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "withdrawal", "type": "tuple", "internalType": "struct IDelegationManager.Withdrawal", "components": [{"name": "staker", "type": "address", "internalType": "address"}, {"name": "delegatedTo", "type": "address", "internalType": "address"}, {"name": "withdrawer", "type": "address", "internalType": "address"}, {"name": "nonce", "type": "uint256", "internalType": "uint256"}, {"name": "startBlock", "type": "uint32", "internalType": "uint32"}, {"name": "strategies", "type": "address[]", "internalType": "contract IStrategy[]"}, {"name": "shares", "type": "uint256[]", "internalType": "uint256[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "slashOperatorShares", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "strategy", "type": "address", "internalType": "contract IStrategy"}, {"name": "prevMaxMagnitude", "type": "uint64", "internalType": "uint64"}, {"name": "newMaxMagnitude", "type": "uint64", "internalType": "uint64"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "undelegate", "inputs": [{"name": "staker", "type": "address", "internalType": "address"}], "outputs": [{"name": "withdrawalRoots", "type": "bytes32[]", "internalType": "bytes32[]"}], "stateMutability": "nonpayable"}], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {"DELEGATION_APPROVAL_TYPEHASH()": "04a4f979", "allocationManager()": "ca8aa7c7", "beaconChainETHStrategy()": "9104c319", "calculateDelegationApprovalDigestHash(address,address,address,bytes32,uint256)": "0b9f487a", "calculateWithdrawalRoot((address,address,address,uint256,uint32,address[],uint256[]))": "597b36da", "completeQueuedWithdrawal((address,address,address,uint256,uint32,address[],uint256[]),address[],bool)": "e4cc3f90", "completeQueuedWithdrawals((address,address,address,uint256,uint32,address[],uint256[])[],address[][],bool[])": "9435bb43", "convertToDepositShares(address,address[],uint256[])": "25df922e", "cumulativeWithdrawalsQueued(address)": "a1788484", "delegateTo(address,(bytes,uint256),bytes32)": "eea9064b", "delegatedTo(address)": "65da1264", "delegationApprover(address)": "3cdeb5e0", "delegationApproverSaltIsSpent(address,bytes32)": "bb45fef2", "depositScalingFactor(address,address)": "bfae3fd2", "domainSeparator()": "f698da25", "getDepositedShares(address)": "66d5ba93", "getOperatorShares(address,address[])": "90041347", "getOperatorsShares(address[],address[])": "f0e0e676", "getQueuedWithdrawal(bytes32)": "5d975e88", "getQueuedWithdrawalRoots(address)": "fd8aa88d", "getQueuedWithdrawals(address)": "5dd68579", "getSlashableSharesInQueue(address,address)": "6e174448", "getWithdrawableShares(address,address[])": "c978f7ac", "isDelegated(address)": "3e28391d", "isOperator(address)": "6d70f7ae", "minWithdrawalDelayBlocks()": "c448feb8", "queueWithdrawals((address[],uint256[],address)[])": "0dd8dd02", "queuedWithdrawals(bytes32)": "99f5371b", "slashOperatorShares(address,address,uint64,uint64)": "601bb36f", "undelegate(address)": "da8be864"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.25+commit.b61c2a91\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"DELEGATION_APPROVAL_TYPEHASH\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"allocationManager\",\"outputs\":[{\"internalType\":\"contract IAllocationManager\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"beaconChainETHStrategy\",\"outputs\":[{\"internalType\":\"contract IStrategy\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_delegationApprover\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"approverSalt\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"expiry\",\"type\":\"uint256\"}],\"name\":\"calculateDelegationApprovalDigestHash\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"delegatedTo\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"withdrawer\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"startBlock\",\"type\":\"uint32\"},{\"internalType\":\"contract IStrategy[]\",\"name\":\"strategies\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"shares\",\"type\":\"uint256[]\"}],\"internalType\":\"struct IDelegationManager.Withdrawal\",\"name\":\"withdrawal\",\"type\":\"tuple\"}],\"name\":\"calculateWithdrawalRoot\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"delegatedTo\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"withdrawer\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"startBlock\",\"type\":\"uint32\"},{\"internalType\":\"contract IStrategy[]\",\"name\":\"strategies\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"shares\",\"type\":\"uint256[]\"}],\"internalType\":\"struct IDelegationManager.Withdrawal\",\"name\":\"withdrawal\",\"type\":\"tuple\"},{\"internalType\":\"contract IERC20[]\",\"name\":\"tokens\",\"type\":\"address[]\"},{\"internalType\":\"bool\",\"name\":\"receiveAsTokens\",\"type\":\"bool\"}],\"name\":\"completeQueuedWithdrawal\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"delegatedTo\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"withdrawer\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"startBlock\",\"type\":\"uint32\"},{\"internalType\":\"contract IStrategy[]\",\"name\":\"strategies\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"shares\",\"type\":\"uint256[]\"}],\"internalType\":\"struct IDelegationManager.Withdrawal[]\",\"name\":\"withdrawals\",\"type\":\"tuple[]\"},{\"internalType\":\"contract IERC20[][]\",\"name\":\"tokens\",\"type\":\"address[][]\"},{\"internalType\":\"bool[]\",\"name\":\"receiveAsTokens\",\"type\":\"bool[]\"}],\"name\":\"completeQueuedWithdrawals\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"},{\"internalType\":\"contract IStrategy[]\",\"name\":\"strategies\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"withdrawableShares\",\"type\":\"uint256[]\"}],\"name\":\"convertToDepositShares\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"}],\"name\":\"cumulativeWithdrawalsQueued\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"components\":[{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"},{\"internalType\":\"uint256\",\"name\":\"expiry\",\"type\":\"uint256\"}],\"internalType\":\"struct ISignatureUtils.SignatureWithExpiry\",\"name\":\"approverSignatureAndExpiry\",\"type\":\"tuple\"},{\"internalType\":\"bytes32\",\"name\":\"approverSalt\",\"type\":\"bytes32\"}],\"name\":\"delegateTo\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"}],\"name\":\"delegatedTo\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"delegationApprover\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_delegationApprover\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"salt\",\"type\":\"bytes32\"}],\"name\":\"delegationApproverSaltIsSpent\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"},{\"internalType\":\"contract IStrategy\",\"name\":\"strategy\",\"type\":\"address\"}],\"name\":\"depositScalingFactor\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"domainSeparator\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"}],\"name\":\"getDepositedShares\",\"outputs\":[{\"internalType\":\"contract IStrategy[]\",\"name\":\"\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"contract IStrategy[]\",\"name\":\"strategies\",\"type\":\"address[]\"}],\"name\":\"getOperatorShares\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"operators\",\"type\":\"address[]\"},{\"internalType\":\"contract IStrategy[]\",\"name\":\"strategies\",\"type\":\"address[]\"}],\"name\":\"getOperatorsShares\",\"outputs\":[{\"internalType\":\"uint256[][]\",\"name\":\"\",\"type\":\"uint256[][]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"withdrawalRoot\",\"type\":\"bytes32\"}],\"name\":\"getQueuedWithdrawal\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"delegatedTo\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"withdrawer\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"startBlock\",\"type\":\"uint32\"},{\"internalType\":\"contract IStrategy[]\",\"name\":\"strategies\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"shares\",\"type\":\"uint256[]\"}],\"internalType\":\"struct IDelegationManager.Withdrawal\",\"name\":\"withdrawal\",\"type\":\"tuple\"},{\"internalType\":\"uint256[]\",\"name\":\"shares\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"}],\"name\":\"getQueuedWithdrawalRoots\",\"outputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"\",\"type\":\"bytes32[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"}],\"name\":\"getQueuedWithdrawals\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"delegatedTo\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"withdrawer\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"startBlock\",\"type\":\"uint32\"},{\"internalType\":\"contract IStrategy[]\",\"name\":\"strategies\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"shares\",\"type\":\"uint256[]\"}],\"internalType\":\"struct IDelegationManager.Withdrawal[]\",\"name\":\"withdrawals\",\"type\":\"tuple[]\"},{\"internalType\":\"uint256[][]\",\"name\":\"shares\",\"type\":\"uint256[][]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"contract IStrategy\",\"name\":\"strategy\",\"type\":\"address\"}],\"name\":\"getSlashableSharesInQueue\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"},{\"internalType\":\"contract IStrategy[]\",\"name\":\"strategies\",\"type\":\"address[]\"}],\"name\":\"getWithdrawableShares\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"withdrawableShares\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"depositShares\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"}],\"name\":\"isDelegated\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"isOperator\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"minWithdrawalDelayBlocks\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"contract IStrategy[]\",\"name\":\"strategies\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"depositShares\",\"type\":\"uint256[]\"},{\"internalType\":\"address\",\"name\":\"__deprecated_withdrawer\",\"type\":\"address\"}],\"internalType\":\"struct IDelegationManager.QueuedWithdrawalParams[]\",\"name\":\"params\",\"type\":\"tuple[]\"}],\"name\":\"queueWithdrawals\",\"outputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"\",\"type\":\"bytes32[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"withdrawalRoot\",\"type\":\"bytes32\"}],\"name\":\"queuedWithdrawals\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"delegatedTo\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"withdrawer\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"startBlock\",\"type\":\"uint32\"},{\"internalType\":\"contract IStrategy[]\",\"name\":\"strategies\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"shares\",\"type\":\"uint256[]\"}],\"internalType\":\"struct IDelegationManager.Withdrawal\",\"name\":\"withdrawal\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"contract IStrategy\",\"name\":\"strategy\",\"type\":\"address\"},{\"internalType\":\"uint64\",\"name\":\"prevMaxMagnitude\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"newMaxMagnitude\",\"type\":\"uint64\"}],\"name\":\"slashOperatorShares\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"}],\"name\":\"undelegate\",\"outputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"withdrawalRoots\",\"type\":\"bytes32[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/interfaces/external/eigen-layer/IDelegationManager.sol\":\"IDelegationManager\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://df6f0c459663c9858b6cba2cda1d14a7d05a985bed6d2de72bd8e78c25ee79db\",\"dweb:/ipfs/QmeTTxZ7qVk9rjEv2R4CpCwdf8UMCcRqDNMvzNxHc3Fnn9\"]},\"src/interfaces/external/eigen-layer/IAllocationManager.sol\":{\"keccak256\":\"0xae3e733a2af24b269f9e861eb091a70123dfb662073296af1e0404d3ef38077c\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://b35c72a2bdebe1d6a1515a1215e90bb354414de570e3723bd1f764204fe51e86\",\"dweb:/ipfs/QmWibt9unFu2MYSMXmkb8rAtCEpxUxio5Rn2fM43D4cKth\"]},\"src/interfaces/external/eigen-layer/IDelegationManager.sol\":{\"keccak256\":\"0x84e8357193d083bd63c3af2ae3ee1583d1ba734a09d24a897fa6c7785a28b700\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://e4ffb431c1e5283743009f98027dcdb06a42b32789d7251fb21f6a223e3565aa\",\"dweb:/ipfs/Qme9V6VZFkTDuB3aM3XhkntjYJ6Fv3Ud7QuHgorY4iDGXL\"]},\"src/interfaces/external/eigen-layer/ISignatureUtils.sol\":{\"keccak256\":\"0xf0a4f7b91b079e85ad7b27132a751955d09694358cb6494b70a6d4217f248f03\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://0773df5dae86559e56fbced497b20cbe8def225f3f2dadf43cf7728c10cd6bc2\",\"dweb:/ipfs/QmS7YDqmyttHZ8sbX1YB21Z2rk7wsS9hneDTkAcSPbED6E\"]},\"src/interfaces/external/eigen-layer/IStrategy.sol\":{\"keccak256\":\"0x938f287999a7edf7f3a67f6c2aa3205268cf6e5ebc8dbc00302d2886a558b098\",\"license\":\"BUSL-1.1\",\"urls\":[\"bzz-raw://c7e22f4a1ed1cf9fc2f255d98d5af1a85077cfb77bdfaa351348f4bb28bca872\",\"dweb:/ipfs/QmeCA5afkHVoW5FVhDmaVNUwV4tN6YzwKyHyC9dJpffRof\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.25+commit.b61c2a91"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "view", "type": "function", "name": "DELEGATION_APPROVAL_TYPEHASH", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "allocationManager", "outputs": [{"internalType": "contract IAllocationManager", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "beaconChainETHStrategy", "outputs": [{"internalType": "contract IStrategy", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}, {"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "_delegationApprover", "type": "address"}, {"internalType": "bytes32", "name": "approver<PERSON><PERSON><PERSON>", "type": "bytes32"}, {"internalType": "uint256", "name": "expiry", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "calculateDelegationApprovalDigestHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "struct IDelegationManager.Withdrawal", "name": "withdrawal", "type": "tuple", "components": [{"internalType": "address", "name": "staker", "type": "address"}, {"internalType": "address", "name": "delegatedTo", "type": "address"}, {"internalType": "address", "name": "withdrawer", "type": "address"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint32", "name": "startBlock", "type": "uint32"}, {"internalType": "contract IStrategy[]", "name": "strategies", "type": "address[]"}, {"internalType": "uint256[]", "name": "shares", "type": "uint256[]"}]}], "stateMutability": "pure", "type": "function", "name": "calculateWithdrawalRoot", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "struct IDelegationManager.Withdrawal", "name": "withdrawal", "type": "tuple", "components": [{"internalType": "address", "name": "staker", "type": "address"}, {"internalType": "address", "name": "delegatedTo", "type": "address"}, {"internalType": "address", "name": "withdrawer", "type": "address"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint32", "name": "startBlock", "type": "uint32"}, {"internalType": "contract IStrategy[]", "name": "strategies", "type": "address[]"}, {"internalType": "uint256[]", "name": "shares", "type": "uint256[]"}]}, {"internalType": "contract IERC20[]", "name": "tokens", "type": "address[]"}, {"internalType": "bool", "name": "receiveAsTokens", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "completeQueuedWithdrawal"}, {"inputs": [{"internalType": "struct IDelegationManager.Withdrawal[]", "name": "withdrawals", "type": "tuple[]", "components": [{"internalType": "address", "name": "staker", "type": "address"}, {"internalType": "address", "name": "delegatedTo", "type": "address"}, {"internalType": "address", "name": "withdrawer", "type": "address"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint32", "name": "startBlock", "type": "uint32"}, {"internalType": "contract IStrategy[]", "name": "strategies", "type": "address[]"}, {"internalType": "uint256[]", "name": "shares", "type": "uint256[]"}]}, {"internalType": "contract IERC20[][]", "name": "tokens", "type": "address[][]"}, {"internalType": "bool[]", "name": "receiveAsTokens", "type": "bool[]"}], "stateMutability": "nonpayable", "type": "function", "name": "completeQueuedWithdrawals"}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}, {"internalType": "contract IStrategy[]", "name": "strategies", "type": "address[]"}, {"internalType": "uint256[]", "name": "withdrawableShares", "type": "uint256[]"}], "stateMutability": "view", "type": "function", "name": "convertToDepositShares", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}]}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "stateMutability": "view", "type": "function", "name": "cumulativeWithdrawalsQueued", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "struct ISignatureUtils.SignatureWithExpiry", "name": "approverSignatureAndExpiry", "type": "tuple", "components": [{"internalType": "bytes", "name": "signature", "type": "bytes"}, {"internalType": "uint256", "name": "expiry", "type": "uint256"}]}, {"internalType": "bytes32", "name": "approver<PERSON><PERSON><PERSON>", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "delegate<PERSON>o"}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "stateMutability": "view", "type": "function", "name": "delegatedTo", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}], "stateMutability": "view", "type": "function", "name": "delegationApprover", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "_delegationApprover", "type": "address"}, {"internalType": "bytes32", "name": "salt", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "delegationApproverSaltIsSpent", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}, {"internalType": "contract IStrategy", "name": "strategy", "type": "address"}], "stateMutability": "view", "type": "function", "name": "depositScalingFactor", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "domainSeparator", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getDepositedShares", "outputs": [{"internalType": "contract IStrategy[]", "name": "", "type": "address[]"}, {"internalType": "uint256[]", "name": "", "type": "uint256[]"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "contract IStrategy[]", "name": "strategies", "type": "address[]"}], "stateMutability": "view", "type": "function", "name": "getOperatorShares", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}]}, {"inputs": [{"internalType": "address[]", "name": "operators", "type": "address[]"}, {"internalType": "contract IStrategy[]", "name": "strategies", "type": "address[]"}], "stateMutability": "view", "type": "function", "name": "getOperatorsShares", "outputs": [{"internalType": "uint256[][]", "name": "", "type": "uint256[][]"}]}, {"inputs": [{"internalType": "bytes32", "name": "withdrawalRoot", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "getQueuedWithdrawal", "outputs": [{"internalType": "struct IDelegationManager.Withdrawal", "name": "withdrawal", "type": "tuple", "components": [{"internalType": "address", "name": "staker", "type": "address"}, {"internalType": "address", "name": "delegatedTo", "type": "address"}, {"internalType": "address", "name": "withdrawer", "type": "address"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint32", "name": "startBlock", "type": "uint32"}, {"internalType": "contract IStrategy[]", "name": "strategies", "type": "address[]"}, {"internalType": "uint256[]", "name": "shares", "type": "uint256[]"}]}, {"internalType": "uint256[]", "name": "shares", "type": "uint256[]"}]}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getQueuedWithdrawalRoots", "outputs": [{"internalType": "bytes32[]", "name": "", "type": "bytes32[]"}]}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getQueuedWithdrawals", "outputs": [{"internalType": "struct IDelegationManager.Withdrawal[]", "name": "withdrawals", "type": "tuple[]", "components": [{"internalType": "address", "name": "staker", "type": "address"}, {"internalType": "address", "name": "delegatedTo", "type": "address"}, {"internalType": "address", "name": "withdrawer", "type": "address"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint32", "name": "startBlock", "type": "uint32"}, {"internalType": "contract IStrategy[]", "name": "strategies", "type": "address[]"}, {"internalType": "uint256[]", "name": "shares", "type": "uint256[]"}]}, {"internalType": "uint256[][]", "name": "shares", "type": "uint256[][]"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "contract IStrategy", "name": "strategy", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getSlashableSharesInQueue", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}, {"internalType": "contract IStrategy[]", "name": "strategies", "type": "address[]"}], "stateMutability": "view", "type": "function", "name": "getWithdrawableShares", "outputs": [{"internalType": "uint256[]", "name": "withdrawableShares", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "depositShares", "type": "uint256[]"}]}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isDelegated", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isOperator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "minWithdrawalDelayBlocks", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}]}, {"inputs": [{"internalType": "struct IDelegationManager.QueuedWithdrawalParams[]", "name": "params", "type": "tuple[]", "components": [{"internalType": "contract IStrategy[]", "name": "strategies", "type": "address[]"}, {"internalType": "uint256[]", "name": "depositShares", "type": "uint256[]"}, {"internalType": "address", "name": "__deprecated_withdrawer", "type": "address"}]}], "stateMutability": "nonpayable", "type": "function", "name": "queueWithdrawals", "outputs": [{"internalType": "bytes32[]", "name": "", "type": "bytes32[]"}]}, {"inputs": [{"internalType": "bytes32", "name": "withdrawalRoot", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "queuedWithdrawals", "outputs": [{"internalType": "struct IDelegationManager.Withdrawal", "name": "withdrawal", "type": "tuple", "components": [{"internalType": "address", "name": "staker", "type": "address"}, {"internalType": "address", "name": "delegatedTo", "type": "address"}, {"internalType": "address", "name": "withdrawer", "type": "address"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint32", "name": "startBlock", "type": "uint32"}, {"internalType": "contract IStrategy[]", "name": "strategies", "type": "address[]"}, {"internalType": "uint256[]", "name": "shares", "type": "uint256[]"}]}]}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "contract IStrategy", "name": "strategy", "type": "address"}, {"internalType": "uint64", "name": "prevMaxMagnitude", "type": "uint64"}, {"internalType": "uint64", "name": "newMaxMagnitude", "type": "uint64"}], "stateMutability": "nonpayable", "type": "function", "name": "slashOperatorShares"}, {"inputs": [{"internalType": "address", "name": "staker", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "undelegate", "outputs": [{"internalType": "bytes32[]", "name": "withdrawalRoots", "type": "bytes32[]"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts-upgradeable/lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/interfaces/external/eigen-layer/IDelegationManager.sol": "IDelegationManager"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts-upgradeable/lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7", "urls": ["bzz-raw://df6f0c459663c9858b6cba2cda1d14a7d05a985bed6d2de72bd8e78c25ee79db", "dweb:/ipfs/QmeTTxZ7qVk9rjEv2R4CpCwdf8UMCcRqDNMvzNxHc3Fnn9"], "license": "MIT"}, "src/interfaces/external/eigen-layer/IAllocationManager.sol": {"keccak256": "0xae3e733a2af24b269f9e861eb091a70123dfb662073296af1e0404d3ef38077c", "urls": ["bzz-raw://b35c72a2bdebe1d6a1515a1215e90bb354414de570e3723bd1f764204fe51e86", "dweb:/ipfs/QmWibt9unFu2MYSMXmkb8rAtCEpxUxio5Rn2fM43D4cKth"], "license": "BUSL-1.1"}, "src/interfaces/external/eigen-layer/IDelegationManager.sol": {"keccak256": "0x84e8357193d083bd63c3af2ae3ee1583d1ba734a09d24a897fa6c7785a28b700", "urls": ["bzz-raw://e4ffb431c1e5283743009f98027dcdb06a42b32789d7251fb21f6a223e3565aa", "dweb:/ipfs/Qme9V6VZFkTDuB3aM3XhkntjYJ6Fv3Ud7QuHgorY4iDGXL"], "license": "BUSL-1.1"}, "src/interfaces/external/eigen-layer/ISignatureUtils.sol": {"keccak256": "0xf0a4f7b91b079e85ad7b27132a751955d09694358cb6494b70a6d4217f248f03", "urls": ["bzz-raw://0773df5dae86559e56fbced497b20cbe8def225f3f2dadf43cf7728c10cd6bc2", "dweb:/ipfs/QmS7YDqmyttHZ8sbX1YB21Z2rk7wsS9hneDTkAcSPbED6E"], "license": "BUSL-1.1"}, "src/interfaces/external/eigen-layer/IStrategy.sol": {"keccak256": "0x938f287999a7edf7f3a67f6c2aa3205268cf6e5ebc8dbc00302d2886a558b098", "urls": ["bzz-raw://c7e22f4a1ed1cf9fc2f255d98d5af1a85077cfb77bdfaa351348f4bb28bca872", "dweb:/ipfs/QmeCA5afkHVoW5FVhDmaVNUwV4tN6YzwKyHyC9dJpffRof"], "license": "BUSL-1.1"}}, "version": 1}, "id": 78}