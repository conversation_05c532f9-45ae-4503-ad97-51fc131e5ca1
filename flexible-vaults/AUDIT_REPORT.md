# Audit Report: LidoDepositHook ETH/WETH Deposit Denial of Service

## Summary

**Missing receive() function in wstETH contract will cause a complete denial of service for users as depositors will have all ETH and WETH deposits revert when the hook attempts to send ETH directly to wstETH.**

In `LidoDepositHook.sol:41` the contract attempts to send ETH directly to the wstETH contract using `Address.sendValue(payable(wsteth), assets)`, but the wstETH contract lacks a payable `receive()` function to accept direct ETH transfers.

## Technical Details

### Root Cause
The vulnerability stems from a fundamental misunderstanding of the wstETH contract's interface. The code incorrectly assumes that wstETH can receive ETH directly, when in fact it only accepts stETH tokens through its `wrap()` function.

**Vulnerable Code:**
```solidity
// LidoDepositHook.sol:36-42
if (asset == weth) {
    IWETH(weth).withdraw(assets);  // Converts WETH to ETH
} else if (asset != TransferLibrary.ETH) {
    revert UnsupportedAsset(asset);
}
Address.sendValue(payable(wsteth), assets);  // ❌ FAILS: wstETH cannot receive ETH
```

**Correct <PERSON> (used for stETH):**
```solidity
// LidoDepositHook.sol:30-32
IERC20(steth).safeIncreaseAllowance(wsteth, assets);
IWSTETH(wsteth).wrap(assets);  // ✅ WORKS: Uses proper wrap() function
```

### Internal Pre-conditions

1. **LidoDepositHook** needs to be deployed with wstETH address set to the mainnet wstETH contract (******************************************)
2. **LidoDepositHook** needs to be deployed with WETH address set to the mainnet WETH contract (******************************************)
3. **User** needs to have ETH balance to be at least the deposit amount
4. **User** needs to have WETH balance to be at least the deposit amount (for WETH deposits)
5. **Vault** needs to call LidoDepositHook.callHook() with asset parameter set to either TransferLibrary.ETH or WETH address
6. **LidoDepositHook contract** needs to have ETH balance to be at least the assets amount (transferred from vault)

### External Pre-conditions

1. **wstETH contract** needs to maintain its current implementation without a payable receive() function
2. **WETH contract** needs to maintain its standard withdraw() functionality
3. **Ethereum network** needs to be operational for transaction execution
4. **Gas price** needs to be sufficient to execute the transaction up to the revert point

### Attack Path

1. **User** calls deposit function with ETH or WETH as the asset
2. **Vault** transfers the asset to the LidoDepositHook contract
3. **LidoDepositHook.callHook()** is invoked with the asset and amount
4. **For WETH**: Contract calls `IWETH(weth).withdraw(assets)` converting WETH to ETH
5. **For both ETH and WETH**: Contract calls `Address.sendValue(payable(wsteth), assets)`
6. **wstETH contract** rejects the ETH transfer (no receive function)
7. **Transaction reverts** with "Address: unable to send value, recipient may have reverted"
8. **User's deposit fails** completely

### Impact

**Users cannot deposit ETH or WETH through the LidoDepositHook, resulting in complete denial of service for these asset types.**

- **Affected Assets**: ETH and WETH deposits (50% of supported asset types)
- **Severity**: HIGH - Complete functionality breakdown
- **User Experience**: All ETH/WETH deposits will revert
- **Financial Impact**: No direct fund loss, but deposits become impossible
- **Scope**: All users attempting to use ETH or WETH with this hook

### Proof of Concept

The vulnerability has been confirmed through comprehensive testing:

```solidity
// Test Results (All Passed - Confirming Vulnerability)
✅ testDirectETHTransferToWstETHFails() - Direct ETH transfer reverts
✅ testLidoDepositHookETHPathFails() - ETH deposits through hook revert
✅ testLidoDepositHookWETHPathFails() - WETH deposits through hook revert
✅ testVulnerabilityImpact() - Confirms complete denial of service
✅ testStETHPathWorksCorrectly() - Control test: stETH works correctly
```

**Test Execution Results:**
```
Ran 9 tests for test/poc/LidoDepositHookVulnerabilityPOC.t.sol:LidoDepositHookVulnerabilityPOC
[PASS] testAnalyzeWstETHContract() (gas: 16101)
[PASS] testCorrectStETHWrappingFlow() (gas: 14177)
[PASS] testDirectETHTransferToWstETHFails() (gas: 20323)
[PASS] testLidoDepositHookETHPathFails() (gas: 25016)
[PASS] testLidoDepositHookWETHPathFails() (gas: 35124)
[PASS] testRootCauseAnalysis() (gas: 8203)
[PASS] testStETHPathWorksCorrectly() (gas: 129073)
[PASS] testVulnerabilityImpact() (gas: 51312)
[PASS] testWstETHHasNoReceiveFunction() (gas: 9900)

Suite result: ok. 9 passed; 0 failed; 0 skipped
```

### Affected Code Locations

1. **Primary Issue**: `LidoDepositHook.sol:41`
   ```solidity
   Address.sendValue(payable(wsteth), assets);
   ```

2. **Contributing Factor**: `LidoDepositHook.sol:36-37`
   ```solidity
   if (asset == weth) {
       IWETH(weth).withdraw(assets);  // Creates ETH that cannot be sent to wstETH
   }
   ```

3. **Interface Gap**: `IWSTETH.sol` - Missing receive function specification
   ```solidity
   interface IWSTETH {
       function stETH() external view returns (address);
       function wrap(uint256 _stETHAmount) external returns (uint256);
       function unwrap(uint256 _wstETHAmount) external returns (uint256);
       // ❌ No receive() function declared
   }
   ```

## Recommendations

### Immediate Fix
Replace the direct ETH transfer with the proper ETH → stETH → wstETH conversion flow:

```solidity
// Instead of: Address.sendValue(payable(wsteth), assets);
// Use proper Lido flow:

if (asset == weth) {
    IWETH(weth).withdraw(assets);
}

// Convert ETH to stETH via Lido's submit function
ILido(steth).submit{value: assets}(address(0));

// Get the stETH balance and wrap to wstETH
uint256 stethBalance = IERC20(steth).balanceOf(address(this));
IERC20(steth).safeIncreaseAllowance(wsteth, stethBalance);
IWSTETH(wsteth).wrap(stethBalance);
```

### Additional Considerations
1. **Add ILido interface** for the `submit()` function
2. **Handle stETH/wstETH exchange rate** variations
3. **Add slippage protection** for the ETH → stETH conversion
4. **Consider gas optimization** for the multi-step conversion
5. **Add comprehensive tests** for the ETH/WETH paths

## Conclusion

This vulnerability represents a critical design flaw that completely breaks ETH and WETH deposit functionality. The issue is deterministic and will affect all users attempting to deposit these assets. While no funds are directly at risk, the denial of service significantly impacts the protocol's usability and user experience.

The fix requires implementing the proper Lido protocol flow: ETH → stETH (via submit) → wstETH (via wrap), rather than attempting impossible direct ETH transfers to the wstETH contract.

**Severity: HIGH**
**Status: CONFIRMED**
**Recommendation: IMMEDIATE FIX REQUIRED**