const { task } = require('hardhat/config');
const { TASK_TEST_SETUP_TEST_ENVIRONMENT } = require('hardhat/builtin-tasks/task-names');
const { setCode } = require('@nomicfoundation/hardhat-network-helpers');

const fs = require('fs');
const path = require('path');

const INSTANCES = {
  // ERC-4337 Entrypoints
  entrypoint: {
    v07: {
      address: '0x0000000071727De22E5E9d8BAf0edAc6f37da032',
      abi: JSON.parse(fs.readFileSync(path.resolve(__dirname, '../test/bin/EntryPoint070.abi'), 'utf-8')),
      bytecode: fs.readFileSync(path.resolve(__dirname, '../test/bin/EntryPoint070.bytecode'), 'hex'),
    },
    v08: {
      address: '0x4337084D9E255Ff0702461CF8895CE9E3b5Ff108',
      abi: JSON.parse(fs.readFileSync(path.resolve(__dirname, '../test/bin/EntryPoint080.abi'), 'utf-8')),
      bytecode: fs.readFileSync(path.resolve(__dirname, '../test/bin/EntryPoint080.bytecode'), 'hex'),
    },
  },
  senderCreator: {
    v07: {
      address: '0xEFC2c1444eBCC4Db75e7613d20C6a62fF67A167C',
      abi: JSON.parse(fs.readFileSync(path.resolve(__dirname, '../test/bin/SenderCreator070.abi'), 'utf-8')),
      bytecode: fs.readFileSync(path.resolve(__dirname, '../test/bin/SenderCreator070.bytecode'), 'hex'),
    },
    v08: {
      address: '0x449ED7C3e6Fee6a97311d4b55475DF59C44AdD33',
      abi: JSON.parse(fs.readFileSync(path.resolve(__dirname, '../test/bin/SenderCreator080.abi'), 'utf-8')),
      bytecode: fs.readFileSync(path.resolve(__dirname, '../test/bin/SenderCreator080.bytecode'), 'hex'),
    },
  },
  deployer: {
    // Arachnid's deterministic deployment proxy
    // See: https://github.com/Arachnid/deterministic-deployment-proxy/tree/master
    arachnid: {
      address: '******************************************',
      abi: [],
      bytecode:
        '0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe03601600081602082378035828234f58015156039578182fd5b8082525050506014600cf3',
    },
    // Micah's deployer
    micah: {
      address: '******************************************',
      abi: [],
      bytecode: '0x60003681823780368234f58015156014578182fd5b80825250506014600cf3',
    },
  },
};

const setup = (input, ethers) =>
  input.address && input.abi && input.bytecode
    ? setCode(input.address, '0x' + input.bytecode.replace(/0x/, '')).then(() =>
        ethers.getContractAt(input.abi, input.address),
      )
    : Promise.all(
        Object.entries(input).map(([name, entry]) => setup(entry, ethers).then(result => [name, result])),
      ).then(Object.fromEntries);

task(TASK_TEST_SETUP_TEST_ENVIRONMENT).setAction((_, env, runSuper) =>
  runSuper().then(() => setup(INSTANCES, env.ethers).then(result => Object.assign(env, result))),
);
